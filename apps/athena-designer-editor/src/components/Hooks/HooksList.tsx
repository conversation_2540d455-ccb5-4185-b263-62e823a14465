/**
 * TODO: 由于submitAction和operation都开始有特定的类型转到组件类型hooks，所以已经打破了之前的整个设计结构
 * 造成了大量的特殊判断
 * 后续需要抽时间重新构建整个匹配逻辑
 */
import React, {
  forwardRef,
  useImperativeHandle,
  useEffect,
  useMemo,
  useContext,
  useState,
} from 'react';
import { HookItem } from '@components/Hooks/components/HooksItem';
import { Collapse, Empty } from 'antd';
import { HooksComprehensiveEditor } from '@components/Hooks/components/HooksCompprehensiveEditor';
import { config, project } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import { HooksContextWrapperHoc } from '@components/Hooks/components/HOC/HooksContextWrapperHoc';

import { HooksContext } from '@components/Hooks/context/HooksContext';
import { useTranslation } from 'react-i18next';

import {
  getRenderInfoByHook,
  getCustomHooksByEventSource,
  transformPageHooksToOutterHooks,
  translateOutterHookToOutputHook,
  getHookNameFromEventSource,
  getCurrentComponentList,
  getAiDslInfoData,
  getAiDslInfoComponent,
} from '@components/Hooks/tools/hooks';
import { EventSourceType, OpType, HookType } from '@components/Hooks/enum';
import { EventSourceSymbol, EventSourceTypeSymbol } from '@components/Hooks/constant';

import './hooksList.less';
import hookJson from '@/assets/config/hooks.json';

import type { CollapseProps } from 'antd';
import type {
  IHooksListProps,
  IPageHookInfo,
  ISVPackageDataInfo,
  IHookModalInfo,
} from '@components/Hooks/types/hooks';
import type { IHooksListRef } from '@components/Hooks/types/component';
import type { IComprehensiveEditorState } from './types/useHooks';
import type { IFieldTreeNodeInfo } from '@core_types/components/DynamicWorkDesign/config/type/common';

const HooksListComponent = forwardRef<IHooksListRef, IHooksListProps>(
  (props: IHooksListProps, ref: React.ForwardedRef<IHooksListRef>) => {
    const {
      outterHooks = [],
      hooksChanges,
      fieldData,
      searchValue,
      isMobile,
      useCollapse = true,
    } = props;

    const {
      editorLoading = false,
      pageHooks,
      updateMark,
      hooksConfig,
      setPageHooks,
      setHooksConfig,
      addHook,
      comprehensiveEditorState,
      setComprehensiveEditorState,
      hideComprehensiveEditor,
      hideEditorLoading,
      showEditorLoading,
    } = useContext(HooksContext);
    const { t } = useTranslation();
    const [isvPackageInfoLists, setISVPackageInfoLists] = useState<ISVPackageDataInfo[]>([]);

    useEffect(() => {
      const info = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
      setISVPackageInfoLists(info?.isvPackageDataList ?? []);
    }, []);

    const pageInfoHooks: Map<EventSourceType, IPageHookInfo[]> = useMemo(() => {
      const result: Map<EventSourceType, IPageHookInfo[]> = new Map();
      pageHooks.forEach((hook) => {
        const eventSourceType: EventSourceType = hook[EventSourceTypeSymbol] as EventSourceType;
        const showEventSourceType =
          eventSourceType === EventSourceType.ACTION ? EventSourceType.COMPONENT : eventSourceType;
        const hooks = result.get(showEventSourceType) ?? [];
        const eventSource = hook[EventSourceSymbol];
        const customHooks = getCustomHooksByEventSource(eventSource, isvPackageInfoLists);
        const renderHook = getRenderInfoByHook(hook, hooksConfig!, customHooks, isMobile);
        result.set(showEventSourceType, [...hooks, renderHook]);
      });
      return result;
    }, [pageHooks, hooksConfig, isMobile, fieldData, isvPackageInfoLists]);

    const pageRenderHooks: Map<EventSourceType, IPageHookInfo[]> = useMemo(() => {
      if (!searchValue) return pageInfoHooks;
      const renderHooks: Map<EventSourceType, IPageHookInfo[]> = new Map();
      for (let [type, hooks] of pageInfoHooks.entries()) {
        if (hooks.length > 0) {
          const newHooks = hooks.filter((hook) =>
            hook.itemDesc.toLowerCase().includes(searchValue.toLowerCase()),
          );
          if (newHooks.length > 0) {
            renderHooks.set(type, newHooks);
          }
        }
      }
      return renderHooks;
    }, [searchValue, pageInfoHooks]);

    useEffect(() => {
      setHooksConfig(hookJson ?? {});
    }, []);

    /**
     * 初始化hooks页面数据
     */
    useEffect(() => {
      setPageHooks(outterHooks);
    }, [outterHooks]);

    /**
     * 专门处理回调
     */
    useEffect(() => {
      if (updateMark && hooksChanges) {
        const outputHooks = translateOutterHookToOutputHook(
          transformPageHooksToOutterHooks(pageHooks),
        );
        hooksChanges(outputHooks);
      }
    }, [pageHooks, updateMark]);

    useImperativeHandle(
      ref,
      () => {
        return {
          getPageHooks: () => {
            return pageHooks;
          },
          getOutputHooks: () => {
            return translateOutterHookToOutputHook(transformPageHooksToOutterHooks(pageHooks));
          },
          openDataModal: (info?: Partial<IComprehensiveEditorState>) => {
            setComprehensiveEditor({
              visible: true,
              opType: OpType.ADD,
              hookType: HookType.DATA,
              fieldData,
              ...info,
            });
          },
          openComponentModal: (info: Partial<IComprehensiveEditorState>) => {
            const { hook } = info ?? {};
            const { eventSource } = hook ?? {};
            const dynamicInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
            const customHooks = getCustomHooksByEventSource(
              eventSource!,
              dynamicInfo?.isvPackageDataList ?? [],
            );
            setComprehensiveEditor({
              visible: true,
              opType: OpType.ADD,
              hookType: HookType.COMPONENT,
              fieldData,
              customHooks,
              ...info,
            });
          },
          openActionModal: (info: Partial<IComprehensiveEditorState>) => {
            setComprehensiveEditor({
              visible: true,
              opType: OpType.ADD,
              hookType: HookType.ACTION,
              fieldData,
              ...info,
            });
          },
        };
      },
      [pageHooks],
    );

    const setComprehensiveEditor = (info: Partial<IComprehensiveEditorState>) => {
      const operations = project.currentDocument?.root?.propsData?.operations ?? [];
      const componentLists = getCurrentComponentList();
      const fieldTree: IFieldTreeNodeInfo[] = config.get(AthLowCodeConfigKey.AthFieldTree);
      const fieldTreeMap: Map<string, any> = config.get(AthLowCodeConfigKey.AthFieldTreeMap);
      setComprehensiveEditorState({
        visible: true,
        opType: OpType.ADD,
        hookType: HookType.ACTION,
        componentList: componentLists,
        fieldData: fieldTree,
        fieldTreeMap,
        dslInfoData: getAiDslInfoData(fieldTree),
        dslInfoComponent: getAiDslInfoComponent(componentLists, operations),
        ...info,
        onOk: async (data?: IHookModalInfo) => {
          showEditorLoading();
          const failure = addHook(data!);
          if (!failure) {
            await hideComprehensiveEditor();
          }
          hideEditorLoading();
        },
        onCancel: () => {
          hideComprehensiveEditor(true);
        },
      });
    };

    const renderCollapseLists = (hooks: IPageHookInfo[] = []) => {
      return (
        <div className="collapse-container">
          {hooks.map((hook) => {
            // const key = getHookNameFromEventSource(hook);
            const eventSource = hook[EventSourceSymbol];
            const componentLists = getCurrentComponentList();
            const customHooks = getCustomHooksByEventSource(eventSource, isvPackageInfoLists);
            const nodesMap = project.currentDocument?.nodesMap;
            if (!nodesMap) return null;
            let componentType;
            for (const node of nodesMap.values()) {
              const dslInfo = node.getPropValue('dslInfo');
              if (dslInfo?.id === eventSource) {
                componentType = dslInfo.type;
                break;
              }
            }
            return (
              <HookItem
                key={hook.id}
                id={hook.id}
                schema={hook.itemFieldName}
                fieldData={fieldData}
                componentList={componentLists}
                componentType={componentType}
                customHooks={customHooks}
                hook={hook}
                isMobile={isMobile}
              />
            );
          })}
        </div>
      );
    };

    const renderCollapse = () => {
      const items: CollapseProps['items'] = [];
      const labelMap = new Map([
        [EventSourceType.DATA, t('dj-数据Hooks')],
        [EventSourceType.COMPONENT, t('dj-组件Hooks')],
      ]);
      let dataHooks: IPageHookInfo[] = [];
      let componentHooks: IPageHookInfo[] = [];
      for (const [type, hooks] of pageRenderHooks.entries()) {
        if (type === EventSourceType.DATA) {
          dataHooks = dataHooks.concat(hooks);
        } else {
          componentHooks = componentHooks.concat(hooks);
        }
      }
      if (dataHooks.length > 0) {
        items.push({
          key: EventSourceType.DATA,
          label: labelMap.get(EventSourceType.DATA),
          children: renderCollapseLists(dataHooks),
        });
      }
      if (componentHooks.length > 0) {
        items.push({
          key: EventSourceType.COMPONENT,
          label: labelMap.get(EventSourceType.COMPONENT),
          children: renderCollapseLists(componentHooks),
        });
      }
      return (
        <Collapse
          defaultActiveKey={[
            EventSourceType.DATA,
            EventSourceType.COMPONENT,
          ]}
          ghost
          items={items}
        />
      );
    };

    const renderContent = () => {
      if (pageRenderHooks.size === 0) {
        return <Empty style={{ marginTop: '18px' }} description={t('dj-暂无数据')} />;
      } else {
        if (!useCollapse) {
          return renderCollapseLists(pageRenderHooks.values().next().value as IPageHookInfo[]);
        } else {
          return renderCollapse();
        }
      }
    };

    return (
      <div className="hooks-list">
        {renderContent()}
        {comprehensiveEditorState?.visible && (
          <HooksComprehensiveEditor
            {...comprehensiveEditorState}
            loading={editorLoading}
            systemConfig={config.get(AthLowCodeConfigKey.AthSystemConfig ?? {})}
          />
        )}
      </div>
    );
  },
);

const HooksList = HooksContextWrapperHoc<IHooksListProps, IHooksListRef>(HooksListComponent);

export { HooksList };
