import React, { useContext, useState } from 'react';
import { Tooltip, Dropdown, MenuProps } from 'antd';
import Icon from '@/components/Icon';
import { CommonModal } from '@/components/CommonModal';

import { useTranslation } from 'react-i18next';
import { HooksContext } from '@components/Hooks/context/HooksContext';
import { EventSourceType, HookType, OpType } from '@components/Hooks/enum';
import {
  DescriptionSymbol,
  EventSourceSymbol,
  EventSourceTypeSymbol,
  TypeSymbol,
} from '@components/Hooks/constant';
import { getAiDslInfoComponent, getAiDslInfoData, getCurrentComponentList, getHookNameFromEventSource } from '@components/Hooks/tools/hooks';

import './index.less';

import type { IHookItemProps, IHookModalInfo } from '@components/Hooks/types/hooks';
import { IFieldTreeNodeInfo } from '@core_types/components/DynamicWorkDesign/config/type';
import { config, project } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import { componentIsButton } from '../../tools';
import { ButtonType } from '@/plugins/plugin-ath-setter/components/Button/enum';

function HookItem(props: IHookItemProps) {
  const { hook, schema, isMobile = false, customHooks = [], componentType } = props;
  const {
    updateHook,
    deleteHook,
    setComprehensiveEditorState,
    hideComprehensiveEditor,
    hideEditorLoading,
    showEditorLoading,
  } = useContext(HooksContext);
  const { t } = useTranslation();
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);

  const {
    itemFieldName,
    hookName,
    [DescriptionSymbol]: description,
  } = hook;


  const doOpenEditor = (event) => {
    if (event?.domEvent) {
      event?.domEvent?.stopPropagation();
    }
    if (hook[EventSourceTypeSymbol] === EventSourceType.DATA) {
      const hookName = getHookNameFromEventSource(hook);
      const operations = project.currentDocument?.root?.propsData?.operations ?? [];
      const componentLists = getCurrentComponentList();
      const fieldTree: IFieldTreeNodeInfo[] = config.get(AthLowCodeConfigKey.AthFieldTree);
      setComprehensiveEditorState({
        visible: true,
        opType: OpType.EDIT,
        hookName,
        value: hook[hookName],
        hookType: HookType.DATA,
        fieldData: fieldTree,
        dslInfoData: getAiDslInfoData(fieldTree),
        dslInfoComponent: getAiDslInfoComponent(componentLists, operations),
        hook: {
          eventSource: hook[EventSourceSymbol],
          eventSourceType: hook[EventSourceTypeSymbol],
          description: hook[DescriptionSymbol],
          type: hook[TypeSymbol],
        },
        onOk: async (data?: IHookModalInfo) => {
          showEditorLoading();
          const failure = updateHook(hook.id, data!);
          if (!failure) {
            await hideComprehensiveEditor();
          }
          hideEditorLoading();
        },
        onCancel: () => {
          hideComprehensiveEditor(true);
        },
      });
    // } else if ([EventSourceType.ACTION, EventSourceType.COMPONENT].includes(hook[EventSourceTypeSymbol])) {
    } else {
      const hookName = getHookNameFromEventSource(hook);
      const isButton = componentIsButton(componentType as ButtonType);
      const isMobile: boolean = false;
      const operations = project.currentDocument?.root?.propsData?.operations ?? [];
      const componentLists = getCurrentComponentList();
      const fieldTree: IFieldTreeNodeInfo[] = config.get(AthLowCodeConfigKey.AthFieldTree);
      setComprehensiveEditorState({
        visible: true,
        opType: OpType.EDIT,
        hookType: HookType.COMPONENT,
        hook: {
          eventSource: hook[EventSourceSymbol],
          eventSourceType: hook[EventSourceTypeSymbol],
          description: hook[DescriptionSymbol],
          type: hook[TypeSymbol],
        },
        fieldData: fieldTree,
        dslInfoData: getAiDslInfoData(fieldTree),
        dslInfoComponent: getAiDslInfoComponent(componentLists, operations),
        value: hook[hookName],
        hookName,
        isMobile,
        customHooks,
        componentType,
        isButton,
        componentList: componentLists,
        schema: schema!,
        onOk: async (data?: IHookModalInfo) => {
          showEditorLoading();
          const failure = updateHook(hook.id, data!);
          if (!failure) {
            await hideComprehensiveEditor();
          }
          hideEditorLoading();
        },
        onCancel: () => {
          hideComprehensiveEditor(true);
        },
      });
    }
    // else if ([EventSourceType.TOOLBAR, EventSourceType.OPERATION].includes(hook[EventSourceTypeSymbol] as EventSourceType)) {
    //   const hookName = getHookNameFromEventSource(hook);
    //   const operations = project.currentDocument?.root?.propsData?.operations ?? [];
    //   const componentLists = getCurrentComponentList();
    //   const fieldTree: IFieldTreeNodeInfo[] = config.get(AthLowCodeConfigKey.AthFieldTree);
    //   setComprehensiveEditorState({
    //     visible: true,
    //     opType: OpType.EDIT,
    //     hookType: HookType.ACTION,
    //     hookName,
    //     value: hook[hookName],
    //     fieldData: fieldTree,
    //     dslInfoData: getAiDslInfoData(fieldTree),
    //     dslInfoComponent: getAiDslInfoComponent(componentLists, operations),
    //     hook: {
    //       eventSource: hook[EventSourceSymbol],
    //       eventSourceType: hook[EventSourceTypeSymbol],
    //       description: hook[DescriptionSymbol],
    //       type: hook[TypeSymbol],
    //     },
    //     actionHookTitle: hook.itemFieldName,
    //     onOk: async (data?: IHookModalInfo) => {
    //       showEditorLoading();
    //       const failure = updateHook(hook.id, data!);
    //       if (!failure) {
    //         await hideComprehensiveEditor();
    //       }
    //       hideEditorLoading();
    //     },
    //     onCancel: () => {
    //       hideComprehensiveEditor(true);
    //     },
    //   });
    // }
  };

  const doDelete = (event) => {
    if (event?.domEvent) {
      event?.domEvent?.stopPropagation();
    }
    setDeleteModalVisible(true);
  };

  const doConfirmDelete = () => {
    deleteHook(hook.id);
    doCloseDeleteModal();
  };

  const doCloseDeleteModal = () => {
    setDeleteModalVisible(false);
  };

  const menus: MenuProps['items'] = [
    {
      key: 'edit',
      label: t('dj-编辑'),
      onClick: doOpenEditor
    },
    {
      key: 'delete',
      label: t('dj-删除'),
      onClick: doDelete,
    }
  ];

  return (
    <div className="hook-list-item">
      <Tooltip overlayClassName="mf-override-tooltip" placement="top" title={description}>
        <div className="item-wrapper" onClick={doOpenEditor}>
          <div className="item-title">
            <div className="item-title-text">{itemFieldName}</div>
            <Dropdown overlayClassName='mf-override-dropdown' menu={{ items: menus }} placement='bottomRight'>
              <Icon type='icongengduo1' className='iconfont' />
            </Dropdown>
          </div>
          <div className="item-title-subtext">{hookName}</div>
          <div className="item-title-subtext">{description}</div>
        </div>
      </Tooltip>
      <CommonModal
        wrapperClassName="delete-modal"
        width={400}
        visible={deleteModalVisible}
        showTitle={false}
        ok={doConfirmDelete}
        close={doCloseDeleteModal}
      >
        <div className="modal-content">{t('dj-确认删除？')}</div>
      </CommonModal>
    </div>
  );
}

export { HookItem };
