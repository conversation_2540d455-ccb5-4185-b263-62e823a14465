import { EventSourceSymbol, EventSourceTypeSymbol, DescriptionSymbol, TypeSymbol } from '@components/Hooks/constant';

import type { ILangInfo, Sboolean, IAnyObj, ILangCollectInfo } from '@components/Hooks/types/common';
import type { IFieldTreeNodeInfo } from '../../../../../athena-designer-core/src/components/DynamicWorkDesign/config/type/common';

/**
  * 数据hook入参hook信息
  */
export interface IEventSourceObj {
  description: string;
  eventSource: string;
  eventSourceType: string;
  itemDesc: string;
  itemFieldName: string;
  itemTypeName: string;
  [hookName: string]: string;
}

/**
  * 字段树信息
  */
export interface IFieldTreeNode {
  value: string;
  title: string;
  isLeaf: boolean;
  origin: IFieldTreeNodeInfo;
  children?: IFieldTreeNode[];
}

/**
  * 字段信息
  */
export interface IFieldData {
  data_name: string;
  data_type: string;
  fieldId?: string;
  fullPath: string;
  key?: string;
  parentCategory?: string;
  path?: string;
  required?: Sboolean;
  target?: string;
  title?: string;
  userTarget?: string;
  is_array?: Sboolean;
  is_businesskey?: Sboolean;
  is_datakey?: Sboolean;
  description?: ILangInfo;
  category?: string;
  field?: IFieldData[];
  [key: string]: unknown;
}


/**
  * formio实例中的customOptions
  */
export interface IFormIOCustomOptions {
  id: string;
  type: string;
  componentId: string;
  schema: string;
}

/**
  * 组件信息
  */
export interface IComponentInfo {
  type: string;
  value?: string;
  schema: string;
  path?: string;
  id?: string;
  label?: ILangInfo;
  editable?: boolean;
  title?: string;
  lang?: ILangCollectInfo;
  items?: IComponentInfo[];
  dslInfo?: any;
}

/**
  * hook开窗内部hook基础信息
  */
export interface IHookModalInfo {
  description: string;
  eventSource: string;
  eventSourceType: string;
  hookName: string;
  [key: string]: string;
}


export type IHookInfo = {
  [DescriptionSymbol]: string;
  [EventSourceSymbol]: string;
  [EventSourceTypeSymbol]: string;
  /**
    * 移动端特有字段
    */
  [TypeSymbol]?: string;
  [key: string]: string;
};

/**
  * ==================================================
  * 这边针对说明一下为什么设计两个外部hook的类型定义IOutterHookInfo,IOutputHookInfo:
  * -- 首先问题的issue地址：https://github.com/Microsoft/TypeScript/issues/17867
  * 主要问题在于ts中一旦在类型定义中使用了indexded map：比如[key: string]: string;这样，当我再在该类型中定义其余属性，如age: number;或者info: IAnyObj;这些非string的属性
  * 就会造成ts的类型报错，处理方式就是将indexed map类型扩大，比如[key: string]: string | number | IAnyObj;这样就造成了类型的泛化
  * 而hook的数据结构足够恶心，个人还是觉得类型足够清晰在这边很重要，所以在外部hook结构(IOutputHookInfo)和内部hook(IHookInfo)结构的中间插入了(IOutterHookInfo)
  * 以牺牲一点性能的代价保证组件内部的hook数据结构的足够清晰,至于最终抛出的IOutputHookInfo，类型不明确，就是调用方的问题了;由于名称类似，下面针对两种类型进行说明：
  * IOutputHookInfo：作为实际对外输出的hook数据结构，是change回调的参数
  * IOutterHookInfo：作为转换了一层，用于组件内部的，定义为来自外部传入的hook数据结构, 这个定义为外部传入，主要是和IHookInfo这个实际用于内部流转的hook数据结构进行区分
  * ==================================================
  */

/**
  * IOutputHookInfo进入组件后会先行转成IOutterHookInfo
  * 这边这么做的原因来自于ts对于使用了indexed map的类型泛化问题的处理,保证组件内部类型的足够明确
  */
export interface IOutterHookInfo {
  [DescriptionSymbol]: IAnyObj<string>;
  [EventSourceSymbol]: string;
  [EventSourceTypeSymbol]: string;
  /**
    * 移动端特有字段
    */
  [TypeSymbol]?: string;
  [key: string]: string;
};

/**
  * 实际和外部对接的hook信息结构
  */
export interface IOutputHookInfo {
  [key: string]: string | IAnyObj<string> | undefined;
}

/**
  * 页面基于IHookInfo额外生成用于渲染的额外字段
  */
export interface IPageHookInfo extends IHookInfo {
  id: string;
  itemDesc: string;
  itemFieldName: string;
  itemTypeName: string;
  componentType: string;
}

/**
  * 数据hook表单字段
  */
export interface IDataHooksForm {
  eventSource?: string;
  /**
    * 隐藏字段
    */
  eventSourceType?: string;
  hookName: string;
  description?: string;
}

/**
  * 组件hook表单字段
  */
export interface IComponentHooksForm {
  eventSource?: string;
  /**
    * 隐藏字段
    */
  eventSourceType?: string;
  hookName?: string;
  description?: string;
  schema?: string;
}

export interface IActionHooksForm {
  eventSource?: string;
  /**
    * 隐藏字段
    */
  eventSourceType?: string;
  hookName?: string;
  description?: string;
  /**
   * 关联submitAction的标题
   */
  title?: string;
}

/**
  * hook list组件入参
  */
export interface IHooksListProps {
  /**
    * 外部的hook数组数据，会被转成页面的hook数据使用
    */
  outterHooks: IOutputHookInfo[];

  /**
    * formio组件信息
    */
  // componentLists?: IComponentInfo[];
  hookSelectList?: IHookParams[];
  /**
    * isv信息, 用来根据组件类型获取自定义hook
    */
  isvPackageInfoLists?: ISVPackageDataInfo[];
  /**
    * 搜索文本
    */
  searchValue?: string;

  /**
    * 是否移动端
    */
  isMobile?: boolean;
  /**
    * 字段信息 data 类型hook需要
    */
  fieldData?: IFieldTreeNodeInfo[];
  /**
    * 是否使用折叠面板分组
    */
  useCollapse?: boolean;
  /**
    * hook数据变更回调，回调入参会转成外部的hook数据结构提供
    */
  hooksChanges?: (hooks: IOutputHookInfo[]) => void;
}

/**
  * HookItem组件入参申明
  */
export interface IHookItemProps {
  /**
    * hook id
    */
  id: string;
  /**
    * page render hook
    */
  hook: IPageHookInfo;
  /**
    * 关联的schema, component 类型hook需要
    */
  schema?: string;
  /**
    * 字段信息 data 类型hook需要
    */
  fieldData?: IFieldTreeNodeInfo[];
  /**
    * 是否移动端, component 类型hook需要
    */
  isMobile?: boolean;
  /**
    * 组件列表信息, component 类型hook需要
    */
  componentList?: IComponentInfo[];
  /**
    * 自定义hook列表, component 类型hook需要
    */
  customHooks?: IHookParams[];
  /**
   * submitAction title，action 类型hooks需要
   */
  title?: string;
  componentType?: string;
}

/**
  * hook配置信息
  */
export interface IHookParams {
  name: string;
  params: string[];
  description: string;
  lang: ILangInfo;
}


/**
  * hook组件配置信息, key是组件类型
  */
export interface IHooksComponentConfig {
  [key: string]: IHookParams[];
}

/**
 * param对应的描述文案map定义
 */
export interface IParamDescription {
  component: string;
  options: string;
  parentData: string;
  value: string;
  status: string;
  e: string;
  event: string;
  dataControl: string;
  index: string;
  button: string;
  response: string;
  changes: string;
  callback: string;
  openWindowParams: string;
  tableComponent: string;
}

/**
  * 全局hook 配置
  */
export interface IHookConfig {
  /**
   * 通用控件勾子信息
   */
  ComponentCommonHooksMobile: IHookParams[];
  /**
   * 通用控件勾子信息
   */
  ComponentCommonHooks: IHookParams[];
  /**
   * 页面/弹框类特有的hook
   */
  PgaeTyepHooksMobile: IHookParams[];
  /**
   * 录入类型的组件hook
   */
  InputTypeOnChangeHook: IHookParams;
  InputTypeOnCompleteHook: IHookParams;
  /**
   * 布局类组件hook
   */
  LayoutTypeOnTapHook: IHookParams;
  LayoutTypeOnTabChangeHook: IHookParams;
  LayoutTypeOnPageChangeHook: IHookParams;
  LayoutTypeOnScrollHook: IHookParams;
  /**
   * 按钮类组件hook
   */
  ButtonTypeOnTapHook: IHookParams;
  ButtonTypeOnLongTapHook: IHookParams;
  ButtonTypeOnDoubleTapHook: IHookParams;
  /**
   * mobile组件私有勾子信息
   * https://mobile-digiwin.yuque.com/cog7oa/gbmk5r/xqzn7ri08aphe2rt#J2Da1
   */
  ComponentCustomHooksMobile: IHooksComponentConfig;
  /**
   * 私有控件类型 https://docs.qq.com/aio/DR1Vpa1RiREJ5VWVa?p=1JreW2rClMVvqXUc30gSjF
   * 1 按钮 (BUTTON)
   * 2 表单 (FORM_LIST)
   * 3 表格 (ATHENA_TABLE)
   * 4 输入框
   * 4.1 文本输入框 (INPUT)
   * 4.2 数字输入框 (INPUT_NUMBER)
   * 4.3 计量输入框 (MEASURE)
   * 4.4 百分比输入框 (PERCENT_INPUT)
   * 4.5 多行文本 (TEXTAREA)
   * 4.6 金额输入框 (AMOUNT_INPUT)
   * 5 下拉框
   * 5.1 单选下拉 (SELECT)
   * 5.2 多选下拉 (SELECT_MULTIPLE)
   * 6 日期
   * 6.1 普通日期 (DATEPICKER)
   * 6.2 日期范围 (DATE_RANGE)
   * 7 时间
   * 7.1 普通时间 (TIMEPICKER)
   * 7.2 时间范围 (TIME_RANGE)
   * 8 单选 (RADIO_GROUP)
   * 9 多选 (CHECKBOX)
   * 10 EOC单选
   * 10.1 运营单元选择 (EOC_SELECT)、(EOC_MULTI_SELECT)
   * 10.2 员工选择 (EOC_USER_SELECT)
   * 11 金额 (4.6)
   * 12 数量 (4.3)
   * 13 百分比 (4.4)
   * 14 开窗 (OPERATION_EDITOR/FORM_OPERATION_EDITOR)
   * 15 上传
   * 15.1 表单中上传 (FORM_UPLOAD)
   * 15.2 表格中上传 (FILE_UPLOAD)
   * 16 页签 (TABS)
   * 17 抽屉 (DRAWER_BUTTON)
   * 18 EOC多选 (10.1)
   */
  ComponentCustomHooks: IHooksComponentConfig;
  /**
   * 通用字段勾子信息
   */
  DataCommonHooks: IHookParams[];
  /**
   * operations和toolbar相关hooks配置
   */
  // OperationsToolbarHooks: IHookParams[];
  /**
   * 子页面钩子信息
   */
  SubpageButtonHooks: IHookParams[];
  /**
   * 提交按钮勾子信息
   */
  SubmitActionsHooks: IHookParams[];
  /**
   * param对应的藐视文案配置
   */
  ParamDescription: IParamDescription;
}

/**
  * 将外部hooks列表转成内部hooks列表的方法的返回值
  */
export interface ITransferOutterToPageReturn {
  /**
    * 页面中实际使用的hooks列表
    */
  result: IHookInfo[];
  /**
    * 当转换方法传递了componentInfo，表示当前hooksList用在制定组件上，那么这时候就会过滤掉不想干的组件hooks，留存在cache中，方便输出hooks重组
    */
  cache: IOutterHookInfo[];
}

export interface IISVComponentInfo {
  hooks?: IHookParams[];
  type?: string;
}

/**
  * 不关注用不到
  */
export interface IPackageInfo {
  code?: string;
  name?: string;
  type?: string;
}

export interface ISVPackageDataInfo {
  isvCustomComponentInfoList?: IISVComponentInfo[];
  packageInfo?: IPackageInfo;
}
