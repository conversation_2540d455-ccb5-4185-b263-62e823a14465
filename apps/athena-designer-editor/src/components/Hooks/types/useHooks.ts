import { ActionType, HookType, OpType } from '@components/Hooks/enum';
import { Dispatch } from 'react';
import { SupportedEditorType } from '../enum/editor';

import type {
  IHookInfo,
  IOutterHookInfo,
  IOutputHookInfo,
  IEventSourceObj,
  IHookConfig,
  IComponentInfo,
  IHookParams,
  IHookModalInfo,
} from '@components/Hooks/types/hooks';
import type {
  IEditorModalProps,
  IEditorModalCallback,
  IHooksProps,
  IFieldTreeNodeInfo,
} from '@core_types/components/MonacoEditor/types/export';
import type { CommonCallback } from '@components/Hooks/types/common';
import type { EditorProps, IEditorTitleState, IToolbarState } from './editor';

export interface ICoreHookInfo {
  description: string;
  eventSource: string;
  eventSourceType: string;
  [key: string]: string | undefined;
}

export interface IAiParamsDslInfoData {
  schema?: string;
  fullPath?: string;
  description?: string;
}

export interface IAiParamsDslInfoComponent {
  schema?: string;
  fullPath?: string;
  id?: string;
  type?: string;
  description?: string;
}

export interface IComprehensiveEditorState extends EditorProps, IEditorTitleState, IToolbarState {
  /**
   * 开窗展示/隐藏
   */
  visible: boolean;
  /**
   * 操作类型
   */
  opType: OpType;
  /**
   * hook类型
   */
  hookType: HookType;
  hookName?: string;
  /**
   * 数据hook关联的字段
   */
  fieldData?: IFieldTreeNodeInfo[];
  fieldTreeMap?: Map<string, any>;
  /**
   * 数据hook相关信息
   */
  eventSourceObj?: IEventSourceObj;
  componentList?: IComponentInfo[];
  componentType?: string;
  customHooks?: IHookParams[];
  /**
   * 关联的schema
   */
  schema?: string;
  /**
   * hook信息
   */
  hook?: ICoreHookInfo;
  isMobile?: boolean;
  /**
   * 编辑器类型
   */
  type?: SupportedEditorType;
  /**
   * 编辑器之上，弹窗title之下的一个行组件容器, 后续称之为toolbar容器,可以自定义全部内容，可以在插槽中提供相应的小组件
   */
  showToolbar?: boolean;
  /**
   * 开窗宽度，默认1000
   */
  width?: number | string;
  /**
   * 编辑器高度，默认560
   */
  height?: number | string;
  /**
   * 是否需要默认的编辑器设置, 目前只有js编辑器会增加设定，默认为true
   */
  needDefaultSet?: boolean;
  actionHookTitle?: string;
  dslInfoComponent?: IAiParamsDslInfoComponent[];
  dslInfoData?: IAiParamsDslInfoData[];
  /**
   * 方便hooks组件内部判断使用
   */
  isButton?: boolean;
  // eventSourceField?: IHooksEventSourceField;
  /**
   * 开窗确认回调
   */
  onOk: CommonCallback<IHookModalInfo>;
  /**
   * 开窗关闭回调
   */
  onCancel: CommonCallback;
}

export interface IUseHooksState {
  editorLoading: boolean;
  /**
   * 页面使用的hook列表
   */
  pageHooks: IHookInfo[];
  hooksConfig?: IHookConfig;
  /**
   * 是否需要出发更新回调标记
   */
  updateMark: boolean;
  /**
   * 缓存其它组件的关联hooks
   */
  cacheOtherComponentHooks?: IOutterHookInfo[];
  comprehensiveEditorState?: IComprehensiveEditorState;
}

export interface ISetPageHookData {
  hooks: IOutputHookInfo[];
  id?: string;
}

export interface IUseHooksAction {
  type: ActionType;
  data?: unknown;
}

export interface IUpdateHookCodeData {
  id: string;
  code?: string;
}

export interface IUpdateHookInfoData {
  id: string;
  hook: IHookModalInfo;
}

export type IHooksAllProps = IEditorModalProps & IEditorModalCallback & IHooksProps;
export interface IHooksDispatch {
  setPageHooks: (hooks: IOutputHookInfo[]) => void;
  setHooksConfig: (config: IHookConfig) => void;
  setComprehensiveEditorState: (data: IComprehensiveEditorState) => void;
  updateHookCode: (id: string, code?: string) => void;
  updateHook: (id: string, hook: IHookModalInfo) => boolean;
  deleteHook: (id: string) => void;
  hideComprehensiveEditor: () => void;
  addHook: (hook: IHookModalInfo) => boolean;
  hideEditorLoading: () => void;
  showEditorLoading: () => void;
  dispatch: Dispatch<IUseHooksAction>;
}

/**
 * hook store 类型申明
 */
export interface IHooksContext extends IUseHooksState, IHooksDispatch {}
