import React, { useState, useRef, useEffect } from 'react';
import { Input, Button } from 'antd';
import { HooksList } from '@components/Hooks/HooksList';
import { SearchOutlined, PlusOutlined } from '@ant-design/icons';

import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type/ath-loader-manage';
import { useTranslation } from 'react-i18next';
import { useResizerObserverError } from '@/hooks/useResizerObserverError';

import './hooksPanel.less';
import './overrideAntd.less';

import type { IHooksPanelProps } from '@components/Hooks/types/panel';
import type { IOutputHookInfo } from '@components/Hooks/types/hooks';
import type { IHooksListRef } from '@components/Hooks/types/component';
import type { IFieldTreeNodeInfo } from '@core_types/components/DynamicWorkDesign/config/type/common';
import Icon from '@/components/Icon';
function HooksPanel(props: IHooksPanelProps) {
  const { context } = props;
  const { config, project } = context;

  useResizerObserverError();
  const [searchValue, setSearchValue] = useState<string>('');
  const { t } = useTranslation();
  const hooksListRef = useRef<IHooksListRef>(null);
  const [currentHooks, setCurrentHooks] = useState<IOutputHookInfo[]>([]);

  const fieldTree: IFieldTreeNodeInfo[] = config.get(AthLowCodeConfigKey.AthFieldTree);
  console.log('HooksPanel receive fieldTree = ', fieldTree);
  const isMobile: boolean = false;

  useEffect(() => {
    const outterHooks: IOutputHookInfo[] = project.currentDocument?.root?.getPropValue('hooks');
    setCurrentHooks(outterHooks ?? []);
  }, []);

  project.currentDocument?.onChangeNodeProp((info) => {
    if (info.key === 'hooks') {
      setCurrentHooks(info.newValue ?? []);
    }
  });

  const doAddHook = () => {
    if (hooksListRef.current) {
      hooksListRef.current.openDataModal({
        hook: {
          eventSourceType: 'data',
          description: '',
        }
      });
    }
  };

  const doHooksChange = (hooks: IOutputHookInfo[]) => {
    console.log('hooks change = ', hooks);
    project.currentDocument?.root?.setPropValue('hooks', hooks);
  };

  return (
    <div className="hooks-panel mf-override-base">
      <div className="hooks-search">
        <Input
          id="appInputWrapper"
          placeholder={t('dj-搜索')}
          suffix={<SearchOutlined style={{ color: 'rgba(0,0,0,.45)' }} />}
          onChange={(e) => setSearchValue(e.target.value)}
          allowClear={true}
        />
        <div className="add-button" onClick={doAddHook}>
          <Icon type="iconshulidejiahao" />
        </div>
      </div>
      <HooksList
        ref={hooksListRef}
        outterHooks={currentHooks ?? []}
        isMobile={isMobile}
        fieldData={fieldTree}
        searchValue={searchValue}
        hooksChanges={doHooksChange}
      />
    </div>
  );
}

export { HooksPanel };
