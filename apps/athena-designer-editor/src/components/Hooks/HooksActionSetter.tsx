import React, { useRef } from 'react';
import { HooksList } from '@components/Hooks/HooksList';
import { Dropdown } from 'antd';
import { PlusCircleFilled } from '@ant-design/icons';
import { HooksContextWrapperHoc } from '@components/Hooks/components/HOC/HooksContextWrapperHoc';

import { transferSchemaComponentList } from '@components/Hooks/tools';
import { useTranslation } from 'react-i18next';
import { useResizerObserverError } from '@/hooks/useResizerObserverError';
import { project } from '@alilc/lowcode-engine';
import { IPublicEnumTransformStage } from '@alilc/lowcode-types';

import './hooksSetter.less';
import './overrideAntd.less';
import hookJson from '@/assets/config/hooks.json';

import type { MenuProps } from 'antd';
import type { AthHooksSetterProps } from '@components/Hooks/types/setter';
import type { IHookParams, IOutputHookInfo } from '@components/Hooks/types/hooks';
import type { IHooksListRef } from '@components/Hooks/types/component';

function HooksActionSetterComponent(props: AthHooksSetterProps) {
  const { value } = props;

  useResizerObserverError();
  const { t } = useTranslation();
  const hooksListRef = useRef<IHooksListRef>(null);

  const schema = project.currentDocument?.exportSchema(IPublicEnumTransformStage.Save);
  const componentLists = transferSchemaComponentList(schema);
  const isMobile: boolean = false;
  const outterHooks: IOutputHookInfo[] = project.currentDocument?.root?.getPropValue('hooks') ?? [];
  const outterActionHooks = outterHooks.filter(
    (hook: IOutputHookInfo) => hook.eventSource === value.id,
  );
  console.log('HooksPanel receive hooks = ', outterHooks, outterActionHooks);

  const renderDropdownItem = (hook: IHookParams) => {
    return (
      <div className="dropdown-option">
        <div className="hook-name">{hook.name}</div>
        <div className="hook-desc">{hook.description}</div>
      </div>
    );
  };

  const dropdownOptions: MenuProps['items'] = hookJson?.SubmitActionsHooks?.map((hook) => {
    return {
      key: hook.name,
      label: renderDropdownItem(hook),
    };
  });

  const doAddHook: MenuProps['onClick'] = (info) => {
    if (hooksListRef.current) {
      hooksListRef.current.openActionModal({
        hook: {
          eventSource: value.id,
          eventSourceType: 'submitAction',
          description: '',
          type: '',
          [info.key]: '',
        },
      });
    }
  };

  const doHooksChange = (hooks: IOutputHookInfo[]) => {
    console.log('hooks change = ', hooks);
    /**
     * 重新获取的原因是可以开着setter的同时打开hooks plugin
     */
    const outterHooks: IOutputHookInfo[] =
      project.currentDocument?.root?.getPropValue('hooks') ?? [];
    const restHooks = outterHooks.filter((hook: IOutputHookInfo) => hook.eventSource !== value.id);
    project.currentDocument?.root?.setPropValue('hooks', [...restHooks, ...hooks]);
  };

  return (
    <div className="hooks-setter mf-override-base">
      <div className="hooks-add">
        <div className="hooks-add-text">{t('dj-订阅相关事件，添加复杂业务逻辑')}</div>
        <Dropdown
          menu={{ items: dropdownOptions, onClick: doAddHook }}
          overlayClassName="mf-override-dropdown setter-dropdown"
        >
          <PlusCircleFilled />
        </Dropdown>
      </div>
      <HooksList
        ref={hooksListRef}
        outterHooks={outterActionHooks ?? []}
        componentLists={componentLists}
        isMobile={isMobile}
        useCollapse={false}
        hooksChanges={doHooksChange}
      />
    </div>
  );
}
const HooksActionSetter = HooksContextWrapperHoc<AthHooksSetterProps, IHooksListRef>(HooksActionSetterComponent);

export { HooksActionSetter };
