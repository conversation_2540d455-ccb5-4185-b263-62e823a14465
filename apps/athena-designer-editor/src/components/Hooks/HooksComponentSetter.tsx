import React, { useRef, useState, useEffect } from 'react';
import { HooksList } from '@components/Hooks/HooksList';
import { PlusCircleFilled } from '@ant-design/icons';
import { ButtonType } from '@/plugins/plugin-ath-setter/components/Button/enum';
import { useTranslation } from 'react-i18next';
import { useResizerObserverError } from '@/hooks/useResizerObserverError';
import { project } from '@alilc/lowcode-engine';
import { getCustomHooksByEventSource } from '@components/Hooks/tools/hooks';
import { getComponentAllOptions } from '@components/Hooks/tools/hooks';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import { HooksContextWrapperHoc } from '@components/Hooks/components/HOC/HooksContextWrapperHoc';

import './hooksSetter.less';
import './overrideAntd.less';
import hookJson from '@/assets/config/hooks.json';

import type { AthHooksSetterProps } from '@components/Hooks/types/setter';
import type {
  IHookParams,
  IOutputHookInfo,
} from '@components/Hooks/types/hooks';
import type { IHooksListRef } from '@components/Hooks/types/component';
import { componentIsButton, getComponentDetailEventSourceType } from './tools';

function HooksComponentSetterComponent(props: AthHooksSetterProps) {
  const { value } = props;
  const { id, schema, type, attachMode } = value ?? {};

  useResizerObserverError();
  const { t } = useTranslation();
  const [currentHooks, setCurrentHooks] = useState<IOutputHookInfo[]>([]);
  const hooksListRef = useRef<IHooksListRef>(null);

  useEffect(() => {
    const outterHooks: IOutputHookInfo[] = project.currentDocument?.root?.getPropValue('hooks');
    const outterComponentHooks = outterHooks.filter((hook: IOutputHookInfo) => hook.eventSource === id);
    setCurrentHooks(outterComponentHooks ?? []);
  }, []);

  const isMobile: boolean = false;
  const dynamicInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
  const customHooks = getCustomHooksByEventSource(id!, dynamicInfo?.isvPackageDataList ?? [])

  const allHooks: IHookParams[] = getComponentAllOptions(
    id,
    hookJson!,
    customHooks,
    isMobile,
  );

  const doAddHook = () => {
    if (hooksListRef.current) {
      hooksListRef.current.openComponentModal({
        hook: {
          eventSource: id,
          eventSourceType: getComponentDetailEventSourceType(type as ButtonType, attachMode),
          description: '',
          type: '',
        },
        schema,
        componentType: type,
        customHooks,
        isMobile: false,
        isButton: componentIsButton(type!),
      });
    }
  };

  project.currentDocument?.onChangeNodeProp((info) => {
    if (info.key === 'hooks') {
      const outterComponentHooks = (info.newValue ?? []).filter((hook: IOutputHookInfo) => hook.eventSource === id);
      setCurrentHooks(outterComponentHooks);
    }
  });

  const doHooksChange = (hooks: IOutputHookInfo[]) => {
    console.log('hooks change = ', hooks);
    /**
     * 重新获取的原因是可以开着setter的同时打开hooks plugin
     */
    const outterHooks: IOutputHookInfo[] = project.currentDocument?.root?.getPropValue('hooks') ?? [];
    const restHooks = outterHooks.filter((hook: IOutputHookInfo) => hook.eventSource !== id);
    project.currentDocument?.root?.setPropValue('hooks', [...restHooks, ...hooks]);
  };

  return (
    <div className="hooks-setter mf-override-base">
      <div className="hooks-add">
        <div className="hooks-add-text">{t('dj-订阅相关事件，添加复杂业务逻辑')}</div>
        <PlusCircleFilled onClick={doAddHook} />
      </div>
      <HooksList
        ref={hooksListRef}
        outterHooks={currentHooks ?? []}
        hookSelectList={allHooks}
        isMobile={isMobile}
        useCollapse={false}
        hooksChanges={doHooksChange}
      />
    </div>
  );
}

const HooksComponentSetter = HooksContextWrapperHoc<AthHooksSetterProps, IHooksListRef>(HooksComponentSetterComponent);

export { HooksComponentSetter };
