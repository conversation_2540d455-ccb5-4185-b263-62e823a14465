import { last } from 'lodash';
import i18next, { t } from 'i18next';
import { project, config } from '@alilc/lowcode-engine';

import { EventSourceType, OpType } from '@components/Hooks/enum';
import {
  EventSourceSymbol,
  EventSourceTypeSymbol,
  DescriptionSymbol,
  TypeSymbol,
  IgnoreSchemaComponentTypeSet,
} from '@components/Hooks/constant';
import { transferSchemaComponentList } from '@components/Hooks/tools';
import {
  SubmitButtonTypeSet,
  BusinessButtonTypeSet,
  CommonButtonTypeSet,
} from '@/plugins/plugin-ath-setter/components/Button/constant';
import { IPublicEnumTransformStage } from '@alilc/lowcode-types';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type/ath-loader-manage';

import type {
  IEventSourceObj,
  IFieldTreeNode,
  IHookInfo,
  IPageHookInfo,
  IHookParams,
  IOutterHookInfo,
  ITransferOutterToPageReturn,
  IOutputHookInfo,
  IHookConfig,
  IComponentInfo,
  ISVPackageDataInfo,
  IHookModalInfo,
} from '@components/Hooks/types/hooks';

import type { IAnyObj, ILangInfo, Sboolean } from '@components/Hooks/types/common';
import type { IFieldTreeNodeInfo } from '../../../../../athena-designer-core/src/components/DynamicWorkDesign/config/type/common';
import type { IAiParamsDslInfoComponent, IAiParamsDslInfoData } from '../types/useHooks';
import { ButtonType } from '@/plugins/plugin-ath-setter/components/Button/enum';

/**
 * 从json对象中拿excludeKeys之外的第一个key
 */
export function getKeyNameExcludeRest(obj: IAnyObj, excludeKeys: string[]): string {
  const restKeys: string[] = Object.keys(obj).filter((key) => !excludeKeys.includes(key));
  if (restKeys?.length > 0) {
    return (restKeys[0] as string) ?? '';
  }
  return '';
}

/**
 * 从pageHook中拿hookName, 只适用于组件内部hook的数据结构，也就是除几个Symbol属性外只会有一个string的属性
 * @param {IHookInfo} pageHook - 页面数据的hook
 * @return {string} - hookName
 */
export function getHookName(pageHook: IHookInfo): string {
  const keys = Object.keys(pageHook);
  if (keys.length === 0) return '';
  return keys[0];
}

/**
 * 从eventSourceObj中拿hookName
 * @param {IEventSourceObj} eventSourceObj - DataHookForm入参
 * @return {string} - hookName
 */
export function getHookNameFromEventSource(
  eventSourceObj: IEventSourceObj | IPageHookInfo | IHookModalInfo,
): string {
  return getKeyNameExcludeRest(eventSourceObj, [
    'description',
    'eventSource',
    'eventSourceType',
    'itemDesc',
    'itemFieldName',
    'itemTypeName',
    'id',
    'componentType',
    'schema',
    'title',
  ]);
}

/**
 * 格式化eventSourceName
 * @param {IFieldData} field - 字段数据
 * @return {string} - eventSourceName
 */
export function formatEventSourceName(field: IFieldTreeNodeInfo): string {
  if (
    field.data_type === 'array' ||
    (field.is_array && ['true' as Sboolean, true].includes(field.is_array))
  ) {
    return `${field.data_name}[*]`;
  }
  return field.data_name;
}

/**
 * 格式化字段数据为字段树数据
 * @param {IFieldData[]} fields - 字段数据数组
 * @param {string} prefix - 前缀
 * @returns {IFieldTreeNode[]} - 字段树数据
 */
export function formatFieldToTreeData(
  fields: IFieldTreeNodeInfo[] = [],
  prefix?: string,
): IFieldTreeNode[] {
  const results: IFieldTreeNode[] = [];
  fields.forEach((element) => {
    const eventSourceName = formatEventSourceName(element);
    const eventSource = !prefix ? eventSourceName : prefix + '.' + eventSourceName;
    const isLeaf = element.data_type !== 'object';
    const treeData: IFieldTreeNode = {
      value: eventSource,
      title: (element?.description?.[i18next.language as keyof ILangInfo] as string) ?? null,
      isLeaf,
      origin: element,
    };
    if (!treeData.isLeaf) {
      treeData.children = formatFieldToTreeData(element.children, eventSource);
    }
    results.push(treeData);
  });
  return results;
}

/**
 * 从eventSource格式化出data_name
 * @param {string} eventSource
 * @return {string} - data_name
 */
export function formatDataNameFromEventSource(eventSource: string = ''): string {
  const eventSourceName = last(eventSource?.split('.'));
  const pattern = new RegExp('\\[\\*\\]');
  return eventSourceName?.replace(pattern, '') ?? '';
}

/**
 * 将页面中使用的hook数组转成输出的hook数据, 转换逻辑沿用的之前的逻辑
 * @param {IHookInfo[]} hooks - 页面中的hook数组
 * @param {IOutterHookInfo[]} cache - 之前缓存的一些和当前场景不想干的hook数据，会和当前页面处理过的数据重组生成完整数据输出
 * @returns {IHookInfo[]} - 往外抛的hooks列表数据
 */
export function transformPageHooksToOutterHooks(
  hooks: IHookInfo[] = [],
  cache: IOutterHookInfo[] = [],
): IOutterHookInfo[] {
  if (!hooks?.length) return [];
  const list: IOutterHookInfo[] = [];
  const cacheMap: Map<string, number> = new Map();
  for (const hook of hooks) {
    // mobile 模式下会多type字段，识别出来，以免误当作hook类型, 这边hookItems只会有一个键值对
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { [EventSourceSymbol]: eventSource } = hook;
    const hookName: string = getHookName(hook);
    if (cacheMap.has(eventSource)) {
      const index: number = cacheMap.get(eventSource) as number;
      const desc = list[index][DescriptionSymbol];
      list[index][DescriptionSymbol] = {
        ...desc,
        [hookName]: hook[DescriptionSymbol],
      };
      list[index][hookName] = hook[hookName];
    } else {
      list.push({
        [EventSourceSymbol]: eventSource,
        [EventSourceTypeSymbol]: hook[EventSourceTypeSymbol],
        [DescriptionSymbol]: {
          [hookName]: hook[DescriptionSymbol],
        },
        [TypeSymbol]: hook[TypeSymbol],
        [hookName]: hook[hookName],
      });
      cacheMap.set(eventSource, list.length - 1);
    }
  }
  return [...list, ...cache];
}

/**
 * 将外来的hook数据转成页面中使用的hook数组, 转换的逻辑沿用之前的逻辑
 * @param [hooks=[]] hooks - 外来的hook数据
 * @param {string} id - 相关id，不传result中就是全量转换后的数据，cache为空,传了result中就是只和当前eventSource=id的相关的hook数据,cache为其余的原始数据
 * @returns {{ITransferOutterToPageReturn}} - result: 页面中使用的hook数据, cache: 缓存的其余组件的不相干的hooks
 */
export function transformOutterHooksToPageHooks(
  hooks: IOutterHookInfo[] = [],
  id?: string,
): ITransferOutterToPageReturn {
  const result: ITransferOutterToPageReturn = { result: [], cache: [] };
  hooks.forEach((item) => {
    const { [EventSourceSymbol]: eventSource } = item;
    if (id && eventSource !== id) {
      result.cache.push(item);
    } else {
      Object.keys(item).forEach((hookName) => {
        const code: string = item[hookName];
        /**
         * 描述必填
         */
        const hookInfo: IHookInfo = {
          [EventSourceSymbol]: eventSource,
          [EventSourceTypeSymbol]: item[EventSourceTypeSymbol],
          [TypeSymbol]: item[TypeSymbol],
          [DescriptionSymbol]: item[DescriptionSymbol][hookName] ?? '',
          [hookName]: code ?? '',
        };
        result.result.push(hookInfo);
      });
    }
  });
  return result;
}

/**
 * 将对外的hooks列表转成对内的一个类型足够明确的hooks定义
 */
export function translateOutputHookToOutterHook(hooks: IOutputHookInfo[] = []): IOutterHookInfo[] {
  return hooks.map((hook) => {
    const { eventSource, eventSourceType, description, type, ...hookItems } = hook;
    return {
      [EventSourceSymbol]: eventSource as string,
      [EventSourceTypeSymbol]: eventSourceType as string,
      [DescriptionSymbol]: description as IAnyObj<string>,
      [TypeSymbol]: type as string,
      ...hookItems,
    };
  });
}

/**
 * 由于处理类型问题时使用了Symbol来明确类型，而实际对外输出时肯定不能使用Symbol作为key，所以实际输出时还是需要将数据转成string作为key的方式，来保证外部可以无感知处理
 */
export function translateOutterHookToOutputHook(hooks: IOutterHookInfo[] = []): IOutputHookInfo[] {
  return hooks.map((hook) => {
    const {
      [EventSourceSymbol]: eventSource,
      [EventSourceTypeSymbol]: eventSourceType,
      [DescriptionSymbol]: description,
      [TypeSymbol]: type,
      ...hookItems
    } = hook;
    return {
      eventSource,
      eventSourceType,
      type,
      description,
      ...hookItems,
    };
  });
}

/**
 * 以下定义都是出于减轻列表item渲染时，每个HookItem都去循环搜索Hook信息的计算压力, 当然可以在HookList入参时即进行计算将IhookInfo[]直接转成IPageHookInfo[]；选择这么做的理由如下：
 * 1. HookList作为一个列表组件，列表的渲染属于自己的职责，那么就不应该将数据处理的压力交给调用方
 * 2. 这个数据也只会在HookList内部渲染HookItem时用到，就更不应该将这种调用方根本用不到的数据, 让外部帮自己加上
 * 3. 同时也是考虑到出入数据的纯粹与精简，不去传递一些最终并不会抛出去的数据
 * ================================================================================
 * 但是这么做，自己在渲染时又是列表渲染，每一个Item都去遍历查一遍就是循环套循环了，所以进行cache的处理
 * 另外这边只是简单存储，而没有考虑清空的理由是因为所有的数据来源是IHookParams[]的常量定义,就不存在变更的情况，所以只会在HookList卸载时调clearHookCacheMap进行清理
 * 所以这几个cache只能在HookList及其内部子组件中使用，超出范围用不了
 */
/**
 * 缓存data common hook 信息的map
 */
const cacheDataCommonHookMap = new Map<string, IHookParams>();
/**
 * 缓存operations toolbar hook 信息的map
 */
const cacheOperationToolbarHookMap = new Map<string, IHookParams>();
/**
 * 缓存component hook 信息的map
 */
const cacheComponnetHookMap = new Map<string, Map<string, IHookParams>>();
/**
 * 缓存mobile component hook 信息的map
 */
const cacheComponentMobileHookMap = new Map<string, Map<string, IHookParams>>();

/**
 * 清理cache
 */
export function clearHookCacheMap() {
  cacheDataCommonHookMap.clear();
  cacheComponnetHookMap.clear();
  cacheComponentMobileHookMap.clear();
}

/**
 * 根据eventSource从ISVPackageDataInfo[]中获取相应的customHooks
 */
export function getCustomHooksByEventSource(
  eventSource: string,
  isvPackageInfoLists: ISVPackageDataInfo[] = [],
): IHookParams[] {
  const componentLists = getCurrentComponentList();
  const component: IComponentInfo | undefined = componentLists?.find(
    (component) => component.id === eventSource,
  );
  if (!component) return [];
  const componentType = component.type;
  if (!componentType) return [];
  return (
    isvPackageInfoLists
      .flatMap((info) => info.isvCustomComponentInfoList ?? [])
      .find((component) => componentType === component.type)?.hooks ?? []
  );
}

export function getCurrentComponentList(): IComponentInfo[] {
  return transferSchemaComponentList(
    project?.currentDocument?.exportSchema(IPublicEnumTransformStage.Save),
  );
}

export function getFullPathFromEventSource(eventSource: string): string {
  return eventSource.replace(/\[\*\]/g, '');
}

export function getNameFromFieldTree(fullPath: string, fieldTree: IFieldTreeNodeInfo[]): string {
  if (!fieldTree || fieldTree.length === 0) return '';
  let name = '';
  function traverse(node?: IFieldTreeNodeInfo[]) {
    if (name) return;
    if (node && node.length > 0) {
      for (const nodeInfo of node) {
        if (name) return;
        if (nodeInfo.fullPath === fullPath) {
          name = nodeInfo.description?.[i18next.language] ?? nodeInfo.title;
          return;
        }
        traverse(nodeInfo.children);
      }
    }
  }
  traverse(fieldTree);
  return name;
}

export function getSpecialItemFieldName(component: IComponentInfo): string {
  return (
    component?.dslInfo?.title ??
    component?.dslInfo?.tableTitle ??
    component?.dslInfo?.headerName ??
    component?.title ??
    ''
  );
}

/**
 * 根据page hook和isMobile以及page hook中的eventSourceType生成实际渲染列表item时需要的完成信息
 * @param {IHookInfo} hook - page hook 信息
 * @param {IHookConfig} hooksConfig - hook配置数据
 * @param {IHookParams[]} inputCustomHooks - 自定义hook列表, 只有组件hook类型才可能传递
 * @param {boolean} isMobile - 是否移动端
 * @return {IPageHookInfo} - 渲染HookItem需要的所以信息集合
 */
export function getRenderInfoByHook(
  hook: IHookInfo,
  hooksConfig: IHookConfig,
  inputCustomHooks: IHookParams[] = [],
  isMobile: boolean = false,
): IPageHookInfo {
  const componentLists = getCurrentComponentList();
  const fieldTree: IFieldTreeNodeInfo[] = config.get(AthLowCodeConfigKey.AthFieldTree);
  const { [EventSourceTypeSymbol]: eventSourceType, [EventSourceSymbol]: eventSource } = hook;
  const {
    DataCommonHooks,
    ComponentCommonHooks,
    ComponentCommonHooksMobile,
    ComponentCustomHooks,
    ComponentCustomHooksMobile,
  } = hooksConfig;
  if ((eventSourceType as EventSourceType) === EventSourceType.DATA) {
    if (cacheDataCommonHookMap.size === 0) {
      DataCommonHooks.map((hook) => {
        cacheDataCommonHookMap.set(hook.name, hook);
      });
    }
    const hookName: string = getHookName(hook);
    const hookInfo: IHookParams = cacheDataCommonHookMap.get(hookName)!;
    const itemFieldName: string = eventSource.split('.').pop() ?? '';
    const fullPath = getFullPathFromEventSource(eventSource);
    const name = getNameFromFieldTree(fullPath, fieldTree);
    const itemTypeName = hookInfo?.description ?? '';
    return {
      ...hook,
      id: `${eventSource}_${hookName}`,
      itemDesc: itemFieldName + itemTypeName,
      itemFieldName: `${itemFieldName} (${name})`,
      itemTypeName,
      componentType: '',
      hookName,
    };
  } else {
    const component: IComponentInfo | undefined = componentLists?.find(
      (component) => component.id === eventSource,
    );
    if (!component) {
      /**
       * 这种场景不该出现, 出现了就是componentLists入参不对
       */
      return {
        ...hook,
        id: '',
        itemDesc: '没找到Hook对应的Lowcode组件信息',
        itemFieldName: '没找到Hook对应的Lowcode组件信息',
        itemTypeName: '没找到Hook对应的Lowcode组件信息',
        componentType: '',
      };
    }
    const componentType = component.type;
    if (isMobile) {
      if (!cacheComponentMobileHookMap.has(componentType)) {
        const customHooks = ComponentCustomHooksMobile[componentType] ?? [];
        const newMap = new Map<string, IHookParams>();
        const wholeHooks = [...customHooks, ...inputCustomHooks, ...ComponentCommonHooksMobile];
        wholeHooks.map((hook) => {
          newMap.set(hook.name, hook);
        });
        cacheComponentMobileHookMap.set(componentType, newMap);
      }
    } else {
      if (!cacheComponnetHookMap.has(componentType)) {
        const customHooks = ComponentCustomHooks[componentType] ?? [];
        let wholeHooks = [...customHooks, ...inputCustomHooks, ...ComponentCommonHooks];
        if (SubmitButtonTypeSet.has(componentType as ButtonType)) {
          wholeHooks = wholeHooks.concat(hooksConfig?.SubmitActionsHooks ?? []);
        }
        if (componentType === 'BUTTON_SUB_PAGE') {
          wholeHooks = wholeHooks.concat(hooksConfig?.SubpageButtonHooks ?? []);
        }
        const newMap = new Map<string, IHookParams>();
        wholeHooks.map((hook) => {
          newMap.set(hook.name, hook);
        });
        cacheComponnetHookMap.set(componentType, newMap);
      }
    }
    const hookName: string = getHookName(hook);
    const hookMap = isMobile ? cacheComponentMobileHookMap : cacheComponnetHookMap;
    const hookInfo: IHookParams = hookMap.get(componentType)!.get(hookName)!;
    let itemFieldName: string | undefined = isMobile ? component?.id : component?.schema;
    if (
      IgnoreSchemaComponentTypeSet.has(componentType) ||
      SubmitButtonTypeSet.has(componentType) ||
      BusinessButtonTypeSet.has(componentType) ||
      CommonButtonTypeSet.has(componentType)
    ) {
      itemFieldName = getSpecialItemFieldName(component);
    }
    const itemTypeName = hookInfo?.description ?? '';
    const fullPath = component.path ? `${component.path}.${component.schema}` : component.schema;
    const name = getNameFromFieldTree(fullPath, fieldTree);
    return {
      ...hook,
      id: `${eventSource}_${hookName}`,
      itemDesc: itemFieldName + itemTypeName,
      itemFieldName: `${itemFieldName ?? ''} ${name ? `(${name})` : ''}`,
      itemTypeName,
      hookName,
      componentType,
    };
  }
}

/**
 * 数据hook下拉选项
 */
export function getDataTypeSelectOptions(hooksConfig: IHookParams[]) {
  return hooksConfig.map((hook) => ({
    value: hook.name,
    label: `${hook.name} ${hook.description}`,
  }));
}

/**
 * 获取组件hook的所有下拉选项
 * @param {string} eventSource - 组件id
 * @param {IHookConfig} hooksConfig - hooks配置
 * @param {IHookParams[]} customHooks - 自定义hook
 * @param {boolean} isMobile - 是否移动端
 * @returns {IHookParams[]}
 */
export function getComponentAllOptions(
  eventSource: string,
  hooksConfig: IHookConfig,
  customHooks: IHookParams[] = [],
  isMobile: boolean = false,
) {
  const componentList = getCurrentComponentList();
  const component = componentList.find((comp) => comp.id === eventSource);
  if (component) {
    const { type } = component;
    const _customHooks = isMobile
      ? hooksConfig?.ComponentCustomHooksMobile
      : hooksConfig?.ComponentCustomHooks;
    const commonHooks = isMobile
      ? hooksConfig?.ComponentCommonHooksMobile
      : hooksConfig?.ComponentCommonHooks;
    let allHooks: IHookParams[] = [];
    if (_customHooks && _customHooks?.[type]?.length > 0) {
      allHooks = allHooks.concat(_customHooks[type]);
    }
    if (commonHooks && commonHooks?.length > 0) {
      allHooks = allHooks.concat(commonHooks);
    }
    /**
     * 这边不知道有没有顺序要求，看起来是有的，所以没有初始化allHooks直接放进去
     */
    allHooks = allHooks.concat(customHooks);
    return allHooks.map((hook) => ({ ...hook, optionLabel: `${hook.name} ${hook.description}` }));
  }
  return [];
}

export function getErrorMessage(
  eventSource: string,
  eventSourceType: string,
  hookName: string,
): string {
  switch (eventSourceType) {
    case 'data': {
      return `${eventSource}${t('dj-已存在hook')}: ${hookName}`;
    }
    case 'component': {
      return `${t('dj-该组件已存在hook')}: ${hookName}`;
    }
    case 'submitAction':
    case 'operation':
    case 'toolbar': {
      return `${t('dj-该按钮已存在hook')}: ${hookName}`;
    }
    default:
      return `${t('dj-已存在hook')}: ${hookName}`;
  }
}

export function isExistHook(
  currentHooks: IHookInfo[] = [],
  hook: IHookModalInfo,
  opType: OpType = OpType.ADD,
  prevId?: string,
): boolean {
  const { eventSource, hookName } = hook;
  const id = `${eventSource}_${hookName}`;
  const currentIdsSet = new Set(
    currentHooks.map((hook) => {
      const hookName = getHookName(hook);
      return `${hook[EventSourceSymbol]}_${hookName}`;
    }),
  );
  if (opType === OpType.EDIT) {
    return currentIdsSet.has(id) && id !== prevId;
  }
  return currentIdsSet.has(id);
}

export function getAiDslInfoData(fieldTree: IFieldTreeNodeInfo[] = []): IAiParamsDslInfoData[] {
  let datas: IAiParamsDslInfoData[] = [];
  let stack: IFieldTreeNodeInfo[] = [...fieldTree];
  while (stack.length > 0) {
    const node = stack.shift();
    datas.push({
      schema: node?.data_name!,
      fullPath: node?.fullPath!,
      description: (node?.description?.[i18next.language] as string) ?? '',
    });
    if (node?.children && node.children.length > 0) {
      stack = stack.concat(node.children);
    }
  }
  return datas;
}

export function getAiDslInfoComponent(
  componentLists: IComponentInfo[] = [],
  operations = [],
): IAiParamsDslInfoComponent[] {
  const components = [];
  componentLists.map((component) => {
    const dslInfo = component?.dslInfo ?? {};
    const titleInfo = dslInfo.lang?.title ?? dslInfo?.lang?.headerName ?? dslInfo?.lang?.tableTitle;
    components.push({
      schema: (dslInfo.schema as string) ?? '',
      fullPath: dslInfo.schema && dslInfo.path ? `${dslInfo.path}.${dslInfo.schema}` : '',
      id: dslInfo.id!,
      type: dslInfo.type!,
      description: titleInfo?.[i18next.language] as string,
    });
    // 2025-02-20 toolbar内部按钮不再传给AI Hooks
    // if (dslInfo?.type === 'TOOLBAR') {
    //   dslInfo?.items?.forEach(item => {
    //     components.push({
    //       schema: '',
    //       fullPath: '',
    //       id: item.id!,
    //       type: item.type!,
    //       description: item?.lang?.title?.[i18next.language] as string,
    //     });
    //   });
    // }
  });
  operations?.forEach((operation) => {
    const paths = operation?.target?.split('.') ?? [];
    components.push({
      schema: paths.length > 0 ? paths[paths.length - 1] : '',
      fullPath: operation?.target,
      id: operation?.id!,
      type: operation?.type!,
      description: operation?.lang?.title?.[i18next.language] as string,
    });
  });
  return components;
}
