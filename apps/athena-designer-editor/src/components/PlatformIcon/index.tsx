import { createFromIconfontCN } from '@ant-design/icons';
import { envParams } from "@/env";

// const isDevelopment = process.env.NODE_ENV === 'development';

// const PlatformIcon = createFromIconfontCN({
//   scriptUrl: `${envParams.domain}/assets/iconfont/iconfont.js`,
// });

const PlatformIcon = createFromIconfontCN({
  scriptUrl: [
    `${envParams.scheduleDomain}/scheduler/static/assets/iconfont/iconfont-platform/iconfont.js`,
    `${envParams.scheduleDomain}/scheduler/static/assets/iconfont/iconfont-app/iconfont.js`,
  ],
});

export { PlatformIcon };

export default PlatformIcon;
