import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import HttpBackend from 'i18next-http-backend';
import { envParams } from './env';

let lng = 'zh_CN';
if (window.__MICRO_APP_ENVIRONMENT__) {
  lng = window.microApp?.getData()?.currentLanguage || lng;
}

/**
 * 后续路径去envParams中去设定
 */
// const i18nOrigin = process.env.NODE_ENV === 'development' ? 'https://athena-dev-platform-paas.digiwincloud.com.cn' : location.origin;

const i18nOrigin = envParams.domain;

const option = {
  lng,
  supportedLngs: ['zh_CN', 'zh_TW'], // 定义支持的语言列表
  fallbackLng: ['zh_CN'],
  backend: {
    loadPath: `${i18nOrigin}/assets/i18n/{{lng}}/basic.json`, // 翻译文件路径模板
  },
};

// 注入react-i18next实例并初始化
export default i18n.use(HttpBackend).use(initReactI18next).init(option);
