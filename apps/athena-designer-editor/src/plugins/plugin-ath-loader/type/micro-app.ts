import { AthTreeDataNode } from '@/plugins/plugin-ath-field-panel/type';
import { DataSourceInfo, DslWorkDesignData, PageUIElementContent } from './common';

// ========================= MicroApp通信相关事件数据 =========================
// 主应用向子应用发出的消息
export enum MessageToSubType {
  InitLcdp = 'InitLcdp', // 初始化低代码设计器
  Action = 'Action',
}

export type MessageToSubContent = MessageToSubInitLcdp | MessageToSubAction;

export interface MessageToSubContentBase {
  isGolbal?: boolean; // microApp 全局事件，业务逻辑无关
}

// 初始化
export interface MessageToSubInitLcdp extends MessageToSubContentBase {
  type: MessageToSubType.InitLcdp;
  data: DslWorkDesignData;
}

// 操作
export interface MessageToSubAction extends MessageToSubContentBase {
  type: MessageToSubType.Action;
  data: ActionData;
}

export type ActionDataMap = {
  rule: ActionRuleData;
  pageUIElementContent: ActionPageUIElementContentData;
  fieldTreeMap: ActionFieldTreeData;
  fieldTreeRefInfo: ActionFieldTreeRefInfo;
  sideBar: ActionSideBarData;
  dataSourceInfo: ActionDataSourceInfoData;
  extraData: ActionExtraDataData;
  hideComponent: ActionHideComponentData;
};

export type ActionData = {
  [K in keyof ActionDataMap]: {
    key: K;
    data: ActionDataMap[K];
  };
}[keyof ActionDataMap];

export interface ActionCommonData {
  type: 'update' | 'delete' | 'add';
  data: any;
}

export interface ActionRuleData extends ActionCommonData {}
export interface ActionPageUIElementContentData extends ActionCommonData {}
export interface ActionFieldTreeData extends ActionCommonData {}
export interface ActionFieldTreeRefInfo extends ActionCommonData {
  dataSourceName: string;
  fieldTreeDom: HTMLDivElement;
  fieldTreeIds: string[];
  treeData: AthTreeDataNode[];
}
export interface ActionDataSourceInfoData extends ActionCommonData {
  dataSourceName: string;
  dataSourceNames: string[];
  dataSources: any;
}
export interface ActionExtraDataData extends ActionCommonData {}

export interface ActionHideComponentData extends ActionCommonData {}

export enum PanelType {
  ComponentsPanel = 'componentsPanel',
  AthFieldPanel = 'athFieldPanel',
  AthHooksPanel = 'athHooksPanel',
  LcdpPermissionPanel = 'lcdpPermissionPanel',
  LcdpOutlineMasterPanel = 'lcdpOutlineMasterPanel',
}

export interface ActionSideBarData {
  isShow: boolean;
  panel?: PanelType;
  isSideBarRivet?: boolean;
}

// 子应用向主应用发送的数据
export type MessageToMainContent =
  | MessageToMainGobal
  | MessageToMainRule
  | MessageToMainPageUIElement
  | MessageToMainLifeCycles
  | MessageToMainDataSourceInfo
  | MessageToMainOpenWindow;

export enum MessageToMainType {
  Gobal = 'Gobal',
  LifeCycles = 'LifeCycles',
  Rules = 'Rules',
  PageUIElementContent = 'PageUIElementContent',
  DataSourceInfo = 'DataSourceInfo',
  OpenWindow = 'OpenWindow',
}

// 全局相关
export interface MessageToMainGobal {
  type: MessageToMainType.Gobal;
  data: GobalData;
}

export interface GobalData {
  type: 'updateIsSideBarRivet' | 'updatePanelState' | 'updatePluginStatus';
  data: any;
}

// 生命周期相关
export interface MessageToMainLifeCycles {
  type: MessageToMainType.LifeCycles;
  data: LifeCyclesData;
}

export interface LifeCyclesData {
  type: 'mounted' | 'simulatorRendererReady' | 'ready' | 'unmount' | 'error';
}

// 规则相关
export interface MessageToMainRule {
  type: MessageToMainType.Rules;
  data: RuleData;
}

export interface RuleData {
  type: 'add' | 'edit' | 'delete';
  data: any;
  contextDataSourceName?: string;
}

// 五要素相关
export interface MessageToMainPageUIElement {
  type: MessageToMainType.PageUIElementContent;
  data: PageUIElementData;
}

export interface PageUIElementData {
  type: 'update';
  pageUIElementContent: PageUIElementContent;
  iamCondition?: any;
  groupSchemaList?: any;
}

// 数据源信息相关
export interface MessageToMainDataSourceInfo {
  type: MessageToMainType.DataSourceInfo;
  data: DataSourceInfoData;
}

export type DataSourceInfoData = {
  [K in keyof DataSourceInfo]: {
    type: 'update';
    key: K;
    data: DataSourceInfo[K];
  };
}[keyof DataSourceInfo];

// 开窗操作
// 由于排期原因，开窗组件 暂时借用外部组件实现编辑能力，所以才需要借助该事件
// 后续 会 内部 实现该逻辑
export interface MessageToMainOpenWindow {
  type: MessageToMainType.OpenWindow;
  data: OpenWindowData;
}

export interface OpenWindowData {
  type: 'edit';
  data: OpenWindowDataContent;
}

export interface OpenWindowDataContent {
  data: any;
  component: any;
  fieldTreeData: any;
  fieldTreeDataMap: any;
  // searchViewDisplay: boolean;
  serachViewDisplay: boolean; // TODO 历史逻辑的单词 都拼错了，这里先暂时沿用，否则没法 正常使用之前的逻辑，但 后续 该 历史逻辑肯定要改
  roleAttention?: any;
}
