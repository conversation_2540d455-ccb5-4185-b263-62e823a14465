import { IOperatePermissionInfo } from '@core_types/components/OperatePermission/types';
import { PageUIElementContent, DslWorkDesignData } from './common';
import {
  MessageToMainDataSourceInfo,
  MessageToMainRule,
  MessageToMainGobal,
  MessageToMainOpenWindow,
  MessageToMainContent,
} from './micro-app';

// ========================= AthLoaderManage相关 =========================
/**
 * AthLoaderManage 与 low code 交互的相关事件名
 * 枚举值命名规则：主体（lowCcode或者ath）+模块名（比如rule）+动作（比如update），和lowcode风格保持一致
 */
export enum AthLowCodeEventName {
  LowCodeRulesUpdate = 'lowCode.rules.update', // 通知lowCcode 规则 更新
  AthRulesHandle = 'ath.rules.handle', // 通知ath 对规则进行操作
  LowCodeFieldTreeUpdate = 'lowCode.fieldTree.update', // 通知lowCcode 字段树 更新
  LowCodeFieldTreeMapUpdate = 'lowCode.fieldTreeMap.update', // 通知lowCcode 字段树集合 更新
  LowCodeDataSourceInfoUpdate = 'lowCode.dataSourceInfo.update', // 通知lowCcode dataSourceNames 更新
  AthDataSourceInfoHandle = 'ath.dataSourceInfo.handle', // 通知ath 对dataSourceInfo进行操作
  LowCodeIsSideBarRivetUpdate = 'lowCode.isSideBarRivet.update', // 通知lowCcode isSideBarRivet 发生更新
  AthIsSideBarRivetHandle = 'ath.isSideBarRivet.handle', // 通知ath 对isSideBarRivet进行操作
  AthOpenWindowHandle = 'ath.OpenWindow.handle', // 通知ath 对自定义开窗进行操作
  LowCodeExtraDataUpdate = 'lowCode.extraData.update', // 通知lowCode extraData 更新
  LowCodeHideComponentUpdate = 'lowCode.hideComponent.update', // 通知lowCode 隐藏组件 更新事件
  AthFieldTreeRefInfoUpdate = 'lowcode.fieldTreeRefInfo.update', // 字段树节点信息集合
}

// AthLoaderManage 与 low code 交互的 low code全局config相关key
export enum AthLowCodeConfigKey {
  AthRules = 'AthRules', // 规则数据
  AthFieldTree = 'AthFieldTree', // 字段树数据
  AthFieldTreeMap = 'AthFieldTreeMap', // 字段树集合数据
  AthLoaderManage = 'AthLoaderManage', // AthLoaderManage
  AthStatusInfo = 'AthStatusInfo', // 状态信息，状态列相关信息
  AthSystemConfig = 'AthSystemConfig', // 系统配置(配置信息，当前主要用在微前端环境存储配置信息)
  AthDataSourceInfo = 'AthDataSourceInfo', // 数据源相关信息
  AthIsvComponentList = 'AthIsvComponentList', // isv组件列表
  AthDynamicWorkDesignInfo = 'AthDynamicWorkDesignInfo', // 界面设计器页面基础信息（设计之初是不希望透传的，但考虑到会用到config处理差异逻辑，还是透传进来）
  AthComponentGroupMap = 'AthComponentGroupMap', // 组件分组
  AthExtraData = 'AthExtraData', // 额外数据
  AthHideComponent = 'AthHideComponent', // 隐藏组件
}

// AthLoaderManage内部事件
export interface AthLoaderManageEventHandler {
  // onPageUIElementChange: (info: { pageUIElementContent: PageUIElementContent, iamCondition: IOperatePermissionInfo[] ,groupSchemaList:string[] }) => void;
  // onHandleRule: (messageToMainRule: MessageToMainRule) => void;
  // onHandleDataSourceInfo: (messageToMainDataSourceInfo: MessageToMainDataSourceInfo) => void;
  // onHandleIsSideBarRivet: (messageToMainGobal: MessageToMainGobal) => void;
  // onHandleOpenWindow: (messageToMainOpenWindow: MessageToMainOpenWindow) => void;
  // onHandleUpdatePanelState: (messageToMainGobal: MessageToMainGobal) => void;
  // onHandleUpdatePluginStatus: (messageToMainGobal: MessageToMainGobal) => void;
  onHandleMessageToOuter: (messageToOuter: MessageToMainContent) => void;
}

export interface AthLoaderManageContextBase {
  isMicroApp: boolean; // 是否是微前端环境
}

export interface MicroAppContext extends AthLoaderManageContextBase {
  isMicroApp: true;
  dslWorkDesignData: DslWorkDesignData; // 当前渲染的DSL数据
}

export interface LocalContext extends AthLoaderManageContextBase {
  isMicroApp: false;
}

// AthLoaderManage的上下文环境
export type AthLoaderManageContext = MicroAppContext | LocalContext;
