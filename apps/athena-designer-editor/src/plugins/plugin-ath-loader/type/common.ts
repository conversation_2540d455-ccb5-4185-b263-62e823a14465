import {
  IPublicTypeComponentDescription,
  IPublicTypeComponentMetadata,
} from '@alilc/lowcode-types';
import { AthTreeDataNode } from '../../plugin-ath-field-panel/type';
import { DynamicWorkDesignInfo } from '@core_types/components/DynamicWorkDesign/config/type/common';

/**
 * 约定的主应用传递给子应用用于加载的dsl相关数据
 * pageUIElement是约定的五要素数据，一般来说，pageUIElement数据我们会具体的加载在project中，
 * 而其他数据会挂载在全局的config中，比如rules，不参操作管理，但会在特定逻辑中被映射展示
 */
export interface DslWorkDesignData {
  pageUIElementContent: PageUIElementContent; // 当前渲染的五要素等,也是被界面设计器接管可操作的数据,也会挂载在project document 的 root节点上便于node节点操作
  rules?: any[]; // 当前页面的规则,将会挂载在 全局的 config中，在设计器中主要用作展示，如果需要操作，将会触发相应事件，交由外部处理
  fieldTree?: AthTreeDataNode[]; // 当前页面的字段树,将会挂载在 全局的 config中，在设计器中主要用作展示，如果需要操作，将会触发相应事件，交由外部处理
  fieldTreeMap: Map<string, AthTreeDataNode[]>; // 当前页面的字段树,将会挂载在 全局的 config中，在设计器中主要用作展示，如果需要操作，将会触发相应事件，交由外部处理
  statusInfo?: any; // 状态信息
  systemConfig?: any; // 系统配置(配置信息，当前主要用在微前端环境存储配置信息)
  dataSourceInfo: DataSourceInfo; // 数据源相关信息
  isvComponentList?: IPublicTypeComponentDescription[]; // isv组件列表
  dynamicWorkDesignInfo: DynamicWorkDesignInfo; // 动态设计器的基础静态信息
  extraData?: any; // 额外数据
  hideComponent?: any; // 隐藏组件
}

export interface DataSourceInfo {
  dataSourceName: string;
  dataSourceNames: string[];
  dataSources: any;
}

export interface PageUIElementContent {
  layout: any[];
  operations: any[];
  submitActions: any[];
  hooks: any[];
  gridSettings: GridSetting[];
}

export interface GridSetting {
  gridPath: string;
  gridSchema: string;
  searchInfo: GridSettingSearchInfo[];
}

export interface GridSettingSearchInfo {
  searchName: string;
  searchField: string;
  dataType: string;
}
