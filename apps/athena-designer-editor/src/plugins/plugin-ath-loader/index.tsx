import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import { AthLoaderManage } from './AthLoaderManage';
import {
  MicroAppContext,
  LocalContext,
  MessageToMainRule,
  PageUIElementContent,
  MessageToSubInitLcdp,
  MessageToSubContent,
  MessageToMainLifeCycles,
  DslWorkDesignData,
  ActionData,
  MessageToMainDataSourceInfo,
  MessageToMainGobal,
  MessageToMainOpenWindow,
  AthLowCodeEventName,
} from './type';
import { MessageToMainType, MessageToSubType, AthLowCodeConfigKey } from './type';
import ReactDOM from 'react-dom';
import { event, project } from '@alilc/lowcode-engine';

// 微前端环境下处理MessageToSubType为Action时的通用逻辑
const handleMicroAppAction = (
  actionData: ActionData,
  athLoaderManage: AthLoaderManage<MicroAppContext> | null,
) => {
  switch (actionData.key) {
    case 'rule':
      if (actionData.data.type === 'update') {
        athLoaderManage?.updateMicroAppContextDslWorkDesignDataRules(actionData.data.data ?? []);
      }
      break;
    case 'fieldTreeMap':
      if (actionData.data.type === 'update') {
        athLoaderManage?.updateMicroAppContextDslWorkDesignDataFieldTreeMap({
          fieldTreeMap: actionData.data.data,
        });
      }
      break;
    case 'fieldTreeRefInfo':
      event.emit(AthLowCodeEventName.AthFieldTreeRefInfoUpdate, actionData.data.data);
      break;
    case 'dataSourceInfo':
      if (actionData.data.type === 'update') {
        athLoaderManage?.updateMicroAppContextDslWorkDesignDataSourceInfo(
          actionData.data.data ?? {
            dataSourceName: '',
            dataSourceNames: [],
            dataSources: {},
          },
        );
      }
      break;
    case 'extraData':
      if (actionData.data.type === 'update') {
        athLoaderManage?.updateMicroAppContextDslWorkDesignDataExtraData(
          actionData.data.data ?? {},
        );
      }
      break;
    case 'hideComponent':
      if (actionData.data.type === 'update') {
        athLoaderManage?.updateMicroAppContextDslWorkDesignDataHideComponent(
          actionData.data.data ?? [],
        );
      }
      break;
    case 'sideBar':
      athLoaderManage?.togglePanel(
        actionData.data.isShow,
        actionData.data.panel,
        actionData.data.isSideBarRivet,
      );
      break;

    case 'pageUIElementContent':
      if (actionData.data.type === 'update') {
        athLoaderManage?.updateMicroAppContextDslWorkDesignDataPageUIElement(
          actionData.data.data ?? {
            layout: [],
            operations: [],
            submitActions: [],
            hooks: [],
            gridSettings: [],
          },
        );
      }
      break;

    default:
      break;
  }
};

const AthLoaderPlugin = (ctx: IPublicModelPluginContext, options: any) => {
  return {
    async init() {
      const { config } = ctx;
      // 非微前端环境
      if (!window.__MICRO_APP_ENVIRONMENT__) {
        config.set(
          AthLowCodeConfigKey.AthLoaderManage,
          new AthLoaderManage<LocalContext>({ isMicroApp: false }, ctx),
        );
        return;
      }

      // 微前端环境
      window.addEventListener('unmount', () => {
        ReactDOM.unmountComponentAtNode(document.getElementById('lce-container')!);
        window.microApp.clearDataListener();
      });

      window.microApp.dispatch({
        type: MessageToMainType.LifeCycles,
        data: { type: 'mounted' },
      } as MessageToMainLifeCycles);

      project.onSimulatorRendererReady(() => {
        window.microApp.clearData();
        window.microApp.dispatch({
          type: MessageToMainType.LifeCycles,
          data: { type: 'simulatorRendererReady' },
        } as MessageToMainLifeCycles);
      });

      let athLoaderManage: AthLoaderManage<MicroAppContext> | null = null;
      // 因为统一为事件驱动，所以会避免从microApp中getData
      // 监听数据变化
      window.microApp.addDataListener((messageData: { [key: string]: MessageToSubContent }) => {
        console.log('来自主应用的数据:', messageData);

        // 在这里还是 由于 microApp的 通信机制 并不是 事件模式
        // 所以如果连续两次setData，而key又相同，那么将会触发一次事件，而值是两次的merge，这个明显不能满足我们对事件逻辑的需求
        // 所以 在 这里，每条消息在通信前将会生成一个唯一key
        // 在lowcode解析时，通过key的规则解析成message list ，然后依次执行
        Object.entries(messageData).forEach(([dataKey, messageToSubContent]) => {
          if (!dataKey.startsWith('@messageData') || !messageToSubContent) return;

          const { type, data, isGolbal } = messageToSubContent;

          switch (type) {
            case MessageToSubType.InitLcdp:
              // 如果没有就创建，并且初始化事件
              if (!athLoaderManage) {
                const { config } = ctx;
                athLoaderManage = new AthLoaderManage<MicroAppContext>(
                  { isMicroApp: true, dslWorkDesignData: { ...data } },
                  ctx,
                );
                config.set(AthLowCodeConfigKey.AthLoaderManage, athLoaderManage);
                break;
              }

              // 创建过，那就执行更新上下文逻辑并重新渲染（也就是 reset）
              athLoaderManage.updateAthLoaderManageContext(
                {
                  isMicroApp: true,
                  dslWorkDesignData: { ...data },
                },
                true,
              );
              break;
            case MessageToSubType.Action:
              if (isGolbal || athLoaderManage) handleMicroAppAction(data, athLoaderManage);
              break;
          }
        });

        window.microApp.clearData();
      });
    },
  };
};
AthLoaderPlugin.pluginName = 'AthLoaderPlugin';
AthLoaderPlugin.meta = {};
export default AthLoaderPlugin;
