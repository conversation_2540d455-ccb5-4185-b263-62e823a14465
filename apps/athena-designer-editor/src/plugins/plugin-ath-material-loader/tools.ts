import { config, material } from '@alilc/lowcode-engine';
import {
  IPublicTypeFieldConfig,
  IPublicTypeComponentDescription,
  IPublicTypeMetadataTransducer,
  IPublicModelSettingField,
  IPublicTypeTransformedComponentMetadata,
  IPublicTypeSnippet,
  IPublicTypeRootSchema,
} from '@alilc/lowcode-types';
import assets from '../../config/assets.json';
import i18n, { t } from 'i18next';
import { AthComponentType } from '@/tools/business/lcdp-converter/type';
import { cloneDeep } from 'lodash';
import { ComponentCustomHooks } from '@/assets/config/hooks.json';

const EXTRA_KEY_PREFIX = '___';

export function getConvertedExtraKey(key: string): string {
  if (!key) {
    return '';
  }
  let _key = key;
  if (key.indexOf('.') > 0) {
    _key = key.split('.')[0];
  }
  return EXTRA_KEY_PREFIX + _key + EXTRA_KEY_PREFIX + key.slice(_key.length);
}

export const getAthAdvanceCombined = (): IPublicTypeFieldConfig => {
  const athAdvanceGroup: IPublicTypeFieldConfig[] = [];
  athAdvanceGroup.push({
    name: 'dslInfo.id',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-唯一标识',
          },
          componentProps: {
            readOnly: true,
          },
        },
      },
    },
  });

  athAdvanceGroup.push({
    title: { type: 'i18n', 'zh-CN': '事件', 'en-US': 'hooks' },
    getValue: (target) => {
      return target.node?.getPropValue('dslInfo');
    },
    setter: {
      isDynamic: false,
      componentName: 'AthComponentHooksSetter',
    },
    condition: (target: IPublicModelSettingField) => {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return (
        Object.keys(ComponentCustomHooks).includes(dslInfo?.type) ||
        (/BUTTON/.test(dslInfo?.type) && dslInfo?.type !== 'BUTTON_GROUP')
      );
    },
    extraProps: {
      // display: 'block',
      display: 'accordion',
    },
  });

  return {
    name: '#ath-advanced',
    title: { type: 'i18n', 'zh-CN': '高级', 'en-US': 'Advanced' },
    items: athAdvanceGroup,
  };
};

// 处理Props的I18n
const handlePropsI18n = (props: IPublicTypeFieldConfig[] = []): void => {
  props?.forEach((prop) => {
    if (prop.display === 'accordion') {
      if (typeof prop.title === 'string') {
        prop.title = t(prop.title);
      }
    }
  });
};

export const i18nList = (list: any[] = []): string[] => {
  return list.map((name: any) => (typeof name === 'string' ? t(name) : name));
};
export const transformAssetsDataI18n = (assetsData: any): any => {
  const i18nAssetsData = { ...(assetsData ?? {}) };
  const { groupList = [], sort } = i18nAssetsData ?? {};
  const { groupList: sortGroupList = [], categoryList = [], componentSortMap } = sort ?? {};
  if (groupList.length > 0) {
    i18nAssetsData.groupList = i18nList(groupList);
  }
  if (sortGroupList.length > 0) {
    i18nAssetsData.sort.groupList = i18nList(sortGroupList);
  }
  if (categoryList.length > 0) {
    i18nAssetsData.sort.categoryList = i18nList(categoryList);
  }
  if (componentSortMap && Object.keys(componentSortMap).length > 0) {
    i18nAssetsData.sort.componentSortMap = {};
    Object.keys(componentSortMap).forEach((key) => {
      const i18nKey = t(key);
      i18nAssetsData.sort.componentSortMap[i18nKey] = componentSortMap[key];
    });
  }
  return i18nAssetsData;
};

/**
 * 组件排序
 * WARN: 这边的排序，拍不到同一组件的多snippets这种场景，如果是这种场景，需要手动去meta文件中，按顺序挪一下snippet的顺序
 */
export const sortComponent = (
  components: IPublicTypeComponentDescription[] = [],
  sort: any,
): IPublicTypeComponentDescription[] => {
  const groupMap: Map<string, Map<string, IPublicTypeComponentDescription>> = new Map();
  const { categoryList = [], componentSortMap = {} } = sort ?? {};
  const lowerPriorityComponents: IPublicTypeComponentDescription[] = [];
  components.forEach((component) => {
    /**
     * 目前只处理athena-designer-editor-components下的标准组件,不在范围内的直接加入低优先级集合中
     */
    if (
      component.npm?.package !== 'athena-designer-editor-components' ||
      (component.group as string) !== t('dj-标准组件')
    ) {
      lowerPriorityComponents.push(component);
    } else {
      /**
       * 这里的category是已经做过国际化的
       */
      const category = component.category as string;
      /**
       * 只关心
       * 1. 在categoryList中
       * 2. 在排序配置中
       * 都存在的category，不存在的要么没有配置在categoryList中不用管，要么没有做排序，这部分也直接加入低优先级集合
       */
      if (!categoryList.includes(category) || !componentSortMap[category]) {
        lowerPriorityComponents.push(component);
      } else {
        /**
         * 针对category进行分组，方便后续处理
         */
        if (groupMap.has(category)) {
          const groupComponentsMap = groupMap.get(category);
          groupComponentsMap?.set(component.componentName, component);
        } else {
          groupMap.set(category, new Map([[component.componentName, component]]));
        }
      }
    }
  });
  const highPriorityComponents: IPublicTypeComponentDescription[] = [];
  /**
   * categoryList是完整的类目集合
   */
  categoryList.forEach((category: string) => {
    /**
     * 这里可能没有，可能配了，但是实际该类目下的组件还没写
     */
    if (groupMap.has(category)) {
      /**
       * 读取assets中配的排序
       */
      const categoryComponentSortList = componentSortMap[category];
      /**
       * 读取相应category的所有component信息map
       */
      const categoryComponentsMap = groupMap.get(category);
      /**
       * 逻辑走到这其实不可能配有，但是需要配合一下tsc和eslint检测
       */
      if (categoryComponentsMap) {
        /**
         * 同上
         */
        if (categoryComponentSortList && categoryComponentSortList.length > 0) {
          /**
           * 根据排序配置，push组件信息数据到高优先级集合中，完成后删掉map中相应的数据
           */
          categoryComponentSortList.forEach((name: string) => {
            const componentInfo = categoryComponentsMap.get(name);
            if (componentInfo) {
              highPriorityComponents.push(componentInfo);
              categoryComponentsMap.delete(name);
            }
          });
        }
        /**
         * 走到这，需要将相应category的全量component的map数据中剩余的数据加到该类目数据的最后,如果有的话
         */
        for (const componentInfo of categoryComponentsMap.values()) {
          highPriorityComponents.push(componentInfo);
        }
      }
    }
  });
  return [...highPriorityComponents, ...lowerPriorityComponents];
};

// 处理组件的国际化
export const handleComponentI18n = (component: IPublicTypeComponentDescription): void => {
  if (typeof component.category === 'string') {
    component.category = t(component.category);
  }
  if (typeof component.group === 'string') {
    component.group = t(component.group);
  }
  if (typeof component.title === 'string') {
    const locale = config.get('locale') || 'zh-CN';
    component.title = {
      type: 'i18n',
      [locale]: t(component.title),
    };
  }

  if (!Array.isArray(component.configure)) {
    handlePropsI18n(component.configure?.props);
  } else {
    handlePropsI18n(component.configure);
  }

  component.snippets?.forEach((snippet) => {
    if (typeof snippet.title === 'string') {
      snippet.title = t(snippet.title);
    }
    if (snippet.schema && typeof snippet.schema.title === 'string') {
      snippet.schema.title = t(snippet.schema.title);
    }
  });
};

/**
 * Page的title国际化
 * @param component
 */
export const handlePageI18n = (component?: IPublicTypeRootSchema): void => {
  if (component?.componentName === 'Page') {
    component.title = t(component.title as string);
  }
};

export const transducerAdvancedTab: IPublicTypeMetadataTransducer = (metadata) => {
  const combined: IPublicTypeFieldConfig[] = [];
  metadata.configure.combined?.forEach((d) => {
    if (d.name !== '#advanced') {
      combined.push(d);
    }
  });

  if (metadata.componentName !== 'Page') {
    combined.push(getAthAdvanceCombined());
  }

  return {
    ...metadata,
    configure: {
      ...metadata.configure,
      combined,
    },
  };
};

export const transducerPageBehaviors: IPublicTypeMetadataTransducer = (metadata) => {
  /**
   * 移除项目中所有组件右上角的lock图标
   */
  material.removeBuiltinComponentAction('lock');
  material.removeBuiltinComponentAction('copy');

  /**
   * 判断是否Page组件，移除Page组件右上角的复制和删除icon
   */
  if (metadata.componentName === 'Page') {
    return {
      ...metadata,
      configure: {
        ...(metadata?.configure ?? {}),
        component: {
          ...(metadata?.configure?.component ?? {}),
          disableBehaviors: ['copy', 'remove'],
        },
      },
    };
  }
  return metadata;
};

// 当前没有找到 从 已注册 组件信息 中获取snippets的方式，所以这里主动获取
export const transducerComponentGroup = (
  metadata: IPublicTypeTransformedComponentMetadata,
  AthComponentGroupMap: Map<string, IPublicTypeSnippet[]>,
) => {
  const { componentName, group, category, snippets } = metadata;
  // 有些组件不展示出来，可以在此配置黑名单
  const groupWhiteList = [t('dj-标准组件')]; // group的白名单
  const categoryWhiteList = [
    t('dj-基础组件'),
    t('dj-开窗组件'),
    t('dj-按钮组件'),
    t('dj-标签组件'),
    t('dj-功能组件'),
  ]; // 分类的黑名单
  const componentBlackList = ['LABEL']; // 组件的黑名单

  if (
    !groupWhiteList.includes(group as string) ||
    !categoryWhiteList.includes(category as string) ||
    componentBlackList.includes(componentName)
  ) {
    return metadata;
  }
  let _category = AthComponentGroupMap.get(category as string);
  if (!_category) {
    _category = [...cloneDeep(snippets as Array<IPublicTypeSnippet>)];
  } else {
    const index = _category.findIndex((d) => d.schema?.componentName === componentName);
    if (index === -1) {
      _category.push(...cloneDeep(snippets as Array<IPublicTypeSnippet>));
    }
  }

  AthComponentGroupMap.set(category as string, _category as Array<IPublicTypeSnippet>);
  return metadata;
};

export const transformAssets = (httpPrefix?: string) => {
  const { packages = [], ...restAssets } = assets;
  return {
    ...restAssets,
    packages: packages.map((s) => {
      const { urls = [], editUrls = [] } = s;
      const tempPackage = s;
      if (Reflect.has(s, 'urls')) {
        tempPackage['urls'] = urls.map((d) =>
          d.replace('${urlPrefix}', httpPrefix ? httpPrefix : location.origin),
        );
      }
      if (Reflect.has(s, 'editUrls')) {
        tempPackage['editUrls'] = editUrls.map((d) =>
          d.replace('${urlPrefix}', httpPrefix ? httpPrefix : location.origin),
        );
      }
      return tempPackage;
    }),
  };
};
