import { IPublicModelPluginContext, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { injectAssets } from '@alilc/lowcode-plugin-inject';
import {
  handleComponentI18n,
  transducerPageBehaviors,
  transducerAdvancedTab,
  transducerComponentGroup,
  sortComponent,
  transformAssetsDataI18n,
  transformAssets,
} from './tools';
import { AthLowCodeConfigKey } from '../plugin-ath-loader/type';
import { envParams } from '@/env';
import { t } from 'i18next';

const AthMaterialLoaderPlugin = (ctx: IPublicModelPluginContext, options: any) => {
  return {
    async init() {
      const { material, config } = ctx;
      const assets = transformAssets(envParams.scheduleDomain);
      // 设置物料描述
      const assetsData = await injectAssets(assets);
      const i18nAssetsData = transformAssetsDataI18n(assetsData);
      await material.setAssets(i18nAssetsData);

      // const assetsAthenaDesignerEditorComponents = await fetch(
      //   `http://***************:8101/athena-designer-editor-layout/build/lowcode/assets-prod.json`,
      // ).then((res) => res.json());
      // await material.loadIncrementalAssets(assetsAthenaDesignerEditorComponents);
      // assetsAthenaDesignerEditorComponents.components.forEach(handleComponentI18n);

      // 产线环境，动态加载物料资源
      if (process?.env?.NODE_ENV !== 'development') {
        const assetsAthenaDesignerEditorComponents = await fetch(
          `${envParams.domain}/athena-designer-editor-components/build/lowcode/assets-prod.json`,
        ).then((res) => res.json());
        await material.loadIncrementalAssets(assetsAthenaDesignerEditorComponents);
        assetsAthenaDesignerEditorComponents.components.forEach(handleComponentI18n);

        // const assetsAthenaDesignerEditorLayout = await fetch(
        //   `${envParams.domain}/athena-designer-editor-layout/build/lowcode/assets-prod.json`,
        // ).then((res) => res.json());
        // await material.loadIncrementalAssets(assetsAthenaDesignerEditorLayout);
        // assetsAthenaDesignerEditorLayout.components.forEach(handleComponentI18n);
      }

      // 在管道中处理snippets中属性没问题，
      // 但处理category，group，title属性时，虽然对象属性已经更新，但在组件管理中的显示不生效
      // 所以逻辑移动到这里
      i18nAssetsData.components.forEach(handleComponentI18n);
      // i18nAssetsData.components = sortComponent(i18nAssetsData.components, i18nAssetsData.sort);

      // const isvComponentList = config.get(AthLowCodeConfigKey.AthIsvComponentList) ?? [];
      // material.loadIncrementalAssets({
      //   version: '',
      //   components: [...isvComponentList],
      // });

      // TODO 为了调试 暂时 注释
      // 动态加载 antd 物料
      // const assetsAntd = await fetch(
      //   `https://alifd.alicdn.com/npm/@alilc/antd-lowcode-materials@1.2.1/build/lowcode/assets-prod.json`,
      // ).then((res) => res.json());
      // material.loadIncrementalAssets(assetsAntd);

      // 动态加载 ath 组件库 物料
      // const assetsAth = await fetch(
      //   ` https://unpkg.com/athena-designer-editor-components@0.1.6/build/lowcode/assets-prod.json`,
      // ).then((res) => res.json());
      // material.loadIncrementalAssets(assetsAth);

      // 物料管道（移除low code 自己的 高级面板, 增加 ath 高级面板）
      material.registerMetadataTransducer(transducerAdvancedTab, 111, 'parse-func');

      material.registerMetadataTransducer(transducerPageBehaviors, 1, 'modify-page-behavior');

      const AthComponentGroupMap = new Map<string, IPublicTypeSnippet[]>();
      config.set(AthLowCodeConfigKey.AthComponentGroupMap, AthComponentGroupMap);
      material.registerMetadataTransducer(
        (metadata) => transducerComponentGroup(metadata, AthComponentGroupMap),
        111,
        'parse-func',
      );

      // 选择父节点按钮
      material.addBuiltinComponentAction({
        name: 'to Parent',
        content: {
          icon: (
            <svg viewBox="0 0 1024 1024" width="14">
              <path
                d="M896 96H128c-17.066667 0-32 14.933333-32 32S110.933333 160 128 160h768c17.066667 0 32-14.933333 32-32s-14.933333-32-32-32zM535.466667 296.533333c-12.8-12.8-32-12.8-44.8 0l-213.333334 213.333334c-12.8 12.8-12.8 32 0 44.8s32 12.8 44.8 0l157.866667-157.866667V853.333333c0 17.066667 14.933333 32 32 32s32-14.933333 32-32V396.8l157.866667 157.866667c6.4 6.4 14.933333 8.533333 23.466666 8.533333s17.066667-2.133333 23.466667-8.533333c12.8-12.8 12.8-32 0-44.8l-213.333333-213.333334z"
                fill="#ffffff"
              />
            </svg>
          ),
          title: `${t('dj-选择父节点')}`,
          action(node) {
            if (node.parent) {
              node.parent.select();
            }
          },
        },
        important: true,
      });
    },
  };
};
AthMaterialLoaderPlugin.pluginName = 'AthMaterialLoaderPlugin';
AthMaterialLoaderPlugin.meta = {};
export default AthMaterialLoaderPlugin;
