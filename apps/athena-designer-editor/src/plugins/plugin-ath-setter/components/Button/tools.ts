import {
  ButtonType,
  ButtonCategory,
  Pattern,
  Category,
  PageCode,
  ButtonApiType,
  ButtonTaskEngineService,
  ButtonWorkflowService,
  ButtonAttachMode,
  ButtonStyleMode,
} from './enum';
import {
  ButtonTypeOptionsMap,
  SingleDocumentSubmitButtonSet,
  BrowserPageSubmitButtonSet,
  EditPageSubmitButtonSet,
  SubmitButtonTypeSet,
  ButtonTypeMap,
  BusinessButtonBaseTypeSet,
  BrowserPageOperationButtonMap,
  EditPageOperationButtonMap,
  SingleDocumentOperationButtonMap,
  SubPageOperationButtonMap,
  OperationButtonTypeSet,
  SingleDocumentSubPageSubmitButtonSet,
} from './constant';
import {
  DefaultButtonInfoTypeMap,
  DefaultBusinessButtonSchema,
  DefaulttSubmitButtonSchema,
} from './defaultButtonValue';

import type { IBusinessButtonInfo, IButtonActionParam, ISubmitButtonInfo } from './buttonCoreTypes';
import type { IButtonTypeSelectOption } from './types';
import { INode } from '@alilc/lowcode-designer';
import { AthComponentType } from '@/tools/business/lcdp-converter/type';
import { IPublicModelNode } from '@alilc/lowcode-types';

export function getCategoryByType(type: ButtonType): ButtonCategory {
  let buttonCategory = ButtonCategory.BUSINESS_BUTTON;
  for (const [category, buttonSet] of ButtonTypeMap.entries()) {
    if (buttonSet.has(type)) {
      buttonCategory = category;
    }
  }
  return buttonCategory;
}

/**
 * @function 根据按钮类目+业务类型+基础资料类型获取可选的按钮类型列表。2025-03-11 入参过多，转成入参的形式
 * @description 2024-12-11 本期由于lowcode只上基础资料，所以SubmitButton实际生效的只有DATA_ENTRY这个条件.后续再上别的时，这块逻辑需要补全
 * 且，由于暂时还没有pattern的值(目前DynamicWorkDesign那边还没有存), 调用方会写死DATA_ENTRY，有值之后应该传入实际值
 * @param buttonCategory - button类目
 * @param pattern - 业务类型, 默认data_entry
 * @param category - 基础资料类型
 * @param attachMode - 按钮处在的位置，注意这里的attachMode我只是复用了ButtonAttachMode枚举，并不是要求传递dslInfo中的attachMode值，因为调这个方法的时候还没有按钮的具体信息。
 * 所以这里的这个attachMode的传递逻辑为：
 * 判断这个按钮当前拖到哪儿去了，如果：
 *  1. 表格TABLE_GROUP中就传ButtonAttachMode.ROW,
 *  2. 表格插槽中或者表单中就传ButtonAttachMode.ALL,
 *  3. 不在以上的位置，就不传
 * 以上是拖拽生成的场景，如果是手动在表格操作列里面添加，那么你肯定知道当前添加的是ROW还是ALL，按需传就行
 * @param filterCommon - 针对提供给表格手动添加操作的列表服务，如果不需要COMMON，传true
 * @returns IButtonTypeSelectOption[]
 */
export function getButtonTypeList({
  buttonCategory,
  pattern = Pattern.DATA_ENTRY,
  category,
  pageCode,
  attachMode,
  filterCommon = false,
}: {
  buttonCategory: ButtonCategory;
  pattern?: Pattern;
  category?: Category;
  pageCode?: PageCode;
  attachMode?: ButtonAttachMode;
  filterCommon?: boolean;
}): IButtonTypeSelectOption[] {
  const allAvailableButtons = ButtonTypeOptionsMap.get(buttonCategory)!;
  switch (buttonCategory) {
    case ButtonCategory.BUSINESS_BUTTON: {
      /**
       * 基础资料
       */
      if (pattern === Pattern.DATA_ENTRY) {
        /**
         * attachMode为空，那么就只支持基础功能按钮
         */
        if (!attachMode) {
          let baseButtons = allAvailableButtons.filter((item) =>
            BusinessButtonBaseTypeSet.has(item.value),
          );
          if (category === Category['SIGN-DOCUMENT'] || pageCode === PageCode.SUB_PAGE) {
            baseButtons = baseButtons.concat(
              allAvailableButtons.filter((item) =>
                SingleDocumentOperationButtonMap.get(ButtonAttachMode.ALL)?.has(item.value),
              ),
            );
          } else if (pageCode === PageCode.BROWSE_PAGE) {
            baseButtons = baseButtons.concat(
              allAvailableButtons.filter((item) =>
                BrowserPageOperationButtonMap.get(ButtonAttachMode.ALL)?.has(item.value),
              ),
            );
          } else if (pageCode === PageCode.EDIT_PAGE) {
            baseButtons = baseButtons.concat(
              allAvailableButtons.filter((item) =>
                EditPageOperationButtonMap.get(ButtonAttachMode.ALL)?.has(item.value),
              ),
            );
          }
          return [...new Set([...baseButtons])];
        }
        let availableButtons = [];
        if (pageCode === PageCode.SUB_PAGE) {
          availableButtons = allAvailableButtons.filter((item) =>
            SubPageOperationButtonMap.get(attachMode)?.has(item.value),
          );
          console.log(
            '%c 🚀 [Neovim AutoGR Log]: path = src/plugins/plugin-ath-setter/components/Button/tools.ts, scope = getButtonTypeList, availableButtons = ',
            'color: orangered; font-weight: bold;',
            availableButtons,
          );
          return filterCommon
            ? availableButtons.filter(({ value }) => value !== ButtonType.BUTTON)
            : availableButtons;
        } else if (category === Category['SIGN-DOCUMENT']) {
          /**
           * 单档多栏界面设计
           */
          availableButtons = allAvailableButtons.filter((item) =>
            SingleDocumentOperationButtonMap.get(attachMode)?.has(item.value),
          );
          return filterCommon
            ? availableButtons.filter(({ value }) => value !== ButtonType.BUTTON)
            : availableButtons;
          /**
           * 浏览界面
           */
        } else if (pageCode === PageCode.BROWSE_PAGE) {
          availableButtons = allAvailableButtons.filter((item) =>
            BrowserPageOperationButtonMap.get(attachMode)?.has(item.value),
          );
          return filterCommon
            ? availableButtons.filter(({ value }) => value !== ButtonType.BUTTON)
            : availableButtons;
          /**
           * 编辑界面
           */
        } else if (pageCode === PageCode.EDIT_PAGE) {
          availableButtons = allAvailableButtons.filter((item) =>
            EditPageOperationButtonMap.get(attachMode)?.has(item.value),
          );
          return filterCommon
            ? availableButtons.filter(({ value }) => value !== ButtonType.BUTTON)
            : availableButtons;
        }
      }
      return allAvailableButtons;
    }
    case ButtonCategory.SUBMIT_BUTTON: {
      /**
       * 基础资料
       */
      if (pattern === Pattern.DATA_ENTRY) {
        /**
         * 单档多栏界面设计
         */
        if (category === Category['SIGN-DOCUMENT']) {
          return pageCode === PageCode.SUB_PAGE
            ? allAvailableButtons.filter((item) =>
                SingleDocumentSubPageSubmitButtonSet.has(item.value),
              )
            : allAvailableButtons.filter((item) => SingleDocumentSubmitButtonSet.has(item.value));
          /**
           * 浏览界面
           */
        } else if (pageCode === PageCode.BROWSE_PAGE) {
          return allAvailableButtons.filter((item) => BrowserPageSubmitButtonSet.has(item.value));
          /**
           * 编辑界面
           */
        } else if (pageCode === PageCode.EDIT_PAGE) {
          return allAvailableButtons.filter((item) => EditPageSubmitButtonSet.has(item.value));
        }
      }
      return allAvailableButtons;
    }
    default:
      return [];
  }
}

export function isUndefindedOrNull(value: any): boolean {
  return value === undefined || value === null;
}

export function fillActionParams(
  actionParams?: IButtonActionParam[],
  schema?: string,
): IButtonActionParam[] {
  if (!actionParams || actionParams.length === 0) return [];
  return actionParams.map((actionParam) => {
    const newParam = { ...actionParam };
    if (isUndefindedOrNull(newParam.name)) {
      newParam.name = schema;
    }
    if (isUndefindedOrNull(newParam.value)) {
      newParam.value = schema;
    }
    return newParam;
  });
}

/**
 * 补足默认值中需要动态添加的表名
 * 前提条件，默认值中定义了相应值，并且值为空：
 * 1. ISubmitButtonInfo.action.submitType 有定义 && 该值为空
 * 2. ISubmitButtonInfo.action.actionParams中的对象 有定义 && 该值的name或者value为空 （都为空都补）
 * 3. ISubmitButtonInfo.action.combineAttachActions中对象中的actionParams有定义 && 该值的name或者value为空 （都为空都补）
 */
export function fillSchemaDataForSubmitInfo(
  info: ISubmitButtonInfo,
  schema?: string,
): ISubmitButtonInfo {
  if (info?.action?.submitType && !info.action.submitType.schema) {
    info.action.submitType.schema = schema;
  }
  if (info?.action?.actionParams && info.action.actionParams.length > 0) {
    info.action.actionParams = fillActionParams(info.action.actionParams, schema);
  }
  if (info?.action?.combineAttachActions && info.action.combineAttachActions.length > 0) {
    info.action.combineAttachActions = info.action.combineAttachActions.map((action) => {
      return {
        ...action,
        actionParams: fillActionParams(action.actionParams, schema),
      };
    });
  }
  return info;
}

export function fillOperationInfoFormOperationButton({
  type,
  buttonInfo,
  pattern,
  category,
  pageCode,
  operationInfo = {},
}: {
  type: ButtonType;
  buttonInfo: IBusinessButtonInfo;
  operationInfo: {
    schema?: string;
    path?: string;
    actionId?: string;
    attachMode?: ButtonAttachMode;
  };
  pattern?: Pattern;
  category?: Category;
  pageCode?: PageCode;
}) {
  /**
   * actionId只预留
   */
  const { schema, path, attachMode, actionId } = operationInfo;
  /**
   * 目前只接了基础资料，所以其余场景不处理
   */
  if (pattern === Pattern.DATA_ENTRY) {
    const baseButtonInfo = {
      ...buttonInfo,
      targetSchema: schema,
      targetPath: path,
      operation: {
        ...(buttonInfo.operation ?? {}),
        attach: { ...(buttonInfo.operation?.attach ?? {}) },
        pageCode,
      },
    };
    if (type === ButtonType.BUTTON_PRINT) {
      baseButtonInfo.attachMode = attachMode;
    }
    if (!attachMode) {
      baseButtonInfo.styleMode = ButtonStyleMode.DEFAULT;
    }
    /**
     * 单档多栏界面设计
     */
    if (category === Category['SIGN-DOCUMENT']) {
      baseButtonInfo.operation.attach.applyToField = 'BUTTON_GROUP';
      /**
       * 浏览界面
       */
    } else if (pageCode === PageCode.BROWSE_PAGE) {
      baseButtonInfo.operation.attach.applyToField = 'BUTTON_GROUP';
      /**
       * 编辑界面
       */
    } else if (pageCode === PageCode.EDIT_PAGE) {
      baseButtonInfo.operation.attach.applyToField = 'UIBOT_BUTTON_GROUP';
    }
    return baseButtonInfo;
  }
  return buttonInfo;
}

/**
 * 根据按钮类型获取静态配置的默认值
 */
export function getDefaultValueByType({
  type,
  pattern,
  pageCode,
  category,
  submitInfo = {},
  operationInfo = {},
}: {
  type: ButtonType;
  pattern?: Pattern;
  category?: Category;
  pageCode?: PageCode;
  submitInfo?: { schema?: string };
  operationInfo?: {
    schema?: string;
    path?: string;
    attachMode?: ButtonAttachMode;
    /**
     * 原来的operation并不会自动添加actionId, 但是文档中似乎是有这个需要的，暂时不加，但是预留这个定义
     */
    actionId?: string;
  };
}): IBusinessButtonInfo | ISubmitButtonInfo {
  const { schema } = submitInfo;
  let buttonInfo = DefaultButtonInfoTypeMap.get(type);
  if (!buttonInfo) {
    buttonInfo = SubmitButtonTypeSet.has(type)
      ? DefaulttSubmitButtonSchema
      : DefaultBusinessButtonSchema;
  }
  if (SubmitButtonTypeSet.has(type)) {
    return fillSchemaDataForSubmitInfo(buttonInfo, schema);
  } else if (OperationButtonTypeSet.has(type)) {
    return fillOperationInfoFormOperationButton({
      type,
      buttonInfo,
      pattern,
      pageCode,
      category,
      operationInfo,
    });
  }
  return buttonInfo;
}

export function enumToArray<T extends Record<string, string | number>>(
  enumObj: T,
): Array<{ label: string; value: T[keyof T] }> {
  return Object.keys(enumObj)
    .filter((key) => isNaN(Number(key))) // 排除反向映射的数值键
    .map((key) => ({
      label: key,
      value: enumObj[key as keyof T],
    }));
}

/**
 * 以下方法全部用于判断action内部表单是否展示
 */
/**
 * 是否需要展示业务类型：非RECYCLE都需要
 */
export function useActionType(apiType?: ButtonApiType): boolean {
  return apiType !== ButtonApiType.RECYCLE;
}

/**
 * 是否需要action选择弹窗的服务表单：只有ESP,SD,TM需要
 */
export function useActionSelector(apiType?: ButtonApiType): boolean {
  return [ButtonApiType.ESP, ButtonApiType.SD, ButtonApiType.TM].includes(apiType);
}

/**
 * 是否需要输入框的服务名表单：只有COMBINE,RECYCLE,ESP,PTM需要
 */
export function useInputServiceName(apiType?: ButtonApiType): boolean {
  return [
    ButtonApiType.COMBINE,
    ButtonApiType.RECYCLE,
    ButtonApiType.ESP,
    ButtonApiType.PTM,
  ].includes(apiType);
}

/**
 * 是否需要下拉框的服务名表单：只有TASK_ENGINE,WORKFLOW需要
 */
export function useSelectServiceName(apiType?: ButtonApiType): boolean {
  return [ButtonApiType.TASK_ENGINE, ButtonApiType.WORKFLOW].includes(apiType);
}

/**
 * 是否需要下拉框的服务表单：只有UIBOT需要
 */
export function useSelectService(apiType?: ButtonApiType): boolean {
  return apiType === ButtonApiType.UIBOT;
}

/**
 * 是否需要api路径输入框：只有COMBINE,RECYCLE,WORKFLOW需要
 */
export function useApiPath(apiType?: ButtonApiType): boolean {
  return [ButtonApiType.COMBINE, ButtonApiType.RECYCLE, ButtonApiType.WORKFLOW].includes(apiType);
}
/**
 * 判断action内部表单是否展示方法结束
 */
export function getSelectServiceNameList(apiType: ButtonApiType) {
  const apiTypeListMap = new Map([
    [ButtonApiType.TASK_ENGINE, enumToArray(ButtonTaskEngineService)],
    [ButtonApiType.WORKFLOW, enumToArray(ButtonWorkflowService)],
  ]);
  return apiTypeListMap.get(apiType) ?? [];
}

/**
 * 从起始节点开始向上找目标类型的节点, 找到任意一个即终止查询
 */
export function findTargetNodeByTypes(
  targetTypes: AthComponentType[],
  startNode?: INode | IPublicModelNode,
): INode | null {
  const rootComponentName = 'Page';
  let currentNode: INode | null = startNode ?? null;
  while (currentNode && currentNode.componentName !== rootComponentName) {
    if (targetTypes.includes(currentNode.componentName)) {
      break;
    }
    currentNode = currentNode.parent;
  }
  if (!currentNode || currentNode.componentName === rootComponentName) {
    return null;
  }
  return currentNode;
}

/**
 * 从起始节点开始向上找目标类型的节点
 */
export function findTargetNode(
  targetType: AthComponentType,
  startNode?: INode | IPublicModelNode,
): INode | IPublicModelNode | null {
  const rootComponentName = 'Page';
  let currentNode: INode | IPublicModelNode | null = startNode ?? null;
  while (currentNode && currentNode.componentName !== rootComponentName) {
    if (currentNode.componentName === targetType) {
      break;
    }
    currentNode = currentNode.parent;
  }
  if (!currentNode || currentNode.componentName === rootComponentName) {
    return null;
  }
  return currentNode;
}

/**
 * 根据当前节点的信息获取它内部可以使用的operation的mode类型
 */
export function getAttachMode(node?: INode | IPublicModelNode): ButtonAttachMode | undefined {
  const targetAllNode = findTargetNode(AthComponentType.DYNAMIC_OPERATION, node);
  const targetRowNode = findTargetNode(AthComponentType.TABLE_GROUP, node);
  if (targetAllNode) {
    return ButtonAttachMode.ALL;
  }
  if (targetRowNode) {
    return ButtonAttachMode.ROW;
  }
  return undefined;
}

/**
 * WARN: 注意这个方式是直接改原值的
 */
export function setNestedValue(target: any, path: string[], value: any): any {
  path.reduce((prev, key, index) => {
    if (index === path.length - 1) {
      prev[key] = value;
    } else {
      prev[key] = prev[key] ?? {};
    }
    return prev[key];
  }, target);
}
