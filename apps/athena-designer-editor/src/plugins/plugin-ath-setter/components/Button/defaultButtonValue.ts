import {
  ButtonType,
  ButtonStyleMode,
  ButtonSizeType,
  ButtonApiType,
  ButtonActionType,
  ActionParamType,
  ButtonConditionTriggerType,
  SubpageOpenType,
  ButtonAttachMode,
  OperationType,
} from './enum';

import type {
  IBusinessButtonInfo,
  ISubmitButtonInfo,
  IOperationConfigInfo,
} from './buttonCoreTypes';

/**
 * BUTTON
 * 功能按钮默认使用的schema
 */
export const DefaultBusinessButtonSchema: IBusinessButtonInfo = {
  id: '',
  type: ButtonType.BUTTON,
  title: '通用',
  lang: {
    title: {
      zh_CN: '通用',
      zh_TW: '通用',
      en_US: 'Universal',
    },
  },
  styleMode: ButtonStyleMode.DEFAULT,
  size: ButtonSizeType.LARGE,
  disabled: false,
  ghost: false,
  danger: false,
  block: false,
  debounce: false,
  debounceTime: 300,
};

/**
 * 子页面按钮
 */
export const DefaultSubPageButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_SUB_PAGE,
  title: '打开子页面',
  lang: {
    title: {
      zh_CN: '打开子页面',
      zh_TW: '打開子頁面',
      en_US: 'Open Subpage',
    },
  },
  subpageConfig: {
    target: SubpageOpenType.MODAL,
    optional: {
      width: '600px',
      height: 'auto',
    },
  },
};

/**
 * 打印按钮
 * WARN: 打印按钮既有动态按钮也有operation，但是结构更像action，所以使用Submit的定义
 * WARN: 打印的attachMode比较特殊，它在行，整表，表格外都可以使用，所以需要在运行时手动处理这个值，这边就不加默认值了
 */
export const DefaultPrintTemplateButtonSchema: ISubmitButtonInfo & IOperationConfigInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_PRINT,
  styleMode: ButtonStyleMode.PRIMARY,
  title: '打印',
  description: '打印',
  lang: {
    title: {
      zh_CN: '打印',
      zh_TW: '列印',
      en_US: 'Print',
    },
    description: {
      zh_CN: '打印',
      zh_TW: '列印',
      en_US: 'Print',
    },
  },
};

/**
 * 一般提交按钮schema, 需要手动覆盖type, title, lang, 以及其它需要变化的属性
 */
export const DefaultBaseSubmitButtonSchema: ISubmitButtonInfo = {
  ...DefaultBusinessButtonSchema,
  action: {
    submitType: {
      /**
       * 所以提交按钮的这个字段由设置器初始化时获取当前fieldTree的根节点data_name赛到这里
       */
      schema: '',
      isBatch: false,
    },
    trackCode: 'SUBMIT',
  },
  condition: {
    relateValidators: false,
    unchangedForbidClick: false,
  },
};

/**
 * BUTTON_DATA_COMBINE_SAVE
 * 组合保存按钮
 */
export const DefaultCombineSaveButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  type: ButtonType.BUTTON_DATA_COMBINE_SAVE,
  styleMode: ButtonStyleMode.PRIMARY,
  title: '保存',
  lang: {
    title: {
      zh_CN: '保存',
      zh_TW: '存檔',
      en_US: 'Save',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.COMBINE,
    actionType: ButtonActionType.COMBINE,
    combineAttachActions: [
      {
        type: ButtonApiType.ESP,
        actionType: ButtonActionType.SAVE,
        title: '新建',
        lang: {
          title: {
            zh_CN: '新建',
            zh_TW: '新建',
            en_US: 'Create',
          },
        },
      },
      {
        type: ButtonApiType.ESP,
        actionType: ButtonActionType.UPDATE,
        title: '保存',
        lang: {
          title: {
            zh_CN: '保存',
            zh_TW: '存檔',
            en_US: 'Save',
          },
        },
      },
    ],
  },
};

/**
 * 子页面保存并关闭按钮
 */
// export const DefaultSubPageSaveButtonSchema: ISubmitButtonInfo = {
//   ...DefaultBaseSubmitButtonSchema,
//   type: ButtonType.BUTTON_SUB_PAGE_SAVE,
//   title: '子页面保存并关闭',
//   lang: {
//     title: {
//       zh_CN: '子页面保存并关闭',
//       zh_TW: '子页面保存并关闭',
//       en_US: 'Subpage save and close',
//     },
//   },
//   styleMode: ButtonStyleMode.PRIMARY,
//   trailingAction: 'close-page',
//   action: {
//     type: ButtonApiType.ESP,
//     actionType: ButtonActionType.SAVE,
//     actionId: 'esp_dpbas.xc.xmbb.update',
//     actionParams: [
//       {
//         type: ActionParamType.GET_ACTION_RESPONSE,
//       },
//     ],
//     trackCode: 'SUBMIT',
//     submitType: {
//       isBatch: false,
//       schema: '',
//     },
//   },
// };

/**
 * BUTTON_DATA_SAVE
 * 组合保存按钮
 */
export const DefaultSaveButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  type: ButtonType.BUTTON_DATA_SAVE,
  styleMode: ButtonStyleMode.PRIMARY,
  title: '保存',
  lang: {
    title: {
      zh_CN: '保存',
      zh_TW: '存檔',
      en_US: 'Save',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.SAVE,
    // combineAttachActions: [
    //   {
    //     type: ButtonApiType.ESP,
    //     actionType: ButtonActionType.SAVE,
    //     title: '新建',
    //     lang: {
    //       title: {
    //         zh_CN: '新建',
    //         zh_TW: '新建',
    //         en_US: 'Create',
    //       },
    //     },
    //   },
    // ],
  },
};

/**
 * BUTTON_DATA_DELETE
 * 删除按钮
 */
export const DefaultDataDeleteButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '删除',
  type: ButtonType.BUTTON_DATA_DELETE,
  lang: {
    title: {
      zh_CN: '删除',
      zh_TW: '删除',
      en_US: 'Delete',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.DELETE,
  },
};

/**
 * BUTTON_DATA_INVALID
 * 失效
 */
export const DefaultInvalidButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '失效',
  type: ButtonType.BUTTON_DATA_INVALID,
  lang: {
    title: {
      zh_TW: '失效',
      en_US: 'Invalid',
      zh_CN: '失效',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.INVALID,
  },
};

/**
 * BUTTON_DATA_VALID
 * 生效
 */
export const DefaultValidButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '生效',
  type: ButtonType.BUTTON_DATA_VALID,
  lang: {
    title: {
      zh_TW: '生效',
      en_US: 'Valid',
      zh_CN: '生效',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.VALID,
  },
};

/**
 * BUTTON_DATA_CANCEL_VALID
 * 取消生效
 */
export const DefaultCancelValidButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '取消生效',
  type: ButtonType.BUTTON_DATA_CANCEL_VALID,
  lang: {
    title: {
      zh_TW: '取消生效',
      en_US: 'Cancel Valid',
      zh_CN: '取消生效',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.CANCEL_VALID,
  },
};

/**
 * BUTTON_DATA_RESET_VALID
 * 重新生效
 */
export const DefaultResetValidButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '重新生效',
  type: ButtonType.BUTTON_DATA_RESET_VALID,
  lang: {
    title: {
      zh_TW: '重新生效',
      en_US: 'Revalidate',
      zh_CN: '重新生效',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.RESET_VALID,
  },
};

/**
 * BUTTON_DELETE_TO_RECYCLE
 * 删除到回收站
 */
export const DefaultDeleteToRecycleButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '删除到回收站',
  type: ButtonType.BUTTON_DELETE_TO_RECYCLE,
  lang: {
    title: {
      zh_TW: '删除到回收站',
      zh_CN: '删除到回收站',
      en_US: 'Delete to recycle',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.DELETE,
    dispatchBPM: false,
    needProxyToken: false,
    combineAttachActions: [
      {
        type: ButtonApiType.RECYCLE,
        actionId: 'recycle.save',
        title: '删除回收站数据',
        lang: {
          title: {
            zh_TW: '刪除回收站數據',
            en_US: 'Delete Recycle Bin data',
            zh_CN: '删除回收站数据',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_DATA_RECOVER
 * 还原按钮
 */
export const DefaultRecoverButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '还原',
  type: ButtonType.BUTTON_DATA_RECOVER,
  lang: {
    title: {
      zh_TW: '還原',
      en_US: 'Recycle',
      zh_CN: '还原',
    },
  },
  styleMode: ButtonStyleMode.PRIMARY,
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionType: ButtonActionType.RECOVER,
    dispatchBPM: false,
    needProxyToken: false,
    extendParas: {
      applyToRecycle: false,
    },
    combineAttachActions: [
      {
        type: ButtonApiType.RECYCLE,
        actionId: 'recycle.delete',
        title: '删除回收站数据',
        lang: {
          title: {
            zh_TW: '刪除回收站數據',
            en_US: 'Delete Recycle Bin data',
            zh_CN: '删除回收站数据',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_RECYCLE_DELETE
 * 彻底删除按钮
 */
export const DefaultDeleteCompleteButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '彻底删除',
  type: ButtonType.BUTTON_RECYCLE_DELETE,
  lang: {
    title: {
      zh_TW: '徹底刪除',
      en_US: 'Delete completely',
      zh_CN: '彻底删除',
    },
  },
  styleMode: ButtonStyleMode.PRIMARY,
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.RECYCLE,
    actionType: ButtonActionType.DELETE,
    actionId: 'recycle.delete',
    dispatchBPM: false,
    needProxyToken: false,
    extendParas: {
      applyToRecycle: true,
    },
  },
};

/**
 * BUTTON_WORKFLOW_ABORT
 * 撤审按钮
 */
export const DefaultAbortButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '撤审',
  type: ButtonType.BUTTON_WORKFLOW_ABORT,
  lang: {
    title: {
      zh_TW: '撤审',
      en_US: 'processabort',
      zh_CN: '撤审',
    },
  },
  condition: {
    script: "data?.manage_status=='I'",
    trigger: [ButtonConditionTriggerType.INIT, ButtonConditionTriggerType.DATA_CHANGED],
  },
  hiddenConfig: {
    script: "data?.manage_status!='I' && data?.manage_status!='Y'",
    trigger: [ButtonConditionTriggerType.INIT, ButtonConditionTriggerType.DATA_CHANGED],
  },
  action: {
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.abort',
    serviceName: 'workflow-abort',
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    combineAttachActions: [
      {
        title: '业务数据更新',
        type: ButtonApiType.ESP,
        lang: {
          title: {
            zh_CN: '业务数据更新',
            zh_TW: '業務數據更新',
            en_US: 'Business data update',
          },
        },
        paras: {
          API入参name: [
            {
              manage_status: 'N',
            },
          ],
        },
        actionParams: [
          {
            name: '#@tableName@.serial_number',
            type: ActionParamType.GET_ACTION_RESPONSE,
            value: 'serialNumber',
          },
        ],
      },
    ],
    extendParas: {
      ignoreDataChange: true,
    },
  },
};

/**
 * BUTTON_WORKFLOW_INVOKE
 * 送审按钮
 */
export const DefaultInvokeButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '送审',
  type: ButtonType.BUTTON_WORKFLOW_INVOKE,
  lang: {
    title: {
      zh_CN: '送审',
      zh_TW: '送审',
      en_US: 'processinvoke',
    },
  },
  hiddenConfig: {
    script: "data?.manage_status!='N'",
    trigger: [ButtonConditionTriggerType.INIT, ButtonConditionTriggerType.DATA_CHANGED],
  },
  action: {
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.invoke',
    serviceName: 'workflow-invoke',
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    combineAttachActions: [
      {
        type: ButtonApiType.ESP,
        title: '送审后更新流程序号',
        lang: {
          title: {
            zh_CN: '送审后更新流程序号',
            zh_TW: '送審後更新流程程式編號',
            en_US: 'Update the flow program number after submission for review',
          },
        },
        paras: {
          API入参name: [
            {
              manage_status: 'I',
            },
          ],
        },
        actionParams: [
          {
            name: '#@tableName@.serial_number',
            type: ActionParamType.GET_ACTION_RESPONSE,
            value: '#@tableName@.serialNumber',
          },
        ],
      },
    ],
    extendParas: {
      ignoreDataChange: true,
    },
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
    paras: {
      processId: '',
    },
  },
};

/**
 * BUTTON_AGREE
 * 同意按钮, 任务卡，人工签核;
 */
export const DefaultAgreeButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '同意',
  type: ButtonType.BUTTON_AGREE,
  lang: {
    title: {
      zh_TW: '同意',
      en_US: 'Agree',
      zh_CN: '同意',
    },
  },
  styleMode: ButtonStyleMode.PRIMARY,
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
    combineAttachActions: [
      {
        type: ButtonApiType.WORKFLOW,
        serviceName: 'workflow-act-agree',
        actionId: 'athena.workflow.process.activity.work.agree',
        title: '同意',
        lang: {
          title: {
            zh_TW: '同意',
            en_US: 'Agree',
            zh_CN: '同意',
          },
        },
        actionParams: [
          {
            type: ActionParamType.GET_ACTION_RESPONSE,
          },
        ],
      },
    ],
  },
};

/**
 * BUTTON_TASK_AGREE
 * 同意按钮，签核任务
 */
export const DefaultTaskAgreeButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '同意',
  type: ButtonType.BUTTON_TASK_AGREE,
  styleMode: ButtonStyleMode.PRIMARY,
  lang: {
    title: {
      zh_TW: '同意',
      en_US: 'Agree',
      zh_CN: '同意',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    combineAttachActions: [
      {
        type: ButtonApiType.TASK_ENGINE,
        serviceName: 'agree',
        actionId: 'agree',
        title: '同意',
        lang: {
          title: {
            zh_TW: '同意',
            en_US: 'Agree',
            zh_CN: '同意',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_DISAGREE
 * 不同意按钮, 任务卡，人工签核
 */
export const DefaultDisAgreeButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '不同意',
  type: ButtonType.BUTTON_DISAGREE,
  lang: {
    title: {
      zh_TW: '不同意',
      en_US: 'disagree',
      zh_CN: '不同意',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
    combineAttachActions: [
      {
        type: ButtonApiType.WORKFLOW,
        title: '不同意',
        lang: {
          title: {
            zh_TW: '不同意',
            en_US: 'disagree',
            zh_CN: '不同意',
          },
        },
        actionId: 'athena.workflow.process.activity.work.disagree',
        serviceName: 'workflow-act-disagree',
        actionParams: [
          {
            type: ActionParamType.GET_ACTION_RESPONSE,
          },
        ],
      },
    ],
  },
};

/**
 * BUTTON_ACT_RETURN
 * 退回按钮, 任务卡，人工签核
 */
export const DefaultActReturnButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '退回',
  type: ButtonType.BUTTON_ACT_RETURN,
  lang: {
    title: {
      zh_TW: '退回',
      en_US: 'return',
      zh_CN: '退回',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.activity.return',
    serviceName: 'workflow-act-return',
    terminateProcess: false,
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_TASK_REEXECUTE
 * 签核任务：退回重办
 */
export const DefaultTaskReexecuteButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '退回重办',
  type: ButtonType.BUTTON_TASK_REEXECUTE,
  lang: {
    title: {
      zh_TW: '退回重辦',
      en_US: 'reexecute',
      zh_CN: '退回重办',
    },
  },
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    dispatch: true,
    combineAttachActions: [
      {
        title: '退回',
        type: ButtonApiType.TASK_ENGINE,
        actionId: 'reexecute',
        serviceName: 'reexecute',
        lang: {
          title: {
            zh_TW: '退回',
            en_US: 'reexecute',
            zh_CN: '退回',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_ACT_ADD
 * 加签, 任务卡，人工签核
 */
export const DefaultActAddButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '加签',
  type: ButtonType.BUTTON_ACT_ADD,
  lang: {
    title: {
      zh_TW: '加簽',
      en_US: 'add',
      zh_CN: '加签',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.activity.add',
    serviceName: 'workflow-act-add',
    terminateProcess: false,
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_DATA_UPDATE
 * 更新保存按钮, 任务卡，人工关卡
 */
export const DefaultUpdateButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '保存',
  type: ButtonType.BUTTON_DATA_UPDATE,
  lang: {
    title: {
      zh_TW: '存檔',
      en_US: 'Save',
      zh_CN: '保存',
    },
  },
  styleMode: ButtonStyleMode.PRIMARY,
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    actionId: 'esp_dpbas.item.data.update',
    serviceName: 'dpbas.item.data.update',
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_WORKFLOW_SUBMIT
 * 提交按钮, 任务卡，人工关卡
 */
export const DefaulttSubmitButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '提交',
  type: ButtonType.BUTTON_WORKFLOW_SUBMIT,
  lang: {
    title: {
      zh_TW: '提交',
      en_US: 'submit',
      zh_CN: '提交',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.WORKFLOW,
    actionId: 'athena.workflow.process.activity.work.submit',
    serviceName: 'workflow-act-submit',
    terminateProcess: false,
    actionParams: [
      {
        type: ActionParamType.GET_ACTION_RESPONSE,
      },
    ],
  },
};

/**
 * BUTTON_DATA_TERMINATE
 * 一般任务：终止按钮
 */
export const DefaultDataTerminateButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '终止',
  type: ButtonType.BUTTON_DATA_TERMINATE,
  lang: {
    title: {
      zh_TW: '終止',
      en_US: 'Termination',
      zh_CN: '终止',
    },
  },
  confirm: {
    enable: true,
    content: '是否确认终止',
    lang: {
      content: {
        zh_TW: '是否確認終止',
        en_US: 'Is termination confirmed',
        zh_CN: '是否确认终止',
      },
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    dispatch: true,
    combineAttachActions: [
      {
        title: '终止任务的数据',
        type: ButtonApiType.UIBOT,
        actionId: 'terminate-data',
        lang: {
          title: {
            zh_TW: '終止任務的數據',
            en_US: 'terminate data',
            zh_CN: '终止任务的数据',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_REPORT_QUERY
 * 报表查询按钮
 */
export const DefaultReportQueryButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '查询',
  type: ButtonType.BUTTON_REPORT_QUERY,
  styleMode: ButtonStyleMode.PRIMARY,
  lang: {
    title: {
      zh_TW: '查詢',
      en_US: 'Search',
      zh_CN: '查询',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.ESP,
    /**
     * 同schema逻辑, 需要运行时挂载
     */
    actionId: '',
    /**
     * 同schema逻辑, 需要运行时挂载
     */
    serviceName: '',
  },
};

/**
 * BUTTON_TASK_TERMINATE
 * 签核任务：终止任务
 */
export const DefaultTaskTerminateButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '终止任务',
  type: ButtonType.BUTTON_TASK_TERMINATE,
  lang: {
    title: {
      zh_TW: '終止任务',
      en_US: 'terminate task',
      zh_CN: '终止任务',
    },
  },
  confirm: {
    enable: true,
    title: '提示',
    content: '是否确定终止？',
    lang: {
      title: {
        zh_TW: '提示',
        en_US: 'prompt',
        zh_CN: '提示',
      },
      content: {
        zh_TW: '是否確定終止？',
        zh_CN: '是否确定终止？',
        en_US: 'Confirm terminate?',
      },
    },
  },
  action: {
    type: ButtonApiType.ESP,
    submitType: {
      schema: '',
      isBatch: false,
      submitAll: true,
    },
    combineAttachActions: [
      {
        type: ButtonApiType.TASK_ENGINE,
        title: '终止任务',
        actionId: 'terminate-task',
        serviceName: 'terminate-task',
        lang: {
          title: {
            zh_TW: '終止任务',
            en_US: 'terminate task',
            zh_CN: '终止任务',
          },
        },
      },
    ],
  },
};

/**
 * BUTTON_TASK_REAPPROVE
 * 签核任务：退回重签
 */
export const DefaultTaskReapproveButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  type: ButtonType.BUTTON_TASK_REAPPROVE,
  title: '退回重签',
  lang: {
    title: {
      zh_TW: '退回重簽',
      en_US: 'ReApprove',
      zh_CN: '退回重签',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.TASK_ENGINE,
    actionId: 'athena_bpm_reapprove',
    serviceName: 'reapprove',
  },
};

/**
 * BUTTON_TASK_ADD
 * 签核任务：加签
 */
export const DefaultTaskAddButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '加签',
  type: ButtonType.BUTTON_TASK_ADD,
  lang: {
    title: {
      zh_TW: '加簽',
      en_US: 'AddApprove',
      zh_CN: '加签',
    },
  },
  action: {
    ...DefaultBaseSubmitButtonSchema.action,
    type: ButtonApiType.TASK_ENGINE,
    actionId: 'athena_bpm_add_task',
    serviceName: 'add-task',
  },
};

/**
 * operations start
 */

/**
 * operation: 前端导出
 */
export const DefaultOperationFrontExportButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_FRONT_EXPORT,
  title: '前端导出',
  description: '前端导出',
  attachMode: ButtonAttachMode.ALL,
  styleMode: ButtonStyleMode.TEXT,
  lang: {
    title: {
      zh_CN: '前端导出',
      zh_TW: '前端導出',
      en_US: 'Front-end export',
    },
    description: {
      zh_CN: '前端导出',
      zh_TW: '前端導出',
      en_US: 'Front-end export',
    },
  },
  operation: {
    type: OperationType.FRONT_EXPORT,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 数据导出
 */
export const DefaultOperationBackendExportButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_BACKEND_EXPORT,
  styleMode: ButtonStyleMode.TEXT,
  title: '数据导出',
  description: '数据导出',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '数据导出',
      zh_TW: '數據導出',
      en_US: 'Data export',
    },
    description: {
      zh_CN: '数据导出',
      zh_TW: '數據導出',
      en_US: 'Data export',
    },
  },
  operation: {
    type: OperationType.DATA_EXPORT,
    isCustomize: false,
    switchData: true,
    extendedFields: null,
  },
};

/**
 * operation: 文件导入
 */
export const DefaultUploadFileButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_UPLOAD_FILE,
  styleMode: ButtonStyleMode.TEXT,
  title: '文件导入',
  description: '文件导入',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '文件导入',
      zh_TW: '檔案匯入',
      en_US: 'File import',
    },
    description: {
      zh_CN: '文件导入',
      zh_TW: '檔案匯入',
      en_US: 'File import',
    },
  },
  operation: {
    type: OperationType.UPLOAD_FILE,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 下载模板
 */
export const DefaultDownloadTemplateButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_DOWNLOAD_TEMPLATE,
  styleMode: ButtonStyleMode.TEXT,
  title: '下载模板',
  description: '下载模板',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '下载模板',
      zh_TW: '下載模板',
      en_US: 'Download template',
    },
    description: {
      zh_CN: '下载模板',
      zh_TW: '下載模板',
      en_US: 'Download template',
    },
  },
  operation: {
    type: OperationType.DOWNLOAD_TEMPLATE,
    isCustomize: false,
    switchData: true,
    extendedFields: null,
  },
};

/**
 * operation: 图纸下载
 */
export const DefaultDrawingsDownloadButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_DRAWINGS_DOWNLOAD,
  styleMode: ButtonStyleMode.TEXT,
  title: '图纸下载',
  description: '图纸下载',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '图纸下载',
      zh_TW: '圖紙下載',
      en_US: 'Download drawings',
    },
    description: {
      zh_CN: '图纸下载',
      zh_TW: '圖紙下載',
      en_US: 'Download drawings',
    },
  },
  operation: {
    type: OperationType.DRAWINGS_DOWNLOAD,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 批量指定交期
 */
export const DefaultBatchSetDateButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_BATCH_SET_DATE,
  styleMode: ButtonStyleMode.TEXT,
  title: '批量指定交期',
  description: '批量指定交期',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '批量指定交期',
      zh_TW: '批量指定交期',
      en_US: 'Batch designated delivery date',
    },
    description: {
      zh_CN: '批量指定交期',
      zh_TW: '批量指定交期',
      en_US: 'Batch designated delivery date',
    },
  },
  operation: {
    type: OperationType.BATCH_SET_DATE,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 自动更换
 */
export const DefaultOperateScriptButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_OPERATE_SCRIPT,
  styleMode: ButtonStyleMode.TEXT,
  title: '自动更换',
  description: '自动更换',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '自动更换',
      zh_TW: '自動更換',
      en_US: 'Automatic replacement',
    },
    description: {
      zh_CN: '自动更换',
      zh_TW: '自動更換',
      en_US: 'Automatic replacement',
    },
  },
  operation: {
    type: OperationType.OPERATE_SCRIPT,
    operate: 'script',
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 手动拆行
 */
export const DefaultSplitRowButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_SPLIT_ROW,
  styleMode: ButtonStyleMode.TEXT,
  title: '手动拆行',
  description: '手动拆行',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '手动拆行',
      zh_TW: '手動拆行',
      en_US: 'Manually split rows',
    },
    description: {
      zh_CN: '手动拆行',
      zh_TW: '手動拆行',
      en_US: 'Manually split rows',
    },
  },
  operation: {
    type: OperationType.SPLIT_ROW,
    targetDetailField: '',
    operate: 'script',
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 自动拆行
 */
export const DefaultAutoSplitRowButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_AUTO_SPLIT_ROW,
  styleMode: ButtonStyleMode.TEXT,
  title: '自动拆分',
  description: '自动拆分',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '自动拆分',
      zh_TW: '自動拆分',
      en_US: 'Automatic split',
    },
    description: {
      zh_CN: '自动拆分',
      zh_TW: '自動拆分',
      en_US: 'Automatic split',
    },
  },
  operation: {
    type: OperationType.AUTO_SPLIT_ROW,
    operate: 'script',
    attach: {
      comparisonField: '',
    },
    switchData: true,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 新增行
 */
export const DefaultAddItemButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_ADD_ITEM,
  styleMode: ButtonStyleMode.TEXT,
  title: '新增行',
  description: '新增行',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '新增行',
      zh_TW: '新增行',
      en_US: 'Add Line',
    },
    description: {
      zh_CN: '新增行',
      zh_TW: '新增行',
      en_US: 'Add Line',
    },
  },
  operation: {
    type: OperationType.ADD_ROW,
    switchData: true,
    operate: 'add-row',
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 批量删除
 */
export const DefaultBatchDeleteButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_BATCH_DELETE_ITEM,
  styleMode: ButtonStyleMode.TEXT,
  title: '批量删除',
  description: '批量删除',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '批量删除',
      zh_TW: '批量刪除',
      en_US: 'Batch delete',
    },
    description: {
      zh_CN: '批量删除',
      zh_TW: '批量刪除',
      en_US: 'Batch delete',
    },
  },
  operation: {
    type: OperationType.BATCH_DELETE,
    operate: 'BatchDelete',
    switchData: true,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 双档新增行
 */
export const DefaultOpenpageAddButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_OPENPAGE_ADD,
  styleMode: ButtonStyleMode.TEXT,
  title: '新增',
  description: '新增',
  attachMode: ButtonAttachMode.ALL,
  lang: {
    title: {
      zh_CN: '新增',
      zh_TW: '新增',
      en_US: 'Create',
    },
    description: {
      zh_CN: '新增',
      zh_TW: '新增',
      en_US: 'Create',
    },
  },
  operation: {
    type: OperationType.OPENPAGE,
    operate: 'openpage',
    switchData: true,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 删除行
 */
export const DefaultDeleteItemButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_DELETE_ITEM,
  styleMode: ButtonStyleMode.TEXT,
  title: '删除',
  description: '删除',
  attachMode: ButtonAttachMode.ROW,
  lang: {
    title: {
      zh_CN: '删除',
      zh_TW: '刪除',
      en_US: 'Delete',
    },
    description: {
      zh_CN: '删除',
      zh_TW: '刪除',
      en_US: 'Delete',
    },
  },
  operation: {
    type: OperationType.DELETE_ROW,
    operate: 'delete-row',
    operateTarget: 'row',
    switchData: true,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 复制行
 */
export const DefaultCopyItemButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_COPY_ITEM,
  styleMode: ButtonStyleMode.TEXT,
  title: '复制',
  description: '复制',
  attachMode: ButtonAttachMode.ROW,
  lang: {
    title: {
      zh_CN: '复制',
      zh_TW: '複製',
      en_US: 'Copy',
    },
    description: {
      zh_CN: '复制',
      zh_TW: '複製',
      en_US: 'Copy',
    },
  },
  operation: {
    type: OperationType.COPY,
    attach: {
      applyToField: '',
    },
    switchData: true,
    isCustomize: false,
    extendedFields: null,
  },
};

/**
 * operation: 双档维护
 */
export const DefaultOpenpageEditButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_OPENPAGE_EDIT,
  styleMode: ButtonStyleMode.TEXT,
  title: '维护',
  description: '维护',
  attachMode: ButtonAttachMode.ROW,
  lang: {
    title: {
      zh_CN: '维护',
      zh_TW: '維護',
      en_US: 'Maintain',
    },
    description: {
      zh_CN: '维护',
      zh_TW: '維護',
      en_US: 'Maintain',
    },
  },
  operation: {
    type: OperationType.EDIT,
    operate: 'edit',
    switchData: true,
    extendedFields: null,
  },
};

/**
 * operation: 双档复制
 */
export const DefaultOpenpageCopyButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_OPENPAGE_COPY,
  styleMode: ButtonStyleMode.TEXT,
  title: '复制',
  description: '复制',
  attachMode: ButtonAttachMode.ROW,
  lang: {
    title: {
      zh_CN: '复制',
      zh_TW: '複製',
      en_US: 'Copy',
    },
    description: {
      zh_CN: '复制',
      zh_TW: '複製',
      en_US: 'Copy',
    },
  },
  operation: {
    type: OperationType.OPENPAGE_COPY,
    operate: 'openpage_copy',
    switchData: true,
    extendedFields: null,
  },
};

/**
 * BUTTON_CLOSE
 * 关闭子页面
 */
export const DefaultCloseButtonSchema: ISubmitButtonInfo = {
  ...DefaultBaseSubmitButtonSchema,
  title: '取消',
  disabled: false,
  type: ButtonType.BUTTON_CLOSE,
  lang: {
    title: {
      zh_TW: '取消',
      zh_CN: '取消',
      en_US: 'Cancel',
    },
  },
};

/**
 * operations end
 */

/**
 * toolbar start
 */

/**
 * 整单操作新增、复制复用的operation的新增和复制
 * 整单操作：编辑
 */
export const DefaultToolbarEditButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_TOOLBAR_EDIT,
  attachMode: ButtonAttachMode.ALL,
  operation: {},
};

/**
 * 整单操作：上一笔
 */
export const DefaultToolbarPreviousButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_TOOLBAR_PREVIOUS,
  attachMode: ButtonAttachMode.ALL,
  operation: {},
};

/**
 * 整单操作：下一笔
 */
export const DefaultToolbarNextButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_TOOLBAR_NEXT,
  attachMode: ButtonAttachMode.ALL,
  operation: {},
};

/**
 * operation: 多模式导入
 */
export const DefaultImportButtonSchema: IBusinessButtonInfo = {
  ...DefaultBusinessButtonSchema,
  type: ButtonType.BUTTON_IMPORT,
  styleMode: ButtonStyleMode.TEXT,
  title: '多模式导入',
  lang: {
    title: {
      zh_CN: '多模式导入',
      zh_TW: '多模式匯入',
      en_US: 'Multi-mode import',
    },
  },
  attachMode: ButtonAttachMode.ALL,
  operation: {
    title: ButtonType.BUTTON_IMPORT,
    type: OperationType.UPLOAD_FILE,
    description: '',
    actions: [],
  },
};

/**
 * toolbar end
 */

/**
 * 按钮类型相应的初始化配置信息
 */
export const DefaultButtonInfoTypeMap: Map<ButtonType, IBusinessButtonInfo | ISubmitButtonInfo> =
  new Map([
    [ButtonType.BUTTON, DefaultBusinessButtonSchema],
    [ButtonType.BUTTON_PRINT, DefaultPrintTemplateButtonSchema],
    [ButtonType.BUTTON_SUB_PAGE, DefaultSubPageButtonSchema],
    // [ButtonType.BUTTON_SUB_PAGE_SAVE, DefaultSubPageSaveButtonSchema],
    [ButtonType.BUTTON_UPLOAD_FILE, DefaultUploadFileButtonSchema],
    [ButtonType.BUTTON_DOWNLOAD_TEMPLATE, DefaultDownloadTemplateButtonSchema],
    [ButtonType.BUTTON_DATA_COMBINE_SAVE, DefaultCombineSaveButtonSchema],
    [ButtonType.BUTTON_DATA_SAVE, DefaultSaveButtonSchema],
    [ButtonType.BUTTON_DATA_DELETE, DefaultDataDeleteButtonSchema],
    [ButtonType.BUTTON_DATA_INVALID, DefaultInvalidButtonSchema],
    [ButtonType.BUTTON_DATA_VALID, DefaultValidButtonSchema],
    [ButtonType.BUTTON_DATA_RESET_VALID, DefaultResetValidButtonSchema],
    [ButtonType.BUTTON_DATA_CANCEL_VALID, DefaultCancelValidButtonSchema],
    [ButtonType.BUTTON_DELETE_TO_RECYCLE, DefaultDeleteToRecycleButtonSchema],
    [ButtonType.BUTTON_DATA_RECOVER, DefaultRecoverButtonSchema],
    [ButtonType.BUTTON_RECYCLE_DELETE, DefaultDeleteCompleteButtonSchema],
    [ButtonType.BUTTON_WORKFLOW_ABORT, DefaultAbortButtonSchema],
    [ButtonType.BUTTON_WORKFLOW_INVOKE, DefaultInvokeButtonSchema],
    [ButtonType.BUTTON_AGREE, DefaultAgreeButtonSchema],
    [ButtonType.BUTTON_TASK_AGREE, DefaultTaskAgreeButtonSchema],
    [ButtonType.BUTTON_DISAGREE, DefaultDisAgreeButtonSchema],
    [ButtonType.BUTTON_ACT_RETURN, DefaultActReturnButtonSchema],
    [ButtonType.BUTTON_TASK_REEXECUTE, DefaultTaskReexecuteButtonSchema],
    [ButtonType.BUTTON_ACT_ADD, DefaultActAddButtonSchema],
    [ButtonType.BUTTON_DATA_UPDATE, DefaultUpdateButtonSchema],
    [ButtonType.BUTTON_WORKFLOW_SUBMIT, DefaulttSubmitButtonSchema],
    [ButtonType.BUTTON_DATA_TERMINATE, DefaultDataTerminateButtonSchema],
    [ButtonType.BUTTON_REPORT_QUERY, DefaultReportQueryButtonSchema],
    [ButtonType.BUTTON_TASK_TERMINATE, DefaultTaskTerminateButtonSchema],
    [ButtonType.BUTTON_TASK_REAPPROVE, DefaultTaskReapproveButtonSchema],
    [ButtonType.BUTTON_TASK_ADD, DefaultTaskAddButtonSchema],
    [ButtonType.BUTTON_CLOSE, DefaultCloseButtonSchema],
    /**
     * 2025-03-10 新增
     */
    /**
     * all operation
     */
    [ButtonType.BUTTON_FRONT_EXPORT, DefaultOperationFrontExportButtonSchema],
    [ButtonType.BUTTON_BACKEND_EXPORT, DefaultOperationBackendExportButtonSchema],
    [ButtonType.BUTTON_UPLOAD_FILE, DefaultUploadFileButtonSchema],
    [ButtonType.BUTTON_DOWNLOAD_TEMPLATE, DefaultDownloadTemplateButtonSchema],
    [ButtonType.BUTTON_DRAWINGS_DOWNLOAD, DefaultDrawingsDownloadButtonSchema],
    [ButtonType.BUTTON_BATCH_SET_DATE, DefaultBatchSetDateButtonSchema],
    [ButtonType.BUTTON_OPERATE_SCRIPT, DefaultOperateScriptButtonSchema],
    [ButtonType.BUTTON_SPLIT_ROW, DefaultSplitRowButtonSchema],
    [ButtonType.BUTTON_AUTO_SPLIT_ROW, DefaultAutoSplitRowButtonSchema],
    [ButtonType.BUTTON_ADD_ITEM, DefaultAddItemButtonSchema],
    [ButtonType.BUTTON_BATCH_DELETE_ITEM, DefaultBatchDeleteButtonSchema],
    [ButtonType.BUTTON_OPENPAGE_ADD, DefaultOpenpageAddButtonSchema],
    /**
     * row operation
     */
    [ButtonType.BUTTON_DELETE_ITEM, DefaultDeleteItemButtonSchema],
    [ButtonType.BUTTON_COPY_ITEM, DefaultCopyItemButtonSchema],
    [ButtonType.BUTTON_OPENPAGE_EDIT, DefaultOpenpageEditButtonSchema],
    [ButtonType.BUTTON_OPENPAGE_COPY, DefaultOpenpageCopyButtonSchema],
    /**
     * toolbar
     */
    [ButtonType.BUTTON_TOOLBAR_EDIT, DefaultToolbarEditButtonSchema],
    [ButtonType.BUTTON_TOOLBAR_PREVIOUS, DefaultToolbarPreviousButtonSchema],
    [ButtonType.BUTTON_TOOLBAR_NEXT, DefaultToolbarNextButtonSchema],
    /**
     * 2025-03-20 新增
     */
    [ButtonType.BUTTON_IMPORT, DefaultImportButtonSchema],
  ]);
