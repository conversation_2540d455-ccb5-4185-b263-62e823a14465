import React, { useEffect, useState } from 'react';
import i18n from 'i18next';
import { CommonModal } from '@components/CommonModal';
import { Form, Select, Row, Col, Input } from 'antd';
import { useTranslation } from 'react-i18next';
import Athena<PERSON>esigner<PERSON>oreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import { ButtonActionType, ButtonApiType, ButtonUIBotService } from '../../enum';
import {
  enumToArray,
  useActionSelector,
  useActionType,
  useApiPath,
  useInputServiceName,
  useSelectService,
} from '../../tools';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type/ath-loader-manage';
import Icon from '../../../../../../components/Icon';
import { getServiceName } from '../tools';

import '../../../../../../assets/mf-override-antd.less';
import './SubActionModal.scss';

import type { ISubActionModalProps } from './types';
import type {
  IButtonActionParam,
  IButtonSubActionInfo,
  ILangDetailInfo,
} from '../../buttonCoreTypes';
import type { TCallBackParams } from '@core_types/components/ActionModal/hooks/hooks';
import type { IActionModalData } from '@core_types/components/DataSource/types/dataSourceBasic';
import type { IFieldData } from '@/components/Hooks/types/hooks';

const FormItem = Form.Item;
const ApiTypeArray = enumToArray(ButtonApiType);
const ActionTypeArray = enumToArray(ButtonActionType);
const UIBotServiceArray = enumToArray(ButtonUIBotService);

function SubActionModal(props: ISubActionModalProps) {
  const { visible, title, info, onOk, onClose } = props;

  const [form] = Form.useForm();
  const { t } = useTranslation();
  const [actionVisible, setActionVisible] = useState<boolean>(false);
  const [editorVisible, setEditorVisible] = useState<boolean>(false);
  const [fieldDatas, setFieldDatas] = useState<IFieldData[]>([]);
  const [actionData, setActionData] = useState<IActionModalData>({
    useApp: 'false',
    appCode: '',
  });
  const [fieldValue, setFieldValue] = useState<IButtonSubActionInfo>();

  useEffect(() => {
    if (!visible) {
      form.resetFields();
    }
  }, [visible]);

  useEffect(() => {
    const dynamicInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
    setActionData({
      ...actionData,
      appCode: dynamicInfo?.applicationCode,
    });
    const fieldDatas = config.get(AthLowCodeConfigKey.AthFieldTree);
    setFieldDatas(fieldDatas ?? []);
  }, []);

  useEffect(() => {
    if (visible) {
      if (!info?.lang && info?.title) {
        info.lang = {
          title: {
            zh_CN: info.title,
            zh_TW: info.title,
            en_US: info.title,
          },
        };
      }
      form?.setFieldsValue(info);
      setFieldValue({
        ...info,
      });
    }
  }, [info, visible]);

  const onLangChange = async (info: ILangDetailInfo) => {
    form.setFieldValue(['lang', 'title'], info);
    setFieldValue({
      ...fieldValue,
      lang: {
        ...(fieldValue?.lang ?? {}),
        title: info,
      },
    });
    try {
      await form.validateFields([['lang', 'title']]);
    } catch (e) {}
  };

  const onReturnLangChange = async (info: ILangDetailInfo) => {
    form.setFieldValue(['lang', 'returnText'], info);
    setFieldValue({
      ...fieldValue,
      lang: {
        ...(fieldValue?.lang ?? {}),
        returnText: info,
      },
    });
    try {
      await form.validateFields([['lang', 'returnText']]);
    } catch (e) {}
  };

  const doActionChange = (info: TCallBackParams) => {
    form.setFieldsValue({
      actionId: info.actionId,
      serviceName: getServiceName(info.actionId!),
    });
    setFieldValue({
      ...fieldValue,
      actionId: info.actionId,
      serviceName: getServiceName(info.actionId!),
    });
    doHideAction();
  };

  const doSelectServiceChange = (value: string) => {
    form.setFieldsValue({
      actionId: value,
      serviceName: value,
    });
    setFieldValue({
      ...fieldValue,
      actionId: value,
      serviceName: value,
    });
  };

  const onActionParamsChange = (params: IButtonActionParam[] = []) => {
    form.setFieldValue('actionParams', params);
    setFieldValue({
      ...fieldValue,
      actionParams: params,
    });
  };

  const doOpenAction = () => {
    setActionData({
      ...actionData,
      actionId: fieldValue?.actionId,
    });
    setActionVisible(true);
  };

  const doHideAction = () => {
    setActionVisible(false);
  };

  const doSaveInfo = (valueStr: string) => {
    const info: IButtonSubActionInfo = JSON.parse(valueStr);
    setFieldValue({ ...info });
    form.setFieldsValue({ ...info });
    setEditorVisible(false);
  };

  const doOk = async () => {
    try {
      const info: IButtonSubActionInfo = await form.validateFields();
      info.title = info?.lang?.title?.[i18n.language as keyof ILangDetailInfo];
      info.returnText = info?.lang?.returnText?.[i18n.language as keyof ILangDetailInfo];
      onOk(info);
    } catch (error) {
      console.log(
        '🚀 [Generated Log]: path = src/plugins/plugin-ath-setter/components/Button/combineAttachActions/components/SubActionModal.tsx, scope = SubActionModal.doOk, error = ',
        error,
      );
    }
  };

  return (
    <CommonModal
      wrapperClassName="sub-action-modal"
      width={800}
      visible={visible}
      title={title}
      ok={doOk}
      close={onClose}
    >
      <Form<IButtonSubActionInfo>
        form={form}
        layout="horizontal"
        colon={false}
        labelCol={{ span: 10 }}
        wrapperCol={{ span: 14 }}
        onValuesChange={(changedValues) => {
          setFieldValue({
            ...fieldValue,
            ...changedValues,
          });
        }}
      >
        <Row gutter={24}>
          <Col span={12}>
            <FormItem
              name={['lang', 'title']}
              label={t('dj-标题')}
              rules={[
                {
                  required: true,
                  message: t('dj-请输入'),
                },
              ]}
            >
              <AthenaDesignerCoreMFComponent
                componentName="AppLangInput"
                componentProps={{
                  className: 'ath-common-lang',
                  size: 'small',
                  title: t('dj-多语言'),
                  onChange: onLangChange,
                  placeholder: t('dj-请输入'),
                  value: fieldValue?.lang?.title ?? {},
                }}
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem
              name="type"
              label={t('dj-类型')}
              rules={[
                {
                  required: true,
                  message: t('dj-请选择'),
                },
              ]}
            >
              <Select placeholder={t('dj-请输入')} options={ApiTypeArray} />
            </FormItem>
          </Col>
          {useActionType(fieldValue?.type) && (
            <Col span={12}>
              <FormItem name="actionType" label={t('dj-业务类型')}>
                <Select placeholder={t('dj-请选择')} options={ActionTypeArray} />
              </FormItem>
            </Col>
          )}
          {useActionSelector(fieldValue?.type) && (
            <Col span={12}>
              <FormItem
                name="actionId"
                label={t('dj-服务')}
                rules={[
                  {
                    required: true,
                    message: t('dj-请选择'),
                  },
                ]}
              >
                <Input
                  className="action-input"
                  placeholder={t('dj-请选择')}
                  size="small"
                  readOnly
                  suffix={
                    <Icon
                      type="iconkaichuang"
                      onClick={doOpenAction}
                      className="window-icon iconfont"
                    />
                  }
                />
              </FormItem>
            </Col>
          )}
          {useSelectService(fieldValue?.type) && (
            <Col span={12}>
              <FormItem
                name="actionId"
                label={t('dj-服务')}
                rules={[
                  {
                    required: true,
                    message: t('dj-请选择'),
                  },
                ]}
              >
                <Select
                  placeholder={t('dj-请输入')}
                  options={UIBotServiceArray}
                  onChange={doSelectServiceChange}
                />
              </FormItem>
            </Col>
          )}
          {useInputServiceName(fieldValue?.type) && (
            <Col span={12}>
              <FormItem name="serviceName" label={t('dj-服务名')}>
                <Input placeholder={t('dj-请输入')} />
              </FormItem>
            </Col>
          )}
          {useApiPath(fieldValue?.type) && (
            <Col span={12}>
              <FormItem name="url" label={t('dj-API路径')}>
                <Input placeholder={t('dj-请输入')} />
              </FormItem>
            </Col>
          )}
          <Col span={12}>
            <FormItem name={['lang', 'returnText']} label={t('dj-成功之后的签章文本')}>
              <AthenaDesignerCoreMFComponent
                componentName="AppLangInput"
                componentProps={{
                  className: 'ath-common-lang',
                  size: 'small',
                  title: t('dj-多语言'),
                  onChange: onReturnLangChange,
                  placeholder: t('dj-请输入'),
                  value: fieldValue?.lang?.returnText ?? {},
                }}
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem name="actionParams" label={t('dj-参数设置')}>
              <AthenaDesignerCoreMFComponent
                componentName="ActionParams"
                componentProps={{
                  fieldTree: fieldDatas,
                  hideLabel: true,
                  useFieldTree: true,
                  needQueryAction: fieldValue?.type === 'ESP',
                  actionId: fieldValue?.actionId,
                  onChange: onActionParamsChange,
                  value: fieldValue?.actionParams ?? [],
                }}
              />
            </FormItem>
          </Col>
          <Col span={12}>
            <FormItem label={t('dj-扩展')}>
              <div className="extend-icon">
                <Icon
                  type="icongaodaima"
                  className="iconfont"
                  onClick={() => setEditorVisible(true)}
                />
              </div>
            </FormItem>
          </Col>
        </Row>
        <AthenaDesignerCoreMFComponent
          componentName="ActionModal"
          componentProps={{
            visible: actionVisible,
            appCode: actionData?.appCode,
            transferData: {
              ...actionData,
              useApp: form.getFieldValue('type') === ButtonApiType.ESP ? 'false' : 'true',
            },
            labelType: form.getFieldValue('type') === ButtonApiType.ESP ? 'EspAction' : '',
            callBack: doActionChange,
            closeModal: doHideAction,
          }}
        />
        <AthenaDesignerCoreMFComponent
          componentName="MonacoEditor"
          componentProps={{
            title: t('dj-JSON设置'),
            showToolbar: false,
            visible: editorVisible,
            value: JSON.stringify(fieldValue ?? {}),
            onCancel: () => setEditorVisible(false),
            onOk: doSaveInfo,
          }}
        />
      </Form>
    </CommonModal>
  );
}

export { SubActionModal };
