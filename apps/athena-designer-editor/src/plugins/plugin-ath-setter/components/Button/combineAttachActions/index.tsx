import React, { useState, useRef, useEffect } from 'react';
import i18n from 'i18next';
import { useTranslation } from 'react-i18next';
import { Tree, Popconfirm } from 'antd';
import { FormOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { SubActionModal } from './components/SubActionModal';
import { formatSubData, rebuildTreeData, getAllTreeDataKeys } from './tools';

import type { TreeDataNode } from 'antd';
import type { ICombineAttachActionsProps } from './types';
import type { IButtonSubActionInfo, ILangDetailInfo } from '../buttonCoreTypes';

import './index.scss';

function CombineAttachActions(props: ICombineAttachActionsProps) {
  const { treeDatas, onChange } = props;

  const { t } = useTranslation();
  const [visible, setVisible] = useState<boolean>(false);
  const [modalData, setModalData] = useState<IButtonSubActionInfo>();
  const [treeData, setTreeData] = useState<TreeDataNode[]>([]);
  const [expandKeys, setExpandKeys] = useState<string[]>([]);
  const opInfoRef = useRef<{ key?: string; type: 'add' | 'edit' | 'delete' }>();
  const cacheTreeDataRef = useRef<TreeDataNode[]>([]);

  useEffect(() => {
    const formatDatas = formatSubData(treeDatas, titleElement);
    setTreeData(formatDatas);
  }, [treeDatas]);

  useEffect(() => {
    cacheTreeDataRef.current = treeData;
    const keys = getAllTreeDataKeys(treeData);
    setExpandKeys(keys);
  }, [treeData]);

  const titleElement = (info: any) => {
    const { key, data } = info;
    const title = data?.lang?.title?.[i18n.language as keyof ILangDetailInfo] ?? data?.title;
    return (
      <div className="tree-title-wrapper">
        <div className="title">{title}</div>
        <div className="operations">
          <FormOutlined onClick={() => doEdit(key, data!)} />
          <PlusOutlined onClick={() => doAdd(key)} />
          <Popconfirm
            className="confirm-delete"
            title={t('dj-确认删除？')}
            onConfirm={() => doDelete(key)}
            onCancel={() => {}}
            okText={t('dj-删除')}
            cancelText={t('dj-取消')}
          >
            <DeleteOutlined />
          </Popconfirm>
        </div>
      </div>
    );
  };

  const doAdd = (key?: string) => {
    opInfoRef.current = {
      key,
      type: 'add',
    };
    setModalData(undefined);
    setVisible(true);
  };
  const doEdit = (key: string, data?: IButtonSubActionInfo) => {
    opInfoRef.current = {
      key,
      type: 'edit',
    };
    setModalData(data);
    setVisible(true);
  };
  const doDelete = (key: string) => {
    const newActions = rebuildTreeData('delete', cacheTreeDataRef.current, key);
    onChange(newActions);
  };

  const doActionOk = (info: IButtonSubActionInfo) => {
    let newActions = rebuildTreeData(
      opInfoRef.current?.type!,
      cacheTreeDataRef.current,
      opInfoRef.current?.key,
      info,
    );
    if (opInfoRef.current?.type === 'add' && !opInfoRef.current.key) {
      newActions = [...newActions, info];
    }
    onChange(newActions);
    doActionClose();
  };
  const doActionClose = () => {
    setVisible(false);
  };

  return (
    <div className="combine-attach-actions-wrapper">
      <div className="setter-title">
        <span>{t('dj-操作配置')}</span>
        <span onClick={() => doAdd()}>+&nbsp;{t('dj-添加')}</span>
      </div>
      <div className="setter-content">
        <Tree expandedKeys={expandKeys} blockNode treeData={treeData} />
      </div>
      <SubActionModal
        title={modalData ? t('dj-编辑操作') : t('dj-新增操作')}
        info={modalData}
        visible={visible}
        onOk={doActionOk}
        onClose={doActionClose}
      />
    </div>
  );
}

export { CombineAttachActions };
