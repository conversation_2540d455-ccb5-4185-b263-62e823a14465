import React, { useEffect, useMemo, useState } from 'react';
import { IPublicModelNode, IPublicModelSettingField } from '@alilc/lowcode-types';
import './index.scss';
import { FormOutlined, DeleteOutlined } from '@ant-design/icons';
import { uniqueId } from 'lodash';
import { SettingField } from '@alilc/lowcode-engine';
import { config } from '@alilc/lowcode-engine';
import { AthLoaderManage } from '../../../plugin-ath-loader/AthLoaderManage';
import {
  MessageToMainType,
  AthLoaderManageContext,
  AthLowCodeEventName,
  AthLowCodeConfigKey,
} from '../../../plugin-ath-loader/type';
import { event } from '@alilc/lowcode-engine';
import i18n, { t } from 'i18next';
import { DslData } from '@/tools/business/lcdp-converter/type';
import { getDataSourceName } from '@/tools/utils/setter';

export interface Rule {
  key: string; // 规则的唯一值
  content: RuleContent; // 规则内容
  [propName: string]: any;
}

export interface RuleContent {
  key: string; // 规则类型
  [propName: string]: any;
}

export interface AthRulesSetterProps {
  field: IPublicModelSettingField;
  value: DslData;
}

const AthRulesSetter: React.FC<AthRulesSetterProps> = (props: AthRulesSetterProps) => {
  const { field, value } = props;
  const [configRules, setConfigRules] = useState<any[]>([]);

  useEffect(() => {
    const eventKey = `common:${AthLowCodeEventName.LowCodeRulesUpdate}`;
    const updateRules = (rules: Rule[]) => {
      setConfigRules([...rules]);
    };
    updateRules(config.get(AthLowCodeConfigKey.AthRules) ?? []);
    event.on(eventKey, updateRules);
    return () => {
      event.off(eventKey, updateRules);
    };
  }, []);

  const rules = useMemo(() => {
    const { path: nodePath, schema: nodeSchema } = value;
    return configRules.filter((rule: Rule) => {
      return rule?.content?.path === nodePath && rule?.content?.schema === nodeSchema;
    });
  }, [value, configRules]);

  const handleAdd = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    const targetElement = e.target as HTMLElement;
    const rect = targetElement.getBoundingClientRect();
    const ruleLeft = rect.left - 320 + rect.width + 10;
    const ruleTop = Math.min(rect.top, window.innerHeight - 470);
    const { path = '', schema = '' } = field.node?.getPropValue('dslInfo');
    event.emit(`${AthLowCodeEventName.AthRulesHandle}`, {
      type: MessageToMainType.Rules,
      data: {
        type: 'add',
        contextDataSourceName: getDataSourceName(field),
        data: {
          addSelectPosition: {
            left: ruleLeft,
            top: ruleTop,
          },
          addRuleBaseInfo: {
            content: {
              path,
              schema,
            },
          },
        },
      },
    });
  };

  const handleEdit = (rule: Rule) => {
    console.log('handleEdit:', rule);
    event.emit(`${AthLowCodeEventName.AthRulesHandle}`, {
      type: MessageToMainType.Rules,
      data: { type: 'edit', contextDataSourceName: getDataSourceName(field), data: rule },
    });
  };

  const handleDelete = (rule: Rule) => {
    console.log('handleDelete:', rule);
    event.emit(`${AthLowCodeEventName.AthRulesHandle}`, {
      type: MessageToMainType.Rules,
      data: { type: 'delete', data: rule },
    });
  };

  const getShowTitle = (rule: Rule) => {
    return (
      rule?.content?.lang?.name?.[i18n.language] ??
      rule.name ??
      rule?.content?.name ??
      rule.key ??
      ''
    );
  };

  return (
    <div className="ath-rules-setter">
      <div className="rule-title">
        <span>{t('dj-规则列表')}</span>
        <span onClick={handleAdd}>+&nbsp;{t('dj-添加')}</span>
      </div>
      <div className="rules-list">
        {rules.map((rule, index) => {
          return (
            <div className="rules-item">
              <span className="rules-item-title">{getShowTitle(rule)}</span>
              <span className="rules-item-button">
                <FormOutlined
                  onClick={() => {
                    handleEdit(rule);
                  }}
                />
                <DeleteOutlined
                  onClick={() => {
                    handleDelete(rule);
                  }}
                />
              </span>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default AthRulesSetter;
