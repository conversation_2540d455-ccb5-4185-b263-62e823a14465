import React, { use<PERSON>allback, useMemo } from 'react';
import i18n, { t } from 'i18next';
import { Modal, Select, Table } from 'antd';
import './index.scss';
import { ColumnType } from 'antd/es/table';
import { TableRowSelection } from 'antd/es/table/interface';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import { BaseOptionType } from 'antd/es/select';
import { omit } from 'lodash';
import { IPublicTypeSnippet } from '@alilc/lowcode-types';
import {
  BusinessButtonTypeSet,
  SubmitButtonTypeSet,
} from '@/plugins/plugin-ath-setter/components/Button/constant';
import { ButtonType } from '@/plugins/plugin-ath-setter/components/Button/enum';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { sortTypeExchange } from '@/tools/utils/common';

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

export interface TableEditModalProps {
  type: 'table' | 'form';
  dataSource: DataType[];
  selectedKeys: string[];
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  onSelectedKeysChange: (keys: string[]) => void;
  onComponentTypeChange: (comType: IPublicTypeSnippet, fieldName: string) => void;
  onSort: (preIndex: number, nextIndex: number) => void;
}

export interface DataType {
  [k: string]: any;
}

const TableEditModal: React.FC<TableEditModalProps> = (props) => {
  const {
    type,
    open,
    dataSource,
    selectedKeys,
    onSelectedKeysChange,
    onOk,
    onComponentTypeChange,
    onSort,
    onCancel,
  } = props;

  const getComponentType = useCallback((componentName: string, dataType: string) => {
    if (componentName === 'INPUT') return `${componentName}&${dataType}`;
    return componentName;
  }, []);

  const options = useMemo(() => {
    const map = config.get(AthLowCodeConfigKey.AthComponentGroupMap);
    const componentSortMap =
      // @ts-ignore
      window.AliLowCodeEngine?.material?.getAssets?.()?.sort?.componentSortMap;
    const next = sortTypeExchange(map, componentSortMap);
    const result: BaseOptionType[] = [];
    for (let [k, v] of next) {
      result.push({
        label: k,
        title: k,
        options: v.map((item: any) => ({
          ...item,
          label: item.title,
          value: getComponentType(item.schema.componentName, item.schema.props.dslInfo?.dataType),
        })),
      });
    }
    return result;
  }, [config.get(AthLowCodeConfigKey.AthComponentGroupMap)]);

  const getSelectOptions = useCallback((type: string) => {
    return options.map((option) => {
      return {
        ...option,
        options: option.options.filter((item: any) => {
          const currentIsSubmitButton =
            item.title === '提交按钮' && SubmitButtonTypeSet.has(type as ButtonType);
          const currentIsBusinessButton =
            item.title === '功能按钮' && BusinessButtonTypeSet.has(type as ButtonType);
          return !(currentIsSubmitButton || currentIsBusinessButton);
        }),
      };
    });
  }, options);

  const hasErrorColumns = useMemo(() => {
    return dataSource.some((e) => !e.dataType);
  }, [dataSource]);

  const expandable = useMemo(() => {
    return {
      childrenColumnName: 'xxxx',
    };
  }, []);

  const rowSelection: TableRowSelection<DataType> = useMemo(() => {
    return {
      getCheckboxProps(record) {
        if (type === 'form') return { disabled: false };
        if (record.dataName === 'manage_status') return { disabled: true };
        return { disabled: false };
      },
      selectedRowKeys: selectedKeys,
      onChange: (keys) => {
        onSelectedKeysChange(keys as string[]);
      },
    };
  }, [selectedKeys, onSelectedKeysChange, type]);

  const onSelectChanged = (value: string, option: BaseOptionType, data: DataType) => {
    let findTarget: BaseOptionType | undefined = undefined;
    for (let i = 0; i < options.length; i++) {
      const target = options[i].options.find((e: BaseOptionType) => e.value === value);
      if (target) {
        findTarget = target;
        break;
      }
    }
    if (findTarget) {
      onComponentTypeChange(omit(findTarget, 'label', 'value'), data.schema);
    }
  };

  const columns: ColumnType<DataType>[] = useMemo(() => {
    return [
      {
        title: t('dj-字段名'),
        dataIndex: 'dataName',
        render: (_, data) => {
          if (data.dataType) return data.dataName;
          return <div className="error-field"> {t('dj-未匹配到数据节点，无效的控件')}</div>;
        },
        onCell: (data) => {
          if (!data.dataType) return { colSpan: 3 };
          return {};
        },
      },
      {
        title: t('dj-字段说明1'),
        dataIndex: 'description',
        render: (_, data) => {
          return data.description;
        },
        onCell: (data) => {
          if (!data.dataType) return { colSpan: 0 };
          return {};
        },
      },
      {
        title: t('dj-数据类型'),
        dataIndex: 'dataType',
        render: (_, data) => {
          return data.dataType;
        },
        onCell: (data) => {
          if (!data.dataType) return { colSpan: 0 };
          return {};
        },
      },
      {
        title: t('dj-控件类型'),
        dataIndex: 'id',
        render: (_, data) => {
          const componentName =
            type === 'table' ? data.children?.[0]?.componentName : data.componentName;
          const dslInfo = type === 'table' ? data.children?.[0]?.props.dslInfo : data.props.dslInfo;
          return (
            <Select
              style={{ width: '90%' }}
              disabled={
                type === 'form' ? false : data.dataType === 'manage_status' ? true : data.isv
              }
              optionFilterProp="label"
              showSearch
              options={getSelectOptions(dslInfo?.type)}
              virtual={false}
              value={getComponentType(componentName, dslInfo?.dataType)}
              onChange={(value, option) => onSelectChanged(value, option as BaseOptionType, data)}
            ></Select>
          );
        },
      },
    ];
  }, [dataSource, onComponentTypeChange, type]);

  const getRowClass = (record: DataType): string => {
    if (record.dataType) {
      if (['object', 'array'].includes(record.dataType)) {
        return 'hide-row';
      }
      return '';
    }
    return 'error-row';
  };

  const Row: React.FC<Readonly<RowProps>> = (props) => {
    const { attributes, listeners, setNodeRef, transform, transition, isDragging } = useSortable({
      id: props['data-row-key'],
    });

    const style: React.CSSProperties = {
      ...props.style,
      transform: CSS.Translate.toString(transform),
      transition,
      cursor: 'move',
      ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
    };

    return <tr {...props} ref={setNodeRef} style={style} {...attributes} {...listeners} />;
  };

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 1,
      },
    }),
  );

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      console.log('da: ', dataSource, active.id, over?.id);
      const activeIndex = dataSource.findIndex((e) => e.rowKey === active.id);
      const overIndex = dataSource.findIndex((e) => e.rowKey === over?.id);
      console.log('da111: ', activeIndex, overIndex);
      onSort(activeIndex, overIndex);
    }
  };

  return (
    <Modal
      className="table-edit-modal"
      title={t(type === 'form' ? 'dj-快速编辑表单' : 'dj-快速编辑表格')}
      open={open}
      width={700}
      okText={t('dj-确定')}
      onOk={onOk}
      cancelText={t('dj-取消')}
      onCancel={onCancel}
      okButtonProps={{
        disabled: !selectedKeys.length,
      }}
    >
      <div className="table-tip">
        <span className="normal">{t('dj-选择数据节点，并设置控件')}</span>
        {hasErrorColumns && (
          <span className="error">{t('dj-标红的控件未匹配到数据节点，即无效控件')}</span>
        )}
      </div>
      <DndContext sensors={sensors} modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataSource.map((e) => e.rowKey)}
          strategy={verticalListSortingStrategy}
        >
          <Table<ColumnType<DataType>>
            components={{
              body: { row: Row },
            }}
            columns={columns}
            expandable={expandable}
            rowSelection={rowSelection}
            dataSource={dataSource}
            pagination={false}
            scroll={{ y: 400 }}
            rowClassName={getRowClass}
            rowKey="rowKey"
          ></Table>
        </SortableContext>
      </DndContext>
    </Modal>
  );
};

export default TableEditModal;
