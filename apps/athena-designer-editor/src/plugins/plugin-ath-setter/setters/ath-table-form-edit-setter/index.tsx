import React, { useEffect, useState } from 'react';
import {
  IPublicModelSettingField,
  IPublicTypeNodeData,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';
import theme from '@/config/theme.json';
import { t } from 'i18next';
import { common, config, event } from '@alilc/lowcode-engine';
import { ConfigProvider } from 'antd';
import TableEditModal, { DataType } from './table-edit-modal';
import './index.scss';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import { getDragTargetData, findNodeByKey } from '@/plugins/plugin-ath-field-panel/tools';
import { AthTreeDataNode } from '@/plugins/plugin-ath-field-panel/type';
import { cloneDeep, isEqual, merge, omit } from 'lodash';
import { arrayMove } from '@dnd-kit/sortable';
import { AthComponentType } from '@/tools/business/lcdp-converter/type';

export interface AthTableFormEditSetterProps {
  field: IPublicModelSettingField;
  onChange: (value: any) => void;
  type: 'table' | 'form';
}

const AthTableFormEditSetter: React.FC<AthTableFormEditSetterProps> = (props) => {
  const { type, field } = props;

  const [open, setOpen] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<DataType[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  useEffect(() => {
    const dispose = event.on(`common:setter:table-form-open`, () => {
      onEditClick();
    });
    return () => dispose();
  }, []);

  const onTableOk = () => {
    const currentDataSources = cloneDeep(field.node?.schema?.children || []) as any[];
    const currentDataSourceMap = currentDataSources.reduce((pre, curr) => {
      const children = getChildren(curr);
      const schema = children?.props?.dslInfo?.schema || curr.id;
      if (schema) pre[schema] = curr;
      return pre;
    }, {});
    const waitUpdate: DataType[] = [];
    const waitAdd: DataType[] = [];
    const waitDel = Object.keys(currentDataSourceMap)
      .filter((key) => !selectedKeys.includes(key))
      .map((key) => currentDataSourceMap[key]);
    const selectDataSource = dataSource.filter((item) => selectedKeys.includes(item.rowKey));
    const sort = selectDataSource.map((item) => item.rowKey);
    selectDataSource.forEach((item) => {
      const nextData = omit(item, 'isv', 'rowKey', 'schema', 'description', 'dataType', 'dataName');
      if (Reflect.has(currentDataSourceMap, item.rowKey)) {
        if (!isEqual(nextData, currentDataSourceMap[item.rowKey])) waitUpdate.push(nextData);
      } else {
        // 需要新增的
        if (type === 'table') {
          waitAdd.push(nextData.children?.[0]);
        } else {
          waitAdd.push(nextData);
        }
      }
    });
    props.onChange({
      waitUpdate,
      waitAdd,
      waitDel: waitDel?.filter(
        (item) => item?.componentName !== AthComponentType.DYNAMIC_OPERATION,
      ),
      sort,
    });
    onTableCancel();
  };

  const onTableCancel = () => {
    setOpen(false);
    setDataSource([]);
  };

  const onSelectedKeysChange = (keys: string[]) => {
    setSelectedKeys(keys);
  };

  const onComponentTypeChange = (com: IPublicTypeSnippet, fieldName: string) => {
    const newDataSource = dataSource.map((item) => {
      if (item.schema !== fieldName) return item;
      const comType = cloneDeep(com);
      const dslInfo = getChildren(item).props.dslInfo;
      const { lang = {} } = dslInfo || {};
      // 属性名数组
      const properties = [
        'id',
        'headerName',
        'path',
        'schema',
        'extraContent',
        'tooltipTitle',
        'placeholder',
        'iconType',
      ];
      let filteredProperties = properties;
      // 如果是ICON组件，则过滤掉替换iconType属性
      if (getChildren(item)?.componentName === 'ICON') {
        filteredProperties = properties.filter((property) => property !== 'iconType');
      }
      // 多语言属性名数组
      const propertiesLang = ['headerName', 'extraContent', 'tooltipTitle', 'placeholder'];
      // 生成新的属性值
      const newValue: any = filteredProperties.reduce((acc: any, prop) => {
        if (prop in dslInfo) acc[prop] = dslInfo[prop];
        return acc;
      }, {});
      // 生成新的多语言属性值
      const newLang = propertiesLang.reduce((acc: any, prop) => {
        if (prop in lang) acc[prop] = lang[prop];
        return acc;
      }, {});
      if (Object.keys(newLang).length > 0) newValue.lang = newLang;
      const nextdslInfo = merge(comType.schema?.props?.dslInfo, newValue);

      if (type === 'table') {
        return {
          ...item,
          children: [
            {
              ...getChildren(item),
              componentName: nextdslInfo.type,
              props: {
                dslInfo: nextdslInfo,
              },
            },
          ],
        };
      } else if (type === 'form') {
        return {
          ...item,
          ...comType.schema,
          props: {
            dslInfo: nextdslInfo,
          },
        };
      }
      return item;
    });
    setDataSource(newDataSource);
  };

  const onSortFields = (fromIndex: number, toIndex: number) => {
    setDataSource((prev) => {
      return arrayMove(prev, fromIndex, toIndex);
    });
  };

  // 获取数据源下的所有字段
  const getFullDataSource = () => {
    const defaultData: AthTreeDataNode[] = config.get(AthLowCodeConfigKey.AthFieldTree) ?? [];
    const nodeData = findNodeByKey(defaultData, defaultData?.[0]?.fullPath);
    const { getLocale } = common.utils.createIntl?.() || {};
    const locale: string = (getLocale?.() || 'zh-CN').replace(/-/g, '_');
    if (nodeData) {
      return (getDragTargetData(nodeData, locale)?.children || []) as IPublicTypeNodeData[];
    }
    return [];
  };

  const getChildren = (originData: any) => {
    if (type === 'form') return originData;
    return Array.isArray(originData.children) ? originData.children?.[0] : originData.children;
  };

  const onEditClick = () => {
    const defaultData: AthTreeDataNode[] = config.get(AthLowCodeConfigKey.AthFieldTree) ?? [];
    const fullDs = getFullDataSource();
    const currentDataSources = cloneDeep(field.node?.schema?.children || []) as any[];
    const currentDataSourceMap = currentDataSources.reduce((pre, curr) => {
      const children = getChildren(curr);
      const schema = children?.props?.dslInfo?.schema || curr.id;
      if (schema) pre[schema] = curr;
      return pre;
    }, {});
    fullDs.forEach((item) => {
      const dslInfo = getChildren(item)?.props?.dslInfo;
      const schema = dslInfo?.schema;
      if (!Reflect.has(currentDataSourceMap, schema)) {
        currentDataSources.push(item);
      }
    });
    const fullDataSource = currentDataSources
      .map((item) => {
        const dslInfo = getChildren(item)?.props?.dslInfo;
        const node = findNodeByKey(defaultData, `${dslInfo?.path}.${dslInfo?.schema}`);
        const schema = dslInfo?.schema;
        const dataName = node?.data_name;
        const dataType = node?.category === 'array' ? 'array' : node?.data_type;
        const description = node?.description?.[t('dj-LANG')];
        return Object.assign(item, {
          isv: !!dslInfo?.isvCustomType,
          rowKey: schema || item.id,
          schema,
          dataType,
          dataName,
          description,
        });
      })
      ?.filter((item) => item?.componentName !== AthComponentType.DYNAMIC_OPERATION);
    setDataSource(fullDataSource);
    setSelectedKeys(Object.keys(currentDataSourceMap));
    setOpen(true);
  };

  return (
    <ConfigProvider theme={theme}>
      <div className="ath-table-form-edit-setter">
        <span className="table-form-title">{t(type === 'table' ? 'dj-表格' : 'dj-表单')}</span>
        <span className="table-form-edit" onClick={onEditClick}>
          {t('dj-快速编辑')}
        </span>
      </div>
      <TableEditModal
        dataSource={dataSource}
        selectedKeys={selectedKeys}
        type={type}
        open={open}
        onOk={onTableOk}
        onCancel={onTableCancel}
        onSelectedKeysChange={onSelectedKeysChange}
        onComponentTypeChange={onComponentTypeChange}
        onSort={onSortFields}
      ></TableEditModal>
    </ConfigProvider>
  );
};

export default AthTableFormEditSetter;
