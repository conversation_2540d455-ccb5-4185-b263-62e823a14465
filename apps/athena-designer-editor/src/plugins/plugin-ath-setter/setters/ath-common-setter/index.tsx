import React, { Component, useEffect, useMemo } from 'react';
import { Button, Modal, Form, Input, Radio, InputNumber, Switch, Select, Space } from 'antd';
import './index.scss';
import { RadioGroup } from '../../components/RadioGroup';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import { t } from 'i18next';
import Icon from '@/components/Icon';

export interface AthCommonSetterProps {
  value: any;
  onChange: (value: any) => void;
  options: {
    titleProps: CommonSetterLayoutProps;
    formItemRules?: any; // form rules 透传，具体配置 查看 antd
    componentProps?: any; // 组件属性 透传，具体配置 查看 antd
    componentType?:
      | 'text'
      | 'number'
      | 'switch'
      | 'select'
      | 'lang'
      | 'iconSelect'
      | 'textarea'
      | 'radioGroup';
  };
}

const AthCommonSetter: React.FC<AthCommonSetterProps> = (props: AthCommonSetterProps) => {
  const { value, onChange, options } = props;
  const { formItemRules = [], componentProps = {}, componentType = 'text' } = options ?? {};
  const setterTitle = options?.titleProps?.setterTitle;

  if (componentProps.placeholder) {
    componentProps.placeholder = t(componentProps.placeholder);
  }

  const formRules = useMemo(() => {
    return (
      (formItemRules || []).map((item: any) => {
        item.message = t(item.message);
        return item;
      }) || []
    );
  }, [formItemRules]);

  // @ts-ignore
  const requiredObj = options?.formItemRules?.find((item) => item?.hasOwnProperty('required')) ?? {
    required: false,
  };
  if (requiredObj?.required && typeof setterTitle === 'string') {
    options.titleProps.setterTitle = (
      <div>
        <span style={{ color: '#eb0000', fontSize: '10px' }}>* </span>
        {t(setterTitle)}
      </div>
    );
  }
  const [form] = Form.useForm();
  useEffect(() => {
    form.setFieldsValue({ value });
  }, [value]);

  // 不用form 的 onValuesChange 是因为 outOfDate的问题，该问题可以继续 查明原因
  const onValueChange = async () => {
    try {
      onChange((await form.validateFields()).value);
    } catch (error) {
      console.log(error);
    }
  };

  // 处理名称变更
  const onLangChange = (value: DV.LangConfig) => {
    form.setFieldValue(['value'], value);
    onValueChange();
  };

  return (
    <CommonSetterLayout {...options.titleProps}>
      <Form
        className={`ath-common-form ${componentType === 'switch' ? 'ath-common-switch-form' : ''}`}
        layout={'horizontal'}
        form={form}
        size="middle"
      >
        <Form.Item name="value" rules={[...formRules]}>
          {componentType === 'text' && (
            <Input
              className="ath-common-input"
              placeholder="请输入"
              {...componentProps}
              onChange={onValueChange}
            />
          )}
          {componentType === 'textarea' && (
            <Input.TextArea
              className="ath-common-input"
              placeholder="请输入"
              {...componentProps}
              onChange={onValueChange}
            />
          )}
          {componentType === 'number' && (
            <InputNumber
              className="ath-common-input ath-common-number"
              {...componentProps}
              placeholder="请输入数字"
              onChange={onValueChange}
            />
          )}
          {componentType === 'switch' && (
            <Switch className="ath-common-switch" {...componentProps} onChange={onValueChange} />
          )}

          {componentType === 'select' && (
            <Select
              className={`ath-common-select ${
                componentProps.customClass ? componentProps.customClass : ''
              }`}
              {...componentProps}
              labelRender={(label: any) => <span>{t(label.label)}</span>}
              optionRender={(option: any) => <span>{t(option.data?.label)}</span>}
              onChange={onValueChange}
            />
          )}

          {componentType === 'iconSelect' && (
            <Select
              className="ath-common-select"
              onChange={onValueChange}
              {...componentProps}
              labelRender={(label: any) => <Icon type={`iconqipaotishi-${label.value}`} />}
              optionRender={(option: any) => <Icon type={`iconqipaotishi-${option.data?.value}`} />}
            />
          )}
          {componentType === 'lang' && (
            <AthenaDesignerCoreMFComponent
              componentName="AppLangInput"
              componentProps={{
                className: 'ath-common-lang',
                size: 'small',
                title: t((props.options.titleProps.setterTitle || 'dj-多语言') as string),
                onChange: onLangChange,
                placeholder: t('dj-请输入'),
                value: { ...value },
                ...componentProps,
              }}
            />
          )}
          {componentType === 'radioGroup' && (
            <RadioGroup onChange={onValueChange} {...componentProps} />
          )}
        </Form.Item>
      </Form>
    </CommonSetterLayout>
  );
};

export default AthCommonSetter;
