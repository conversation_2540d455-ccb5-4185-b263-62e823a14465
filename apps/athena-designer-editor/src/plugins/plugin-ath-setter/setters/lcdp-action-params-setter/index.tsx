import React, { useState, useEffect } from 'react';
import CommonSetterLayout from '../../components/common-setter-layout';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type/ath-loader-manage';

import type { IActionParamsSetterProps } from './types';
import type { IFieldData } from '@/components/Hooks/types/hooks';
import type { IButtonActionParam } from '../../components/Button/buttonCoreTypes';

function LcdpActionParamsSetter(props: IActionParamsSetterProps) {

  const { value, options, onChange } = props;

  const { actionParams, actionId, type } = value ?? {};

  const [fieldDatas, setFieldDatas] = useState<IFieldData[]>([]);

  useEffect(() => {
    const fieldDatas = config.get(AthLowCodeConfigKey.AthFieldTree);
    setFieldDatas(fieldDatas ?? []);
  }, []);

  const onActionParamsChange = (params: IButtonActionParam[] = []) => {
    onChange(params);
  }

  return (

    <CommonSetterLayout {...(options?.titleProps ?? {})}>
      <div className='action-params-setter-wrapper'>
        <AthenaDesignerCoreMFComponent
          componentName="ActionParams"
          componentProps={{
            fieldTree: fieldDatas,
            hideLabel: true,
            useFieldTree: true,
            needQueryAction: type === 'ESP',
            actionId: actionId,
            onChange: onActionParamsChange,
            value: actionParams ?? [],
          }}
        />
      </div>
    </CommonSetterLayout>
  )
}

LcdpActionParamsSetter.displayName = 'LcdpActionParamsSetter';
export default LcdpActionParamsSetter;
