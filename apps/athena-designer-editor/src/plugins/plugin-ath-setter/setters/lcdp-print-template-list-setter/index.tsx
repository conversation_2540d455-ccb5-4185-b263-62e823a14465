import React, { Fragment, useRef, useState, useEffect } from 'react';
import CommonSetterLayout from '../../components/common-setter-layout';
import { <PERSON><PERSON>, Popconfirm } from 'antd';
import { useTranslation } from 'react-i18next';
import { DraggableList } from '../../components/DraggableList';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type/ath-loader-manage';
import { DeleteOutlined } from '@ant-design/icons';

import type { ILcdpPrintTemplateListSetterProps, IListsInfoItem } from './types';
import type { IDraggableConfig } from '../../components/DraggableList/types';
import type { IReportListItem } from '@core_types/components/PrintTemplateModal/types';
import type {
  IButtonExtendedFieldsDetailInfo,
  IButtonActionParam,
} from '../../components/Button/buttonCoreTypes';
import type { IFieldData } from '@/components/Hooks/types/hooks';
import i18next from 'i18next';

function LcdpPrintTemplateListSetter(props: ILcdpPrintTemplateListSetterProps) {
  const { options, value, onChange } = props;
  const { showTitle = true } = options ?? {};

  const { t } = useTranslation();
  const printTemplateModalRef = useRef<{
    queryActivityBasicInfo: (codes: string) => Promise<any[]>;
  }>();
  const [visible, setVisible] = useState<boolean>(false);
  const [appCode, setAppCode] = useState<string>('');
  const [listData, setListData] = useState<IListsInfoItem[]>([]);
  const [fieldDatas, setFieldDatas] = useState<IFieldData[]>([]);
  const [fieldDataMap, setFieldDataMap] = useState<Map<string, IFieldData>>(new Map());

  useEffect(() => {
    const dynamicInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
    setAppCode(dynamicInfo?.applicationCode);
    const fieldDatas = config.get(AthLowCodeConfigKey.AthFieldTree);
    setFieldDatas(fieldDatas ?? []);
    const fieldDataMap = config.get(AthLowCodeConfigKey.AthFieldTreeMap);
    setFieldDataMap(fieldDataMap);
  }, []);

  useEffect(() => {
    createListData();
  }, [value?.templates, appCode]);

  const createListData = async () => {
    if (value?.templates && appCode && printTemplateModalRef.current) {
      const codes = (value?.templates ?? []).map((template) => template.templateId).join(',');
      const reportList = await printTemplateModalRef.current?.queryActivityBasicInfo(codes);
      const reportListMap = new Map(
        reportList.map((report) => [
          report.code,
          {
            name: report?.lang?.name?.[i18next.language] || report.name,
            actionId: report.actionId,
          },
        ]),
      );
      const listData: IListsInfoItem[] = [];
      value.templates.forEach((template) => {
        const name = reportListMap.has(template.templateId!)
          ? reportListMap.get(template.templateId!)?.name!
          : template.templateId!;
        listData.push({
          ...template,
          name,
        });
      });
      setListData(listData);
    }
  };

  const doMounted = () => {
    if (listData?.length !== value?.templates?.length) {
      createListData();
    }
  };

  const doDelete = (dIndex: number) => {
    const newInfo: IButtonExtendedFieldsDetailInfo = {
      ...(value ?? {}),
      templates: value?.templates?.filter((_, index) => dIndex !== index),
    };
    onChange(newInfo);
  };

  const onActionParamsChange = (params: IButtonActionParam[], index: number) => {
    const newInfo: IButtonExtendedFieldsDetailInfo = {
      ...(value ?? {}),
      templates: value?.templates?.map((template, cIndex) => {
        if (index === cIndex) {
          return {
            ...template,
            actionParams: params,
          };
        } else {
          return template;
        }
      }),
    };
    onChange(newInfo);
  };

  const doAdd = () => {
    setVisible(true);
  };

  const renderAddButton = () => {
    const { setterTitle } = options?.titleProps ?? {};
    return (
      <Fragment>
        {showTitle && <span>{t(setterTitle)}</span>}
        <span>
          <Button type="link" onClick={doAdd}>
            +&nbsp;{`${t('dj-添加模板')}`}
          </Button>
        </span>
      </Fragment>
    );
  };

  const draggableConfig: IDraggableConfig<IListsInfoItem> = {
    label: (data) => data.name,
    operations: [
      {
        custom: (data, index) => {
          return (
            <AthenaDesignerCoreMFComponent
              componentName="ActionParams"
              componentProps={{
                fieldTree: fieldDatas,
                fieldDataMap,
                hideLabel: true,
                useFieldTree: true,
                useSettingIcon: true,
                actionId: data?.actionId,
                onChange: (params: IButtonActionParam[] = []) =>
                  onActionParamsChange(params, index),
                value: data?.actionParams ?? [],
              }}
            />
          );
        },
      },
      {
        custom: (data, index) => {
          return (
            <Popconfirm
              className="confirm-delete"
              title={t('dj-确认删除吗？')}
              onConfirm={() => {
                doDelete(index);
              }}
              onCancel={() => {}}
              okText={t('dj-删除')}
              cancelText={t('dj-取消')}
            >
              <DeleteOutlined />
            </Popconfirm>
          );
        },
      },
    ],
  };

  const doMove = (datas: IListsInfoItem[]) => {
    onChange({
      ...value,
      templates: datas,
    });
  };

  const doTemplateChange = (data: IButtonExtendedFieldsDetailInfo) => {
    onChange(data);
    doCancel();
  };

  const doCancel = () => {
    setVisible(false);
  };

  return (
    <CommonSetterLayout {...{ ...(options?.titleProps ?? {}), setterTitle: renderAddButton() }}>
      <div className="lcdp-print-template-list-setter">
        <DraggableList<IListsInfoItem> datas={listData} onMove={doMove} config={draggableConfig} />
      </div>
      <AthenaDesignerCoreMFComponent
        componentName="PrintTemplateModal"
        componentProps={{
          ref: printTemplateModalRef,
          visible: visible,
          appCode: appCode,
          schema: fieldDatas?.[0]?.data_name,
          value: value,
          mounted: doMounted,
          onChange: doTemplateChange,
          onCancel: doCancel,
        }}
      />
    </CommonSetterLayout>
  );
}

LcdpPrintTemplateListSetter.displayName = 'LcdpPrintTemplateListSetter';
export default LcdpPrintTemplateListSetter;
