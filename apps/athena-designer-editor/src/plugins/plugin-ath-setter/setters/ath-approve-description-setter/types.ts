import type { ILangInfo } from '@core_types/components/DataSource/types/dataSource';
import type { IAthCommmonSetterProps } from '../../types';

export interface IApproveDescriptionItemData {
  headerName: string;
  schema: string;
  path: string;
  important: boolean;
  hidden: boolean;
  type: string;
  label: string;
  lang: ILangInfo;
}

export type IApproveDescriptionSetterProps = IAthCommmonSetterProps<IApproveDescriptionItemData[]>;

export interface IOperationModalProps {
  visible: boolean;
  onChange: (data: IApproveDescriptionItemData) => void;
  onCancel: () => void;
  data: IApproveDescriptionItemData|null;
}
