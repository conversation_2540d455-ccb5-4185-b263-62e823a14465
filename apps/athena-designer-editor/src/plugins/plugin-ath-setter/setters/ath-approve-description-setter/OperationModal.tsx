import React, { useEffect, useState } from 'react';
import { Modal, Form, Input, Checkbox } from 'antd';
import { useTranslation } from 'react-i18next';
import Icon from '../../../../components/Icon';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';

import './OperationModal.scss';

import type { IOperationModalProps, IApproveDescriptionItemData } from './types';
import type { ILangDetailInfo } from '@core_types/components/DataSource/types/dataSource';
import type { AthTreeDataNode } from '@/plugins/plugin-ath-field-panel/type';

const FormItem = Form.Item;

function OperationModal(props: IOperationModalProps) {
  const { visible, data, onChange, onCancel } = props;

  const { t } = useTranslation();
  const [form] = Form.useForm<IApproveDescriptionItemData>();
  const [lang, setLang] = useState<ILangDetailInfo|undefined>(data?.lang?.label);
  const [fieldVisible, setVisible] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<AthTreeDataNode[] | null>(null);
  useEffect(() => {
    setTreeData(config.get(AthLowCodeConfigKey.AthFieldTree) ?? []);
  }, []);

  useEffect(() => {
    if (visible && data) {
      form.setFieldsValue({
        ...data,
      });
      setLang(data?.lang?.label);
    } else if (!visible) {
      form.resetFields();
      setLang(undefined);
    }
  }, [visible, data]);

  const doOk = async () => {
    try {
      const value = await form.validateFields();
      const title = lang?.zh_CN ?? '';
      value.label = title;
      value.headerName = title;
      value.lang.label = lang!;
      value.lang.headerName = lang!;
      onChange(value);
    } catch(error) {
      console.log(error)
    }
  };

  const doLangChange = (info: ILangDetailInfo) => {
    form.setFieldValue(['lang', 'label'], info);
    setLang(info);
  }

  const doHideModal = () => {
    setVisible(false);
  }

  const doOpenModal = () => {
    setVisible(true);
  }

  const doFieldCancel = () => {
    doHideModal();
  }

  const doFieldOk = (nodes: AthTreeDataNode[]) => {
    const { data_name, path } = nodes[0] ?? {};
    form.setFieldsValue({
      schema: data_name,
      path,
    });
    doHideModal();
  }

  return (
    <Modal
      destroyOnClose
      classNames={{
        body: 'operation-modal-body'
      }}
      title={data ? t('dj-修改选项') : t('dj-增加选项')}
      open={visible}
      width={440}
      okText={t('dj-确定')}
      onOk={doOk}
      cancelText={t('dj-取消')}
      onCancel={onCancel}
    >
      <Form<IApproveDescriptionItemData> form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <FormItem
          name={['lang', 'label']}
          rules={[
            {
              required: true,
              validator(_, value: ILangDetailInfo, callback) {
                if (!value?.['zh_CN'] || !value?.['zh_TW']) {
                  callback(`${t('dj-请输入')}`);
                } else {
                  callback();
                }
              },
            },
          ]}
          label={t('dj-标题')}
          colon={false}
        >
          <AthenaDesignerCoreMFComponent
            componentName="AppLangInput"
            componentProps={{
              className: 'ath-lang-input',
              size: 'small',
              title: t('dj-标题'),
              onChange: doLangChange,
              value: lang,
              placeholder: t('dj-请输入'),
            }}
          />
        </FormItem>
        <FormItem
          name='schema'
          rules={[{ required: true, message: t('dj-请输入') }]}
          label='schema'
          colon={false}
        >
          <Input
            className='action-input'
            placeholder={t('dj-请选择')}
            size='small'
            readOnly
            suffix={
              (
                <Icon
                  type="iconkaichuang"
                  onClick={doOpenModal}
                  className='window-icon iconfont'
                />
              )
            }
          />
        </FormItem>
        <FormItem
          name='path'
          rules={[{ required: true, message: t('dj-请输入') }]}
          label='path'
          colon={false}
        >
          <Input placeholder={t('dj-请输入')} />
        </FormItem>
        <FormItem
          name='important'
          wrapperCol={{ offset: 6 }}
          label={null}
          valuePropName='checked'
          colon={false}
        >
          <Checkbox>{t('dj-重要')}</Checkbox>
        </FormItem>
        <FormItem
          name='hidden'
          wrapperCol={{ offset: 6 }}
          label={null}
          valuePropName='checked'
          colon={false}
        >
          <Checkbox>{t('dj-隐藏')}</Checkbox>
        </FormItem>
      </Form>
      <AthenaDesignerCoreMFComponent
        componentName="SelectFieldModal"
        componentProps={{
          isOpen: fieldVisible,
          treeData: treeData,
          title: t('dj-选择字段'),
          onCancel: doFieldCancel,
          onSubmit: doFieldOk,
        }}
      />
    </Modal>
  );
}

export { OperationModal };
