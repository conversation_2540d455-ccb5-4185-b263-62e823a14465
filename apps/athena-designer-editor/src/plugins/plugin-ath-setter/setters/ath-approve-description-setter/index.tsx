import React, { useState } from 'react';
import { Popconfirm } from 'antd';
import { DraggableList } from '../../components/DraggableList';
import { OperationModal } from './OperationModal';
import { useTranslation } from 'react-i18next';
import { DeleteOutlined, FormOutlined } from '@ant-design/icons';

import './index.scss';

import type { IApproveDescriptionSetterProps, IApproveDescriptionItemData } from './types';
import type { IDraggableConfig } from '../../components/DraggableList/types';

function AthApproveDescriptionSetter(props: IApproveDescriptionSetterProps) {
  const { value, onChange } = props;

  const { t } = useTranslation();
  const [opData, setOpData] = useState<{
    visible: boolean;
    data: IApproveDescriptionItemData | null;
    index: number | null;
  }>({ visible: false, data: null, index: null });

  const doMove = (datas: IApproveDescriptionItemData[]) => {
    onChange(datas);
  };

  const doEdit = (data: IApproveDescriptionItemData, index: number) => {
    setOpData({
      visible: true,
      data,
      index,
    });
  };

  const doDelete = (dIndex: number) => {
    onChange(value?.filter((_, index) => dIndex !== index));
  };

  const config: IDraggableConfig<IApproveDescriptionItemData> = {
    label: (data) => data.label,
    operations: [
      {
        custom: (data, index) => {
          return (
            <FormOutlined
              onClick={() => {
                doEdit(data, index);
              }}
            />
          );
        },
      },
      {
        custom: (data, index) => {
          return (
            <Popconfirm
              className="confirm-delete"
              title={t('dj-确认删除吗？')}
              onConfirm={() => {
                doDelete(index);
              }}
              onCancel={() => {}}
              okText={t('dj-删除')}
              cancelText={t('dj-取消')}
            >
              <DeleteOutlined />
            </Popconfirm>
          );
        },
      },
    ],
  };

  const doAdd = () => {
    setOpData({
      visible: true,
      data: null,
      index: null,
    });
  };

  const doCancel = () => {
    setOpData({
      visible: false,
      data: null,
      index: null,
    });
  };

  const doChange = (newData: IApproveDescriptionItemData) => {
    if (Number.isInteger(opData.index)) {
      onChange(
        value?.map((data, index) => {
          if (index === opData.index) return newData;
          return data;
        }),
      );
    } else {
      onChange([...value, newData]);
    }
    doCancel();
  };

  return (
    <div className="draggable-list-setter-wrapper">
      <div className="setter-title">
        <span>{t('dj-配置选项')}</span>
        <span onClick={doAdd}>+&nbsp;{t('dj-添加')}</span>
      </div>
      <div className="setter-content">
        <DraggableList<IApproveDescriptionItemData> datas={value} onMove={doMove} config={config} />
      </div>
      <OperationModal
        visible={opData.visible}
        data={opData.data}
        onChange={doChange}
        onCancel={doCancel}
      />
    </div>
  );
}

AthApproveDescriptionSetter.displayName = 'AthApproveDescriptionSetter';
export default AthApproveDescriptionSetter;
