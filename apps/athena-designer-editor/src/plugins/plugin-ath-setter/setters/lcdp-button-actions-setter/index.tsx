import React from 'react';
import { CombineAttachActions } from '../../components/Button/combineAttachActions';

import type { ILcdpButtonActionsSetterProps } from './types';
import type { IButtonSubActionInfo } from '../../components/Button/buttonCoreTypes';

function LcdpButtonActionsSetter(props: ILcdpButtonActionsSetterProps) {

  const { value, onChange } = props;

  const doChange = (datas?: IButtonSubActionInfo[]) => {
    onChange(datas ?? []);
  }

  return (
    <div className='lcdp-button-actions-setter-wrapper' style={{ width: '100%' }}>
      <CombineAttachActions treeDatas={value?.actions} onChange={doChange} />
    </div>
  )
}

LcdpButtonActionsSetter.displayName = 'LcdpButtonActionsSetter';
export default LcdpButtonActionsSetter;
