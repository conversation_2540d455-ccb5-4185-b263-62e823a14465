import React, { useEffect, useState } from 'react';
import { Select } from 'antd';
import { useTranslation } from 'react-i18next';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type/ath-loader-manage';
import { createSelectOptions } from './tools';

import './index.scss';

import type { IAthSchemasSelectSetterProps, ISelectItem } from './types';
import type { IFieldTreeNodeInfo } from '@core_types/components/DynamicWorkDesign/config/type';

/**
 * path.schema = fullPath
 */
function AthSchemasSelectSetter(props: IAthSchemasSelectSetterProps) {

  const { value, onChange } = props;

  const [options, setOptions] = useState<ISelectItem[]>([]);


  useEffect(() => {
    const filedTree: IFieldTreeNodeInfo[] = config.get(AthLowCodeConfigKey.AthFieldTree);
    setOptions(createSelectOptions(filedTree));
  }, []);

  const { t } = useTranslation();

  const doChange = (schemas: string[]) => {
    onChange(schemas);
  }

  return (
    <div className='ath-schemas-select-setter-wrapper'>
      <div className="setter-title">schemas</div>
      <div className="setter-content">
        <Select
          value={value}
          mode='multiple'
          size='small'
          dropdownClassName="ath-schemas-select-setter-dropdown"
          placeholder={t('dj-请选择')}
          options={options}
          onChange={doChange}
        />
      </div>
    </div>
  )
}

AthSchemasSelectSetter.displayName = 'AthSchemasSelectSetter';
export default AthSchemasSelectSetter;
