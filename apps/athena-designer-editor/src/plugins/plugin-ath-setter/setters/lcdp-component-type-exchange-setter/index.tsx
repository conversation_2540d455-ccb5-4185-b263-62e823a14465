import React, { useCallback, useEffect, useMemo, useState } from 'react';
import './index.scss';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import { config, material } from '@alilc/lowcode-engine';
import Icon from '@/components/Icon';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import { Dropdown, DropdownProps, Input, Modal, Tooltip } from 'antd';
import { ExclamationCircleFilled, QuestionCircleOutlined, SearchOutlined } from '@ant-design/icons';
import { t } from 'i18next';
import { IPublicTypeSnippet } from '@alilc/lowcode-types';
import { debounce, merge } from 'lodash';
import {
  BusinessButtonTypeSet,
  ButtonTypeTextMap,
  SubmitButtonTypeSet,
} from '../../components/Button/constant';
import { multiSnippetsComponentDetail, multiSnippetsComponentType } from './utils';
import { sortTypeExchange } from '@/tools/utils/common';
import { AthComponentType } from '@/tools/business/lcdp-converter/type';
const { confirm } = Modal;

export interface LcdpComponentTypeExchangeSetterProps {
  value: any;
  onChange: (value: any) => void;
  options: {
    titleProps: CommonSetterLayoutProps;
  };
}

const LcdpComponentTypeExchangeSetter: React.FC<LcdpComponentTypeExchangeSetterProps> = (
  props: LcdpComponentTypeExchangeSetterProps,
) => {
  const { value, onChange, options } = props;
  const [open, setOpen] = useState(false);
  const [dslInfo, setDslInfo] = useState<any>();
  const [currentComType, setCurrentComType] = useState<string>();
  const [filteredData, setFilteredData] = useState<Map<string, IPublicTypeSnippet[]>>();
  const [hovered, setHovered] = useState(false); // 鼠标悬停状态

  useEffect(() => {
    handleSearch('');
  }, [config.get(AthLowCodeConfigKey.AthComponentGroupMap)]);

  useEffect(() => {
    setDslInfo(value);
  }, [value]);

  /**
   * 展开
   * @param nextOpen 是否展开
   * @param info
   */
  const handleOpenChange: DropdownProps['onOpenChange'] = (nextOpen, info) => {
    if (info.source === 'trigger' || nextOpen) {
      setOpen(nextOpen);
      handleSearch('');
    }
  };

  /**
   * 获取到当前组件的类型
   */
  const currentComTitle = useMemo(() => {
    if (ButtonTypeTextMap.get(value?.type)) {
      return ButtonTypeTextMap.get(value?.type);
    }

    const componentType =
      value.type && material.componentsMap[value.type] ? value.type : AthComponentType.COMMON;

    const { componentName = '', title } =
      material.getComponentMeta(componentType)?.getMetadata() || {};

    if (multiSnippetsComponentType?.includes(componentName)) {
      const component: any = multiSnippetsComponentDetail[componentName];
      const fieldType = value[component?.field];
      return component.options[fieldType];
    }
    const locale = config?.get('locale') || 'zh-CN';

    return title?.[locale];
  }, [material, value, ButtonTypeTextMap]);

  /**
   * 确认替换
   * @param comType
   */
  const onChangeComponent = (comType: IPublicTypeSnippet) => {
    console.log(comType, 'comType');
    setCurrentComType(comType?.schema?.componentName);
    // 标题、path、schema、辅助提示、注释说明、占位提示、注释图标属性
    const { lang = {} } = dslInfo || {};
    // 属性名数组
    const properties = [
      'headerName',
      'path',
      'schema',
      'extraContent',
      'tooltipTitle',
      'placeholder',
      'iconType',
    ];
    let filteredProperties = properties;

    // 如果是ICON组件，则过滤掉替换iconType属性
    if (comType?.schema?.componentName === 'ICON') {
      filteredProperties = properties.filter((property) => property !== 'iconType');
    }
    // 多语言属性名数组
    const propertiesLang = ['headerName', 'extraContent', 'tooltipTitle', 'placeholder'];

    // 生成新的属性值
    const newValue: any = filteredProperties.reduce((acc: any, prop) => {
      if (prop in dslInfo) acc[prop] = dslInfo[prop];
      return acc;
    }, {});

    // 生成新的多语言属性值
    const newLang = propertiesLang.reduce((acc: any, prop) => {
      if (prop in lang) acc[prop] = lang[prop];
      return acc;
    }, {});
    if (Object.keys(newLang).length > 0) newValue.lang = newLang;

    const mergeData = merge(comType?.schema?.props?.dslInfo, newValue);

    const titleValue: any = {};
    if (mergeData.type === 'INPUT') {
      // 文本输入和数字输入公用同一个组件类型
      titleValue.title = mergeData.dataType === 'numeric' ? t('dj-数字输入') : t('dj-文本输入');
    }
    if (mergeData.type === 'ATH_TAG') {
      // 标签公用同一个组件类型
      const titleEum: any = {
        face: t('dj-面性标签'),
        line: t('dj-线性标签'),
        color: t('dj-深色标签'),
        stamp: t('dj-戳形标签'),
      };
      titleValue.title = titleEum[mergeData.tagType];
    }

    onChange({
      dslInfo: { ...(mergeData || {}) },
      titleValue,
    });
  };

  /**
   * 确认删除
   */
  const confirmChange = useCallback(
    (comType: IPublicTypeSnippet) => {
      confirm({
        title: t('dj-确认有损转化？'),
        icon: <ExclamationCircleFilled />,
        content: t(
          'dj-转换后的组件将会丢失部分配置以及可能会遇到数据类型不匹配的问题，确定要转换吗？',
        ),
        okText: t('dj-确定'),
        cancelText: t('dj-取消'),
        onOk() {
          onChangeComponent(comType);
        },
        onCancel() {
          console.log('Cancel');
        },
      });
    },
    [dslInfo],
  );

  /**
   * 执行搜索
   */
  const handleSearch = useCallback(
    debounce((query: string) => {
      const data = config.get(AthLowCodeConfigKey.AthComponentGroupMap);
      const componentSortMap =
        // @ts-ignore
        window.AliLowCodeEngine?.material?.getAssets?.()?.sort?.componentSortMap;
      const next = sortTypeExchange(data, componentSortMap);
      const newFilteredData = new Map<string, IPublicTypeSnippet[]>();
      next.forEach((items: IPublicTypeSnippet[], key: string) => {
        const filteredItems = items.filter(
          (item: any) =>
            item?.schema?.componentName.toLowerCase().includes(query) ||
            item.title.toLowerCase().includes(query),
        );
        if (filteredItems.length > 0) {
          newFilteredData.set(key, filteredItems);
        }
      });

      setFilteredData(newFilteredData);
    }, 500),
    [config.get(AthLowCodeConfigKey.AthComponentGroupMap)],
  );

  /**
   * 过滤掉当前组件
   * @param item
   * @returns
   */
  const filterData = (item: IPublicTypeSnippet) => {
    const currentCom =
      item?.schema?.componentName === value?.type && item.title === t(currentComTitle as string);
    const currentIsSubmitButton = item.title === '提交按钮' && SubmitButtonTypeSet.has(value?.type);
    const currentIsBusinessButton =
      item.title === '功能按钮' && BusinessButtonTypeSet.has(value?.type);

    return !(currentCom || currentIsSubmitButton || currentIsBusinessButton);
  };

  /**
   * 组件列表
   */
  const componentTypeList = useMemo(() => {
    return (
      <div className="exchange-component-type-list">
        {filteredData &&
          Array.from(filteredData.entries()).map((item: any, index) => {
            return (
              <div className="exchange-component-type-item-container">
                <div className="exchange-component-type-title">{item[0]}</div>
                {item[1]?.filter(filterData).map((comType: IPublicTypeSnippet) => {
                  return (
                    <div
                      className={`exchange-component-type-item ${
                        currentComType === comType?.schema?.componentName ? 'item-active' : ''
                      }`}
                      onClick={() => {
                        confirmChange(comType);
                      }}
                    >
                      {comType?.title}
                    </div>
                  );
                })}
              </div>
            );
          })}
      </div>
    );
  }, [
    config.get(AthLowCodeConfigKey.AthComponentGroupMap),
    filteredData,
    value?.type,
    dslInfo,
    currentComTitle,
  ]);

  return (
    <CommonSetterLayout {...options.titleProps}>
      <div className="exchange-component-type">
        <span className="exchange-component-title">{t(currentComTitle as string)}</span>
        <Dropdown
          trigger={['hover']}
          onOpenChange={handleOpenChange}
          open={open}
          overlayClassName="exchange-component-type-overlay"
          dropdownRender={() => {
            return (
              <div>
                <Tooltip title={t('dj-转化后的组件将丢失部分配置以及可能遇到数据类型不匹配的问题')}>
                  <span className="exchange-component-type-title">{t('dj-有损转化')}</span>
                  <QuestionCircleOutlined />
                </Tooltip>
                <Input
                  className="exchange-component-type-search"
                  prefix={<SearchOutlined />}
                  allowClear
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                    handleSearch(e.target.value.toLowerCase());
                  }}
                  placeholder={t('dj-请输入关键词')}
                />
                {componentTypeList}
              </div>
            );
          }}
        >
          <Icon
            onMouseEnter={() => {
              setHovered(true);
            }}
            onMouseLeave={() => {
              setHovered(false);
            }}
            type={open || hovered ? 'iconleixingxuanzehover1' : 'iconleixingxuanze1'}
            className="exchange-component-type-icon"
          />
        </Dropdown>
      </div>
    </CommonSetterLayout>
  );
};

export default LcdpComponentTypeExchangeSetter;
