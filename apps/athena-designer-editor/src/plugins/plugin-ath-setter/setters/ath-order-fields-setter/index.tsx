import React, { Component, useEffect, useMemo, useRef, useState } from 'react';
import './index.scss';
import { CloseOutlined, RightSquareOutlined } from '@ant-design/icons';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import i18n, { t } from 'i18next';
import _cloneDeep from 'lodash/cloneDeep';
import { AthTreeDataNode } from '@/plugins/plugin-ath-field-panel/type';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import { getDataSourceName } from '@/tools/utils/setter';
import { IPublicModelSettingField } from '@alilc/lowcode-types';
export interface AthOrderFieldsSetterProps {
  value: any[];
  options: {
    propName?: string; //属性名称，用于特殊属性特殊处理 orderFields：table的二次排序属性
    titleProps: CommonSetterLayoutProps;
  };
  onChange: (value: any) => void;
  field: IPublicModelSettingField;
}

const AthOrderFieldsSetter: React.FC<AthOrderFieldsSetterProps> = (
  props: AthOrderFieldsSetterProps,
) => {
  const { value, field, onChange, options } = props;
  const [isOpenSelectFieldModal, setIsOpenSelectFieldModal] = useState(false);
  const [treeData, setTreeData] = useState<AthTreeDataNode[] | null>(null);
  useEffect(() => {
    const treeMap = config.get(AthLowCodeConfigKey.AthFieldTreeMap) ?? [];
    const dataSourceName = getDataSourceName(field);
    const treeDataItem = treeMap?.get(dataSourceName) ?? [];
    const filteredData = formatTreeData(treeDataItem);
    setTreeData(filteredData ?? []);
  }, [field, isOpenSelectFieldModal]);

  const initCheckedKeys = useMemo(() => {
    return value.map((field) => [treeData?.[0]?.data_name, field.name].filter(Boolean).join('.'));
  }, [value]);

  const handleOpenSelectFieldModal = () => {
    setIsOpenSelectFieldModal(true);
  };

  /**
   * 超过第二层，连第二层一起过滤掉
   */
  const formatTreeData = (treeData: any, level: number = 1) => {
    if (!treeData || treeData?.length === 0) return [];
    const tree = _cloneDeep(treeData);

    const filterTree = tree.filter((node) => !(level >= 2 && node.children?.length > 0));
    return filterTree.map((node) => {
      return {
        ...node,
        key: node?.fullPath,
        children: formatTreeData(node.children, level + 1),
        disabled: node.children?.length > 0,
      };
    });
  };
  // 选择字段弹窗确认
  const handleSelectFieldModalSubmit = (nodes?: AthTreeDataNode[]) => {
    onChange(
      nodes?.map((node, i) => {
        let option: any = {
          name: node.data_name,
          description: node.description?.[i18n.language] ?? '',
          sortFieldPath: "",
        };
        // table的二次排序属性特殊处理
        if (options?.propName === 'orderFields') {
          option['lang'] = {
            description: node.description,
          };
        }
        return option;
      }),
    );
    setIsOpenSelectFieldModal(false);
  };

  // 选择字段弹窗取消
  const handleSelectFieldModalCancel = () => {
    setIsOpenSelectFieldModal(false);
  };

  const handleDelete = (index: number) => {
    const result = [...value];
    result.splice(index, 1);
    onChange(result);
  };

  return (
    <CommonSetterLayout {...(options?.titleProps ?? {})}>
      <div
        className="ath-order-fields-setter"
        onClick={() => {
          handleOpenSelectFieldModal();
        }}
      >
        {value.map((item, index) => {
          return (
            <div className="item-card">
              <span>{item.description}</span>
              <CloseOutlined
                onClick={(e) => {
                  e.stopPropagation();
                  handleDelete(index);
                }}
                className="item-card-delete"
              />
            </div>
          );
        })}
        <AthenaDesignerCoreMFComponent
          componentName="SelectFieldModal"
          componentProps={{
            isOpen: isOpenSelectFieldModal,
            treeData: treeData,
            title: t('dj-选择字段'),
            isMultiple: true,
            initCheckedKeys: initCheckedKeys,
            onCancel: handleSelectFieldModalCancel,
            onSubmit: handleSelectFieldModalSubmit,
          }}
        />
      </div>
    </CommonSetterLayout>
  );
};

export default AthOrderFieldsSetter;
