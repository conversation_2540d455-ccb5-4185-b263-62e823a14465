import React, { useEffect, useMemo, useState } from 'react';
import './index.scss';
import { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import { AppTypes } from '@core_types/pages/Deployer/components/Publish/utils';
import { useTranslation } from 'react-i18next';
export interface LcdpOpenSourceSetterProps {
  value: any;
  onChange: (value: any) => void;
  options: {
    titleProps: CommonSetterLayoutProps;
  };
}

const LcdpOpenSourceSetter: React.FC<LcdpOpenSourceSetterProps> = (
  props: LcdpOpenSourceSetterProps,
) => {
  const { t } = useTranslation();
  const { value, onChange, options } = props;
  const [dslInfo, setDslInfo] = useState<any>();

  useEffect(() => {
    setDslInfo(value);
  }, [value]);
  const [appCode, setAppCode] = useState<string>();
  const [appType, setAppType] = useState<number>();
  const [systemConfig, setSystemConfig] = useState<>();

  useEffect(() => {
    const configInfo = config.get(AthLowCodeConfigKey.AthDynamicWorkDesignInfo);
    const { applicationCode, appType } = configInfo ?? {};
    setAppCode(applicationCode);
    setAppType(appType);
    setSystemConfig(config.get(AthLowCodeConfigKey.AthSystemConfig));
  }, []);

  /**
   * 打开源码
   * */
  const doOpenSource = () => {
    const { iamToken, tenantId } = (systemConfig as any)?.userInfo ?? {};
    const type = dslInfo?.type;
    // 打开源码，参数携带
    const route = 'manage';
    const vscodeProtocol =
      `@vscodeinit&route=${route}&env=@env&version=@version&fileId=@fileId` +
      `&token=${iamToken}&code=${appCode}&type=${type}`;

    const encodedUrl = encodeURIComponent(vscodeProtocol);
    window.open(`/open-vscode?url=${encodedUrl}`, '_blank');
  };
  return (
    <>
      {appType === AppTypes.MODEL_DRIVEN && systemConfig?.config?.enableOpenVscode && (
        <div className="open-source-type" onClick={doOpenSource}>
          <span className="open-source-title">{t('dj-打开源码')}</span>
        </div>
      )}
    </>
  );
};

export default LcdpOpenSourceSetter;
