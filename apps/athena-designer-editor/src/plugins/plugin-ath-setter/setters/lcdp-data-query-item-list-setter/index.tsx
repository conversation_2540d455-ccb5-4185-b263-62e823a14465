import React, { useEffect, useMemo, useState } from 'react';
import {
  IPublicEnumTransformStage,
  IPublicModelNode,
  IPublicModelSettingField,
  IPublicTypePropsMap,
} from '@alilc/lowcode-types';
import './index.scss';
import { DslData } from '@/tools/business/lcdp-converter/type';
import { material, project } from '@alilc/lowcode-engine';
import i18n, { t } from 'i18next';
import DataQueryItemListItem from './data-query-item-list-item';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import theme from '@/config/theme.json';
import { ConfigProvider } from 'antd';
import zh_CN from 'antd/es/locale/zh_CN';
import en_US from 'antd/es/locale/en_US';
import zh_TW from 'antd/es/locale/zh_TW';

const configProviderLocale = { zh_CN, en_US, zh_TW }?.[i18n?.language] ?? zh_CN;

export interface LcdpDataQueryItemListSetterProps {
  field: IPublicModelSettingField;
  onChange: () => void;
}

const LcdpDataQueryItemListSetter: React.FC<LcdpDataQueryItemListSetterProps> = (
  props: LcdpDataQueryItemListSetterProps,
) => {
  const { field, onChange } = props;

  const panelList = useMemo(() => {
    return (
      field?.node?.children?.map((item: IPublicModelNode) => {
        return item;
      }) ?? []
    );
  }, [field?.node?.children]);

  const handleAdd = () => {
    const { snippets = [] } = material.getComponentMeta('DATA_QUERY_ITEM')?.getMetadata() as any;
    const newNode = project.currentDocument?.createNode(snippets[0]?.schema ?? {});
    field?.node?.children?.insert(newNode!);
    onChange();
  };

  const handleEdit = (index = 0) => {
    panelList[index].select();
  };

  const handleDelete = (index = 0) => {
    // field?.node?.children?.delete(panelList[index]);
    panelList[index].remove();
    onChange();
  };

  // 移动了操作顺序
  const handleMove = (fromIndex: number, toIndex: number) => {
    const idOrderList = field?.node?.children?.map((item: IPublicModelNode) => item.id) ?? [];
    const [removed] = idOrderList.splice(fromIndex, 1);
    idOrderList.splice(toIndex, 0, removed);

    field?.node?.mergeChildren(
      (node: IPublicModelNode, idx: number) => false,
      (children: IPublicModelNode[]) => [],
      (firstNode: IPublicModelNode, secondNode: IPublicModelNode) => {
        return (
          idOrderList.findIndex((id) => {
            return firstNode.id === id;
          }) -
          idOrderList.findIndex((id) => {
            return secondNode.id === id;
          })
        );
      },
    );

    // 触发setter刷新
    onChange();
  };

  return (
    <ConfigProvider theme={theme} locale={configProviderLocale}>
      <div className="lcdp-data-query-item-list-setter">
        <div className="data-query-item-list-title">
          <span>{t('dj-页签列表')}</span>
          <span onClick={handleAdd}>+{t('dj-添加')}</span>
        </div>
        <div className="data-query-item-list">
          <DndProvider backend={HTML5Backend} context={window}>
            {panelList.map((panel, index) => {
              const panelData = panel.getPropValue('dslInfo');
              return (
                <DataQueryItemListItem
                  key={index}
                  index={index}
                  title={panelData?.lang?.viewName?.[i18n?.language] ?? panelData?.viewName}
                  onEdit={handleEdit}
                  onMove={handleMove}
                  onDelete={handleDelete}
                />
              );
            })}
          </DndProvider>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default LcdpDataQueryItemListSetter;
