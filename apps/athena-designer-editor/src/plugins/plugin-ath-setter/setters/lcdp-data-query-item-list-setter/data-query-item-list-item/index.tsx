import React, { useEffect, useMemo, useState } from 'react';
import i18n, { t } from 'i18next';
import './index.scss';
import { DeleteOutlined, FormOutlined, HolderOutlined } from '@ant-design/icons';
import { useDrag, useDrop } from 'react-dnd';
import Icon from '@/components/Icon';
import { Popconfirm } from 'antd';

export interface DataQueryItemListItemProps {
  title: string;
  index: number;
  onMove: (fromIndex: number, toIndex: number) => void;
  onEdit: (index: number) => void;
  onDelete: (index: number) => void;
}
const DataQueryItemListItem: React.FC<DataQueryItemListItemProps> = (
  props: DataQueryItemListItemProps,
) => {
  const { title, index, onDelete, onEdit, onMove } = props;

  const [, ref] = useDrag({
    type: 'panel',
    item: { index },
  });

  const [, drop] = useDrop<DataQueryItemListItemProps>({
    accept: 'panel',
    hover: (draggedItem, monitor) => {
      if (draggedItem.index !== index) {
        onMove(draggedItem.index, index);
        draggedItem.index = index; // 更新拖动项的索引
      }
    },
  });

  return (
    <div className="data-query-item-list-item" ref={(node) => ref(drop(node))}>
      <span className="data-query-item-list-item-title">{title}</span>
      <span className="data-query-item-list-item-button">
        <FormOutlined
          onClick={() => {
            onEdit(index);
          }}
        />
        <Popconfirm
          className="confirm-delete"
          title={t('dj-确认删除吗？')}
          onConfirm={() => {
            onDelete(index);
          }}
          onCancel={() => {}}
          okText={t('dj-删除')}
          cancelText={t('dj-取消')}
        >
          <DeleteOutlined />
        </Popconfirm>
        {/* TODO */}
        <Icon className="iconfont button-move" type="icontuozhuai1" />
      </span>
    </div>
  );
};

export default DataQueryItemListItem;
