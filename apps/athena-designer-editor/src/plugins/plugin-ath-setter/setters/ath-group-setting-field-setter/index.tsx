import React, { Component, useEffect, useMemo, useRef, useState } from 'react';
import { Button, Modal, Form, Input, Radio } from 'antd';
import { InputNumber } from 'antd';
import './index.scss';
import { RightSquareOutlined } from '@ant-design/icons';
import { config } from '@alilc/lowcode-engine';
import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import i18n, { t } from 'i18next';
import { AthTreeDataNode } from '@/plugins/plugin-ath-field-panel/type';
import AthenaDesignerCoreMFComponent from '@/components/AthenaDesignerCoreMFComponent';
import CommonSetterLayout, { CommonSetterLayoutProps } from '../../components/common-setter-layout';
import _cloneDeep from 'lodash/cloneDeep';
import { getDataSourceName } from '@/tools/utils/setter';
import { IPublicModelSettingField } from '@alilc/lowcode-types';
export interface AthGroupSettingFieldSetterProps {
  value: any;
  options: {
    titleProps: CommonSetterLayoutProps;
  };
  onChange: (value: any) => void;
  field: IPublicModelSettingField;
}

const AthGroupSettingFieldSetter: React.FC<AthGroupSettingFieldSetterProps> = (
  props: AthGroupSettingFieldSetterProps,
) => {
  const { value, field, onChange, options } = props;
  const [isOpenSelectFieldModal, setIsOpenSelectFieldModal] = useState(false);
  const [treeData, setTreeData] = useState<AthTreeDataNode[] | null>(null);
  useEffect(() => {
    const treeDataMap = config.get(AthLowCodeConfigKey.AthFieldTreeMap);
    const dataSourceName = getDataSourceName(field);
    const treeDataItem = treeDataMap?.get(dataSourceName) ?? [];
    const filteredData = formatTreeData(treeDataItem);
    setTreeData(filteredData); // 更新过滤后的 treeData
  }, [isOpenSelectFieldModal]);

  const handleOpenSelectFieldModal = () => {
    setIsOpenSelectFieldModal(true);
  };

  // 选择字段弹窗确认
  const handleSelectFieldModalSubmit = (nodes: AthTreeDataNode[]) => {
    onChange({
      ...value,
      schema: nodes?.map((item) => item.fullPath).join(', ') ?? '',
      title: nodes[0]?.description?.[i18n.language] ?? '',
      value: nodes[0]?.data_name ?? '',
    });
    setIsOpenSelectFieldModal(false);
  };

  // 选择字段弹窗取消
  const handleSelectFieldModalCancel = () => {
    setIsOpenSelectFieldModal(false);
  };
  /**
   * 超过第二层，连第二层一起过滤掉
   */
  const formatTreeData = (treeData: any, level: number = 1) => {
    if (!treeData || treeData?.length === 0) return [];
    const tree = _cloneDeep(treeData);

    const filterTree = tree.filter((node) => !(level >= 2 && node.children?.length > 0));
    return filterTree.map((node) => {
      return {
        ...node,
        key: node?.fullPath,
        children: formatTreeData(node.children, level + 1),
        disabled: node.children?.length > 0,
      };
    });
  };

  return (
    <CommonSetterLayout {...options.titleProps}>
      <div
        className="ath-group-setting-field-setter"
        onClick={() => {
          handleOpenSelectFieldModal();
        }}
      >
        <Input
          className="schema-form-input"
          placeholder="请选择"
          value={value?.schema}
          readOnly
          suffix={<RightSquareOutlined />}
        />
        <AthenaDesignerCoreMFComponent
          componentName="SelectFieldModal"
          componentProps={{
            isOpen: isOpenSelectFieldModal,
            treeData: treeData,
            isMultiple: true,
            initCheckedKeys: value?.schema?.split(', ') ?? [],
            title: t('dj-选择字段'),
            onCancel: handleSelectFieldModalCancel,
            onSubmit: handleSelectFieldModalSubmit,
          }}
        />
      </div>
    </CommonSetterLayout>
  );
};

export default AthGroupSettingFieldSetter;
