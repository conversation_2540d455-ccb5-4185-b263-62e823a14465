import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import AthCheckboxSetter from './setters/ath-checkbox-setter';
import AthOperationsSetter from './setters/ath-operations-setter';
import AthTableFormEditSetter from './setters/ath-table-form-edit-setter';
import AthRulesSetter from './setters/ath-rules-setter';
import AthCommonSetter from './setters/ath-common-setter';
import AthComponentHooksSetter from './setters/ath-hooks-setter/component-setter';
import AthLayoutEnableSetter from './setters/ath-layout-enable-setter';
import AthActionHooksSetter from './setters/ath-hooks-setter/action-setter';
import AthTabPanelListSetter from './setters/ath-tab-panel-list-setter';
import AthCollapseItemSetter from './setters/ath-collapse-item-setter';
import AthSelectAssociationFieldSetter from './setters/ath-select-association-field-setter';
import AthManageStatusSetter from './setters/ath-manage-status-setter';
import AthSearchInfoSetter from './setters/ath-search-info-setter';
import AthGroupSettingFieldSetter from './setters/ath-group-setting-field-setter';
import AthGroupSummarySetter from './setters/ath-group-summary-setter';
import AthOrderFieldsSetter from './setters/ath-order-fields-setter';
import AthMonacoEditorSetter from './setters/ath-monaco-editor-setter';
import LcdpActionSetter from './setters/lcdp-action-setter';
import AthTreeDataOperationsSetter from './setters/ath-treedata-operations-setter';
import AthPickerOptionsSetter from './setters/ath-picker-options-setter';
import AthPickerDisabledSetter from './setters/ath-picker-disabled-setter';
import AthPickerDisplaySetter from './setters/ath-picker-display-setter';
import AthGridsterChildSetter from './setters/ath-gridster-child-setter';
import AthGridsterLayoutSetter from './setters/ath-gridster-layout-setter';
import AthSchemasSelectSetter from './setters/ath-schemas-select-setter';
import AthApproveDescriptionSetter from './setters/ath-approve-description-setter';
import AthNameCodeSetter from './setters/ath-name-code-setter';
import AthTagConfigSetter from './setters/ath-tag-config-setter';
import AthOperationsOpenWindowSetter from './setters/ath-operations-open-window-setter';
import AthLayoutBoxSetter from './setters/ath-layout-box-setter';
import AthLayoutPositionSetter from './setters/ath-layout-position-setter';
import AthColorSetter from './setters/ath-color-setter';
import LcdpOptionsSetter from './setters/lcdp-options-setter';
import ButtonNavigationTypeSetter from './setters/button-navigation-type-setter';
import ButtonNavigationTargetSetter from './setters/button-navigation-target-setter';
import ButtonNavigationReportSetter from './setters/button-navigation-report-setter';
import AthIconSetter from './setters/ath-icon-setter';
import LcdpPaddingSetter from './setters/lcdp-padding-setter';
import AthColorPanelSetter from './setters/ath-color-panel-setter';
import LcdpGridBreakPointSetter from './setters/lcdp-grid-break-point-setter';
import LcdpImageSetter from './setters/lcdp-image-setter';
import LcdpInputEditorSetter from './setters/lcdp-input-editor-setter';
import LcdpButtonTypeSetter from './setters/lcdp-button-type-setter';
import LcdpButtonActionsSetter from './setters/lcdp-button-actions-setter';
import LcdpToolbarItemSetter from './setters/lcdp-toolbar-item-setter';
import LcdpActionParamsSetter from './setters/lcdp-action-params-setter';
import LcdpSubpageSelectorSetter from './setters/lcdp-subpage-selector-setter';
import LcdpPrintTemplateListSetter from './setters/lcdp-print-template-list-setter';
import LcdpComponentTypeExchangeSetter from './setters/lcdp-component-type-exchange-setter';
import LcdpIconPositionSetter from './setters/lcdp-icon-position-setter';
import LcdpBindWorkflowSetter from './setters/lcdp-bind-workflow-setter';
import LcdpDateModeSetter from './setters/lcdp-date-mode-setter';
import LcdpPermissionSetter from './setters/lcdp-permission-setter';
import LcdpColorPickerSetter from './setters/lcdp-color-picker-setter';
import LcdpDataSourceNamesSetter from './setters/lcdp-dataSourceNames-setter';
import LcdpDataQueryItemListSetter from './setters/lcdp-data-query-item-list-setter';
import LcdpOutletSelectorSetter from './setters/lcdp-outlet-selector-setter';
import LcdpTableOperationSetter from './setters/lcdp-table-operation-setter';
import LcdpSubpageListSetter from './setters/lcdp-subpage-list-setter';
import LcdpOpenSourceSetter from './setters/lcdp-open-source-setter';

const AthSetterPlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { setters } = ctx;
      setters.registerSetter('AthCheckboxSetter', AthCheckboxSetter);
      setters.registerSetter('AthOperationsSetter', AthOperationsSetter);
      setters.registerSetter('AthTableFormEditSetter', AthTableFormEditSetter);
      setters.registerSetter('AthRulesSetter', AthRulesSetter);
      setters.registerSetter('AthCommonSetter', AthCommonSetter);
      setters.registerSetter('AthComponentHooksSetter', AthComponentHooksSetter);
      setters.registerSetter('AthActionHooksSetter', AthActionHooksSetter);
      setters.registerSetter('AthTabPanelListSetter', AthTabPanelListSetter);
      setters.registerSetter('AthCollapseItemSetter', AthCollapseItemSetter);
      setters.registerSetter('AthLayoutEnableSetter', AthLayoutEnableSetter);
      setters.registerSetter('AthSelectAssociationFieldSetter', AthSelectAssociationFieldSetter);
      setters.registerSetter('AthManageStatusSetter', AthManageStatusSetter);
      setters.registerSetter('AthSearchInfoSetter', AthSearchInfoSetter);
      setters.registerSetter('AthGroupSettingFieldSetter', AthGroupSettingFieldSetter);
      setters.registerSetter('AthGroupSummarySetter', AthGroupSummarySetter);
      setters.registerSetter('AthOrderFieldsSetter', AthOrderFieldsSetter);
      setters.registerSetter('AthMonacoEditorSetter', AthMonacoEditorSetter);
      setters.registerSetter('LcdpActionSetter', LcdpActionSetter);
      setters.registerSetter('AthTreeDataOperationsSetter', AthTreeDataOperationsSetter);
      setters.registerSetter('AthPickerOptionsSetter', AthPickerOptionsSetter);
      setters.registerSetter('AthPickerDisabledSetter', AthPickerDisabledSetter);
      setters.registerSetter('AthPickerDisplaySetter', AthPickerDisplaySetter);
      setters.registerSetter('AthGridsterChildSetter', AthGridsterChildSetter);
      setters.registerSetter('AthGridsterLayoutSetter', AthGridsterLayoutSetter);
      setters.registerSetter('AthSchemasSelectSetter', AthSchemasSelectSetter);
      setters.registerSetter('AthApproveDescriptionSetter', AthApproveDescriptionSetter);
      setters.registerSetter('AthNameCodeSetter', AthNameCodeSetter);
      setters.registerSetter('AthTagConfigSetter', AthTagConfigSetter);
      setters.registerSetter('AthOperationsOpenWindowSetter', AthOperationsOpenWindowSetter);
      setters.registerSetter('AthLayoutBoxSetter', AthLayoutBoxSetter);
      setters.registerSetter('AthLayoutPositionSetter', AthLayoutPositionSetter);
      setters.registerSetter('AthColorSetter', AthColorSetter);
      setters.registerSetter('LcdpOptionsSetter', LcdpOptionsSetter);
      setters.registerSetter('ButtonNavigationTypeSetter', ButtonNavigationTypeSetter);
      setters.registerSetter('ButtonNavigationReportSetter', ButtonNavigationReportSetter);
      setters.registerSetter('ButtonNavigationTargetSetter', ButtonNavigationTargetSetter);
      setters.registerSetter('AthIconSetter', AthIconSetter);
      setters.registerSetter('LcdpPaddingSetter', LcdpPaddingSetter);
      setters.registerSetter('AthColorPanelSetter', AthColorPanelSetter);
      setters.registerSetter('LcdpGridBreakPointSetter', LcdpGridBreakPointSetter);
      setters.registerSetter('LcdpImageSetter', LcdpImageSetter);
      setters.registerSetter('LcdpInputEditorSetter', LcdpInputEditorSetter);
      setters.registerSetter('LcdpButtonTypeSetter', LcdpButtonTypeSetter);
      setters.registerSetter('LcdpButtonActionsSetter', LcdpButtonActionsSetter);
      setters.registerSetter('LcdpToolbarItemSetter', LcdpToolbarItemSetter);
      setters.registerSetter('LcdpActionParamsSetter', LcdpActionParamsSetter);
      setters.registerSetter('LcdpSubpageSelectorSetter', LcdpSubpageSelectorSetter);
      setters.registerSetter('LcdpPrintTemplateListSetter', LcdpPrintTemplateListSetter);
      setters.registerSetter('LcdpComponentTypeExchangeSetter', LcdpComponentTypeExchangeSetter);
      setters.registerSetter('LcdpOpenSourceSetter', LcdpOpenSourceSetter);
      setters.registerSetter('LcdpIconPositionSetter', LcdpIconPositionSetter);
      setters.registerSetter('LcdpBindWorkflowSetter', LcdpBindWorkflowSetter);
      setters.registerSetter('LcdpDateModeSetter', LcdpDateModeSetter);
      setters.registerSetter('LcdpPermissionSetter', LcdpPermissionSetter);
      setters.registerSetter('LcdpColorPickerSetter', LcdpColorPickerSetter);
      setters.registerSetter('LcdpDataSourceNamesSetter', LcdpDataSourceNamesSetter);
      setters.registerSetter('LcdpDataQueryItemListSetter', LcdpDataQueryItemListSetter);
      setters.registerSetter('LcdpOutletSelectorSetter', LcdpOutletSelectorSetter);
      setters.registerSetter('LcdpTableOperationSetter', LcdpTableOperationSetter);
      setters.registerSetter('LcdpSubpageListSetter', LcdpSubpageListSetter);
    },
  };
};
AthSetterPlugin.pluginName = 'AthSetterPlugin';
export default AthSetterPlugin;
