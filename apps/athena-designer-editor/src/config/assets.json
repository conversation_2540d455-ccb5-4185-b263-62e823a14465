{"packages": [{"urls": ["${urlPrefix}/scheduler/static/lib/lowcode/micro-app-environment.js"]}, {"package": "moment", "version": "2.24.0", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/moment/2.24.0/moment.min.js"], "library": "moment"}, {"package": "dayjs", "version": "1.10.4", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/dayjs/1.10.4/dayjs.min.js"], "library": "dayjs"}, {"package": "lodash", "library": "_", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/lodash/4.6.1/lodash.min.js"]}, {"title": "fusion组件库", "package": "@alifd/next", "version": "1.26.4", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/@alifd/next/1.26.4/next.min.css", "${urlPrefix}/scheduler/static/lib/lowcode/@alifd/next/1.26.4/next-with-locales.min.js"], "library": "Next"}, {"title": "NextTable", "package": "NextTable", "version": "1.0.1", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/NextTable/1.0.1/next-table.js", "${urlPrefix}/scheduler/static/lib/lowcode/NextTable/1.0.1/next-table.css"], "library": "NextTable"}, {"package": "@alilc/lowcode-materials", "version": "1.0.7", "library": "AlilcLowcodeMaterials", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/@alilc/lowcode-materials/1.0.7/AlilcLowcodeMaterials.js", "${urlPrefix}/scheduler/static/lib/lowcode/@alilc/lowcode-materials/1.0.7/AlilcLowcodeMaterials.css"], "editUrls": ["${urlPrefix}/scheduler/static/lib/lowcode/@alilc/lowcode-materials/1.0.7/view.js", "${urlPrefix}/scheduler/static/lib/lowcode/@alilc/lowcode-materials/1.0.7/view.css"]}, {"package": "@alifd/layout", "version": "2.0.7", "library": "AlifdLayout", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/@alifd/layout/2.0.7/AlifdLayout.js", "${urlPrefix}/scheduler/static/lib/lowcode/@alifd/layout/2.0.7/AlifdLayout.css"], "editUrls": ["${urlPrefix}/scheduler/static/lib/lowcode/@alifd/layout/2.0.7/view.js", "${urlPrefix}/scheduler/static/lib/lowcode/@alifd/layout/2.0.7/view.css"]}, {"package": "@alifd/fusion-ui", "version": "2.0.0", "library": "AlifdFusionUi", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/@alifd/fusion-ui/2.0.0/AlifdFusionUi.js", "${urlPrefix}/scheduler/static/lib/lowcode/@alifd/fusion-ui/2.0.0/AlifdFusionUi.css"], "editUrls": ["${urlPrefix}/scheduler/static/lib/lowcode/@alifd/fusion-ui/2.0.0/view.js", "${urlPrefix}/scheduler/static/lib/lowcode/@alifd/fusion-ui/2.0.0/view.css"]}, {"package": "antd", "version": "5.18.3", "urls": ["${urlPrefix}/scheduler/static/lib/lowcode/dayjs/1.11.10/dayjs.min.js", "${urlPrefix}/scheduler/static/lib/lowcode/antd/5.18.3/antd.min.js", "${urlPrefix}/scheduler/static/lib/lowcode/antd/5.18.3/reset.min.css"], "library": "antd"}], "components": [], "sort": {"groupList": ["dj-标准组件", "dj-自定义组件"], "categoryList": ["dj-容器组件", "dj-基础组件", "dj-开窗组件", "dj-按钮组件", "dj-标签组件", "dj-功能组件"], "componentSortMap": {"dj-容器组件": ["ATHENA_TABLE", "FORM_LIST", "LIST", "TABS", "COLLAPSE", "LAYOUT", "FLEXIBLE_BOX", "GRIDSTER", "FLEX"], "dj-基础组件": ["TEXT", "INPUT", "INPUT_NUMBER", "PERCENT_INPUT", "AMOUNT_INPUT", "MEASURE", "SELECT", "SELECT_MULTIPLE", "DATEPICKER", "TIMEPICKER", "DATE_RANGE", "TIME_RANGE", "LABEL", "TEXTAREA", "RADIO_GROUP", "CHECKBOX", "FILE_UPLOAD", "FORM_UPLOAD", "ICON", "IMAGE", "DYNAMIC_GRAPH_VIEWER", "DIVIDER", "COMMON"], "dj-开窗组件": ["OPERATION_EDITOR", "FORM_OPERATION_EDITOR"], "dj-按钮组件": ["BUTTON", "BUTTON_GROUP"], "dj-标签组件": ["ATH_TAG"], "dj-功能组件": ["CURRENT_ACCOUNT", "ADDRESS", "CONTACT", "PERSON_SELECT", "PERSON_SELECT_NEW", "PLAN_SELECT", "EOC_USER_SELECT", "EOC_USER_SELECT_NEW", "EOC_SELECT", "EOC_SELECT_NEW", "EOC_MULTI_SELECT", "EOC_MULTI_SELECT_NEW", "NAME_CODE_COMPONENT", "NEW_OLD_COMPONENT", "DIFFERENCE_CALCULATION", "APPROVAL_DESCRIPTION", "TOOLBAR", "ADD_DOCUMENTID_CONTROL", "TASK_PROGRESS_STATUS", "SIGN_OFF_PROGRESS", "WORKFLOW_PROGRESS", "TREEDATA", "ACTIVITY_TITLE", "DELIVERY_REPLY_DESCRIPTION", "DELIVERY_REPLY_TITLE", "SIGN_OFF_PROGRESS_LINK"]}}, "groupList": ["dj-标准组件", "dj-自定义组件"], "ignoreComponents": {"athena-designer-editor-components": {"TAB_PANEL": 1, "AthCollapseItem": 1, "AthLayoutChild": 1, "AthModal": 1, "AthPersonalCard": 1, "IsvButton": 1, "SubmitActions": 1, "AthGridsterChild": 1, "LABEL": 1, "TABLE_GROUP": 1, "DATA_QUERY_ITEM": 1, "DYNAMIC_OPERATION": 1}}}