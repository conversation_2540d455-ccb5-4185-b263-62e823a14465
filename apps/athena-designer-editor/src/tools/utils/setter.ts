import { AthLowCodeConfigKey } from '@/plugins/plugin-ath-loader/type';
import { config } from '@alilc/lowcode-engine';
import { AthComponentType } from '@/tools/business/lcdp-converter/type';
import { IPublicModelSettingField } from '@alilc/lowcode-types';

export const getDataSourceName = (field: IPublicModelSettingField) => {
  //当前层
  const dslInfo = field.getNode()?.getPropValue('dslInfo');
  const currentDataSourceName = dslInfo?.queryInfo?.dataFilter?.dataSourceNames?.[0];
  if (currentDataSourceName) return currentDataSourceName;

  //父级
  //table中间裹了一成table-group,是两层嵌套的，需要再向上取一级。
  //form-list和list是单层
  const firstLevel = [AthComponentType.FORM_LIST, AthComponentType.LIST];
  const secondLevel = [AthComponentType.TABLE_GROUP];

  const parentType = field.getNode()?.parent?.componentName;

  let parentDslInfo: any = firstLevel.includes(parentType)
    ? field.getNode()?.parent?.getPropValue('dslInfo')
    : secondLevel?.includes(parentType)
    ? field.getNode()?.parent?.parent?.getPropValue('dslInfo')
    : null;

  const parentDataSource = parentDslInfo?.queryInfo?.dataFilter?.dataSourceNames?.[0];
  if (parentDataSource) return parentDataSource;

  //全局生效
  return config.get(AthLowCodeConfigKey.AthDataSourceInfo)?.dataSourceName;
};
