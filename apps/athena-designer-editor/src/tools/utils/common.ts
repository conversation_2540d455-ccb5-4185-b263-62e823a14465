import { IPublicTypeSnippet } from '@alilc/lowcode-types';

export const sortTypeExchange = (
  source: Map<string, IPublicTypeSnippet[]>,
  componentSortMap?: { [k: string]: string[] },
): Map<string, IPublicTypeSnippet[]> => {
  if (!componentSortMap) return source;
  const currentKV: { [k: string]: IPublicTypeSnippet[] } = {};
  for (let [k, v] of source) {
    const value = [...v];
    const sortList = componentSortMap[k] || [];
    value.sort((pre, next) => {
      const preIndex = sortList.indexOf(pre.schema!.componentName);
      const nextIndex = sortList.indexOf(next.schema!.componentName);
      return preIndex - nextIndex;
    });
    currentKV[k] = value;
  }
  const result = new Map<string, IPublicTypeSnippet[]>();
  Object.keys(componentSortMap)
    .filter((key) => source.has(key))
    .forEach((key) => result.set(key, currentKV[key]));
  return result;
};
