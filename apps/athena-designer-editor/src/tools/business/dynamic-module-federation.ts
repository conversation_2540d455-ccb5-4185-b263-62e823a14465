import { init, loadRemote, registerRemotes } from '@module-federation/runtime';
const isDevelopment = process.env.NODE_ENV === 'development';

const remotes = [
  {
    name: 'athena_designer_core',
    entry: `${isDevelopment ? 'http://localhost:3000' : '/athena-designer-core'}/remoteEntry.js`,
  },
];

init({
  name: 'athena-designer-editor',
  remotes,
});

registerRemotes(remotes, { force: true });

export interface LoadRemoteProps {
  remoteName: string;
  exposedModule: string;
}

async function loadRemoteLocal(loadRemoteProps: LoadRemoteProps) {
  const container = (window as any)[loadRemoteProps.remoteName] as any;
  const factory = await container.get(`./${loadRemoteProps.exposedModule}`);
  return factory();
}

const athloadRemote = async (loadRemoteProps: LoadRemoteProps) => {
  // 如果对应的container已经存在，那就直接加载，无需init
  if ((window as any)[loadRemoteProps.remoteName]) {
    return await loadRemoteLocal(loadRemoteProps);
  }
  return await loadRemote(`${loadRemoteProps.remoteName}/${loadRemoteProps.exposedModule}`);
};

export const dynamicModuleFederationService = {
  remotes,
  loadRemote: athloadRemote,
};
