import { AthComponentType, Converter, DslData, DslSchema } from './type';
import { commonAthConverter } from './components/common';
import { athenaTableAthConverter } from './components/athena-table';
import { athCollapseItemConverter } from './components/ath-collapse-item';
import { tableGroupAthConverter } from './components/table-group';
import { athLayoutConverter } from './components/ath-layout';
import { athLayoutChildConverter } from './components/ath-layout-child';
import { athCollapseConverter } from './components/ath-collapse';
import { athFlexibleBoxConverter } from './components/ath-flexible-box';
import { tabsAthConverter } from './components/tabs';
import { tabPanelAthConverter } from './components/tab-panel';
import { athGridsterConverter } from './components/ath-gridster';
import { athGridsterChildConverter } from './components/ath-gridster-child';
import { athNameCodeConverter } from './components/ath-name-code';
import { athNewOldConverter } from '@/tools/business/lcdp-converter/components/ath-new-old';
import { lcdpListConverter } from '@/tools/business/lcdp-converter/components/lcdp-list-converter';
import { LcdpFormListConverter } from '@/tools/business/lcdp-converter/components/lcdp-form-list';
import { allButtonConverter } from '@/tools/business/lcdp-converter/components/button';
import { buttonGroupConverter } from '@/tools/business/lcdp-converter/components/button-group';
import { lcdpFlexConverter } from '@/tools/business/lcdp-converter/components/lcdp-flex-converter';
import { lcdpFlexItemConverter } from '@/tools/business/lcdp-converter/components/lcdp-flex-item-converter';

import { lcdpDataQueryConverter } from '@/tools/business/lcdp-converter/components/lcdp-data-query';
import { lcdpDataQueryItemConverter } from '@/tools/business/lcdp-converter/components/lcdp-data-query-item';
import { lcdpDynamicOperationConverter } from './components/lcdp-dynamic-operation';
import { LcdpInputConverter } from './components/lcdp-input';

class LcdpConverterManager {
  private converterMap = new Map<string, Converter>();
  constructor(converterList: Converter[] = []) {
    this.converterMap = new Map(converterList.map((item) => [item.key, item]));
  }

  // 在执行转换器具体转换逻辑之前的前置操作（预留的口子，一般用不到）
  private beforeToDsl(schema: DslSchema): DslSchema {
    return { ...schema };
  }

  // 在执行转换器具体转换逻辑之前的前置操作（预留的口子，一般用不到）
  private beforeToSchema(dsl: DslData): DslData {
    return { ...dsl };
  }

  setConverter(key: string, athConverter: Converter): void {
    this.converterMap.set(key, athConverter);
  }
  deleteConverter(key: string): void {
    this.converterMap.delete(key);
  }
  getConverter(key: string = AthComponentType.COMMON): Converter {
    return this.converterMap.get(key) ?? commonAthConverter;
  }

  private toObjectDsl(schema: DslSchema): DslData {
    const schemaData = this.beforeToDsl(schema);
    const converter = this.getConverter(schemaData.componentName);
    const { data: dslData, childrenData = [], customTransfer } = converter.toDsl(schemaData);
    if (customTransfer) {
      const childDsl = this.toDsl(customTransfer.data);
      customTransfer.callback(dslData, childDsl);
    } else {
      childrenData.forEach((childrenItem) => {
        dslData[childrenItem.key] = this.toDsl(childrenItem.data);
      });
    }
    return dslData;
  }

  // lowcode的标准schema转dsl
  // 特别说明，可以看到，当前 toDsl 与 toSchema 在入参上存在不对称的情况
  // 设计之初，toDsl 与 toSchema 是对称的，但 由于 需要 适配 平台某些特殊组件的dsl 结构，才产生了 差异
  // 但对于 对外的 使用 和 对 具体 转换器的 定制开发，没有太大影响
  toDsl(schema: DslSchema | DslSchema[]): DslData | DslData[] {
    if (Object.prototype.toString.call(schema) === '[object Array]') {
      return schema.map((schemaOrigin: DslSchema) => {
        return this.toObjectDsl(schemaOrigin);
      });
    }
    return this.toObjectDsl(schema as DslSchema);
  }

  // dsl转lowcode的标准schema
  toSchema(dsl: DslData[]): DslSchema[] {
    return dsl.map((dslOrigin: DslData) => {
      const dslData = this.beforeToSchema(dslOrigin);
      const converter = this.getConverter(dslData.type);
      const { data: dslSchema, childrenData = [] } = converter.toSchema(dslData);
      childrenData.forEach((childrenItem) => {
        dslSchema[childrenItem.key] = this.toSchema(childrenItem.data);
      });
      return dslSchema;
    });
  }
}

export const lcdpConverterManager = new LcdpConverterManager([
  commonAthConverter,
  athenaTableAthConverter,
  athCollapseItemConverter,
  tableGroupAthConverter,
  athLayoutConverter,
  athLayoutChildConverter,
  athCollapseConverter,
  tabsAthConverter,
  tabPanelAthConverter,
  athFlexibleBoxConverter,
  athGridsterConverter,
  athGridsterChildConverter,
  athNameCodeConverter,
  athNewOldConverter,
  lcdpListConverter,
  LcdpFormListConverter,
  ...allButtonConverter,
  buttonGroupConverter,
  lcdpFlexConverter,
  lcdpFlexItemConverter,
  lcdpDataQueryConverter,
  lcdpDataQueryItemConverter,
  lcdpDynamicOperationConverter,
  LcdpInputConverter,
]);
