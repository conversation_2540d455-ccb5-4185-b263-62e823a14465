import { IPublicTypeNodeSchema } from '@alilc/lowcode-types';

// 平台dsl数据
export interface DslData {
  /**
   * type组件类型
   * 正常来说type必然存在，但有个特例
   * TABLE_GROUP组件，在lowcode中存在，在dsl中不存在该组件，所以就没有type，但是，他却有自己的属性
   * 那么在TABLE_GROUP转dsl时，将会抹除其type
   * 而在dsl转schema时，在 ATHENA_TABLE组件中，重新赋予 子 层级 type TABLE_GROUP 渲染成 lowcode组件
   */
  type?: string;
  [key: string]: any;
}

// DslData 转换后的 lowcode Schema 数据
export interface DslSchema extends IPublicTypeNodeSchema {
  props: DslSchemaProps;
  children?: DslSchema[];
  [key: string]: any;
}

export interface DslSchemaProps {
  dslInfo: DslData;
  [key: string]: any;
}

export interface ChildrenData<T> {
  key: string;
  data: T[];
}

export interface CustomTransfer<T> {
  data: T[];
  callback: (dslData: DslData, childDslData: DslData | DslData[]) => void;
}

// dsl与Schema转换的中间产物
export interface ConvertOutput<T, F> {
  data: T;
  childrenData?: ChildrenData<F>[];
  customTransfer?: CustomTransfer<F>;
}

export type ConvertToSchemaFunc = (dslData: DslData) => ConvertOutput<DslSchema, DslData>;
export type ValidDslFunc = (dslData: DslData) => boolean;
export type ConvertToDslFunc = (dslSchema: DslSchema) => ConvertOutput<DslData, DslSchema>;

export interface Converter {
  key: AthComponentType;
  toSchema: ConvertToSchemaFunc; // dsl 转换为 lowcode schema
  toDsl: ConvertToDslFunc; // lowcode schema 转换为 dsl
  valid: ValidDslFunc;
}

// 组件类型（正常情况下dsl，lowcode schema，转换器 都是一一对应的）
export enum AthComponentType {
  /**
   * COMMON 在 dsl中不存在
   * 在 lowcode schema 中 将会作为 兜底的 渲染组件使用
   * 在 转换器中也会作为 兜底的 通用转换器
   */
  COMMON = 'COMMON',
  ATHENA_TABLE = 'ATHENA_TABLE',
  AthCollapse = 'COLLAPSE',
  AthCollapseItem = 'AthCollapseItem',
  AthLayout = 'LAYOUT',
  AthLayoutChild = 'AthLayoutChild',
  AthFlexibleBox = 'FLEXIBLE_BOX',
  AthTreeData = 'TREEDATA',
  FORM_LIST = 'FORM_LIST',
  ADD_DOCUMENTID_CONTROL = 'ADD_DOCUMENTID_CONTROL',
  FLEXIBLE_BOX = 'FLEXIBLE_BOX',
  LAYOUT = 'LAYOUT',
  DIFFERENCE_CALCULATION = 'DIFFERENCE_CALCULATION',
  LABEL = 'LABEL',
  DYNAMIC_GRAPH_VIEWER = 'DYNAMIC_GRAPH_VIEWER',
  ADDRESS = 'ADDRESS',
  PLAN_SELECT = 'PLAN_SELECT',
  ATH_TAG = 'ATH_TAG',
  CURRENT_ACCOUNT = 'CURRENT_ACCOUNT',
  APPROVAL_DESCRIPTION = 'APPROVAL_DESCRIPTION',
  TOOLBAR = 'TOOLBAR',
  TASK_PROGRESS_STATUS = 'TASK_PROGRESS_STATUS',
  SIGN_OFF_PROGRESS = 'SIGN_OFF_PROGRESS',
  TREEDATA = 'TREEDATA',
  WORKFLOW_PROGRESS = 'WORKFLOW_PROGRESS',
  SIGN_OFF_PROGRESS_LINK = 'SIGN_OFF_PROGRESS_LINK',
  ACTIVITY_TITLE = 'ACTIVITY_TITLE',
  DELIVERY_REPLY_DESCRIPTION = 'DELIVERY_REPLY_DESCRIPTION',
  DELIVERY_REPLY_TITLE = 'DELIVERY_REPLY_TITLE',
  NAME_CODE_COMPONENT = 'NAME_CODE_COMPONENT',
  NEW_OLD_COMPONENT = 'NEW_OLD_COMPONENT',
  /**
   * TABLE_GROUP 在 dsl中不存在
   * 在 lowcode schema 中 将会作为 TABLE_GROUP 组件使用，承载业务数据
   * 在 转换器中 转换成 dsl 时，会移除 type属性
   */
  TABLE_GROUP = 'TABLE_GROUP',
  INPUT = 'INPUT',
  TABS = 'TABS',
  TAB_PANEL = 'TAB_PANEL', // lowcode 特有
  AthGridster = 'GRIDSTER',
  AthGridsterChild = 'AthGridsterChild',
  /**
   * BUTTON_GROUP相关
   */
  BUTTON_GROUP = 'BUTTON_GROUP',
  BUTTON = 'BUTTON',
  LIST = 'LIST',

  SELECT = 'SELECT', // 下拉选择
  DATEPICKER = 'DATEPICKER', // 日期选择
  TIMEPICKER = 'TIMEPICKER', // 时间选择

  FLEX = 'FLEX', // 弹性布局组件
  FLEX_ITEM = 'FLEX_ITEM', // 弹性布局组件的子元素在 dsl中不存在

  DATA_QUERY = 'DATA_QUERY', // 数组查询组件
  DATA_QUERY_ITEM = 'DATA_QUERY_ITEM', // 数组查询组件的子组件在 dsl中不存在
  DYNAMIC_OPERATION = 'DYNAMIC_OPERATION', // 动态操作
}
