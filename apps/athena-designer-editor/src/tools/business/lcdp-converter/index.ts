import { project, config } from '@alilc/lowcode-engine';
import { lcdpConverterManager } from './LcdpConverterManager';
import {
  IPublicEnumTransformStage,
  IPublicTypeNodeSchema,
  IPublicTypeProjectSchema,
  IPublicTypeRootSchema,
} from '@alilc/lowcode-types';
import {
  AthLowCodeConfigKey,
  DslWorkDesignData,
  PageUIElementContent,
} from '@/plugins/plugin-ath-loader/type';
import { DslData, DslSchema } from './type';
import { t } from 'i18next';

// 从指定 project schema (不指定就是当前的project) 中 获取 WorkDesignData
export const getProjectPageUIElement = (
  projectSchema?: IPublicTypeProjectSchema<IPublicTypeRootSchema>,
): PageUIElementContent => {
  const schema = projectSchema ?? project.exportSchema(IPublicEnumTransformStage.Save);
  let page = schema?.componentsTree?.[0] ?? {};
  let schemaToShow = page.children ?? [];
  schemaToShow = Array.isArray(schemaToShow) ? schemaToShow : [schemaToShow];
  const layout = lcdpConverterManager.toDsl(schemaToShow as DslSchema[]);
  const {
    operations = [] as any,
    hooks = [] as any,
    submitActions = [] as any,
    gridSettings = [] as any,
  } = page.props ?? {};
  return {
    layout: Array.isArray(layout) ? layout : [layout],
    operations,
    submitActions,
    hooks,
    gridSettings,
  };
};

// 在当前项目中加载PageUIElement数据
export const loadPageUIElementToProject = (
  pageUIElementContent: PageUIElementContent,
  projectSchema?: IPublicTypeProjectSchema<IPublicTypeRootSchema>,
  rememberSelected = true,
) => {
  const {
    layout = [],
    operations = [],
    hooks = [],
    submitActions = [],
    gridSettings = [] as any[], // TODO
  } = pageUIElementContent;
  const schema = projectSchema ?? project.exportSchema(IPublicEnumTransformStage.Save);
  const page = schema.componentsTree[0];
  page.props = {
    ...schema.componentsTree[0].props,
    // operations,
    hooks,
    submitActions,
    gridSettings,
    athLowCodeConfig: {
      // 将部分ath的config挂载到root上，以便某些组件在渲染时需要
      [AthLowCodeConfigKey.AthFieldTree]: config.get(AthLowCodeConfigKey.AthFieldTree) ?? [],
      [AthLowCodeConfigKey.AthStatusInfo]: config.get(AthLowCodeConfigKey.AthStatusInfo) ?? null,
    },
  };
  page.children = lcdpConverterManager.toSchema(layout);
  page.title = t(page.title as string);

  const selectedComponentId = project?.currentDocument?.selection?.node?.getPropValue('dslInfo.id');

  project.importSchema(schema);

  // 问题描述，通过project.importSchema导入的operations数据，如果某个字段值为null
  // 在project.exportSchema(IPublicEnumTransformStage.Save) 时，该字段 会被 赋值为 ''，会有问题

  // 继续描述细节
  // 在importSchema之后，从root节点上获取 propsData 的operation属性，如果该值的value是null的，那么连key都不会出现
  // 但是 通过 path的形式 却能取到值null（非undefined），比如**.getPropValue('operations.0.condition')，这里应该是lowcode的bug
  // 当然这没问题，只需要保证exportSchema也是这个逻辑 取不到 这个 值就行（而不是 赋值为 ''）
  // 那么看起来只需要在exportSchema时，把IPublicEnumTransformStage.Save改为IPublicEnumTransformStage.Clone或者其他非Save的就行了(会和propsData逻辑一致，为null的这个值的key都不存在)
  // 但是，像以下的结构，propsData上连test1的key都没有，这个就和我们的预期有所差异
  // test1: {
  //   b: null,
  // } =======> propsData上没有test1
  // 但是 test2:{} ,propsData上有test2,明显的bug
  // 所以，在这里使用了setPropValue的方式挂载operations，这些问题都不存在了，置为null的属性的key和value都会原封不动的保留（这也是我觉得lowcode自身逻辑不一致的地方）
  // TODO 至少在lowcode 1.1.4 版本中是这种现象，后续 如果有时间可以继续研究lowcode 源码逻辑 或者 升级版本
  project?.currentDocument?.root?.setPropValue('operations', operations);

  if (rememberSelected && selectedComponentId) {
    const nodeList = Array.from(project?.currentDocument?.nodesMap?.values() ?? []);
    const selectedNode = nodeList.find(
      (node) => node.getPropValue('dslInfo.id') === selectedComponentId,
    );
    selectedNode?.select();
  } else {
    project?.currentDocument?.root?.select();
  }
};

export function transferDslToSchema(dslInfo: DslData[]): DslSchema[] {
  return lcdpConverterManager.toSchema(dslInfo) ?? [];
}
