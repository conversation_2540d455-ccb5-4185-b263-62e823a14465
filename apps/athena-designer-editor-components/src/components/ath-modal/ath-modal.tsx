import * as React from 'react';
import { createElement } from 'react';
import './index.scss';

export interface AthModalProps {
  children: any;
}

const AthModal: React.FC<AthModalProps> = (props: AthModalProps) => {
  return (
    <div className="ath-modal">
      <div className="ath-modal-content">{props.children}</div>
    </div>
  );
};

AthModal.displayName = 'AthModal';
export default AthModal;
