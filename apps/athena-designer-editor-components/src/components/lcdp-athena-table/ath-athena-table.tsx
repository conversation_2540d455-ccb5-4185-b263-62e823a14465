import React, { createElement, useMemo } from 'react';
import { SearchOutlined, SortAscendingOutlined, SettingOutlined } from '@ant-design/icons';
import { Checkbox, Tooltip } from 'antd';
import './index.scss';
import { getAppHelperUtils, getI18n } from '../../tools';
import { AthenaComponentType } from '../../../lowcode/common/common.config';
import { isEmpty } from 'lodash';

export interface LcdpAthenaTableProps {
  children: any;
  dslInfo: any;
  _leaf: any;
}

const LcdpAthenaTable: React.FC<LcdpAthenaTableProps> = (props: LcdpAthenaTableProps) => {
  const { dslInfo, children, _leaf } = props;
  const { t, language } = getI18n(_leaf);
  const { athLowCodeConfig = {}, gridSettings } = props._leaf?.document?.root.propsData;
  const { AthStatusInfo, AthFieldTree } = athLowCodeConfig;

  // 业务逻辑，是否为主表
  const isMainTable = useMemo(() => {
    const { path = '', schema = '' } = dslInfo;
    const { path: rootPath, data_name: rootSchema } = AthFieldTree?.[0];
    return path === rootPath && schema === rootSchema;
  }, [dslInfo]);

  // 是否开启了高级查询
  const isAdvancedSearch = useMemo(() => {
    const { path = '', schema = '' } = dslInfo;
    const gridSetting = gridSettings.find((gridSetting) => {
      return gridSetting.gridPath === path && gridSetting.gridSchema === schema;
    });
    return gridSetting?.searchInfo?.length > 0;
  }, [gridSettings, dslInfo]);

  // 是否可能表格设置
  const canSetting = useMemo(() => {
    return !dslInfo?.setting?.hideDefaultToolbar?.find(() => 'setting');
  }, [dslInfo]);

  const isShowStatus = useMemo(() => {
    return !isEmpty(AthStatusInfo) && isMainTable;
  }, [AthStatusInfo, isMainTable]);

  const childInfo = useMemo(() => {
    const { slots, columnDefs } = (children || []).reduce(
      (acc, cur) => {
        if (cur.props?.dslInfo?.type === AthenaComponentType.DYNAMIC_OPERATION) {
          acc.slots.push(cur);
        } else {
          acc.columnDefs.push(cur);
        }
        return acc;
      },
      { slots: [], columnDefs: [] },
    );
    return { slots, columnDefs };
  }, [children]);

  const title = dslInfo?.lang?.tableTitle?.[language] ?? dslInfo?.tableTitle;

  return (
    <div className="ath-athena-table">
      {/* {title && <div className="title-bar">{title}</div>} */}
      {isMainTable && (
        <div className="title-bar">
          <Tooltip title={t('dj-关联字段是当前数据源根节点的表格就是主表表格')}>
            {t('dj-主表')}
          </Tooltip>
        </div>
      )}
      <div className="tool-bar">
        <div className="label-group">{title && <div className="label-item">{title}</div>}</div>
        <div className="operation-group">{childInfo.slots}</div>
        <div className="icon-group">
          {isAdvancedSearch && <SearchOutlined />}
          {dslInfo.isSort && <SortAscendingOutlined />}
          {canSetting && <SettingOutlined />}
        </div>
      </div>
      <div className="content">
        {dslInfo.checkbox && (
          <div className="base-item ath-checkbox">
            <div className="header">
              <Checkbox></Checkbox>
            </div>
            <div className="content">
              <Checkbox></Checkbox>
            </div>
          </div>
        )}
        {dslInfo.rowIndex && (
          <div className="base-item ath-row-index">
            <div className="header">{t('dj-序号')}</div>
            <div className="content">
              <span>1</span>
            </div>
          </div>
        )}
        {!childInfo.columnDefs?.length ? (
          <div className="empty-table-container-placeholder  lc-container-placeholder">
            {t('dj-拖拽组件或模板到这里')}
          </div>
        ) : (
          childInfo.columnDefs
        )}
        {isShowStatus && (
          <div className="base-item ath-status">
            <div className="header">{t('dj-状态')}</div>
            <div className="content"></div>
          </div>
        )}
      </div>
    </div>
  );
};

LcdpAthenaTable.displayName = 'LcdpAthenaTable';
export default LcdpAthenaTable;
