.ath-athena-table {
  width: 100%;
  min-height: 210px;
  // position: relative;
  padding: 10px;
  // background-color: rgba(0,0,0,0.1);

  .title-bar {
    font-weight: bold;
    padding: 0 8px;
    font-size: 12px;
    color: #fff;
    background-color: #6a4cff;
    font-weight: bold;
    border-radius: 4px;
    line-height: 24px;
    float: left;
    cursor: default;
  }

  & > .tool-bar {
    // position: absolute;
    // top: 1px;
    // left: 0;
    width: 100%;
    min-height: 60px;
    display: flex;
    padding: 0 10px;
    & > .operation-group {
      display: flex;
      flex: 1;
      flex-wrap: nowrap;
      overflow-x: auto;
      overflow-y: hidden;
      align-items: center;
      white-space: nowrap;
      flex-direction: row-reverse;
      & > .operation {
        flex-shrink: 0;
        flex-grow: 0;
        padding: 0 10px;
        color: #6a4cff;
        font-weight: normal;
        font-size: 12px;
        line-height: 24px;
      }

      & > div {
        flex: 1;
        // padding: 0 10px;
        color: #6a4cff;
        font-weight: normal;
        font-size: 12px;
        min-height: 42px;
      }

      .lc-container-placeholder {
        min-height: 38px;
      }
    }

    & > .icon-group {
      display: flex;
      align-items: center;
      flex-shrink: 0;
      flex-grow: 0;
      & > span {
        flex: 1;
        padding: 0 4px;
        font-size: 16px;
      }
    }

    & > .label-group {
      flex-shrink: 0;
      flex-grow: 0;
      display: flex;
      align-items: center;

      & > .label-item {
        font-size: 14px;
        color: rgb(51, 51, 51);
        font-weight: bold;
        margin-right: 12px;
      }
    }
  }

  & > .content {
    width: 100%;
    height: 140px;
    overflow-x: auto;
    display: flex;

    & > .base-item {
      height: 100%;
      overflow: hidden;
      flex-grow: 0;
      flex-shrink: 0;
      border: 1px solid #ddd;
      border-left: none;
      &:nth-of-type(1) {
        border: 1px solid #ddd;
      }

      & > .header {
        line-height: 40px;
        background-color: #eef0ff;
        color: #383878;
        -webkit-font-smoothing: antialiased;
        font-size: 13px;
        height: 40px;
        border-bottom: 1px solid #dfdfdf;
        font-weight: 700;
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;
        word-wrap: break-word;
        word-break: break-all;
        padding-left: 2px;
        // text-align: center;
      }

      & > .content {
        height: calc(100% - 40px);
        overflow-x: hidden;
        overflow-y: auto;
        background-color: rgb(255, 255, 255);
        padding: 2px;
        // text-align: center;
      }
    }

    & > .ath-checkbox,
    & > .ath-row-index {
      flex-basis: 60px;
      text-align: center;
    }

    & > .ath-checkbox > .content > .ant-checkbox-wrapper {
      line-height: 32px;
    }

    & > .ath-row-index > .content {
      padding-top: 4px;
      & > span {
        display: inline-block;
        line-height: 32px;
      }
    }

    & > .ath-operation-row,
    & > .ath-status {
      flex-basis: 160px;
      & > .content {
        padding-left: 6px;
        & > span {
          color: #6a4cff;
          margin-right: 6px;
          line-height: 24px;
          word-break: keep-all;
          float: left;
        }
      }
    }
  }

  .hidden-dom {
    display: none !important;
  }
}
.empty-table-container-active-placeholder {
  background-color: #c3d9ff !important;
}
