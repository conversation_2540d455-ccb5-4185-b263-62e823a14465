import { createElement } from 'react';
import { Button as AntdButton } from 'antd';
import Icon from '../../common/Icon';
import { getI18n } from '../../tools';

import './Button.scss';

import type { IButtonSchema } from './types';

const positionMap = {
  before: 'start',
  after: 'end',
};

function Button(props: IButtonSchema) {
  const { dslInfo, _leaf } = props;
  const {
    title,
    styleMode,
    size,
    disabled = false,
    ghost = false,
    danger = false,
    block = false,
    shape,
    lang,
    iconConfig,
  } = dslInfo ?? {};
  const { name, position } = iconConfig ?? {};
  const i18n = getI18n(_leaf);

  const cSize = !size || size === 'default' ? 'middle' : size;
  const cShape = !shape ? 'default' : shape;
  const cType = !styleMode ? 'default' : styleMode;
  const cPosition = !position ? 'start' : positionMap[position] ?? 'start';

  const renderIcon = () => {
    return name ? <Icon type={name} className="iconfont" /> : null;
  };

  return (
    <div className="button-layout-wrapper" style={{ display: block ? 'block' : 'inline-block' }}>
      <AntdButton
        className={`button-layout ${disabled ? 'button-layout-disabled' : ''}`}
        size={cSize}
        shape={cShape}
        type={cType}
        ghost={ghost}
        autoInsertSpace={false}
        danger={danger}
        block={block}
        icon={renderIcon()}
        iconPosition={cPosition}
      >
        {lang?.title?.[i18n?.language] ?? title ?? ''}
      </AntdButton>
    </div>
  );
}

Button.displayName = 'Button';
export default Button;
