import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const LcdpAmountInputMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.AMOUNT_INPUT,
  title: 'dj-金额',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonBasicSetter.currencyField },
          { ...commonDataTypeSetter('AMOUNT_INPUT') },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.AMOUNT_INPUT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-金额',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/AMOUNT_INPUT.svg`,
    schema: {
      componentName: AthenaComponentType.AMOUNT_INPUT,
      title: 'dj-金额',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.AMOUNT_INPUT,
          headerName: '金额',
          placeholder: '请输入',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          currencyField: '',
          dataType: AthenaDataType.NUMERIC,
          iconType: '',
          tooltipMode: 'normal', // 有注释iconType的都要加
          lang: {
            headerName: {
              zh_CN: '金额',
              zh_TW: '金額',
              en_US: 'amount',
            },
            placeholder: {
              zh_CN: '请输入',
              zh_TW: '請輸入',
              en_US: 'please enter',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...LcdpAmountInputMeta,
  snippets,
};
