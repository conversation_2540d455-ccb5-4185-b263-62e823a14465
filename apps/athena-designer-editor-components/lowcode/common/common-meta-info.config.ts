import {
  IPublicTypeFieldConfig,
  IPublicModelSettingField,
  IPublicEnumTransformStage,
} from '@alilc/lowcode-types';
import {
  AthenaComponentType,
  DataTypeList,
  IconSelectList,
  UseApproveOptionComponents,
  VocabularyComponents,
} from './common.config';
import { AthenaDataType } from './common.type';
import { BusinessButtonTypeSet, SubmitButtonTypeSet } from '../button/constant';
import { cloneDeep } from 'lodash';

// 全局设置
export const commonAthMonacoEditorSetter: IPublicTypeFieldConfig = {
  title: 'dj-全局设置',
  type: 'group',
  display: 'accordion',
  items: [
    {
      setter: {
        componentName: 'AthMonacoEditorSetter',
        isDynamic: false,
        props: {
          options: {
            showText: '配置',
            monacoEditorProps: {
              type: 'json',
              title: '配置</>',
            },
          },
        },
      },
      getValue: (target) => {
        return target?.node?.getPropValue('dslInfo');
      },
      setValue: (target, value) => {
        target.node?.setPropValue('dslInfo', value);
        // 在这里做个记录，找到更合适的方法时可以优化
        // setPropValue 对应的 path ，name 或者 getValue 对应 path 触发render没问题
        // 非对应的情况下 可能会 不触发 render，虽然 value 已经 update，具体原因 和 lowcode 的 get 等逻辑 有关
        // 解决方式 就是 get时 取 父路径 或者 set时 set 子路径
        // 这里 对 第一层 属性 setValue，提升兼容性
        Object.keys(value).forEach((key) => {
          target.node?.setPropValue(`dslInfo.${key}`, value[key]);
        });

        // 以下只是一些之前的尝试，可以忽略
        // const schema = target.node?.exportSchema(IPublicEnumTransformStage.Save);
        // if (!schema) return;

        // 会刷新，体验不好
        // target.node?.replaceWith({
        //   ...schema,
        //   props: value,
        // });

        // target?.node?.clearPropValue
        // target?.node?.setProps()

        // 从node上删除被删除的属性（没找到全量操作的api，replaceWith会刷新，体验不好）
        // for (const key in target.node?.propsData) {
        //   if (!Reflect.has(value, key)) target.node?.setPropValue(key, null);
        // }

        // 更新属性
        // for (const key in value) {
        //   target.node?.setPropValue(key, value[key]);
        // }
      },
    },
  ],
};

// 数据类型
export const commonDataSourceNamesSetter: IPublicTypeFieldConfig = {
  name: 'dslInfo.queryInfo.dataFilter.dataSourceNames',
  getValue: (target) => {
    const dslInfo = target?.node?.getPropValue('dslInfo');
    return dslInfo.queryInfo?.dataFilter?.dataSourceNames?.[0];
  },
  setValue: (target, value) => {
    const { dataSourceName, clear } = value;
    target?.node?.setPropValue(
      'dslInfo.queryInfo.dataFilter.dataSourceNames',
      dataSourceName ? [dataSourceName] : [],
    );
    if (!clear) return;
    // 所有都要清path和schema
    target?.node?.setPropValue('dslInfo.schema', '');
    target?.node?.setPropValue('dslInfo.path', '');
    // 如果是表格，清空排序和选项
    if (target?.node?.getPropValue('dslInfo')?.type === AthenaComponentType.ATHENA_TABLE) {
      target?.node?.setPropValue('dslInfo.setting.orderFields', null);
      target?.node?.setPropValue('dslInfo.setting.options', null);
    }
    target.node?.children?.mergeChildren(()=>{return true}, ()=>{return false}, ()=> {return false})
  },
  setter: {
    componentName: 'LcdpDataSourceNamesSetter',
    isDynamic: false,
    props: {
      options: {
        titleProps: {
          setterTitle: 'dj-数据源',
        },
        componentType: 'lang',
      },
    },
  },
};

// 关联字段
export const commonAthAssociationFieldSetter: IPublicTypeFieldConfig = {
  title: 'dj-关联字段',
  type: 'group',
  display: 'accordion',
  items: [
    {
      getValue: (target) => {
        const dslInfo = target?.node?.getPropValue('dslInfo');
        const { schema, path } = dslInfo;
        return { schema, path };
      },
      setValue: (target, value) => {
        const { schema, path } = value;
        target?.node?.setPropValue('dslInfo.schema', schema);
        target?.node?.setPropValue('dslInfo.path', path);
      },
      setter: {
        componentName: 'AthSelectAssociationFieldSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'dj-关联字段',
            },
          },
        },
      },
    },
    {
      name: 'dslInfo.schema',
      setter: {
        componentName: 'AthCommonSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'schema',
            },
            formItemRules: [
              {
                required: true,
                message: '不可为空',
              },
            ],
          },
        },
      },
    },
    {
      name: 'dslInfo.path',
      setter: {
        componentName: 'AthCommonSetter',
        isDynamic: false,
        props: {
          options: {
            titleProps: {
              setterTitle: 'path',
            },
          },
        },
      },
    },
  ],
};

export const commonAthAssociationFieldSchemaNotRequiredSetter: IPublicTypeFieldConfig = {
  ...cloneDeep(commonAthAssociationFieldSetter),
};

// @ts-ignore
commonAthAssociationFieldSchemaNotRequiredSetter.items[1].setter.props.options.formItemRules = [];

// 规则
export const commonAthRuleSetter: IPublicTypeFieldConfig = {
  title: 'dj-规则（关联属性）',
  display: 'accordion',
  condition: (target) => {
    const utils = target?.node?.document?.project?.simulator?.renderer?.context?.utils;
    const athDynamicWorkDesignInfo = utils?.getConfigByKey('AthDynamicWorkDesignInfo');
    return ['basic-data', 'edit-page', 'sub-page'].includes(athDynamicWorkDesignInfo?.pageCode);
  },
  getValue: (target) => {
    const dslInfo = target?.node?.getPropValue('dslInfo');
    return dslInfo;
  },
  setter: {
    isDynamic: false,
    componentName: 'AthRulesSetter',
  },
};

// 提示类设置
export const tipSetter: IPublicTypeFieldConfig[] = [
  {
    // extraContent: { type: 'i18n', 'zh-CN': '辅助提示', 'en-US': 'placeholder' },
    name: 'dslInfo.lang.extraContent',
    setValue: (target, value) => {
      target?.node?.setPropValue('dslInfo.extraContent', value['zh_CN']);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-辅助提示',
          },
          componentType: 'lang',
        },
      },
    },
  },
  {
    // extraContent: { type: 'i18n', 'zh-CN': '注释说明', 'en-US': 'placeholder' },
    name: 'dslInfo.lang.tooltipTitle',
    setValue: (target, value) => {
      target?.node?.setPropValue('dslInfo.tooltipTitle', value['zh_CN']);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-注释说明',
          },
          componentType: 'lang',
        },
      },
    },
  },
  {
    name: 'dslInfo.iconType',
    condition: (target: IPublicModelSettingField) => {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return !!dslInfo?.tooltipTitle;
    },
    setter: {
      componentName: 'AthCommonSetter',
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-注释图标',
          },
          componentType: 'iconSelect',
          componentProps: {
            options: [...IconSelectList],
          },
        },
      },
    },
  },
];

// 上传设置
export const commonUploadSetter: IPublicTypeFieldConfig[] = [
  {
    name: 'dslInfo.attribute.uploadEnable',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否可上传文件',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  {
    name: 'dslInfo.attribute.uploadCategory',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-上传目录',
          },
          formItemRules: [
            {
              required: true,
              message: 'dj-不可为空',
            },
          ],
        },
      },
    },
  },
  {
    name: 'dslInfo.attribute.fileExtensions',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-上传文件类型(多个以英文逗号 , 分隔)',
          },
        },
      },
    },
    getValue: (target, value) => {
      return value.toString();
    },
    setValue: (target, value) => {
      target?.node?.setPropValue('dslInfo.attribute.fileExtensions', value.split(','));
    },
  },
  {
    name: 'dslInfo.attribute.fileCount',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-最大上传文件个数',
          },
          componentType: 'number',
        },
      },
    },
  },
  {
    name: 'dslInfo.attribute.fileMaxSize',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-单文件最大上传大小',
          },
          componentType: 'number',
        },
      },
    },
  },
  {
    name: 'dslInfo.buckets',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'buckets',
          },
        },
      },
    },
  },
  {
    name: 'dslInfo.attribute.draggable',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-拖拽上传',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  {
    name: 'dslInfo.attribute.disableAam',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否为API级别附件',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  {
    name: 'dslInfo.attribute.enableEffectAfterSubmit',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-提交后才会生效',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  {
    name: 'dslInfo.attribute.onlyDeleteByOwner',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否只允许上传者删除',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
];

// 数据类型
export const commonDataTypeSetter = (type: string): IPublicTypeFieldConfig => ({
  name: 'dslInfo.dataType',
  setter: {
    componentName: 'AthCommonSetter',
    props: {
      options: {
        titleProps: {
          setterTitle: 'dj-数据类型',
        },
        componentType: 'select',
        componentProps: {
          options: [...DataTypeList[type]],
        },
      },
    },
  },
});

// 通用组装
export const commonBasicSetter = {
  showInput: {
    name: 'dslInfo.showInput',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否可输入',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  isFocusDisplay: {
    name: 'dslInfo.isFocusDisplay',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-标题在内',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  placeholder: {
    name: 'dslInfo.lang.placeholder',
    setValue: (target, value) => {
      target?.node?.setPropValue('dslInfo.placeholder', value['zh_CN']);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-占位提示',
          },
          componentType: 'lang',
        },
      },
    },
  },
  currencyField: {
    name: 'dslInfo.currencyField',
    setter: {
      componentName: 'AthCommonSetter',
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-单位字段',
          },
        },
      },
    },
  },
  filterKey: {
    // 上传设置：表格附件独有
    name: 'dslInfo.attribute.filterKey',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-表格中附件支持表头过滤的key',
          },
        },
      },
    },
  },
  headerName: {
    name: 'dslInfo.lang.headerName',
    setValue: (target, value) => {
      target?.node?.setPropValue('dslInfo.headerName', value['zh_CN']);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-标题',
          },
          componentType: 'lang',
        },
      },
    },
  },
  componentTypeExchange: {
    name: 'dslInfo.type',
    getValue(target) {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return { ...dslInfo };
    },
    setValue(target, value) {
      const { dslInfo, titleValue } = value;
      let componentName = dslInfo.type;
      if (SubmitButtonTypeSet?.has(dslInfo.type) || BusinessButtonTypeSet?.has(dslInfo.type)) {
        componentName = 'BUTTON';
      }

      target.node?.replaceWith({
        componentName: componentName,
        ...titleValue,
        props: {
          dslInfo: { ...dslInfo },
        },
      });
    },
    setter: {
      componentName: 'LcdpComponentTypeExchangeSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {},
        },
      },
    },
  },
  openSource: {
    name: 'dslInfo.openSource',
    getValue(target) {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return { ...dslInfo };
    },
    setter: {
      componentName: 'LcdpOpenSourceSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {},
        },
      },
    },
  },
  status: {
    name: 'dslInfo.disabled',
    setter: {
      isDynamic: false,
      componentName: 'AthCommonSetter',
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否禁用',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
    // getValue: (target) => {
    //   return !!target.node?.getPropValue('dslInfo.disabled');
    // },
    setValue: (target, value) => {
      target.node?.setPropValue('dslInfo.editable', !value);
      target.node?.setPropValue('dslInfo.disabled', !!value);
    },
  },
  schemas: {
    getValue(target) {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.schemas;
    },
    setValue(target, value) {
      target.node?.setPropValue('dslInfo.schemas', value);
    },
    setter: {
      componentName: 'AthSchemasSelectSetter',
      isDynamic: false,
    },
  },
  enableTrim: {
    name: 'dslInfo.enableTrim',
    condition: (target: IPublicModelSettingField) => {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.dataType !== AthenaDataType.NUMERIC;
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-去除前后空格',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  important: {
    name: 'dslInfo.important',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否重要',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  className: {
    name: 'dslInfo.className',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-样式类名',
            layout: 'horizontal',
          },
          componentType: 'select',
          componentProps: {
            size: 'small',
            options: [
              { label: '无', value: 'none' },
              { label: 'background-grey', value: 'background-grey' },
            ],
          },
        },
      },
    },
  },
  enableSearch: {
    name: 'dslInfo.enableSearch',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-是否支持搜索',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  expandAll: {
    name: 'dslInfo.expandAll',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-默认展开',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  options: {
    // 下拉单选、下拉多选、单选框、方案选择
    getValue(target) {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      const { options, dictionaryId, dictionaryKey, enumKey, type } = dslInfo;
      return {
        type,
        useVocabulary: VocabularyComponents.includes(type), // 是否使用下拉词汇
        useApprove: UseApproveOptionComponents.includes(type), // 是否选项中需要冗余approve
        options,
        dictionaryId,
        dictionaryKey,
        enumKey,
      };
    },
    setValue(target, value) {
      const { type } = target?.node?.getPropValue('dslInfo');
      const { options, dictionaryId, dictionaryKey = '', enumKey = '' } = value;
      target.node?.setPropValue('dslInfo.options', options);
      if (VocabularyComponents.includes(type)) {
        target.node?.setPropValue('dslInfo.dictionaryId', dictionaryId);
        target.node?.setPropValue('dslInfo.dictionaryKey', dictionaryKey);
        target.node?.setPropValue('dslInfo.enumKey', enumKey);
      }
    },
    setter: {
      componentName: 'LcdpOptionsSetter',
      isDynamic: false,
      props: {
        move: true,
        delete: true,
        edit: true,
      },
    },
  },
  nzCheckStrictly: {
    name: 'dslInfo.nzCheckStrictly',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-父子节点选中是否关联',
            layout: 'horizontal',
          },
          componentType: 'switch',
          componentProps: {
            size: 'small',
          },
        },
      },
    },
  },
  nzMaxTagCount: {
    name: 'dslInfo.nzMaxTagCount',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-最多显示的tag数',
          },
          componentType: 'number',
        },
      },
    },
  },
};

// 数字输入
export const numberSetter: IPublicTypeFieldConfig[] = [
  {
    name: 'dslInfo.dataPrecision.length',
    condition: (target: IPublicModelSettingField) => {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.dataType === AthenaDataType.NUMERIC;
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-总长度',
          },
          componentType: 'number',
        },
      },
    },
  },
  {
    name: 'dslInfo.dataPrecision.place',
    condition: (target: IPublicModelSettingField) => {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.dataType === AthenaDataType.NUMERIC;
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-小数精度',
          },
          componentType: 'number',
        },
      },
    },
  },
  {
    name: 'dslInfo.min',
    condition: (target: IPublicModelSettingField) => {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.dataType === AthenaDataType.NUMERIC;
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-最小值',
          },
          componentType: 'number',
        },
      },
    },
  },
  {
    name: 'dslInfo.max',
    condition: (target: IPublicModelSettingField) => {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.dataType === AthenaDataType.NUMERIC;
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-最大值',
          },
          componentType: 'number',
        },
      },
    },
  },
  {
    name: 'dslInfo.step',
    condition: (target: IPublicModelSettingField) => {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.dataType === AthenaDataType.NUMERIC;
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-步长',
          },
          componentType: 'number',
        },
      },
    },
  },
];

// 新旧值
export const differenceSetter: IPublicTypeFieldConfig[] = [
  {
    getValue(target) {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.fields[0]?.schema;
    },
    setValue(target, value) {
      const { fields } = target?.node?.getPropValue('dslInfo');
      fields[0].schema = value;
      target.node?.setPropValue('dslInfo.fields', fields);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-新值schema',
          },
        },
      },
    },
  },
  {
    getValue(target) {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.fields[0]?.unit;
    },
    setValue(target, value) {
      const { fields } = target?.node?.getPropValue('dslInfo');
      fields[0].unit = value;
      target.node?.setPropValue('dslInfo.fields', fields);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-新值单位schema',
          },
        },
      },
    },
  },
  {
    getValue(target) {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.fields[1]?.schema;
    },
    setValue(target, value) {
      const { fields } = target?.node?.getPropValue('dslInfo');
      fields[1].schema = value;
      target.node?.setPropValue('dslInfo.fields', fields);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-旧值schema',
          },
        },
      },
    },
  },
  {
    getValue(target) {
      const dslInfo = target?.node?.getPropValue('dslInfo');
      return dslInfo?.fields[1]?.unit;
    },
    setValue(target, value) {
      const { fields } = target?.node?.getPropValue('dslInfo');
      fields[1].unit = value;
      target.node?.setPropValue('dslInfo.fields', fields);
    },
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-旧值单位schema',
          },
        },
      },
    },
  },
];

// 文本域
export const textareaSetter: IPublicTypeFieldConfig[] = [
  {
    name: 'dslInfo.dataPrecision.length',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-长度',
          },
          componentType: 'number',
        },
      },
    },
  },
  {
    name: 'dslInfo.maxRows',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-最大行数',
          },
          componentType: 'number',
        },
      },
    },
  },
  {
    name: 'dslInfo.minRows',
    setter: {
      componentName: 'AthCommonSetter',
      isDynamic: false,
      props: {
        options: {
          titleProps: {
            setterTitle: 'dj-最小行数',
          },
          componentType: 'number',
        },
      },
    },
  },
];
