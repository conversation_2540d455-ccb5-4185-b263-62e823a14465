import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthMeasureMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.MEASURE,
  title: 'dj-计量组件',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder }, // 占位提示
          ...tipSetter, // 辅助提示+注释说明+注释图标
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay }, // 启用标题在内
          { ...commonDataTypeSetter('MEASURE') }, // 数据类型
          {
            name: 'dslInfo.currencyField',
            setValue: (target, value) => {
              target?.node?.setPropValue('dslInfo.unitSchema', value);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'unitSchema', // 多语言：单位字段
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.MEASURE) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-计量组件',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/MEASURE.svg`,
    schema: {
      componentName: AthenaComponentType.MEASURE,
      title: 'dj-计量组件',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.MEASURE,
          headerName: '计量组件',
          placeholder: '请输入',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          dataType: AthenaDataType.NUMERIC,
          tooltipMode: 'normal', // 有注释iconType的都要加
          iconType: '',
          dataPrecision: {
            length: 80,
            place: 5,
          },
          thousandthPercentile: true,
          important: false,
          fieldType: 'decimal',
          requiredConfig: { type: 'required', selected: false },
          currencyField: '', // 单位字段
          queryCurrencyApi: {
            tmAction: {
              sequence: 0,
              type: 'ESP',
              actionId: 'esp_unit.decimal.places.get',
              actionParams: [
                {
                  name: 'unit_data.unit_no', //数量单位字段
                  type: 'RAW_ARRAY_PARAS',
                  value: 'unit',
                  required: false,
                },
              ],
              override: false,
            },
          },
          queryCurrencyAPIReturnFields: {
            decimal_places: 'unit_decimal_places',
          },
          lang: {
            headerName: {
              zh_CN: '计量组件',
              zh_TW: '計量組件',
              en_US: 'Measure',
            },
            placeholder: {
              zh_CN: '请输入',
              zh_TW: '請輸入',
              en_US: 'please enter',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthMeasureMeta,
  snippets,
};
