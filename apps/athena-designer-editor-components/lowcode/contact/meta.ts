import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const ContactMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.CONTACT,
  title: 'dj-采购员',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonShow',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.CONTACT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-采购员',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/CONTACT.svg`,
    schema: {
      componentName: AthenaComponentType.CONTACT,
      title: 'dj-采购员',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.CONTACT,
          headerName: '采购员',
          schema: '',
          path: '',
          lang: {
            headerName: {
              zh_CN: '采购员',
              zh_TW: '採購員',
              en_US: 'Contact',
            },
          },
        },
      },
    },
  },
];

export default {
  ...ContactMeta,
  snippets,
};
