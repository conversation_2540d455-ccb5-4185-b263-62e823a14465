import {
  IPublicModelNode,
  IPublicTypeComponentMetadata,
  IPublicModelSettingField,
} from '@alilc/lowcode-types';

import { FLEX } from './common/config';

const defaultStyle = {
  justifyContent: 'flex-start',
  alignItems: 'flex-start',
  flexDirection: 'row',
  flexWrap: 'wrap',
  alignContent: '',
  rowGap: '',
  columnGap: '',
  gap: '16px',
  width: 'auto',
  height: 'auto',
  minWidth: '',
  maxWidth: '',
  minHeight: '',
  maxHeight: '',
  margin: '',
  padding: '',
  overflow: 'auto',
  position: '',
  top: '',
  left: '',
  right: '',
  bottom: '',
  background: '',
  border: '',
  borderRadius: '',
  boxShadow: '',

  flexGrow: '1',
  flexShrink: '1',
  flexBasis: 'auto',
};

function disableDivider() {
  const iframe = window.AliLowCodeEngine.project.simulatorHost;
  iframe && iframe.contentWindow?.dispatchEvent(new Event('dividerDisable'));
}
function enableDivider() {
  const iframe = window.AliLowCodeEngine.project.simulatorHost;
  iframe && iframe.contentWindow?.dispatchEvent(new Event('dividerEnable'));
}

export const defaultSnippet = {
  title: 'dj-弹性布局组件',
  screenshot:
    'https://img.alicdn.com/imgextra/i2/O1CN01B1NMW926IFrFxjqQT_!!6000000007638-55-tps-56-56.svg',
  schema: {
    componentName: FLEX,
    props: {
      dslInfo: {
        type: FLEX,
        ...defaultStyle,
      },
      style: {
        ...defaultStyle,
      },
    },
  },
};

const config: IPublicTypeComponentMetadata = {
  componentName: FLEX,
  title: 'dj-弹性布局组件',
  category: 'dj-容器组件',
  group: 'dj-标准组件',
  icon:
    'https://img.alicdn.com/imgextra/i1/O1CN01AQZw941ZgdfVtjsDO_!!6000000003224-55-tps-128-128.svg',
  docUrl: '',
  screenshot: '',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpFlex',
    main: '',
    destructuring: true,
    subName: '',
  },
  props: [],
  configure: {
    component: {
      isContainer: true,
    },
    supports: {
      // style: true,
      loop: false,
    },
    advanced: {
      getResizingHandlers: (node: IPublicModelNode) => {
        const directionList: string[] = [];
        const parentNode = node.parent;

        const pFlexDirection = parentNode?.getPropValue('dslInfo.flexDirection');
        const isRow = ['row', 'row-reverse'].includes(pFlexDirection);

        if (isRow) {
          directionList.push('w');
          directionList.push('e');
        }

        return directionList;
      },
      callbacks: {
        onResizeStart(
          e: MouseEvent & {
            trigger: string;
            deltaX?: number;
            deltaY?: number;
          },
          currentNode: IPublicModelNode,
        ) {
          disableDivider();

          currentNode.startRect = currentNode.getRect();
          currentNode.siblingNode =
            e.trigger === 'n' || e.trigger === 'w'
              ? currentNode.prevSibling
              : currentNode.nextSibling;
          currentNode.siblingRect = currentNode.siblingNode
            ? currentNode.siblingNode.getRect()
            : null;
        },
        onResize(
          e: MouseEvent & {
            trigger: string;
            deltaX?: number;
            deltaY?: number;
          },
          currentNode: IPublicModelNode,
        ) {
          const { deltaY, deltaX } = e;
          const { height: startHeight, width: startWidth } = currentNode.startRect;

          if (e.trigger === 'e' || e.trigger === 'w') {
            const newWidth = e.trigger === 'w' ? startWidth - deltaX : startWidth + deltaX;

            currentNode.getDOMNode().style.width = `${Math.floor(newWidth)}px`;
            currentNode.getDOMNode().style.flexGrow = `0`;
            currentNode.getDOMNode().style.flexShrink = `0`;
            currentNode.getDOMNode().style.flexBasis = `${Math.floor(newWidth)}px`;

            currentNode.setPropValue('style.width', `${Math.floor(newWidth)}px`);
            currentNode.setPropValue('style.flexGrow', `0`);
            currentNode.setPropValue('style.flexShrink', `0`);
            currentNode.setPropValue('style.flexBasis', `${Math.floor(newWidth)}px`);
          } else if (e.trigger === 's' || e.trigger === 'n') {
            const newHeight = e.trigger === 'n' ? startHeight - deltaY : startHeight + deltaY;

            currentNode.setPropValue('style.minHeight', newHeight);
            currentNode.getDOMNode().style.flex = '0 0 auto';
          }
        },
        onResizeEnd(
          e: MouseEvent & {
            trigger: string;
            deltaX?: number;
            deltaY?: number;
          },
          currentNode: IPublicModelNode,
        ) {
          enableDivider();

          const style = currentNode.getPropValue('style');
          const dslInfo = currentNode.getPropValue('dslInfo');

          if (dslInfo?.width.includes('%')) {
            const { width: nodeWidth } = currentNode.getRect() ?? {};
            const { width: parentWidth } = currentNode.parent?.getRect() ?? {};

            if (nodeWidth && parentWidth) {
              const width = `${Math.floor((nodeWidth / parentWidth) * 100)}%`;
              style.width = width;
              style.flexBasis = width;
            }
          }

          currentNode.setPropValue('dslInfo', {
            ...dslInfo,
            ...style,
          });
        },
      },
    },
    props: [
      // {
      //   name: 'dslInfo.lang.title',
      //   setValue: (target, value) => {
      //     target?.node?.setPropValue('dslInfo.title', value['zh_CN']);
      //   },
      //   setter: {
      //     componentName: 'AthCommonSetter',
      //     isDynamic: false,
      //     props: {
      //       options: {
      //         titleProps: {
      //           setterTitle: 'dj-标题',
      //         },
      //         componentType: 'lang',
      //       },
      //     },
      //   },
      // },
      {
        type: 'group',
        title: 'dj-布局',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.flexDirection',
            setValue: (target: IPublicModelSettingField, value: any) => {
              const flexDirection = value;
              target?.node?.setPropValue('dslInfo.flexDirection', flexDirection);
              target?.node?.setPropValue('style.flexDirection', flexDirection);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-布局方向',
                  },
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'row',
                        icon: 'iconshuipingfangxiangqidianzuo',
                        tooltip: 'dj-水平方向起点在左侧',
                      },
                      {
                        value: 'row-reverse',
                        icon: 'iconshuipingfangxiangqidianyou',
                        tooltip: 'dj-水平方向起点在右侧',
                      },
                      {
                        value: 'column',
                        icon: 'iconchuizhifangxiangqidianshang',
                        tooltip: 'dj-垂直方向起点在上沿',
                      },
                      {
                        value: 'column-reverse',
                        icon: 'iconchuizhifangxiangqidianxia',
                        tooltip: 'dj-垂直方向起点在下沿',
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.justifyContent',
            setValue: (target: IPublicModelSettingField, value: any) => {
              const justifyContent = value;
              target?.node?.setPropValue('dslInfo.justifyContent', justifyContent);
              target?.node?.setPropValue('style.justifyContent', justifyContent);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-主轴对齐',
                  },
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'flex-start',
                        icon: 'iconzuoduiqi2',
                        tooltip: 'dj-左对齐',
                      },
                      {
                        value: 'flex-end',
                        icon: 'iconyouduiqi2',
                        tooltip: 'dj-右对齐',
                      },
                      {
                        value: 'center',
                        icon: 'iconshuipingjuzhong',
                        tooltip: 'dj-水平居中',
                      },
                      {
                        value: 'space-between',
                        icon: 'iconliangduanduiqi3',
                        tooltip: 'dj-两端对齐',
                      },
                      {
                        value: 'space-around',
                        icon: 'iconhengxiangpingfen',
                        tooltip: 'dj-横向平分',
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.alignItems',
            setValue: (target: IPublicModelSettingField, value: any) => {
              const alignItems = value;
              target?.node?.setPropValue('dslInfo.alignItems', alignItems);
              target?.node?.setPropValue('style.alignItems', alignItems);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-辅轴对齐',
                  },
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'flex-start',
                        icon: 'iconqidianduiqi',
                        tooltip: 'dj-起点对齐',
                      },
                      {
                        value: 'flex-end',
                        icon: 'iconzhongdianduiqi',
                        tooltip: 'dj-终点对齐',
                      },
                      {
                        value: 'center',
                        icon: 'iconshuipingjuzhong1',
                        tooltip: 'dj-水平居中',
                      },
                      {
                        value: 'baseline',
                        icon: 'icondiyihangwenzijixianduiqi',
                        tooltip: 'dj-项目第一行文字的基线对齐',
                      },
                      {
                        value: 'stretch',
                        icon: 'iconzhanmanrongqigaodu',
                        tooltip: 'dj-占满整个容器的高度',
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.flexWrap',
            setValue: (target: IPublicModelSettingField, value: any) => {
              const flexWrap = value;
              target?.node?.setPropValue('dslInfo.flexWrap', flexWrap);
              target?.node?.setPropValue('style.flexWrap', flexWrap);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-换行',
                  },
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'nowrap',
                        label: '不换行',
                      },
                      {
                        value: 'wrap',
                        label: '正换行',
                      },
                      {
                        value: 'wrap-reverse',
                        label: '逆换行',
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.gap',
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return parseInt(dslInfo?.gap, 10);
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const gap = `${value}px`;
              target?.node?.setPropValue('dslInfo.gap', gap);
              target?.node?.setPropValue('style.gap', gap);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-间距',
                  },
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 200,
                    min: 0,
                    step: 1,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 0,
                      max: 200,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              if (dslInfo?.width === 'auto') {
                const flexGrow = target.node?.getPropValue('dslInfo.flexGrow');
                if (flexGrow === '1') return 'stretch';
                return 'auto';
              }

              if (dslInfo?.width.includes('%')) return 'relative';
              return 'fix';
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              let width = 'auto';
              if (value === 'relative') {
                const { width: nodeWidth } = target.node?.getRect() ?? {};
                const { width: parentWidth } = target.node?.parent?.getRect() ?? {};

                if (nodeWidth && parentWidth) {
                  width = `${Math.floor((nodeWidth / parentWidth) * 100)}%`;
                }
              }

              if (value === 'fix') width = `${Math.floor(target.node?.getRect()?.width ?? 100)}px`;

              target?.node?.setPropValue('dslInfo.width', width);
              target?.node?.setPropValue('style.width', width);
              target?.node?.setPropValue('dslInfo.flexGrow', '0');
              target?.node?.setPropValue('style.flexGrow', '0');
              target?.node?.setPropValue('dslInfo.flexShrink', '0');
              target?.node?.setPropValue('style.flexShrink', '0');

              const pFlexDirection = target.node?.parent?.getPropValue('dslInfo.flexDirection');
              if (['row', 'row-reverse'].includes(pFlexDirection)) {
                target?.node?.setPropValue('dslInfo.flexBasis', width);
                target?.node?.setPropValue('style.flexBasis', width);
              }

              if (value === 'stretch') {
                target?.node?.setPropValue('dslInfo.flexGrow', '1');
                target?.node?.setPropValue('style.flexGrow', '1');
                target?.node?.setPropValue('dslInfo.flexShrink', '1');
                target?.node?.setPropValue('style.flexShrink', '1');
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-宽度控制',
                  },
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'stretch',
                        label: 'dj-拉伸',
                      },
                      {
                        value: 'auto',
                        label: 'dj-适应',
                      },
                      {
                        value: 'fix',
                        label: 'dj-固定',
                      },
                      {
                        value: 'relative',
                        label: 'dj-相对',
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.width',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return dslInfo?.width.includes('px');
            },
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return parseInt(dslInfo?.width, 10);
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const width = `${value}px`;
              target?.node?.setPropValue('dslInfo.width', width);
              target?.node?.setPropValue('style.width', width);

              const pFlexDirection = target.node?.parent?.getPropValue('dslInfo.flexDirection');
              if (['row', 'row-reverse'].includes(pFlexDirection)) {
                target?.node?.setPropValue('dslInfo.flexBasis', width);
                target?.node?.setPropValue('style.flexBasis', width);
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-宽度值',
                  },
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 1000,
                    min: 0,
                    step: 10,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 0,
                      max: 1000,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.width',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return dslInfo?.width.includes('%');
            },
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return parseInt(dslInfo?.width, 10);
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const width = `${value}%`;
              target?.node?.setPropValue('dslInfo.width', width);
              target?.node?.setPropValue('style.width', width);

              const pFlexDirection = target.node?.parent?.getPropValue('dslInfo.flexDirection');
              if (['row', 'row-reverse'].includes(pFlexDirection)) {
                target?.node?.setPropValue('dslInfo.flexBasis', width);
                target?.node?.setPropValue('style.flexBasis', width);
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-宽度值',
                  },
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: '%',
                    max: 200,
                    min: 0,
                    step: 5,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 0,
                      max: 200,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              if (dslInfo?.height === 'auto') return 'auto';
              return 'fix';
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const height =
                value === 'auto' ? 'auto' : `${Math.floor(target.node?.getRect()?.height ?? 60)}px`;
              target?.node?.setPropValue('dslInfo.height', height);
              target?.node?.setPropValue('style.height', height);

              const pFlexDirection = target.node?.parent?.getPropValue('dslInfo.flexDirection');
              if (['column', 'column-reverse'].includes(pFlexDirection)) {
                target?.node?.setPropValue('dslInfo.flexBasis', height);
                target?.node?.setPropValue('style.flexBasis', height);
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-高度控制',
                  },
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'auto',
                        label: 'dj-适应内容',
                      },
                      {
                        value: 'fix',
                        label: 'dj-固定高度',
                      },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.height',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return dslInfo?.height !== 'auto';
            },
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return parseInt(dslInfo?.height, 10);
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const height = `${value}px`;
              target?.node?.setPropValue('dslInfo.height', height);
              target?.node?.setPropValue('style.height', height);

              const pFlexDirection = target.node?.parent?.getPropValue('dslInfo.flexDirection');
              if (['column', 'column-reverse'].includes(pFlexDirection)) {
                target?.node?.setPropValue('dslInfo.flexBasis', height);
                target?.node?.setPropValue('style.flexBasis', height);
              }
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-高度值',
                  },
                  componentType: 'number',
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 1000,
                    min: 0,
                    step: 10,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 0,
                      max: 1000,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
        ],
      },
      {
        type: 'group',
        title: 'dj-样式',
        display: 'accordion',
        items: [
          {
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo.border;
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const border = !!value ? '1px solid #e5e6eb' : '';
              target?.node?.setPropValue('dslInfo.border', border);
              target?.node?.setPropValue('style.border', border);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-边框',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo.borderRadius;
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const borderRadius = !!value ? '8px' : '';
              target?.node?.setPropValue('dslInfo.borderRadius', borderRadius);
              target?.node?.setPropValue('style.borderRadius', borderRadius);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-圆角',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            getValue: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo.boxShadow;
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const boxShadow = !!value ? '0 3px 8px 0px rgba(30, 33, 43, 0.04)' : '';
              target?.node?.setPropValue('dslInfo.boxShadow', boxShadow);
              target.node?.setPropValue('style.boxShadow', boxShadow);
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-阴影',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.background',
            setValue: (target: IPublicModelSettingField, value: string) => {
              target.node?.setPropValue('style.background', value);
            },
            setter: {
              isDynamic: false,
              componentName: 'LcdpColorPickerSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-背景色',
                    layout: 'horizontal',
                  },
                },
              },
            },
          },
          {
            getValue: (target: IPublicModelSettingField) => {
              const { margin, padding } = target.node?.getPropValue('dslInfo') ?? {};
              const marginList = margin?.replace(/\s+/g, ' ')?.split(' ') ?? [];
              const paddingList = padding?.replace(/\s+/g, ' ')?.split(' ') ?? [];
              return {
                margin: marginList,
                padding: paddingList,
              };
            },
            setValue: (target: IPublicModelSettingField, value: any) => {
              const { margin: marginList, padding: paddingList } = value;
              const margin = marginList.map((item: any) => item || '0').join(' ');
              const padding = paddingList.map((item: any) => item || '0').join(' ');
              target.node?.setPropValue('dslInfo.margin', margin);
              target.node?.setPropValue('dslInfo.padding', padding);
              target.node?.setPropValue('style.margin', margin);
              target.node?.setPropValue('style.padding', padding);
            },
            setter: {
              componentName: 'AthLayoutBoxSetter',
              isDynamic: false,
              props: {
                setterTitle: 'dj-内外边距',
              },
            },
          },

          {
            getValue: (target: IPublicModelSettingField) => {
              const { position, top, left, right, bottom } =
                target.node?.getPropValue('dslInfo') ?? {};
              return {
                position,
                top,
                left,
                right,
                bottom,
              };
            },
            setValue: (target: IPublicModelSettingField, value = {}) => {
              const dslInfo = target.node?.getPropValue('dslInfo') ?? {};
              target.node?.setPropValue('dslInfo', {
                ...dslInfo,
                ...value,
              });
              const style = target.node?.getPropValue('style') ?? {};
              target.node?.setPropValue('style', {
                ...style,
                ...value,
              });
            },
            setter: {
              componentName: 'AthLayoutPositionSetter',
              isDynamic: false,
              props: {
                options: {
                  positionType: 'all',
                },
              },
            },
          },
        ],
      },
    ],
  },
  snippets: [defaultSnippet],
};

export default config;
