import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
} from '../common/common-meta-info.config';
import { v4 as uuidv4 } from 'uuid';
import { envParams } from '@/env';

const LcdpToolbarMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.TOOLBAR,
  title: 'dj-整单操作',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpToolbar',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      {
        title: 'dj-关联字段',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.target',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'target',
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
        ],
      },
      // {
      //   type: 'group',
      //   title: '基础设置',
      //   display: 'accordion',
      //   items: [{ ...commonBasicSetter.status }],
      // },
      {
        title: 'dj-开窗配置（关联属性）',
        display: 'accordion',
        name: 'dslInfo.items',
        getValue: (target) => {
          return target?.node?.getPropValue('dslInfo.items');
        },
        setter: {
          isDynamic: false,
          componentName: 'LcdpToolbarItemSetter',
          props: {
            eventSourceType: 'toolbar',
          },
        },
      },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.TOOLBAR) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-整单操作',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/BUTTONGROUP.svg`,
    schema: {
      componentName: AthenaComponentType.TOOLBAR,
      title: 'dj-整单操作',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.TOOLBAR,
          headerName: '整单操作',
          schema: '',
          path: '',
          position: 'right', // right | left
          disabled: false,
          editable: true,
          target: '',
          items: [
            {
              id: uuidv4(),
              title: '新增',
              icon: '',
              type: 'add',
              sequence: 1,
              lang: {
                title: {
                  zh_CN: '新增',
                  zh_TW: '新增',
                  en_US: 'add',
                },
              },
            },
            {
              id: uuidv4(),
              title: '复制',
              icon: '',
              type: 'copy',
              sequence: 2,
              lang: {
                title: {
                  zh_CN: '复制',
                  zh_TW: '複製',
                  en_US: 'copy',
                },
              },
            },
            {
              id: uuidv4(),
              title: '编辑',
              icon: '',
              type: 'edit',
              sequence: 3,
              lang: {
                title: {
                  zh_CN: '编辑',
                  zh_TW: '編輯',
                  en_US: 'edit',
                },
              },
            },
            {
              id: uuidv4(),
              title: '上一笔',
              icon: '',
              type: 'previous',
              sequence: 4,
              lang: {
                title: {
                  zh_CN: '上一笔',
                  zh_TW: '上一筆',
                  en_US: 'previous',
                },
              },
            },
            {
              id: uuidv4(),
              title: '下一笔',
              icon: '',
              type: 'next',
              sequence: 5,
              lang: {
                title: {
                  zh_CN: '下一笔',
                  zh_TW: '下一筆',
                  en_US: 'next',
                },
              },
            },
          ],
          lang: {
            headerName: {
              zh_CN: '整单操作',
              zh_TW: '',
              en_US: 'text',
            },
          },
        },
      },
    },
  },
];

export default {
  ...LcdpToolbarMeta,
  snippets,
};
