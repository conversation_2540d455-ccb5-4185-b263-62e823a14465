import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  differenceSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const DifferenceCalculationMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.DIFFERENCE_CALCULATION,
  title: 'dj-差异值',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonShow',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [...differenceSetter],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.DIFFERENCE_CALCULATION) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-差异值',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/DIFFERENCE_CALCULATION.svg`,
    schema: {
      componentName: AthenaComponentType.DIFFERENCE_CALCULATION,
      title: 'dj-差异值',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.DIFFERENCE_CALCULATION,
          headerName: '差异值',
          schema: '',
          path: '',
          lang: {
            headerName: {
              zh_CN: '差异值',
              zh_TW: '差异值',
              en_US: 'difference',
            },
          },
          fields: [
            {
              schema: 'after_change_qty',
              icon: 'new',
              unit: 'unit_no',
            },
            {
              schema: 'before_change_qty',
              icon: 'old',
              unit: 'unit_no',
            },
          ],
        },
      },
    },
  },
];

export default {
  ...DifferenceCalculationMeta,
  snippets,
};
