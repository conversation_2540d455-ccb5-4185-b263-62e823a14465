import {
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthRuleSetter,
  commonBasicSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const LcdpDividerMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.DIVIDER,
  title: 'dj-分割线',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpDivider',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      {
        name: 'dslInfo.lang.text',
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.text', value['zh_CN']);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-标题',
              },
              componentType: 'lang',
            },
          },
        },
      },
      // { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.isDash',
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-是否虚线',
                  },
                  layout: 'vertical',
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.showType',
            setValue(target, value) {
              if (value === 'horizontal') {
                target?.node?.setPropValue('dslInfo.padding', '20px 0');
              } else {
                target?.node?.setPropValue('dslInfo.padding', '0 20px');
              }
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-方向',
                  },
                  layout: 'vertical',

                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      { label: 'dj-水平', value: 'horizontal' },
                      { label: 'dj-垂直', value: 'vertical' },
                    ],
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.fixLength',
            setValue(target, value) {
              // target?.node?.setPropValue('dslInfo.fixLength', value);
              target?.node?.setPropValue('dslInfo.length', value ? '100px' : '100%');
            },
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-固定线长',
                  },
                  layout: 'vertical',
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.length',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return !!dslInfo?.fixLength;
            },
            getValue(target) {
              const dslInfo = target?.node?.getPropValue('dslInfo');
              return Number(dslInfo?.length.replace(/(px|%)$/, ''));
            },
            setValue(target, value) {
              target?.node?.setPropValue('dslInfo.length', value ? `${value}px` : '');
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-线长',
                  },
                  componentType: 'number',
                  componentProps: {
                    step: 1,
                    addonAfter: 'px',
                  },
                  formItemRules: [
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.lineWeight',
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-线粗',
                  },
                  layout: 'vertical',

                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      { label: 'normal', value: 'normal', tooltip: 'dj-普通' },
                      { label: 'strong', value: 'strong', tooltip: 'dj-加粗' },
                      { label: 'deep', value: 'deep', tooltip: 'dj-超粗' },
                    ],
                  },
                },
              },
            },
          },
        ],
      },
      {
        title: 'dj-文字样式',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.textColor',
            setter: {
              isDynamic: false,
              componentName: 'AthColorPanelSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-文字颜色',
                  },
                  layout: 'vertical',
                },
              },
            },
          },
          {
            name: 'dslInfo.orientation',
            setter: {
              isDynamic: false,
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-文字位置',
                  },
                  layout: 'vertical',
                  componentType: 'radioGroup',
                  componentProps: {
                    block: true,
                    buttonStyle: 'outline',
                    optionType: 'button',
                    options: [
                      {
                        value: 'left',
                        icon: 'iconzuoduiqi',
                        tooltip: 'dj-左对齐'
                      },
                      {
                        value: 'center',
                        icon: 'iconjuzhongduiqi',
                        tooltip: 'dj-居中对齐'
                      },
                      {
                        value: 'right',
                        icon: 'iconyouduiqi',
                        tooltip: 'dj-右对齐'
                      },
                    ],
                  },
                },
              },
            },
          },
        ],
      },
      {
        title: 'dj-样式',
        type: 'group',
        display: 'accordion',
        items: [
          {
            getValue: (target) => {
              const { padding } = target.node?.getPropValue('dslInfo') ?? {};
              const listPaddingList = padding?.replace(/\s+/g, ' ')?.split(' ') ?? [];
              const [top, right, bottom, left] = listPaddingList;
              return {
                top,
                right: right ?? top,
                bottom: bottom ?? top,
                left: left ?? right,
              };
            },
            setValue: (target, value = {}) => {
              let { top, right, bottom, left } = value;
              const listPadding = [top || '0', right || '0', bottom || '0', left || '0'].join(' ');
              target.node?.setPropValue('dslInfo.padding', listPadding);
            },
            setter: {
              componentName: 'LcdpPaddingSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-内边距',
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.DIVIDER) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-分割线',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/DIVIDER.png`,
    schema: {
      componentName: AthenaComponentType.DIVIDER,
      title: 'dj-分割线',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.DIVIDER,
          text: '',
          isDash: false,
          showType: 'horizontal', // 'horizontal' 水平  vertical 垂直
          fixLength: false, // 是否固定长度-开发平台的字段，无关于运行态
          length: '100%',
          lineWeight: 'normal', //分割线粗细 "normal" || "strong" || "deep"
          orientation: 'center',
          textColor: '#000000',
          padding: '20px 0',
          lang: {
            text: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...LcdpDividerMeta,
  snippets,
};
