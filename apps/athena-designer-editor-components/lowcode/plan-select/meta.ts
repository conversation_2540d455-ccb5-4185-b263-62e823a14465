import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonDataTypeSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const PlanSelectMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.PLAN_SELECT,
  title: 'dj-方案选择',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonDataTypeSetter(AthenaComponentType.PLAN_SELECT) },
          // 选项setter
          { ...commonBasicSetter.options },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.PLAN_SELECT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-方案选择',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/PLAN_SELECT.svg`,
    schema: {
      componentName: AthenaComponentType.PLAN_SELECT,
      title: 'dj-方案选择',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.PLAN_SELECT,
          headerName: '方案选择',
          placeholder: '请选择',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          dataType: AthenaDataType.STRING,
          options: [],
          dictionaryId: null,
          dictionaryKey: '',
          enumKey: '',
          lang: {
            headerName: {
              zh_CN: '方案选择',
              zh_TW: '方案選擇',
              en_US: 'plan select',
            },
            placeholder: {
              zh_CN: '请选择',
              zh_TW: '請選擇',
              en_US: 'please select',
            },
          },
        },
      },
    },
  },
];

export default {
  ...PlanSelectMeta,
  snippets,
};
