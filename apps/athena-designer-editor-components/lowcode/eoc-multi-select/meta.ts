import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonDataTypeSetter,
  commonBasicSetter,
  tipSetter,
} from '../common/common-meta-info.config';
import { AthenaDataType } from '../common/common.type';
import { envParams } from '@/env';

const EocMultiSelectMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.EOC_MULTI_SELECT,
  title: 'dj-运营单元多选',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        type: 'group',
        title: 'dj-基础设置',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonBasicSetter.status },
          { ...commonDataTypeSetter(AthenaComponentType.EOC_MULTI_SELECT) },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
    },
    advanced: {
      initialChildren: [],
    },
  },
};

const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-运营单元多选',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/EOC_MULTI_SELECT.svg`,
    schema: {
      componentName: AthenaComponentType.EOC_MULTI_SELECT,
      title: 'dj-运营单元多选',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.EOC_MULTI_SELECT,
          schema: '',
          path: '',
          label: '运营单元多选',
          headerName: '运营单元多选',
          placeholder: '请选择',
          disabled: false,
          editable: true,
          sortable: true,
          filterable: true,
          rowGroupable: false,
          isFocusDisplay: false,
          extraContent: '',
          tooltipTitle: '',
          iconType: 'wenhao',
          tooltipMode: 'normal',
          dataType: AthenaDataType.ARRAY,
          lang: {
            headerName: {
              zh_CN: '运营单元多选',
              zh_TW: '運營單元多選',
              en_US: 'Eoc Multi Select',
            },
            placeholder: {
              zh_CN: '请选择',
              zh_TW: '請選擇',
              en_US: 'Please select',
            },
            label: {
              zh_CN: '运营单元多选',
              zh_TW: '運營單元多選',
              en_US: 'Eoc Multi Select',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...EocMultiSelectMeta,
  snippets,
};
