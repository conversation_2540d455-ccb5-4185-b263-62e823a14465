import {
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
  IPublicModelSettingField,
} from '@alilc/lowcode-types';

import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonDataSourceNamesSetter,
} from '../common/common-meta-info.config';
import { AthenaComponentType, childWhitelistMap } from '../common/common.config';
import { envParams } from '@/env';

const LcdpListMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.LIST,
  title: 'dj-列表',
  group: 'dj-标准组件',
  category: 'dj-容器组件',
  docUrl: '',
  screenshot: '',
  icon: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpList',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonDataSourceNamesSetter },
      {
        name: 'dslInfo.lang.title',
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.title', value['zh_CN']);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-标题',
              },
              componentType: 'lang',
            },
          },
        },
      },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.isGrid',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-栅格布局',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.gridCount',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo?.isGrid;
            },
            setValue: (target, value) => {
              // 如果修改了gridCount，那么需要同步所有的gridBreakPointSet
              target.node?.setPropValue('dslInfo.gridBreakPointSet', {
                xs: value,
                sm: value,
                md: value,
                lg: value,
                xl: value,
                xxl: value,
              });
            },

            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  componentType: 'number',
                  titleProps: {
                    setterTitle: 'dj-栅格列数',
                  },
                  componentProps: {
                    disabled: false,
                    addonAfter: '列',
                    max: 12,
                    min: 1,
                    step: 1,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 1,
                      max: 12,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.gridGutter',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo?.isGrid;
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  componentType: 'number',
                  titleProps: {
                    setterTitle: 'dj-间距',
                  },
                  componentProps: {
                    disabled: false,
                    addonAfter: 'px',
                    max: 200,
                    min: 1,
                    step: 1,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 1,
                      max: 200,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
            },
          },
          {
            name: 'dslInfo.gridBreakPointSet',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo?.isGrid;
            },
            setter: {
              componentName: 'LcdpGridBreakPointSetter',
              isDynamic: false,
            },
          },
          {
            name: 'dslInfo.split',
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-分割线',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
        ],
      },
      {
        title: 'dj-分页设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            getValue: (target) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo.queryInfo.pageInfo;
            },
            setValue: (target, value) => {
              target?.node?.setPropValue(
                'dslInfo.queryInfo',
                value
                  ? {
                      pageInfo: {
                        pageSize: 20,
                      },
                    }
                  : {},
              );
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-后端分页',
                    layout: 'horizontal',
                  },
                  componentType: 'switch',
                  componentProps: {
                    size: 'small',
                  },
                },
              },
            },
          },
          {
            name: 'dslInfo.queryInfo.pageInfo.pageSize',
            condition: (target: IPublicModelSettingField) => {
              const dslInfo = target.node?.getPropValue('dslInfo');
              return !!dslInfo?.queryInfo?.pageInfo;
            },
            setter: {
              componentName: 'AthCommonSetter',
              isDynamic: false,
              props: {
                options: {
                  componentType: 'number',
                  titleProps: {
                    setterTitle: 'dj-每页条数',
                  },
                  componentProps: {
                    disabled: false,
                    addonAfter: '条',
                    max: 200,
                    min: 10,
                    step: 10,
                  },
                  formItemRules: [
                    {
                      type: 'number',
                      min: 10,
                      max: 200,
                    },
                    {
                      required: true,
                      message: '不可为空',
                    },
                  ],
                },
              },
              initialValue: 50,
            },
          },
        ],
      },
      {
        title: 'dj-样式',
        type: 'group',
        display: 'accordion',
        items: [
          {
            getValue: (target) => {
              const { listPadding } = target.node?.getPropValue('dslInfo') ?? {};
              const listPaddingList = listPadding?.replace(/\s+/g, ' ')?.split(' ') ?? [];
              const [top, right, bottom, left] = listPaddingList;
              return {
                top,
                right: right ?? top,
                bottom: bottom ?? top,
                left: left ?? right,
              };
            },
            setValue: (target, value = {}) => {
              let { top, right, bottom, left } = value;
              const listPadding = [top || '0', right || '0', bottom || '0', left || '0'].join(' ');
              target.node?.setPropValue('dslInfo.listPadding', listPadding);
            },
            setter: {
              componentName: 'LcdpPaddingSetter',
              isDynamic: false,
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-内边距',
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: true,
      // nestingRule: {
      //   childWhitelist: childWhitelistMap.get(AthenaComponentType.ATHENA_TABLE) ?? [], // TODO 等需求明确再处理
      // },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-列表',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/LIST.svg`,
    schema: {
      componentName: AthenaComponentType.LIST,
      props: {
        dslInfo: {
          type: AthenaComponentType.LIST,
          schema: '',
          path: '',
          title: '列表',
          lang: {
            title: {
              zh_CN: '列表',
              zh_TW: '清單',
              en_US: 'list',
            },
          },
          isGrid: true,
          split: false,
          gridCount: 4,
          gridGutter: 16,
          gridBreakPointSet: {
            xs: 4,
            sm: 4,
            md: 4,
            lg: 4,
            xl: 4,
            xxl: 4,
          },
          queryInfo: {
            pageInfo: {
              pageSize: 20,
            },
            dataFilter: {
              dataSourceNames: [],
              apiCondition: {},
            },
            isAsync: true,
          },
          pagination: {
            showSizeChanger: true,
          },
          listPadding: '0',
        },
      },
    },
  },
];

export default {
  ...LcdpListMeta,
  snippets,
};
