import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonBasicSetter,
  commonDataTypeSetter,
} from '../common/common-meta-info.config';
import { envParams } from "@/env";

const PersonSelectMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.PERSON_SELECT,
  title: 'dj-人员选择',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'PersonSelect',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.enableSearch },
          { ...commonBasicSetter.expandAll },
          { ...commonDataTypeSetter('PERSON_SELECT') },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.PERSON_SELECT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-人员选择',
    screenshot:
      `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/PERSON_SELECT.svg`,
    schema: {
      componentName: AthenaComponentType.PERSON_SELECT,
      title: 'dj-人员选择',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.PERSON_SELECT,
          headerName: '人员选择',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          dataType: AthenaDataType.ARRAY,
          expandAll: true,
          enableSearch: true,
          lang: {
            headerName: {
              zh_CN: '人员选择',
              zh_TW: '人員選擇',
              en_US: 'person select',
            },
          },
        },
      },
    },
  },
];

export default {
  ...PersonSelectMeta,
  snippets,
};
