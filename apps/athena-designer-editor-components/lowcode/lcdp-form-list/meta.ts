import {
  IPublicModelNode,
  IPublicModelSettingField,
  IPublicTypeComponentMetadata,
  IPublicTypeSnippet,
} from '@alilc/lowcode-types';

import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonDataSourceNamesSetter,
} from '../common/common-meta-info.config';
import { AthenaComponentType, childWhitelistMap } from '../common/common.config';
import { envParams } from '@/env';
import { BusinessButtonTypeSet, SubmitButtonTypeSet } from '../button/constant';

const LcdpFormListMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.FORM_LIST,
  title: 'dj-表单',
  group: 'dj-标准组件',
  category: 'dj-容器组件',
  docUrl: '',
  screenshot: '',
  icon: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpFormList',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      {
        setValue(target: IPublicModelSettingField, value) {
          // 进行排序
          target.node?.children?.mergeChildren(
            (node) => {
              if (value.waitDel?.some((item) => item.id === node.id)) return true;
              return false;
            },
            () => {
              value.waitAdd?.forEach((item) => {
                const value = item.props?.dslInfo;
                item.componentName = value.type;
                if (
                  SubmitButtonTypeSet?.has(value.type) ||
                  BusinessButtonTypeSet?.has(value.type)
                ) {
                  item.componentName = 'BUTTON';
                }
                target.node?.children?.insert(target?.node?.document?.createNode(item)!, 0);
              });
              return null;
            },
            (pre: IPublicModelNode, next: IPublicModelNode) => {
              if (!value.sort?.length) return 0;
              const firstValue = pre?.getPropValue('dslInfo.schema') || pre?.id;
              const nextValue = next?.getPropValue('dslInfo.schema') || next?.id;
              const indexPre = value.sort.indexOf(firstValue);
              const indexNext = value.sort.indexOf(nextValue);
              return indexPre - indexNext;
            },
          );
          // 修改的
          value.waitUpdate?.forEach((item) => {
            const value = item.props?.dslInfo;
            item.componentName = value.type;
            if (SubmitButtonTypeSet?.has(value.type) || BusinessButtonTypeSet?.has(value.type)) {
              item.componentName = 'BUTTON';
            }
            const val = target.node?.children?.find((x) => x.id === item.id);
            val?.replaceWith({ ...item });
          });
        },
        setter: {
          componentName: 'AthTableFormEditSetter',
          isDynamic: false,
          props: {
            type: 'form',
          },
        },
      },
      { ...commonDataSourceNamesSetter },
      {
        name: 'dslInfo.lang.title',
        setValue: (target, value) => {
          target?.node?.setPropValue('dslInfo.title', value['zh_CN']);
        },
        setter: {
          componentName: 'AthCommonSetter',
          isDynamic: false,
          props: {
            options: {
              titleProps: {
                setterTitle: 'dj-标题',
              },
              componentType: 'lang',
            },
          },
        },
      },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          {
            name: 'dslInfo.direction',
            setter: {
              componentName: 'AthCommonSetter',
              props: {
                options: {
                  titleProps: {
                    setterTitle: 'dj-排列方式',
                  },
                  componentType: 'select',
                  componentProps: {
                    options: [
                      {
                        label: 'ROW',
                        value: 'ROW',
                      },
                      {
                        label: 'COLUMN',
                        value: 'COLUMN',
                      },
                    ],
                  },
                },
              },
            },
          },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      // {
      //   title: 'dj-操作设置（关联属性）',
      //   display: 'accordion',
      //   getValue: (target) => {
      //     const dslInfo = target?.node?.getPropValue('dslInfo');
      //     return dslInfo;
      //   },
      //   setter: {
      //     isDynamic: false,
      //     componentName: 'AthOperationsSetter',
      //     props: {
      //       componentProps: {
      //         hiddenRow: true,
      //         allTitle: 'dj-表单操作',
      //       },
      //       eventSourceType: 'operation',
      //     },
      //   },
      // },
      {
        title: 'dj-操作设置（关联属性）',
        display: 'accordion',
        getValue: (target) => {
          const dslInfo = target?.node?.getPropValue('dslInfo');
          return dslInfo;
        },
        setter: {
          isDynamic: false,
          componentName: 'LcdpTableOperationSetter',
          props: {
            componentProps: {
              allTitle: 'dj-表单操作',
              showAll: true, // 是否整体操作
            },
          },
        },
      },
    ],
    component: {
      isContainer: true,
      nestingRule: {
        childWhitelist: childWhitelistMap.get(AthenaComponentType.FORM_LIST) ?? [],
      },
    },
    advanced: {
      initialChildren: [
        {
          componentName: AthenaComponentType.DYNAMIC_OPERATION,
          title: '动态操作',
          props: {
            dslInfo: {
              type: AthenaComponentType.DYNAMIC_OPERATION,
              select: 'slot-top-right',
              height: 140,
              group: [],
            },
          },
        },
      ],
      callbacks: {
        onNodeAdd: (addedNode, currentNode) => {},
      },
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-表单',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/form-list.svg`,
    schema: {
      componentName: AthenaComponentType.FORM_LIST,
      props: {
        dslInfo: {
          id: '',
          title: '表单',
          dataType: 'object',
          lang: {
            title: {
              zh_CN: '表单',
              en_US: 'form list',
              zh_TW: '表单',
            },
          },
          direction: 'ROW',
          type: AthenaComponentType.FORM_LIST,
          schema: '',
          path: '',
          collapse: false,
          queryInfo: {
            dataFilter: {
              dataSourceNames: [],
              apiCondition: {},
            },
            isAsync: true,
          },
        },
      },
    },
  },
];

export default {
  ...LcdpFormListMeta,
  snippets,
};
