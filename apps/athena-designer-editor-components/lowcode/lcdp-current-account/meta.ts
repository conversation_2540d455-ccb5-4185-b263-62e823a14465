import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonDataTypeSetter,
  commonBasicSetter,
} from '../common/common-meta-info.config';
import { AthenaDataType } from '../common/common.type';
import { envParams } from '@/env';

const LcdpCurrentAccount: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.AthCurrentAccount,
  title: 'dj-登录用户',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCurrentAccount',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        type: 'group',
        title: 'dj-基础设置',
        display: 'accordion',
        items: [{ ...commonDataTypeSetter('CURRENT_ACCOUNT') }],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
    },
  },
};

const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-登录用户',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/CURRENT_ACCOUNT.svg`,
    schema: {
      componentName: AthenaComponentType.AthCurrentAccount,
      title: 'dj-登录用户',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.AthCurrentAccount,
          schema: '',
          path: '',
          label: '登录用户',
          headerName: '登录用户',
          dataType: AthenaDataType.NUMERIC,
          lang: {
            headerName: {
              zh_CN: '登录用户',
              zh_TW: '登入用戶',
              en_US: 'Current Account',
            },
          },
        },
      },
    },
  },
];

export default {
  ...LcdpCurrentAccount,
  snippets,
};
