import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonDataTypeSetter,
  commonBasicSetter,
  tipSetter,
} from '../common/common-meta-info.config';
import { AthenaDataType } from '../common/common.type';
import { envParams } from '@/env';

const EocUserSelectNewMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.EOC_USER_SELECT_NEW,
  title: 'dj-EOC员工（新）',
  group: 'dj-标准组件',
  category: 'dj-功能组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        type: 'group',
        title: 'dj-基础设置',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonBasicSetter.status },
          { ...commonDataTypeSetter(AthenaComponentType.EOC_USER_SELECT_NEW) },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
    },
    advanced: {
      initialChildren: [],
    },
  },
};

const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-EOC员工（新）',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/EOC_USER_SELECT.svg`,
    schema: {
      componentName: AthenaComponentType.EOC_USER_SELECT_NEW,
      title: 'dj-EOC员工（新）',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.EOC_USER_SELECT_NEW,
          schema: '',
          path: '',
          label: 'EOC员工（新）',
          headerName: 'EOC员工（新）',
          placeholder: '请选择负责人',
          disabled: false,
          editable: true,
          sortable: true,
          filterable: true,
          rowGroupable: false,
          isFocusDisplay: false,
          extraContent: '',
          tooltipTitle: '',
          iconType: 'wenhao',
          tooltipMode: 'normal',
          backFills: [
            {
              valueScript: "selectedObject['userId']",
              key: 'a1idqu5uhd',
            },
            {
              valueScript: "selectedObject['userName']",
              key: 'eoc_user_name',
            },
          ],
          dataType: AthenaDataType.STRING,
          lang: {
            headerName: {
              zh_CN: 'EOC员工（新）',
              zh_TW: 'EOC員工（新）',
              en_US: 'Eoc User（new）',
            },
            placeholder: {
              zh_CN: '请选择负责人',
              zh_TW: '請選擇負責人',
              en_US: 'Please select',
            },
            label: {
              zh_CN: 'EOC员工（新）',
              zh_TW: 'EOC員工（新）',
              en_US: 'Eoc User（new）',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...EocUserSelectNewMeta,
  snippets,
};
