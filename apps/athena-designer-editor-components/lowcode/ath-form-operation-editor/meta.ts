import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
  numberSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthFormOperationEditor: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.FORM_OPERATION_EDITOR,
  title: 'dj-多选开窗',
  group: 'dj-标准组件',
  category: 'dj-开窗组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.status },
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonBasicSetter.showInput },
          { ...commonDataTypeSetter('AthenaDataType') },
          ...numberSetter,
          { ...commonBasicSetter.enableTrim },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
      {
        title: 'dj-开窗配置（关联属性）',
        display: 'accordion',
        getValue: (target) => {
          return target?.node?.getPropValue('dslInfo');
        },
        setter: {
          isDynamic: false,
          componentName: 'AthOperationsOpenWindowSetter',
        },
      },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.INPUT) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-多选开窗',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/FORM_OPERATION_EDITOR.svg`,
    schema: {
      componentName: AthenaComponentType.FORM_OPERATION_EDITOR,
      title: 'dj-多选开窗',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.FORM_OPERATION_EDITOR,
          headerName: '多选开窗',
          placeholder: '请输入',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          showInput: false,
          dataType: AthenaDataType.ARRAY,
          lang: {
            headerName: {
              zh_CN: '多选开窗',
              zh_TW: '多選開窗',
              en_US: 'OPERATION EDITOR',
            },
            placeholder: {
              zh_CN: '请输入',
              zh_TW: '請輸入',
              en_US: 'please enter',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthFormOperationEditor,
  snippets,
};
