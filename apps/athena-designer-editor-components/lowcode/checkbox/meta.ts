import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const CheckboxMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.CHECKBOX,
  title: 'dj-复选框',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder },
          ...tipSetter,
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay },
          { ...commonDataTypeSetter('CHECKBOX') },
        ],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.CHECKBOX) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-复选框',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/CHECKBOX.png`,
    schema: {
      componentName: AthenaComponentType.CHECKBOX,
      title: 'dj-复选框',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.CHECKBOX,
          headerName: '复选框',
          placeholder: '',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          dataType: AthenaDataType.BOOLEAN,
          lang: {
            headerName: {
              zh_CN: '复选框',
              zh_TW: '複選框',
              en_US: 'checkbox',
            },
            placeholder: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...CheckboxMeta,
  snippets,
};
