import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType, parentWhitelistMap } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  commonUploadSetter,
  commonBasicSetter,
} from '../common/common-meta-info.config';
import { envParams } from "@/env";

const AthFileUploadMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.FILE_UPLOAD,
  title: 'dj-表格附件',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'AthFileUpload',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-上传设置',
        type: 'group',
        display: 'accordion',
        items: [...commonUploadSetter, { ...commonBasicSetter.filterKey }],
      },
      { ...commonAthMonacoEditorSetter },
      { ...commonAthRuleSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        parentWhitelist: parentWhitelistMap.get(AthenaComponentType.FILE_UPLOAD) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-表格附件',
    screenshot:
      `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/FILE_UPLOAD.svg`,
    schema: {
      componentName: AthenaComponentType.FILE_UPLOAD,
      title: 'dj-表格附件',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.FILE_UPLOAD,
          headerName: '表格附件',
          placeholder: '',
          schema: '',
          path: '',
          // isFocusDisplay: false,
          dataType: AthenaDataType.OBJECT,
          lang: {
            headerName: {
              zh_CN: '表格附件',
              zh_TW: '表格附檔',
              en_US: 'file upload',
            },
          },
          buckets: '',
          attribute: {
            uploadEnable: false,
            uploadCategory: '',
            fileExtensions: [],
            fileCount: '',
            fileMaxSize: '',
            draggable: false,
            disableAam: true,
            enableEffectAfterSubmit: false,
            onlyDeleteByOwner: false,
            filterKey: '',
          },
        },
      },
    },
  },
];

export default {
  ...AthFileUploadMeta,
  snippets,
};
