import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';
import { AthenaComponentType } from '../common/common.config';
import { AthenaDataType } from '../common/common.type';
import {
  commonAthMonacoEditorSetter,
  commonAthAssociationFieldSetter,
  commonAthRuleSetter,
  tipSetter,
  commonBasicSetter,
  commonDataTypeSetter,
} from '../common/common-meta-info.config';
import { envParams } from '@/env';

const AthTimepickerMeta: IPublicTypeComponentMetadata = {
  componentName: AthenaComponentType.TIMEPICKER,
  title: 'dj-时间选择',
  group: 'dj-标准组件',
  category: 'dj-基础组件',
  docUrl: '',
  screenshot: '',
  devMode: 'proCode',
  npm: {
    package: 'athena-designer-editor-components',
    version: '0.1.4',
    exportName: 'LcdpCommonColumn',
    main: 'src/index.tsx',
    destructuring: true,
    subName: '',
  },
  configure: {
    supports: {
      style: false,
      loop: false,
    },
    props: [
      { ...commonBasicSetter.componentTypeExchange },
      { ...commonBasicSetter.headerName },
      { ...commonAthAssociationFieldSetter },
      {
        title: 'dj-基础设置',
        type: 'group',
        display: 'accordion',
        items: [
          { ...commonBasicSetter.placeholder }, // 占位提示
          ...tipSetter, // 辅助提示+注释说明+注释图标
          { ...commonBasicSetter.status }, // 是否启用
          { ...commonBasicSetter.isFocusDisplay }, // 启用标题在内
          { ...commonDataTypeSetter('TIMEPICKER') }, // 数据类型
        ],
      },
      { ...commonAthRuleSetter },
      { ...commonAthMonacoEditorSetter },
    ],
    component: {
      isContainer: false,
      nestingRule: {
        // parentWhitelist: parentWhitelistMap.get(AthenaComponentType.TIMEPICKER) ?? [],
      },
    },
    advanced: {
      initialChildren: [],
    },
  },
};
const snippets: IPublicTypeSnippet[] = [
  {
    title: 'dj-时间选择',
    screenshot: `${envParams.scheduleDomain}/scheduler/static/assets/img/component-icons/TIMEPICKER.png`,
    schema: {
      componentName: AthenaComponentType.TIMEPICKER,
      title: 'dj-时间选择',
      props: {
        dslInfo: {
          id: '',
          type: AthenaComponentType.TIMEPICKER,
          headerName: '时间选择',
          placeholder: 'hhmmss',
          schema: '',
          path: '',
          disabled: false,
          editable: true,
          isFocusDisplay: false,
          fieldType: 'string',
          dataType: AthenaDataType.TIME,
          tooltipMode: 'normal', // 有注释iconType的都要加
          iconType: 'wenhao',
          rules: { value: [] },
          lang: {
            headerName: {
              zh_CN: '时间选择',
              zh_TW: '時間選擇',
              en_US: 'TimePicker',
            },
            placeholder: { zh_CN: 'hhmmss', zh_TW: 'hhmmss', en_US: 'hhmmss' },
            extraContent: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
            tooltipTitle: {
              zh_CN: '',
              zh_TW: '',
              en_US: '',
            },
          },
        },
      },
    },
  },
];

export default {
  ...AthTimepickerMeta,
  snippets,
};
