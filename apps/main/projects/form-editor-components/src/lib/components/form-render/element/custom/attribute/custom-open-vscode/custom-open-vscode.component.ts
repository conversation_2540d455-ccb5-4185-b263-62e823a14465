import { Component, Input } from '@angular/core';
import { FormEditorFramework } from '@webdpt/form-editor';
import { GlobalService } from 'common/service/global.service';
import { AppService } from 'pages/apps/app.service';
import { AdUserService } from 'pages/login/service/user.service';
import { AppTypes } from 'pages/app/typings';
import { SystemConfigService } from 'common/service/system-config.service';

@Component({
  selector: 'dw-custom-open-vscode',
  templateUrl: './custom-open-vscode.component.html',
  styleUrls: ['./custom-open-vscode.component.less'],
})
export class CustomOpenVscodeComponent {
  MODEL_DRIVEN = AppTypes.MODEL_DRIVEN;
  enableOpenVscode: boolean;
  @Input() value;
  constructor(
    private userService: AdUserService,
    private appService: AppService,
    public globalService: GlobalService,
    private systemConfigService: SystemConfigService,
  ) {
    this.systemConfigService.get('enableOpenVscode').subscribe((val) => {
      this.enableOpenVscode = val === 'true';
    });
  }
  openSource(event: MouseEvent): void {
    event.stopPropagation();
    event.preventDefault();
    // 打开源码，参数携带
    const route = 'manage';
    const tenantId = this.userService.getUser('tenantId');
    const token = this.userService.getUser('iamToken');
    const appCode = this.appService?.selectedApp?.code;
    const { customType } = this.value;

    const vscodeProtocol =
      `@vscodeinit&route=${route}&env=@env&version=@version&fileId=@fileId` +
      `&token=${token}&code=${appCode}&type=${customType}`;

    const encodedUrl = encodeURIComponent(vscodeProtocol);
    window.open(`/open-vscode?url=${encodedUrl}`, '_blank');
  }
}
const option = {
  type: 'custom-open-vscode',
  selector: 'custom-open-vscode-ele',
  group: 'basic',
};

FormEditorFramework.setComponent('common', option, CustomOpenVscodeComponent);
