import { Component, Inject, OnInit } from '@angular/core';
import microApp from '@micro-zoe/micro-app';
import { AD_AUTH_TOKEN } from '../../login/service/auth.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { environment } from 'environments/environment';
import { AdUserService } from '../../login/service/user.service';
import { ActivatedRoute, NavigationEnd, Router } from '@angular/router';
import { filter } from 'rxjs/operators';
import { Subscription } from 'rxjs';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'app-module-wrapper',
  templateUrl: './module-wrapper.component.html',
  styleUrls: ['./module-wrapper.component.less'],
})
export class ModuleWrapperComponent implements OnInit {
  url = '';
  appName = 'heighCode';
  packageName = 'athena-designer-core';
  microAppData = {
    token: this.userService.getUser('iamToken'),
  };

  routeSubscribe$: Subscription;

  constructor(
    private configService: SystemConfigService,
    private router: Router,
    private userService: AdUserService,
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
  ) {
    this.configService.getConfig().subscribe((config: any): void => {
      this.url = config.devOpsUrl;
    });
  }

  ngOnInit(): void {
    this.routeSubscribe$ = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((data: any) => {
        window.history.pushState(history.state, '', data?.url);
        window.dispatchEvent(new PopStateEvent('popstate', { state: history.state }));
      });
  }

  ngOnDestroy(): void {
    microApp?.unmountApp('deployer');
    this.routeSubscribe$?.unsubscribe();
  }

  handleCreate(): void {
    console.log('react_deploy 创建了');
  }

  handleBeforeMount(): void {
    console.log('react_deploy 即将被渲染');
  }

  handleMount(): void {
    console.log('react_deploy 已经渲染完成');
  }

  handleUnmount(): void {
    console.log('react_deploy 卸载了');
  }

  handleError(): void {
    console.log('react_deploy 加载出错了');
  }

  handleDataChange(e: CustomEvent): void {
    const { type, data } = e.detail.data;
    console.log('type', type);
    if (type === 'routeLink') {
      window.history.pushState(history.state, '', data?.url);
      window.dispatchEvent(new PopStateEvent('popstate', { state: history.state }));
    }
    if (type === 'routeLinkBlank') {
      // const url = this.router.serializeUrl(this.router.createUrlTree([data?.url]));
      window.open(data?.url, '_blank');
    }
  }
}
