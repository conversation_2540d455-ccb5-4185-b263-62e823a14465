import { Component, OnDestroy, OnInit } from '@angular/core';
import { HomeService } from '../service/home.service';
import { Subscription } from 'rxjs';
import { mockToolsSupport } from './tools-support.mock';
import { TranslateService } from '@ngx-translate/core';
import { AdUserService } from 'pages/login/service/user.service';
import { SystemConfigService } from 'common/service/system-config.service';

@Component({
  selector: 'home-tools-support',
  templateUrl: './tools-support.component.html',
  styleUrls: ['./tools-support.component.less'],
})
export class ToolsSupportComponent implements OnInit, OnDestroy {
  private subscription: Subscription;
  enableOpenVscode: boolean;
  constructor(
    private homeService: HomeService,
    private translateService: TranslateService,
    private adUserService: AdUserService,
    private systemConfigService: SystemConfigService,
  ) {
    this.systemConfigService.get('enableOpenVscode').subscribe((val) => {
      this.enableOpenVscode = val === 'true';
    });
  }

  toolsItems: any[] = [];

  ngOnInit(): void {
    this.subscription = this.homeService.queryTenantPipeline().subscribe((res) => {
      if (res.code === 0) {
        const groups = res.data?.groups;
        const friendItems = groups.map((group, index) => {
          const { serviceId, serviceName, friendlyLinkList } = group.node;
          return {
            id: index + serviceId,
            title: serviceName,
            friendlyLinkList: friendlyLinkList.filter(
              (item) =>
                item.url &&
                !(item.url.includes('console') && !item.url.includes('sc-console')) &&
                !item.url.includes('athena') &&
                !item.url.includes('esplmc') &&
                // !item.url.includes('console') &&
                !item.name.includes('TEMGR'),
            ),
          };
        });
        this.toolsItems = this.enableOpenVscode
          ? [
              ...friendItems,
              {
                id: 'devTools',
                title: this.translateService.instant('dj-本地'),
                friendlyLinkList: [
                  {
                    name: 'dj-定制开发工具',
                    url: '',
                  },
                ],
              },
            ]
          : friendItems;
      }
    });
  }

  openDevTools() {
    const token = this.adUserService.getUser('iamToken');
    const route = 'init';
    const vscodeProtocol = `@vscodeinit&route=${route}&env=@env&version=@version&fileId=@fileId` + `&token=${token}`;
    const encodedUrl = encodeURIComponent(vscodeProtocol);
    window.open(`/open-vscode?url=${encodedUrl}`, '_blank');
  }

  handleCopyLink(item: any) {
    const userInfo = this.adUserService.getUserInfo();
    const url = item.url
      .replace(/@token/g, userInfo.iamToken)
      .replace(/@userToken/g, userInfo.iamToken)
      .replace(/@username/g, userInfo.name)
      .replace(/@lang/g, this.translateService.currentLang)
      .replace(/@tenantid/g, userInfo.tenantId);
    window.open(url, '_blank');
  }

  ngOnDestroy() {
    this.subscription?.unsubscribe();
  }
}
