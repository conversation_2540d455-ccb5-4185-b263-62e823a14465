<section class="rookie-task-wrapper first-element">
  <div class="home-row">
    <h1 class="home-item-title">
      {{ 'dj-欢迎您使用鼎捷雅典娜开发平台！' | translate }}{{ 'dj-请完成新手任务！' | translate }}
    </h1>
    <span class="home-operate" (click)="handleCloseRookieTask()">{{ 'dj-关闭新手任务' | translate }}</span>
  </div>
  <section class="rookie-task">
    <div *ngFor="let task of taskList; let i = index">
      <div class="create-item">
        <div class="img-box">
          <img [src]="'assets/img/rookie/task-' + (i + 1) + '.svg'" />
        </div>
        <div class="item-info">
          <h3 *ngIf="!isNeedConstructTask(task)" class="info-title" (click)="handleViewDocument(task)">
            {{ task.lang?.taskName?.[currentLanguage] }} >
          </h3>
          <h3 *ngIf="isNeedConstructTask(task)" class="info-title" (click)="handleConstructTask(task, i)">
            {{ task.lang?.taskName?.[currentLanguage] }} >
          </h3>
          <div class="home-row">
            <div *ngIf="task.status !== 2" class="home-tag-uncomplete">{{ 'dj-未完成' | translate }}</div>
            <div *ngIf="task.status === 2" class="home-tag-complete">{{ 'dj-已完成' | translate }}</div>
            <div *ngIf="isNeedConstructTask(task)" class="home-link" (click)="handleViewDocument(task)">
              <i style="margin-right: 2px" adIcon iconfont="iconchakanwendang"></i>
              <span>{{ 'dj-查看文档' | translate }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</section>

<!-- 任务构建开窗 -->
<home-task-construct-modal
  [visible]="constructVisible"
  [task]="constructTask"
  [taskNum]="constructTaskIndex + 1"
  (cancel)="handleCancelConstructTask()"
  (refresh)="refresh.emit()"
></home-task-construct-modal>
