<section class="home-wrapper">
  <ng-container *ngIf="rookieTask">
    <h1 class="home-item-title">{{ 'dj-创建解决方案' | translate }}</h1>
  </ng-container>
  <ng-container *ngIf="!rookieTask">
    <h1 class="home-item-title">{{ 'dj-欢迎您使用鼎捷雅典娜开发平台！' | translate | envTranslate }}</h1>
    <p class="home-item-description">
      {{ 'dj-您可以快速构建多种类型的解决方案，并直接在线发布使用' | translate }}
      <span class="home-link" (click)="handleLinkAthena()">
        {{ 'dj-了解鼎捷雅典娜开发平台' | translate | envTranslate }} >
      </span>
    </p>
    <p class="hot-app">
      {{ 'dj-创建热门解决方案' | translate }}
    </p>
  </ng-container>
  <section class="create-types">
    <ng-container *ngIf="createItems?.length">
      <ng-container *ngFor="let item of createItems">
        <solution-entry-card
          *ngIf="item.auth === undefined || item.auth"
          [data]="item"
          [loading]="solutionCardLoading[item.appType]"
          (cardClick)="handleAddApp($event, item)"
          (detailClick)="handleLinkDetail($event)"
        ></solution-entry-card>
      </ng-container>
    </ng-container>
  </section>
</section>

<ng-container>
  <app-create-app
    *ngIf="addAppVisible"
    [visible]="addAppVisible"
    [type]="type"
    [params]="params"
    (refreshAppList)="handleRefreshAppList()"
    (visibleChange)="onVisibleChange($event)"
    (afterCreated)="onAfterCreated($event)"
  ></app-create-app>

  <app-create-agile-data-app
    *ngIf="addAgileDataAppVisible"
    [visible]="addAgileDataAppVisible"
    [type]="type"
    [params]="params"
    (visibleChange)="addAgileDataAppVisible = $event"
    (afterCreated)="onAfterCreated($event)"
  ></app-create-agile-data-app>
</ng-container>

<app-create-nana-assistant-app
  *ngIf="addNanaAssistantAppVisible"
  [visible]="addNanaAssistantAppVisible"
  [type]="type"
  [params]="params"
  (visibleChange)="addNanaAssistantAppVisible = $event"
  (afterCreated)="onAfterCreated($event)"
></app-create-nana-assistant-app>

<div class="page-fixed" *ngIf="position !== undefined" (click)="handleCloseMask()">
  <div class="menu-list" [ngStyle]="{ left: position.x + 'px', top: position.y + 'px' }">
    <div class="menu-item" (click)="handleSelectType($event, 'blank')">
      <img alt="" src="../../../assets/img/athena/app-blank.png" class="menu-icon" />
      <span class="menu-text">{{ 'dj-创建空白解决方案' | translate }}</span>
    </div>
    <div class="menu-item" (click)="handleSelectType($event, 'template')">
      <img alt="" src="../../../assets/img/athena/app-template.png" class="menu-icon" />
      <span class="menu-text">{{ 'dj-通过模板创建' | translate }}</span>
    </div>
  </div>
</div>

<app-template-list
  [appType]="params.appType"
  [visible]="templateVisible"
  *ngIf="templateVisible"
  (onClose)="handleCloseTemplates()"
  (onSelectTemplate)="handleSelectTemplate($event)"
>
</app-template-list>

<!-- 新建解决方案弹窗 -->
<ad-modal
  nzClassName="switch-datasource-modal"
  nzWidth="470px"
  [(nzVisible)]="createSolutionVisible"
  [nzTitle]="'dj-新建解决方案' | translate"
  [nzClosable]="true"
  [nzMaskClosable]="false"
  [nzOkLoading]="createSolutionLoading"
  (nzOnCancel)="createSolutionVisible = false"
  (nzOnOk)="handleOk()"
>
  <ng-container *adModalContent>
    <solution-base-info-form #solutionInfoForm [solutionInfo]="currentSolutionCardData"></solution-base-info-form>
  </ng-container>
</ad-modal>
