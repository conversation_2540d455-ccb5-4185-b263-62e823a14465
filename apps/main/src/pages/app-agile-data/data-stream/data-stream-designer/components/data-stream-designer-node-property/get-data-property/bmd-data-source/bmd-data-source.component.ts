import { Component, ElementRef, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { SystemConfigService } from 'common/service/system-config.service';
import { cloneDeep, omit } from 'lodash';
import { AdUserService } from 'pages/login/service/user.service';
import { PanelStoreService } from '../../panel-store.service';
import { IEditParams } from '../bmd-interface';
import { AgileDataStoreService } from 'pages/app-agile-data/agile-data-store.service';
import { rawType } from 'pages/app-agile-data/utils/utils';
import { AppService } from 'pages/apps/app.service';

@Component({
  selector: 'app-bmd-data-source',
  templateUrl: './bmd-data-source.component.html',
  styleUrls: ['./bmd-data-source.component.less'],
})
export class BmdDataSourceComponent implements OnInit, OnDestroy {
  dataSourceForm: FormGroup;
  querySchema: any;
  dccUrl: string;
  iframeSrc: SafeResourceUrl;
  showIframeModal: boolean = false;
  iframeLoading: boolean = true;
  sqlVisible: boolean = false;

  @Input() data: any;
  @Output() sourceChanged: EventEmitter<any> = new EventEmitter();

  @ViewChild('iframe') iframeElement: ElementRef<HTMLIFrameElement>;

  constructor(
    private fb: FormBuilder,
    private domSanitizer: DomSanitizer,
    protected userService: AdUserService,
    private configService: SystemConfigService,
    private panelStoreService: PanelStoreService,
    private agileDataStoreService: AgileDataStoreService,
    private appService: AppService,
  ) {
    this.configService.get('dccUrl').subscribe((url) => {
      this.dccUrl = url;
      this.iframeSrc = this.domSanitizer.bypassSecurityTrustResourceUrl(
        `${url}/semantic/externalQuerier?token=${this.userService.getUser('iamToken')}`,
      );
    });
  }

  get readOnly(): boolean {
    return this.panelStoreService.getState().readOnly;
  }

  get application(): string {
    return this.appService.selectedApp.code;
  }

  ngOnInit() {
    this.dataSourceForm = this.fb.group({
      source: ['model', [Validators.required]],
    });
    window.addEventListener('message', this.handleDccData.bind(this));
  }

  handleDccData(e): void {
    if (e.data.type === 'dcc:status') {
      this.iframeElement &&
        this.iframeElement.nativeElement.contentWindow.postMessage(
          {
            type: 'init',
            data: {
              queryId: this.data?.querySchema?.queryId ?? null, //模型id
              schema: this.handleModalSchema(), //模型数据
              defaultValue: {
                //模型参数
                params: this.agileDataStoreService.getState().schemaParams,
                application: this.application,
              },
            },
          },
          this.dccUrl,
        );
      this.iframeLoading = false;
    }
    if (e.data.type === 'dcc:saveQueryScheme') {
      this.data.querySchema = e.data.data;
      this.data.modelName = this.data.querySchema.modelName;
      this.data.modelCode = this.data.querySchema.modelId;
      this.sourceChanged.emit(cloneDeep(this.data));
      this.showIframeModal = false;
    }
  }

  handleModalSchema(): any {
    if (this.data?.primitiveSchema) {
      return JSON.parse(this.data.primitiveSchema);
    } else if (this.data?.querySchema) {
      return this.handleModalSchemaData(this.data?.querySchema);
    } else {
      return null;
    }
  }

  handleModalSchemaData(data): any {
    if (rawType(data) === 'String') {
      data = this.handleModalSchemaData(JSON.parse(data));
    }
    if (rawType(data) === 'Object') {
      const schemaData = cloneDeep(data);
      schemaData.params = schemaData.params.map((item: IEditParams) =>
        omit(item, ['interval', 'source', 'paramDefault']),
      );
      return schemaData;
    }
  }

  handleOpenModel($event: any): void {
    $event.stopPropagation();
    this.showIframeModal = true;
  }

  // 开窗打开
  handleOpenSqlModal() {
    this.sqlVisible = true;
  }

  // 开窗关闭
  handleCancelSqlModal() {
    this.sqlVisible = false;
  }

  ngOnDestroy(): void {
    window.removeEventListener('message', this.handleDccData);
  }
}
