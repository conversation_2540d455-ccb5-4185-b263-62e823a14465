import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Node, Edge, Graph } from '@antv/x6';
import { Snapline } from '@antv/x6-plugin-snapline';
import { Keyboard } from '@antv/x6-plugin-keyboard';
import { Selection } from '@antv/x6-plugin-selection';
import { Scroller } from '@antv/x6-plugin-scroller';
import { DataStreamStoreService } from './store.service';
import { NODE_WARNING_COLOR, NodeType } from '../config/constants';
import { cloneDeep, isEmpty, omit } from 'lodash';
import { IField } from '../components/data-stream-designer-node-property/node-interface';
import { generateNodeId, handleGraphDataStructure, isObjectAndHasProperty } from '../config/utils';
import { RegexHelper } from 'pages/app-agile-data/utils/regex';

@Injectable()
export class DataStreamToolsService {
  constructor(private translateService: TranslateService, private dataStreamStoreService: DataStreamStoreService) {}

  // 画布插件配置
  toolsGraphPlugin(graph) {
    graph
      .use(new Snapline({ enabled: true })) // 对齐线
      .use(new Keyboard({ enabled: true })) // 快捷键
      .use(
        new Selection({
          // 框选
          enabled: true,
          multiple: true,
          rubberband: true,
          rubberEdge: true,
          movable: true,
          showNodeSelectionBox: true,
          modifiers: 'shift',
        }),
      )
      .use(
        new Scroller({
          // 滚动
          enabled: true,
          pageVisible: false,
          pageBreak: true,
          autoResize: true,
          pannable: true,
          autoResizeOptions: {
            border: 50,
          },
        }),
      );
  }

  // 初始化工具栏
  toolsInit(graph) {
    // @ts-ignore
    this.toolsOriginalSize(graph); // 100% 视图
  }

  // 复位
  toolsReset(graph) {
    const nodes = graph.getNodes();
    const centerNode = nodes?.find((node) => node?.data?.nodeType === NodeType.DATA_PULLING);
    centerNode && graph.centerCell(centerNode);
  }

  // 100% 还原
  toolsOriginalSize(graph, changeZoomProportion) {
    graph.zoom(1, {
      absolute: true,
    });
    if (changeZoomProportion) {
      changeZoomProportion(100);
    }
  }

  // 改变尺寸
  changeProportion(value): number {
    return Math.round(100 * value);
  }

  // 放大
  toolsEnlarge(graph, changeZoomProportion) {
    graph.zoom(0.2, {
      maxScale: 2,
    });
    changeZoomProportion(this.changeProportion(graph.zoom()));
  }

  // 缩小
  toolsReduce(graph, changeZoomProportion) {
    graph.zoom(-0.2, {
      minScale: 0.1,
    });
    changeZoomProportion(this.changeProportion(graph.zoom()));
  }

  /**
   * 下面用于处理画布的逻辑
   * ==================================================
   */

  // 根据数据渲染画布
  toolsRenderGraphFromJSON(graph, nodes: Node.Metadata[], links: Edge.Metadata[]) {
    const { renderNodes, renderLinks } = handleGraphDataStructure(nodes, links, this.translateService);
    // 渲染到画布上
    graph.fromJSON([...renderNodes, ...renderLinks]);
    // 复位
    this.toolsReset(graph);
  }

  // 获取画布中心坐标
  toolsGraphCenter(graph, graphWrapper) {
    const { origin } = graph.getGraphArea();
    const offset = graph.coord.getClientOffset();
    const viewX = graphWrapper.clientWidth / 2;
    const viewY = graphWrapper.clientHeight / 2;
    const scale = graph.scale();
    const x = origin.x - offset.x / scale.sx + viewX / scale.sx;
    const y = origin.y - offset.y / scale.sy + viewY / scale.sy;

    return { x, y };
  }

  // 获取节点连接桩 group 集合（入线）
  toolsNodeIncomingPortGroups(graph, node) {
    const _groups = [];
    const incomingEdges = graph.getIncomingEdges(node);
    incomingEdges?.forEach((edge) => {
      const targetPortId = edge.getTargetPortId();
      const targetPortMetaData = node.getPort(targetPortId);
      _groups.push(targetPortMetaData.group);
    });
    const groups = [...new Set(_groups)];

    return groups;
  }

  // 获取节点连接桩 group 集合（入线和出线）
  toolsNodePortGroups(graph, node) {
    const _groups = [];
    const outEdges = graph.getOutgoingEdges(node);
    outEdges?.forEach((edge) => {
      const sourcePortId = edge.getSourcePortId();
      const sourcePortMetaData = node.getPort(sourcePortId);
      _groups.push(sourcePortMetaData.group);
    });
    const incomingEdges = graph.getIncomingEdges(node);
    incomingEdges?.forEach((edge) => {
      const targetPortId = edge.getTargetPortId();
      const targetPortMetaData = node.getPort(targetPortId);
      _groups.push(targetPortMetaData.group);
    });
    const groups = [...new Set(_groups)];

    return groups;
  }

  // 通过节点获取节点id某个连接桩id和节点id（边创建格式： { cell: string, port: string }）
  toolsNodeAndPortIdByNodeId(graph, group, nodeId) {
    const node = graph.getCellById(nodeId);
    const port = node.getPortsByGroup(group)[0];

    return {
      cell: node.id,
      port: port.id,
    };
  }

  // 获取单向节点的来源节点连接桩和目标节点连接桩（只有一个入边和一个出边）
  toolsOneWayNodePort(graph, node) {
    const incomingEdges = graph.getIncomingEdges(node);
    const outgoingEdges = graph.getOutgoingEdges(node);
    const sourcePort: any = incomingEdges?.[0]?.source;
    const targetPort: any = outgoingEdges?.[0]?.target;

    return { sourcePort, targetPort };
  }

  /** 判断当前节点是否有下游 */
  toolsNodeHasNextNode = (graph: Graph, nodeId: string): boolean =>
    graph.getOutgoingEdges(graph.getCellById(nodeId)) ? true : false;

  // 通过边获取来源和目标连接桩元信息
  toolsPortMetaDataByEdge(edge) {
    const sourcePortId = edge.getSourcePortId();
    const targetPortId = edge.getTargetPortId();
    const sourceNode = edge.getSourceNode();
    const targetNode = edge.getTargetNode();
    const sourcePortMetaData = sourceNode.getPort(sourcePortId);
    const targetPortMetaData = targetNode.getPort(targetPortId);

    return { sourcePortMetaData, targetPortMetaData };
  }

  // 通过边 ID 获取来源和目标节点坐标
  toolsNodePositionByEdgeId(graph, edgeId) {
    const edge: any = graph.getCellById(edgeId);
    const sourceNode: any = edge.getSourceCell();
    const targetNode: any = edge.getTargetCell();
    const sourcePosition = sourceNode.position();
    const targetPosition = targetNode.position();

    return { sourcePosition, targetPosition };
  }

  // 通过边 ID 获取边、来源和目标节点
  toolsEdgeAndNodeByEdgeId(graph, edgeId) {
    const edge: any = graph.getCellById(edgeId);
    const sourceNode: any = edge.getSourceCell();
    const targetNode: any = edge.getTargetCell();

    return { edge, sourceNode, targetNode };
  }

  // 画布上删除某个节点
  toolsRemoveNode(graph, node) {
    // 删除属性配置
    const { nodeId } = node?.data || {};
    this.dataStreamStoreService.setState((state) => {
      const { propertiesObj } = state;
      if (propertiesObj?.[nodeId]) {
        delete propertiesObj[nodeId];
      }
    });

    graph.removeCell(node);
  }

  /**
   * 节点样式控制
   * @param node 节点
   * @param type 类型，none
   */
  toolsNodeStyle(node, type?) {
    const { isSelected, isPassed } = node?.data || {};

    if (type === 'none') {
      // 无边框
      node.setAttrByPath('body/strokeWidth', 0);
      node.setAttrByPath('body/filter', {
        name: 'dropShadow',
        args: {
          color: '#4943a3',
          dx: 0,
          dy: 0,
          blur: 4,
          opacity: 0.4,
        },
      });
      return;
    }

    node.setAttrByPath('body/filter', {
      name: 'dropShadow',
      args: {
        color: '#ffffff',
        dx: 0,
        dy: 0,
        blur: 0,
        opacity: 0,
      },
    });

    if (isSelected && !isPassed) {
      // 警告加粗
      node.setAttrByPath('body/strokeWidth', 4);
      node.setAttrByPath('contentTop/fill', NODE_WARNING_COLOR);
      node.setAttrByPath('contentBottom/fill', NODE_WARNING_COLOR);
      return;
    }

    if (isSelected && isPassed) {
      // 选中加粗
      node.setAttrByPath('body/strokeWidth', 4);
      node.setAttrByPath('contentTop/fill', '#FFFFFF');
      node.setAttrByPath('contentBottom/fill', '#FFFFFF');
      return;
    }

    if (!isSelected && !isPassed) {
      // 警告不加粗
      node.setAttrByPath('body/strokeWidth', 1);
      node.setAttrByPath('contentTop/fill', NODE_WARNING_COLOR);
      node.setAttrByPath('contentBottom/fill', NODE_WARNING_COLOR);
      return;
    }

    if (!isSelected && isPassed) {
      // 普通边框
      node.setAttrByPath('body/strokeWidth', 1);
      node.setAttrByPath('contentTop/fill', '#FFFFFF');
      node.setAttrByPath('contentBottom/fill', '#FFFFFF');
      return;
    }
  }

  /**
   * 边样式控制
   * @param edge 边
   * @param isHightLight 是否高亮
   */
  toolsEdgeStyle(edge, isHightLight) {
    if (isHightLight) {
      edge.setAttrByPath('line/strokeWidth', 2);
    } else {
      edge.setAttrByPath('line/strokeWidth', 1);
    }

    const targetNode: any = edge.getTargetCell();
    const { nodeType } = targetNode?.data || {};
    if (nodeType !== NodeType.END) {
      edge.setAttrByPath('line/stroke', '#5565FB');
      edge.setAttrByPath('line/strokeDasharray', 0);
    }
  }

  // 清除所有节点的高亮和工具
  toolsClearAllNodesTools(graph) {
    const allNodes = graph.getNodes();
    allNodes?.forEach((node) => {
      // 设置节点未选中状态
      node.setData({ ...(node?.data || {}), isSelected: false });
      // 设置节点样式
      this.toolsNodeStyle(node);
      // 链接桩不可见
      const ports = node.getPorts();
      ports.forEach((port) => {
        node.setPortProp(port.id, 'attrs/circle/style/visibility', 'hidden');
      });
      // 设置节点工具按钮不可见
      node.removeTool('copy-node-button');
      node.removeTool('delete-node-button');
      node.removeTool('template-node-button');
    });
  }

  // 清除所有边的高亮和工具
  toolsClearAllEdgesTools(graph) {
    const allEdges = graph.getEdges();
    allEdges?.forEach((edge) => {
      this.toolsClearEdgeTools(edge);
    });
  }

  // 清除边的高亮和工具
  toolsClearEdgeTools(edge) {
    // 移除删除边的按钮
    edge.removeTool('remove-edge-button');
    // 边恢复默认样式
    this.toolsEdgeStyle(edge, false);
  }

  // 移除对象中所有以$开头字段
  toolsRemoveDollarFields(obj) {
    if (typeof obj !== 'object' || obj === null) {
      return obj; // 非对象或null直接返回
    }
  
    if (Array.isArray(obj)) {
      // 处理数组：创建新数组并递归处理每个元素
      return obj.map(item => this.toolsRemoveDollarFields(item));
    } else if (Object.prototype.toString.call(obj) === '[object Object]') {
      // 处理普通对象：创建新对象并过滤属性
      return Object.keys(obj).reduce((acc, key) => {
        if (!key.startsWith('$')) {
          acc[key] = this.toolsRemoveDollarFields(obj[key]);
        }
        return acc;
      }, {});
    } else {
      // 其他对象类型（如Date/RegExp等）直接返回
      return obj;
    }
  }

  // 处理画布数据
  toolsHandleGraphData(graph) {
    const allNodes = graph.getNodes() || [];
    const allLinks = graph.getEdges() || [];

    const { originalFlowData = {}, basicObj = {}, propertiesObj = {} } = this.dataStreamStoreService.state;
    const configNodes =
      Object.entries(propertiesObj)?.map((l) => {
        const config: any = l?.[1] || {};
        // 移除所有$符号字段
        return this.toolsRemoveDollarFields(config);
      }) || [];
    const configLinks =
      allLinks
        ?.filter((m) => {
          return propertiesObj?.[m.source.cell]?.nodeConfig?.id && propertiesObj?.[m.target.cell]?.nodeConfig?.id;
        })
        ?.map((n) => {
          return {
            id: n.id,
            fromId: propertiesObj?.[n.source.cell]?.nodeConfig?.id,
            toId: propertiesObj?.[n.target.cell]?.nodeConfig?.id,
          };
        }) || [];

    const params = {
      ...(originalFlowData || {}),
      ...(basicObj || {}),
      dataFlowGraph: {
        nodes: allNodes?.map((cell) => cell.toJSON()),
        links: allLinks?.map((cell) => cell.toJSON()),
      },
      dataFlowConfig: {
        nodes: configNodes,
        links: configLinks,
      },
    };

    return params;
  }

  // 检查所有的节点输入配置是否有来源，找不到来源节点则清除
  toolsCheckAllNodeInputConfig() {
    const propertiesObj = this.dataStreamStoreService.state.propertiesObj;
    const propertiesArr = Object.entries(propertiesObj)?.filter((m) => m?.[0] && m?.[0] !== 'undefined') || [];
    const propertiesNodeIds = propertiesArr?.map((item) => item[0]);
    const propertiesConfigs = propertiesArr?.map((item) => item[1]);

    propertiesConfigs.forEach((i) => {
      const { inputConfig, nodeId }: any = i || {};
      const { inputParams, dataSet }: any = inputConfig || {};
      if (inputParams?.length) {
        const absentParamsNodes = inputParams?.filter((j) => !propertiesNodeIds.includes(j.nodeId)) || [];
        if (absentParamsNodes?.length) {
          this.dataStreamStoreService.setState((state) => {
            state.propertiesObj[nodeId].inputConfig.inputParams =
              inputParams?.filter((j) => propertiesNodeIds.includes(j.nodeId)) || [];
          });
        }
      }
      if (dataSet?.length) {
        const absentDatasetNodes = dataSet?.filter((j) => !propertiesNodeIds.includes(j.nodeId)) || [];
        if (absentDatasetNodes?.length) {
          this.dataStreamStoreService.setState((state) => {
            state.propertiesObj[nodeId].inputConfig.dataSet =
              dataSet?.filter((j) => propertiesNodeIds.includes(j.nodeId)) || [];
          });
        }
      }
    });
  }

  // 获取所有的报错信息
  toolsGetAllError() {
    const propertiesObj = this.dataStreamStoreService.state.propertiesObj;
    const propertiesArr = Object.entries(propertiesObj)?.filter((m) => m?.[0] && m?.[0] !== 'undefined') || [];

    const errorsArr =
      propertiesArr?.map((m) => {
        const _nodeId: string = m?.[0];
        const _nodeConfig: any = m?.[1] || {};
        const errors = {
          nodeId: _nodeId,
          nodeName: _nodeConfig?.nodeConfig?.name,
          nodeType: _nodeConfig?.nodeType,
          errors: _nodeConfig?.errors,
        };
        return errors;
      }) || [];

    const errorsList = errorsArr?.filter((item) => item?.errors?.length);
    return errorsList;
  }

  /** 刷数据 MappingConfig */
  toolsUpdateMapping(flowNodes: any): any[] {
    flowNodes = flowNodes.map((node: any) => {
      if (Reflect.has(node, 'mappingConfig')) return node;
      if ([NodeType.BMD_GENERAL, NodeType.SQL].includes(node.nodeType)) {
        // 处理bmd节点
        const inputParamList = [
          ...(node?.inputConfig?.inputParams?.map((item) => item.field)?.flat(Infinity) ?? []),
          ...(node?.inputConfig?.customParams ?? []),
        ];
        const schemeParams = node?.inputConfig?.schemeParams || [];
        const mappingConfig = [];
        cloneDeep(schemeParams).forEach((sp: any) => {
          sp.mappingValue = {};
          if (sp?.quoteParam && !!sp?.quoteParam) {
            sp.mappingValue = inputParamList.find((ip) => ip.data_name === sp?.quoteParam);
          }
          sp.can_del = false;
          mappingConfig.push(sp);
        });
        node.mappingConfig = mappingConfig;
      } else {
        // 处理参数
        let inputParamList = [
          ...(node?.inputConfig?.inputParams?.map((item) => item.field)?.flat(Infinity) ?? []),
          ...(node?.inputConfig?.customParams ?? []),
        ];
        // 兼容推送节点的输出
        if (node.nodeType === NodeType.DATA_PULLING) node.outputConfig.paramList = [];
        inputParamList = inputParamList.filter((field: IField) => {
          if (!Reflect.has(field, 'accept')) return true;
          return field?.accept === true;
        });
        inputParamList = cloneDeep(inputParamList).map((ip) => {
          let _ip = cloneDeep(ip);
          // 兼容new_data_name
          if (ip?.rename || Reflect.has(ip, 'new_data_name')) {
            ip.data_name = ip?.new_data_name;
            ip.name = ip?.new_name;
            ip.lang = {
              name: ip.lang.new_name,
            };
            ip = omit(ip, ['new_data_name', 'new_name', 'rename', 'lang.new_name']);
          }
          _ip = omit(_ip, ['new_data_name', 'new_name', 'rename', 'lang.new_name']);
          // 兼容推送节点的输出
          if (node.nodeType === NodeType.DATA_PULLING) node.outputConfig.paramList.push(cloneDeep(ip));
          ip.mappingValue = {
            ...cloneDeep(_ip),
            nodeId: node?.inputConfig?.inputParams?.[0]?.nodeId,
          };
          ip.nodeId = node.nodeId;
          if (node.nodeType === NodeType.FORMULAC) {
            // 处理算术节点mapping是否可以删除
            const { formula = '' } = node.nodeConfig?.formulaConfig || {};
            const matches: string[] = RegexHelper.variableFieldReg(formula);
            ip.can_del = !matches?.includes(ip.data_name);
          }
          return ip;
        });
        // 处理数据集
        let inputDataSetList = [...(node?.inputConfig?.dataSet ?? [])];
        inputDataSetList = cloneDeep(inputDataSetList).map((iDS: any) => {
          iDS.field = iDS.field.filter((field: IField) => {
            if (!Reflect.has(field, 'accept')) return true;
            return field?.accept === true;
          });
          const _iDS = cloneDeep(iDS);
          iDS.field = iDS?.field?.map((field: any) => {
            // 兼容new_data_name
            if (field?.rename || Reflect.has(field, 'new_data_name')) {
              field.data_name = field?.new_data_name;
              field.name = field?.new_name;
              field.lang = {
                name: field.lang.new_name,
              };
              field = omit(field, ['new_data_name', 'new_name', 'rename', 'lang.new_name']);
            }
            return field;
          });
          _iDS.field = _iDS?.field?.map((field: any) =>
            omit(field, ['new_data_name', 'new_name', 'rename', 'lang.new_name']),
          );
          iDS.mappingValue = cloneDeep(_iDS);
          // 兼容推送节点的输出
          if (node.nodeType === NodeType.DATA_PULLING) node.outputConfig.dataSet = cloneDeep(iDS);
          iDS?.field?.forEach((field: any, index: number) => {
            field.mappingValue = cloneDeep(_iDS.field[index]);
            field.can_del = true;
            if (node.nodeType === NodeType.MICRO_TRANS) {
              // 处理运算节点mapping是否可以删除
              const { operationConfig = {} } = node.nodeConfig;
              let configType = '';
              if (
                operationConfig.fieldConfig &&
                JSON.stringify(operationConfig.fieldConfig) !== '{}' &&
                (operationConfig.fieldConfig?.dimensions?.length > 0 ||
                  operationConfig.fieldConfig?.measures?.length > 0)
              ) {
                configType = 'field';
                const { dimensions = [], measures = [] } = operationConfig.fieldConfig;
                let expressionField = [];
                measures.forEach((ms) => {
                  if (Reflect.has(ms, 'expression')) {
                    const matches: string[] = RegexHelper.variableFieldReg(ms.expression);
                    expressionField = [...expressionField, ...matches];
                  }
                });
                const fieldUsed = [...dimensions, ...measures].map((m) => m.data_name);
                if ([...fieldUsed, ...expressionField].includes(field.data_name)) field.can_del = false;
              } else if (operationConfig.calcField && operationConfig.calcField.length > 0) {
                configType = 'calculate';
                const { calcField } = operationConfig;
                let expressionField = [];
                calcField.forEach((cf) => {
                  const matches: string[] = RegexHelper.variableFieldReg(cf.expression);
                  expressionField = [...expressionField, ...matches];
                });
                if (expressionField.includes(field.data_name)) field.can_del = false;
              } else if (operationConfig?.queryNum) {
                configType = 'search';
                field.can_del = true;
              } else if (operationConfig.sort && operationConfig.sort.length > 0) {
                configType = 'order';
                const { sort } = operationConfig;
                const fieldUsed = sort.map((s) => s.field);
                if (fieldUsed.includes(field.data_name)) field.can_del = false;
              } else {
                configType = null;
              }
              operationConfig.configType = configType;
            }
            if (node.nodeType === NodeType.DATA_JOIN) {
              // 处理数据连接节点mapping是否可以删除
              const { operationConfig = {} } = node.nodeConfig;
              const fieldUsed = [
                ...(operationConfig?.left?.joinFields ?? []),
                ...(operationConfig?.right?.joinFields ?? []),
              ].map((fu) => fu.data_name);
              if (fieldUsed.includes(field.data_name)) field.can_del = false;
            }
          });
          return iDS;
        });
        node.mappingConfig = [...inputParamList, ...inputDataSetList];
        if (node?.outputConfig?.dataSet) {
          node.outputConfig.dataSet.field =
            node?.outputConfig?.dataSet?.field?.map((field) => {
              if (field?.rename || Reflect.has(field, 'new_data_name')) {
                field.data_name = field?.new_data_name;
                field.name = field?.new_name;
                field.lang = {
                  name: field.lang.new_name,
                };
                field = omit(field, ['new_data_name', 'new_name', 'rename', 'lang.new_name']);
              }
              return field;
            }) ?? [];
        }
      }
      return node;
    });
    return flowNodes;
  }

  /** 刷数据 MappingId */
  toolsUpdateMappingId = (flowNodes: any): any[] =>
    flowNodes.map((node: any): any => {
      const { mappingConfig } = node;
      if (mappingConfig.find((item: any) => Reflect.has(item, 'mappingId'))) return node;
      mappingConfig.forEach((item: IField) => {
        const mappingId = `MAPPING_ID_${generateNodeId()}`;
        if (!Reflect.has(item, 'mappingId')) {
          item.mappingId = mappingId;
        }
        if (!Reflect.has(item, 'mappingName')) {
          item.mappingName = item?.nodeName ?? '';
        }
        if (Reflect.has(item, 'lang')) {
          if (!Reflect.has(item.lang ?? {}, 'mappingName')) {
            item.lang = {
              ...(item?.lang || {}),
              mappingName: item.lang?.name ?? {},
            };
          }
        } else {
          item.lang = {
            name: {
              zh_CN: item.name,
              zh_TW: item.name,
              en_US: item.name,
            },
            mappingName: {
              zh_CN: item.name,
              zh_TW: item.name,
              en_US: item.name,
            },
          };
        }
      });
      if (node.nodeType === NodeType.MICRO_TRANS) {
        const { operationConfig = {} } = node.nodeConfig;
        if (!Reflect.has(operationConfig, 'mappingId')) {
          operationConfig.mappingId = mappingConfig.find(
            (mc: IField): boolean => mc.nodeId === operationConfig.nodeId,
          )?.mappingId;
        }
      }
      if (node.nodeType === NodeType.DATA_JOIN) {
        const { left, right } = node.nodeConfig?.operationConfig ?? {};
        if (!!left?.id) {
          left.id = mappingConfig.find((mc: IField): boolean => mc.nodeId === left.id)?.mappingId;
        }
        if (right?.id) {
          right.id = mappingConfig.find((mc: IField): boolean => mc.nodeId === right.id)?.mappingId;
        }
      }
      return node;
    });

  /** 刷数据 MappingName */
  toolsUpdateMappingName = (flowNodes: any): any[] =>
    flowNodes.map((node: any): any => {
      const { mappingConfig, nodeType } = node;
      if ([NodeType.MICRO_TRANS, NodeType.DATA_JOIN].includes(nodeType)) {
        const { dataSet = [] } = node?.inputConfig || {};
        if (dataSet.length > 0) {
          dataSet.forEach((ds: any, index: number) => {
            if (ds.nodeName !== mappingConfig?.[index]?.nodeName) {
              mappingConfig[index].nodeName = ds.nodeName;
            }
          });
        }
      }
      mappingConfig.forEach((item: IField) => {
        if (!Reflect.has(item, 'mappingName')) {
          item.mappingName = item?.nodeName ?? '';
        }
        if (Reflect.has(item, 'lang')) {
          if (!Reflect.has(item.lang ?? {}, 'mappingName')) {
            item.lang = {
              ...(item?.lang || {}),
              mappingName: item.lang?.name ?? {},
            };
          }
        }
      });
      return node;
    });

  /** 刷数据  */
  toolsUpdateMappingValue = (flowNodes: any): any[] =>
    flowNodes.map((node: any): any => {
      const { mappingConfig, nodeType, inputConfig } = node;
      mappingConfig.forEach((mc: IField): void => {
        if (!isEmpty(mc?.mappingValue)) {
          if ([NodeType.FORMULAC, NodeType.BMD_GENERAL, NodeType.SQL, NodeType.DATA_PULLING].includes(nodeType)) {
            const { data_name, data_type, name } = mc?.mappingValue;
            const { inputParams = [], customParams = [] } = inputConfig;
            if (Reflect.has(mc?.mappingValue, 'nodeId')) {
              const _nodeIds = inputParams.map((ip: IField) => ip.nodeId);
              const nodeIds = [node.nodeId, ..._nodeIds];
              if (!nodeIds.includes(mc?.mappingValue.nodeId)) {
                const inputParam = inputParams.find((param: IField): unknown =>
                  param.field.find(
                    (p: IField): boolean => p.data_name === data_name && p.data_type === data_type && p.name === name,
                  ),
                );
                if (inputParam) {
                  mc.mappingValue.nodeId = inputParam.nodeId;
                  return;
                }
                if (nodeType !== NodeType.DATA_PULLING) {
                  mc.mappingValue.nodeId = node.nodeId;
                }
              }
              return;
            } else {
              const inputParam = inputParams.find((param: IField): unknown =>
                param.field.find(
                  (p: IField): boolean => p.data_name === data_name && p.data_type === data_type && p.name === name,
                ),
              );
              if (inputParam) {
                mc.mappingValue.nodeId = inputParam.nodeId;
                return;
              }
              const customParam = customParams.find(
                (param: IField): boolean =>
                  param.data_name === data_name && param.data_type === data_type && param.name === name,
              );
              if (customParam) {
                mc.mappingValue.nodeId = node.nodeId;
                return;
              }
            }
          }
        }
      });
      return node;
    });

  /** 删除一些没有必要的字段 */
  toolsDeleteUnusedField = (flowNodes: any): any[] =>
    flowNodes.map((node: any): any => {
      // 删除mappingConfig里面的nodeId
      const { mappingConfig } = node;
      mappingConfig.forEach((item: any) => {
        Reflect.deleteProperty(item, 'nodeId');
        item.mappingValue = omit(item.mappingValue, ['decimal', 'formula', 'percent']);
      });
      return node;
    });

  /**
   *
   * @param currentNodeId 当前节点ID
   * @param selectID 下拉框选择后的dataSet id
   * @param dataSetIndex 当前编辑是哪个数组
   */
  toolsUpdateAfterSelectChange({ currentNodeId, selectID, dataSetIndex }) {
    this.dataStreamStoreService.setState((state) => {
      const { propertiesObj } = state;
      const currentPropertiesObj = propertiesObj?.[currentNodeId] || {};
      const dataSet = [...(currentPropertiesObj?.inputConfig?.dataSet || [])];
      const afterSelectIndex = dataSet.findIndex((item) => item?.nodeId === selectID);
      if (afterSelectIndex >= -1) {
        propertiesObj[currentNodeId].mappingConfig[dataSetIndex].mappingValue = dataSet[afterSelectIndex];
      }
    });
  }

  // 更新复制节点映射id
  toolsUpdateCopyNodeSourceId(config, mapping) {
    const nodePropertiesObj: any = cloneDeep(config);

    // 最外层
    nodePropertiesObj.nodeId = mapping?.[nodePropertiesObj.nodeId] ?? nodePropertiesObj.nodeId;

    // inputConfig 层
    if (nodePropertiesObj?.inputConfig?.hasOwnProperty('dataSet')) {
      const _dataSet = nodePropertiesObj.inputConfig.dataSet;
      _dataSet.forEach((i) => {
        if (i.hasOwnProperty('nodeId')) {
          i.nodeId = mapping?.[i.nodeId] ?? i.nodeId;
        }
      });
    }
    if (nodePropertiesObj?.inputConfig?.hasOwnProperty('inputParams')) {
      const _inputParams = nodePropertiesObj.inputConfig.inputParams;
      _inputParams.forEach((j) => {
        // 判断 field 是否存在 nodeId，存在再更新
        j.field?.map((item) => {
          if (item.hasOwnProperty('nodeId')) {
            return {
              ...(item || {}),
              nodeId: mapping?.[item.nodeId] ?? item.nodeId,
            };
          }
          return item;
        });
        if (j.hasOwnProperty('nodeId')) {
          j.nodeId = mapping?.[j.nodeId] ?? j.nodeId;
        }
      });
    }

    // mappingConfig 层
    nodePropertiesObj?.mappingConfig?.forEach((k) => {
      if (k.hasOwnProperty('nodeId')) {
        k.nodeId = mapping?.[k.nodeId] ?? k.nodeId;
      }
      if (k?.mappingValue?.hasOwnProperty('nodeId')) {
        k.mappingValue.nodeId = mapping?.[k?.mappingValue?.nodeId] ?? k.mappingValue.nodeId;
      }
    });

    // nodeConfig 层
    const { nodeConfig } = nodePropertiesObj || {};
    const { operationConfig } = nodeConfig || {};
    if (nodeConfig?.hasOwnProperty('id')) {
      nodeConfig.id = mapping?.[nodeConfig.id] ?? nodeConfig.id;
    }
    if (operationConfig?.hasOwnProperty('nodeId')) {
      operationConfig.nodeId = mapping?.[operationConfig.nodeId] ?? operationConfig.nodeId;
    }
    if (operationConfig?.left?.hasOwnProperty('id')) {
      operationConfig.left.id = mapping?.[operationConfig.left.id] ?? operationConfig.left.id;
    }
    if (operationConfig?.right?.hasOwnProperty('id')) {
      operationConfig.right.id = mapping?.[operationConfig.right.id] ?? operationConfig.right.id;
    }

    // outputConfig 层
    const { outputConfig } = nodePropertiesObj;
    if (outputConfig?.hasOwnProperty('dataSet')) {
      outputConfig.dataSet = {
        ...(outputConfig.dataSet || {}),
        nodeId: mapping?.[outputConfig?.dataSet?.nodeId] ?? outputConfig?.dataSet?.nodeId,
      };
    }
    if (outputConfig?.hasOwnProperty('params')) {
      outputConfig.params = {
        ...(outputConfig.params || {}),
        nodeId: mapping?.[outputConfig?.params?.nodeId] ?? outputConfig?.params?.nodeId,
      };
    }

    return nodePropertiesObj;
  }

  // 更新复制节点属性中配置数据
  toolsUpdateCopyNodeData(config, nodeIds) {
    const { nodeType } = config || {};
    const nodePropertiesObj: any = cloneDeep(config);
    if (
      [
        NodeType.BMD_GENERAL,
        NodeType.SQL,
        NodeType.FORMULAC,
      ].includes(nodeType)
    ) {
      // inputConfig inputParams
      nodePropertiesObj.inputConfig.inputParams =
        nodePropertiesObj?.inputConfig?.inputParams?.filter((item) => nodeIds.includes(item.nodeId)) || [];
      // mappingConfig
      nodePropertiesObj.mappingConfig =
        nodePropertiesObj?.mappingConfig?.map((i) => {
          const _mappingValue =
            nodeIds.includes(i?.mappingValue?.nodeId) || !i?.mappingValue?.hasOwnProperty('nodeId')
              ? i.mappingValue
              : {};
          return {
            ...i,
            mappingValue: _mappingValue,
          };
        }) || [];
    }

    if ([NodeType.MICRO_TRANS, NodeType.DATA_JOIN, NodeType.API].includes(nodeType)) {
      // inputConfig dataSet
      nodePropertiesObj.inputConfig.dataSet =
        nodePropertiesObj?.inputConfig?.dataSet?.filter((item) => nodeIds.includes(item.nodeId)) || [];
      // mappingConfig
      nodePropertiesObj.mappingConfig =
        nodePropertiesObj?.mappingConfig?.map((i) => {
          const _mappingValue =
            nodeIds.includes(i?.mappingValue?.nodeId) || !i?.mappingValue?.hasOwnProperty('nodeId')
              ? i.mappingValue
              : {};
          const _field =
            i?.field?.map((j) => {
              const _mappingValue2 = nodeIds.includes(j.nodeId) || !j.hasOwnProperty('nodeId') ? j.mappingValue : {};
              return {
                ...j,
                mappingValue: _mappingValue2,
              };
            }) || [];
          return {
            ...i,
            mappingValue: _mappingValue,
            field: _field,
          };
        }) || [];
    }
    return nodePropertiesObj;
  }

  // 单个校验节点错误信息
  toolsSingleValidErrorMsg(data) {
    const { nodeType, inputConfig, mappingConfig, nodeConfig, outputConfig } = data || {};

    const errorArr = [];

    if (NodeType.BMD_GENERAL === nodeType) {
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (!nodeConfig?.dataSourceConfig?.modelCode) {
        errorArr.push(this.translateService.instant('dj-节点设定中，数据来源不能为空'));
      }
      if (!isObjectAndHasProperty(outputConfig?.dataSet)) {
        errorArr.push(this.translateService.instant('dj-输出预览中，输出数据集不能为空'));
      }
      if (mappingConfig?.length > 0) {
        mappingConfig.forEach((item) => {
          if (isEmpty(item?.mappingValue)) {
            errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
          }
        });
      }
      if (this.dataStreamStoreService.get('flowType') === 'INDICATOR') {
        const allStandardParams = this.dataStreamStoreService.get('allStandardParamsData');
        outputConfig?.dataSet?.field?.forEach((i) => {
          const isExistField = allStandardParams?.find((j) => i.data_name === j.data_name);
          if (!isExistField) {
            errorArr.push(this.translateService.instant('dj-节点输出字段中，标准参数字段', { field: i.data_name }));
          }
        });
      }
      return errorArr;
    }

    if (NodeType.SQL === nodeType) {
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (!nodeConfig?.dataSourceConfig?.selectSql) {
        errorArr.push(this.translateService.instant('dj-节点设定中，业务查询SQL文本不能为空'));
      }
      if (!nodeConfig?.dataSourceConfig?.totalSql) {
        errorArr.push(this.translateService.instant('dj-节点设定中，分页总量SQL文本不能为空'));
      }
      if (!nodeConfig?.dataSourceConfig?.output?.length) {
        errorArr.push(this.translateService.instant('dj-节点设定中，输出字段不能为空'));
      }
      if (nodeConfig?.dataSourceConfig?.output?.length) {
        const invalidFields: any[] =
          nodeConfig?.dataSourceConfig?.output?.filter((i) => !i.name || !i.data_type)?.map((j) => j.data_name) || [];
        if (invalidFields?.length) {
          errorArr.push(
            this.translateService.instant('dj-节点设定中，输出字段缺少必填项配置', { name: invalidFields.join('、') }),
          );
        }
      }
      if (!isObjectAndHasProperty(outputConfig?.dataSet)) {
        errorArr.push(this.translateService.instant('dj-输出预览中，输出数据集不能为空'));
      }
      if (mappingConfig?.length > 0) {
        mappingConfig.forEach((item) => {
          if (isEmpty(item?.mappingValue)) {
            errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
          }
        });
      }
      if (this.dataStreamStoreService.get('flowType') === 'INDICATOR') {
        const allStandardParams = this.dataStreamStoreService.get('allStandardParamsData');
        outputConfig?.dataSet?.field?.forEach((i) => {
          const isExistField = allStandardParams?.find((j) => i.data_name === j.data_name);
          if (!isExistField) {
            errorArr.push(this.translateService.instant('dj-节点输出字段中，标准参数字段', { field: i.data_name }));
          }
        });
      }
      if (nodeConfig?.dynamicQuery === 'Y') {
        const field = outputConfig?.dataSet?.field || [];
        const dynamicDataError = field.find((item) => {
          return item?.data_type && (!item?.extractionType || (item?.extractionType === 'measure' && !item?.extractionFunction));
        })
        if (dynamicDataError) {
          errorArr.push(this.translateService.instant('dj-输出预览中，输出数据集的动态取数配置不能为空'));
        }
      }
      return errorArr;
    }

    if (NodeType.FORMULAC === nodeType) {
      if (!inputConfig?.inputParams?.length && !inputConfig?.customParams?.length) {
        errorArr.push(this.translateService.instant('dj-输入配置中，节点输入参数和自定义参数不能同时为空'));
      }
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (!nodeConfig?.formulaConfig?.data_name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，输出参数设定的字段不能为空'));
      }
      if (!nodeConfig?.formulaConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，输出参数设定的名称不能为空'));
      }
      if (!nodeConfig?.formulaConfig?.data_type) {
        errorArr.push(this.translateService.instant('dj-节点设定中，输出参数设定的类型不能为空'));
      }
      if (!nodeConfig?.formulaConfig?.formula) {
        errorArr.push(this.translateService.instant('dj-节点设定中，输出参数设定的计算公式不能为空'));
      }
      if (!isObjectAndHasProperty(outputConfig?.params)) {
        errorArr.push(this.translateService.instant('dj-输出预览中，输出参数不能为空'));
      }
      if (mappingConfig?.length > 0) {
        mappingConfig.forEach((item) => {
          if (isEmpty(item?.mappingValue)) {
            errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
          }
        });
      }
      return errorArr;
    }

    if (NodeType.MICRO_TRANS === nodeType || NodeType.DATA_JOIN === nodeType) {
      if (!inputConfig?.dataSet?.length) {
        errorArr.push(this.translateService.instant('dj-输入配置中，节点输入数据集不能为空'));
      }
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (!nodeConfig?.operationConfig?.action) {
        errorArr.push(this.translateService.instant('dj-节点设定中，运算配置不能为空'));
      }
      if (nodeConfig?.operationConfig?.action === 'dataJoin') {
        const { left, right, joinType } = nodeConfig?.operationConfig || {};
        if (!joinType) {
          errorArr.push(this.translateService.instant('dj-节点设定中，运算配置的连接方式不能为空'));
        }
        if (!left?.id) {
          errorArr.push(this.translateService.instant('dj-节点设定中，运算配置的数据集1不能为空'));
        }
        if (!right?.id) {
          errorArr.push(this.translateService.instant('dj-节点设定中，运算配置的数据集2不能为空'));
        }
      }
      if (nodeConfig?.operationConfig?.action === 'dataProcess') {
        const { mappingId } = nodeConfig?.operationConfig;
        if (!mappingId) {
          errorArr.push(this.translateService.instant('dj-节点设定中，运算配置的数据集不能为空'));
        }
      }
      if (!isObjectAndHasProperty(outputConfig?.dataSet)) {
        errorArr.push(this.translateService.instant('dj-输出预览中，输出数据集不能为空'));
      }
      if (mappingConfig?.length > 0) {
        mappingConfig.forEach((item) => {
          if (isEmpty(item?.mappingValue)) {
            errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
          }
          item?.field?.forEach((field) => {
            if (isEmpty(field?.mappingValue)) {
              errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
            }
          });
        });
      }
      return errorArr;
    }

    if (NodeType.API === nodeType) {
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (!nodeConfig?.apiConfig?.apiType || !nodeConfig?.apiConfig?.apiId) {
        errorArr.push(this.translateService.instant('dj-节点设定中，API配置的选择API不能为空'));
      }
      if (!isObjectAndHasProperty(outputConfig?.dataSet)) {
        errorArr.push(this.translateService.instant('dj-输出预览中，输出数据集不能为空'));
      }
      if (mappingConfig?.length > 0) {
        mappingConfig.forEach((item) => {
          if (!isEmpty(item.mappingValue)) {
            item?.field?.forEach((field) => {
              if (field.need_relate && !field.isFixedValue && isEmpty(field.mappingValue)) {
                errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
              }
            });
          }
        });
      }
      if (nodeConfig?.dynamicQuery === 'Y') {
        const field = outputConfig?.dataSet?.field || [];
        const dynamicDataError = field.find((item) => {
          return item?.data_type && (!item?.extractionType || (item?.extractionType === 'measure' && !item?.extractionFunction));
        })
        if (dynamicDataError) {
          errorArr.push(this.translateService.instant('dj-输出预览中，输出数据集的动态取数配置不能为空'));
        }
      }
      return errorArr;
    }

    if (NodeType.DATA_PULLING === nodeType) {
      if (!inputConfig?.inputParams?.length && !inputConfig?.dataSet?.length) {
        errorArr.push(this.translateService.instant('dj-输入配置中，节点输入参数和节点输入数据集不能同时为空'));
      }
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (mappingConfig?.length > 0) {
        mappingConfig?.forEach((item) => {
          if (isEmpty(item?.mappingValue)) {
            errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
          }
          item?.field?.forEach((field) => {
            if (isEmpty(field?.mappingValue)) {
              errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
            }
          });
        });
      }
      return errorArr;
    }

    if (NodeType.METRIC === nodeType) {
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (!nodeConfig?.metricConfig?.metricCode) {
        errorArr.push(this.translateService.instant('dj-节点设定中，指标配置不能为空'));
      }
      if (!isObjectAndHasProperty(outputConfig?.dataSet)) {
        errorArr.push(this.translateService.instant('dj-输出预览中，输出数据集不能为空'));
      }
      if (mappingConfig?.length > 0) {
        mappingConfig.forEach((item) => {
          if (isEmpty(item?.mappingValue)) {
            errorArr.push(this.translateService.instant('dj-节点参数对应的输入参数不能为空'));
          }
        });
      }
      return errorArr;
    }

    if (NodeType.INPUT === nodeType) {
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (!nodeConfig?.inputConfig?.data_name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，参数配置的字段不能为空'));
      }
      if (!nodeConfig?.inputConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，参数配置的说明不能为空'));
      }
      if (!nodeConfig?.inputConfig?.data_type) {
        errorArr.push(this.translateService.instant('dj-节点设定中，参数配置的类型不能为空'));
      }
      if (!isObjectAndHasProperty(outputConfig?.params)) {
        errorArr.push(this.translateService.instant('dj-输出预览中，输出参数不能为空'));
      }
      return errorArr;
    }

    if (NodeType.BIZ_PARAM === nodeType) {
      if (!nodeConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，节点名称不能为空'));
      }
      if (!nodeConfig?.businessVariablesConfig?.businessVariable) {
        errorArr.push(this.translateService.instant('dj-节点设定中，参数配置的业务变量不能为空'));
      }
      if (!nodeConfig?.businessVariablesConfig?.data_name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，参数配置的字段不能为空'));
      }
      if (!nodeConfig?.businessVariablesConfig?.name) {
        errorArr.push(this.translateService.instant('dj-节点设定中，参数配置的说明不能为空'));
      }
      if (!nodeConfig?.businessVariablesConfig?.data_type) {
        errorArr.push(this.translateService.instant('dj-节点设定中，参数配置的类型不能为空'));
      }
      if (!isObjectAndHasProperty(outputConfig?.params)) {
        errorArr.push(this.translateService.instant('dj-输出预览中，输出参数不能为空'));
      }
      return errorArr;
    }

    return errorArr;
  }
}
