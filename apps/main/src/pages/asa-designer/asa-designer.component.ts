import { Component, Inject, OnInit, ViewChild, <PERSON>ement<PERSON><PERSON>, On<PERSON><PERSON>roy } from '@angular/core';
import microApp from '@micro-zoe/micro-app';
import { AD_AUTH_TOKEN, AdAuthService } from '../login/service/auth.service';
import { DigiMiddlewareAuthApp } from 'common/config/app-auth-token';
import { environment } from 'environments/environment';
import { AdUserService } from '../login/service/user.service';
import { DomSanitizer } from '@angular/platform-browser';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { Router } from '@angular/router';
import { MicroAppLanguageSyncService } from 'common/service/microapp-language-sync.service';

@Component({
  selector: 'app-asa-designer',
  templateUrl: './asa-designer.component.html',
  styleUrls: ['./asa-designer.component.less'],
  providers: [MicroAppLanguageSyncService],
})
export class AsaDesigner<PERSON>omponent implements OnInit {
  @ViewChild('asaFrame', { static: false }) asaFrame: ElementRef;
  url = 'http://localhost:4202';
  appName = 'asa-designer-web';
  microAppData = {
    auth: this.authService.userPermissionMap,
    authId: this.authService.authId,
    currentLanguage: this.languageService?.currentLanguage || 'zh_CN',
    digiMiddlewareAuthApp: DigiMiddlewareAuthApp,
    userInfo: this.userService.getUserInfo(),
    sessionStorage: sessionStorage,
  };
  jumpLink: any;

  constructor(
    private authService: AdAuthService,
    private userService: AdUserService,
    private languageService: LocaleService,
    private configService: SystemConfigService,
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
    private sanitizer: DomSanitizer,
    private router: Router,
    private microLangSyncService: MicroAppLanguageSyncService,
  ) {
    this.microLangSyncService.syncLanguage(this.appName);
    this.configService.getConfig().subscribe((config) => {
      if (environment.production) {
        this.url = config['asaDesignerUrl'].split('asa-designer-web').join('asa-designer-web-ng');
      }
      this.url = this.url + '?appCode=' + location.search.split('appCode=')?.[1].split('&')?.[0];
      this.jumpLink = this.sanitizer.bypassSecurityTrustResourceUrl(this.url); // 信任该url
      microApp.setData(this.appName, {
        config,
      });
    });
    microApp.setData(this.appName, {
      authToken: this.authToken,
      userInfo: this.userService.getUserInfo(),
    });

    // 将sessionStrage中数据传入子解决方案
    const sessionStorageData = {};
    Object.keys(sessionStorage).forEach((key) => {
      try {
        sessionStorageData[key] = JSON.parse(sessionStorage.getItem(key));
      } catch (error) {
        sessionStorageData[key] = sessionStorage.getItem(key);
      }
    });

    // 将localStorage中数据传入子解决方案
    const localStorageData = {};
    Object.keys(localStorage).forEach((key) => {
      try {
        localStorageData[key] = JSON.parse(localStorage.getItem(key));
      } catch (error) {
        localStorageData[key] = localStorage.getItem(key);
      }
    });

    microApp.setData(this.appName, {
      sessionStorage: sessionStorageData,
      localStorage: localStorageData,
    });
  }

  ngOnInit(): void {}

  onIframeLoad() {
    this.setIframeMsg();
    window.addEventListener('message', this.getIframeMsg.bind(this));
    // 监听sessionStorage变化
    window.addEventListener('storage', this.handleSessionStorageChange.bind(this));
  }

  setIframeMsg() {
    const iframe = this.asaFrame.nativeElement;
    // 确保 iframe 加载完成后调用 postMessage 发送消息

    // 将sessionStrage中数据传入子解决方案
    const sessionStorageData = {};
    Object.keys(sessionStorage).forEach((key) => {
      try {
        sessionStorageData[key] = JSON.parse(sessionStorage.getItem(key));
      } catch (error) {
        sessionStorageData[key] = sessionStorage.getItem(key);
      }
    });
    iframe.contentWindow.postMessage(sessionStorageData, this.url);
  }

  getIframeMsg(event) {
    // 在 父页面 中接收消息
    const iframeUrl = this.url;
    const onLogout = () => {
      localStorage.clear();
      sessionStorage.clear();
      this.userService.isLoggedIn$.next(false);
      this.router.navigateByUrl('/login').then(() => {
        location.reload();
      });
    };
    if (!iframeUrl.includes(event.origin)) return;
    if (event.data == 'logout') {
      onLogout();
    }
  }

  handleSessionStorageChange(e) {
    // const key = e.key; // 变化的key
    // const newValue = e.newValue; // 新的值
    // const oldValue = e.oldValue; // 旧的值

    this.setIframeMsg();
  }

  ngOnDestroy(): void {
    window.removeEventListener('message', this.getIframeMsg);
    window.removeEventListener('storage', this.handleSessionStorageChange);
  }

  // handleCreate(): void {
  //   console.log('asa-designer 创建了');
  // }

  // handleBeforeMount(): void {
  //   console.log('asa-designer 即将被渲染');
  // }

  // handleMount(): void {
  //   console.log('asa-designer 已经渲染完成');
  // }

  // handleUnmount(): void {
  //   console.log('asa-designer 卸载了');
  // }

  // handleError(): void {
  //   console.log('asa-designer 加载出错了');
  // }

  // handleDataChange(e: CustomEvent): void {
  // }

  // changeData() {
  //   microApp.setData(this.appName, {
  //     count: Date.now(),
  //   });
  // }
}
