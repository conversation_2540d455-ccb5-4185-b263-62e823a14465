import { Component, OnInit } from '@angular/core';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { NavigationEnd, Router } from '@angular/router';
import { SystemConfigService } from 'common/service/system-config.service';
import { AdUserService } from 'pages/login/service/user.service';
import { Subscription } from 'rxjs';
import { filter } from 'rxjs/operators';

@Component({
  selector: 'app-iframe-wrap',
  templateUrl: './iframe-wrap.component.html',
  styleUrls: ['./iframe-wrap.component.less'],
})
export class IframeWrapComponent implements OnInit {
  baseUrl = '';
  url: SafeResourceUrl = '';
  routeSubscribe$: Subscription;
  iframeLoaded = false;
  authentication = `?m=1&tk=${this.userService.getUser('iamToken')}`

  constructor(private userService: AdUserService,private router: Router, private configService: SystemConfigService, private domSanitizer: DomSanitizer) {
    this.configService.getConfig().subscribe((config: any): void => {
      this.baseUrl = config.devOpsUrl;
    });
  }

  ngOnInit(): void {
    let url = `${this.baseUrl}${this.router.url}${this.authentication}`;
    this.url = this.domSanitizer.bypassSecurityTrustResourceUrl(url);
    this.routeSubscribe$ = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe((data: any) => {
        url = `${this.baseUrl}${data?.url}${this.authentication}`;
        this.url = this.domSanitizer.bypassSecurityTrustResourceUrl(url);
        // window.history.pushState(history.state, '', data?.url);
        // window.dispatchEvent(new PopStateEvent('popstate', { state: history.state }));
      });
  }

  ngOnDestroy(): void {
    this.routeSubscribe$?.unsubscribe();
  }
}
