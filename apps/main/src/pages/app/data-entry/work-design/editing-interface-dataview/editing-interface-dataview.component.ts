// TODO PC
import { Component, Input, OnInit, TemplateRef, Output, EventEmitter, SimpleChanges } from '@angular/core';
import { EditingInterfaceDataViewService } from './editing-interface-dataview.service';
import { AppService } from 'pages/apps/app.service';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { isAllow } from 'common/utils/auth';
import { AdUserService } from 'pages/login/service/user.service';
type TPageType = 'browse' | 'rule' | 'edit' | 'design';
@Component({
  selector: 'app-editing-interface-dataview',
  template: `
    <app-data-entry-work-design
      *ngIf="useLowcode && !workUseFormio"
      #dataEntryWorkDesign
      [workData]="workData"
      [headerCustomTemplate]="headerCustomTemplate"
      [modelPageType]="pageType"
      [isHideHeaderRight]="isHideHeaderRight"
      (saveAndPublishLoadingChange)="handleSaveAndPublishLoadingChange($event)"
      (contentChangeWithoutSaveChange)="handleContentChangeWithoutSaveChange($event)"
    ></app-data-entry-work-design>
    <app-data-entry-work-design-formio
      *ngIf="!useLowcode || workUseFormio"
      #dataEntryWorkDesign
      [workData]="workData"
      [headerCustomTemplate]="headerCustomTemplate"
      [modelPageType]="pageType"
      [isHideHeaderRight]="isHideHeaderRight"
      (saveAndPublishLoadingChange)="handleSaveAndPublishLoadingChange($event)"
      (contentChangeWithoutSaveChange)="handleContentChangeWithoutSaveChange($event)"
    ></app-data-entry-work-design-formio>
  `,
  styleUrls: [],
})
export class EditingInterfaceDataviewComponent implements OnInit {
  designOptions: any;
  saveSign: any;
  appToken: any;
  workData: any;
  useLowcode: boolean = false;
  @Input() workCode: string; // 作业code
  @Input() dataViewQueryCode: string = ''; // 支持pageUIElement结构跳转到对应数据源的dsl界面
  @Input() pageType: TPageType; // 页面类型
  @Input() headerCustomTemplate: TemplateRef<any> | null = null; // 定制头部（头部的左侧部分）
  @Input() isHideHeaderRight: boolean = false; // 是否隐藏头部右侧操作
  @Input() serviceInfo; // 作业的服务信息
  @Input() isModelDrivenEdit: boolean = false; // 是否是模型驱动解决方案（默认模式）
  @Input() isModelDrivenBrowse: boolean = false; // 是否是模型驱动解决方案（浏览界面模式）
  @Input() workUseFormio: boolean; // 作业是否启用formio
  @Output() saveAndPublishLoadingChange: EventEmitter<boolean> = new EventEmitter(); // 当保存和发布状态变化时触发的事件，配合优化需求，保存和发布时模型驱动解决方案左侧菜单不可点击
  @Output() contentChangeWithoutSaveChange: EventEmitter<boolean> = new EventEmitter(); // 内容变更未保存状态的变化

  constructor(
    private service: EditingInterfaceDataViewService,
    private appService: AppService,
    private languageService: LocaleService,
    protected configService: SystemConfigService,
    protected userService: AdUserService,
  ) {}

  get application() {
    return this.appService?.selectedApp?.code;
  }

  get lang() {
    return this.languageService.currentLanguage;
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('serviceInfo')) {
      this.workData = {
        ...this.workData,
        ...this.serviceInfo,
      };
    }
  }

  ngOnInit(): void {
    this.configService.get('allowLowcodeTenants').subscribe((allowLowcodeTenantsStr) => {
      const allowLowcodeTenants = allowLowcodeTenantsStr?.split(',') ?? [];
      const tenantId = this.userService.getUser('tenantId');
      if (!allowLowcodeTenantsStr || allowLowcodeTenants.includes(tenantId)) {
        this.useLowcode = true;
      }
    });
    // this.appToken = this.configService.get('appToken').toPromise();
    // // 存储作业code
    // this.service.setWorkCode(this.workCode);
    // let type: string;
    // if (this.pageType === 'design') {
    //   type = 'SIGN-DOCUMENT';
    // } else if (this.pageType === 'edit') {
    //   type = 'TREEDATA-DOUBLE-DOCUMENT-MULTI';
    // }
    // this.designOptions = {
    //   code: this.workCode,
    //   application: this.application,
    //   type,
    //   sourceType: 'MODEL_DRIVEN',
    //   language: this.lang,
    //   editAllow,
    //   publishAllow,
    //   lcdpAppToken: this.appToken,
    //   lcdpUserToken: this.userService.getUser('iamToken'),
    //   from: 'ModelDrivenApp',
    //   headerCustomTemplate: this.headerCustomTemplate,
    // };
    this.workData = {
      ...this.serviceInfo,
      code: this.workCode,
      category: 'DataEntry',

      // headerCustomTemplate: this.headerCustomTemplate,
    };
  }

  handleSaveAndPublishLoadingChange(isLoading: boolean) {
    this.saveAndPublishLoadingChange.emit(isLoading);
  }

  handleContentChangeWithoutSaveChange(value: boolean) {
    this.contentChangeWithoutSaveChange.emit(value);
  }
}
