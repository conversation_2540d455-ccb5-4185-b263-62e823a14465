import { Component, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { AppService } from '../../../../apps/app.service';
import { FormBuilder } from '@angular/forms';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { AdUserService } from '../../../../login/service/user.service';
import { DomSanitizer } from '@angular/platform-browser';
import { ActivatedRoute } from '@angular/router';
// import { AdAuthService } from 'pages/login/service/auth.service';
import { AppTypes } from 'pages/app/typings';
import { cloneDeep } from 'lodash';
import { CaseModalComponent } from 'components/bussiness-components/case-modal/case-modal.component';
import { DataEntryWorkDesignComponent } from 'components/page-design/entries/data-entry-work-design/data-entry-work-design.component';
import { ExtendedInfoComponent } from 'components/bussiness-components/extended-info/extended-info.component';
import { BasicDataService } from './basic-data.service';
import { CustomQueryParams } from 'pages/app/other/custom-setting/custom-setting.type';
import { AuthService } from 'common/service/auth.service';
import { AuthOperate } from 'common/types/auth.types';
import { ICheckPageDesign } from './components/backup-branch/backup.types';
import { to } from '../../../../../common/utils/core.utils';
import { DesignerTypes } from 'app/types';
import { IndividualService } from 'pages/individual/individual.service';

const categroyTitleDescription = {
  'SIGN-DOCUMENT': 'dj-新建uibot数据录入单档',
  'DOUBLE-DOCUMENT': 'dj-新建uibot数据录入双档',
  'TREEDATA-SINGLE-DOCUMENT': 'dj-树型uibot',
  'TREEDATA-DOUBLE-DOCUMENT': 'dj-树型uibot',
  'SIGN-DOCUMENT-MODEL_DRIVEN': 'dj-模型单档多栏',
  'SINGLE-DOCUMENT-MODEL_DRIVEN': 'dj-模型单档多栏',
  'DOUBLE-DOCUMENT-FORM-MODEL_DRIVEN': 'dj-模型单档',
  'DOUBLE-DOCUMENT-MODEL_DRIVEN': 'dj-模型双档',
  'DOUBLE-DOCUMENT-MULTI-MODEL_DRIVEN': 'dj-模型多档',
};

@Component({
  selector: 'app-basic-data',
  templateUrl: './basic-data.component.html',
  styleUrls: ['./basic-data.component.less'],
  encapsulation: ViewEncapsulation.None,
})
export class BasicDataComponent implements OnInit {
  @ViewChild('dataForm') dataForm;
  @ViewChild('detailForm') detailForm;
  @ViewChild('detailFormEdit') detailFormEdit;
  @ViewChild('dataEntryWorkDesign') dataEntryWorkDesign: DataEntryWorkDesignComponent;
  @ViewChild('extendedInfoRef') extendedInfoRef: ExtendedInfoComponent;

  useLowcode: boolean = false;
  caseModalVisible: boolean = false;
  caseData: any;
  loading: boolean; // 页面加载状态
  addLoading: boolean; // 新增数据loading
  basicData: any[] = []; // 基础资料数据
  currentIndex: any; // 当前基础资料序号
  currentBasic: any; // 当前基础资料
  addModal: any; // 新增资料开窗
  modalTitle: string; // 开窗标题
  modalTitleDescription: string; // 开窗标题描述
  cloneData: any; // 备份数据
  searchValue: string;
  noSearchData: boolean = false;
  reportFormLang: any;
  drawerDataEntryVisible: boolean = false; // 普通数据录入drawer可见控制
  UIDesignerLoading: boolean = false;
  appToken: string;
  workVisible: any; // 界面设计
  workData: any; // 界面设计数据
  lcdpDesignerUrl: any; // 数据录入ui设计器预览url
  canSaveDesignDrawer = true;
  showConfirm = false; // 数据录入关闭确认弹框
  addCategory: any;
  workType: any; // uibot单双档类型
  // showExcelModal: boolean = false; // excel导入弹框开启
  showCopyModal: boolean = false; // 复制弹框
  designOptions: any; // 设计器配置
  type: any; // 如果type='MODEL_DRIVEN'则表示模型驱动
  modelVisible: boolean = false; // 模型设计开窗可见
  modelTitle: string; // 模型标题
  modelParams: any; // 模型驱动组件需要的接口参数
  saveSign: string = ''; // 保存标识
  isMobile: boolean = false; // 是否为移动入口
  extendedInfo: any = {}; // 扩展信息
  isBakBranch: boolean = false; // 是否备份分支
  isDoMigrate: boolean = false; // 是否开始备份
  migrateBranchInfo: ICheckPageDesign = null; // 迁移分支信息

  detailType: string;
  addModalDetail = false;
  designerType = `${DesignerTypes.WORK}@${this.userService.getUser('branch')}`;

  language: string;

  // 是否公共解决方案
  get isCommonApp() {
    return this.appService.isCommonApp;
  }

  // 界面设计入口参数 控制界面设计打开时的tab页
  uiKey: string = null;
  dataViewQueryCode: string = ''; // 支持pageUIElement结构跳转到对应数据源的dsl界面
  @ViewChild('caseModalRef') caseModalRef: CaseModalComponent;
  constructor(
    private fb: FormBuilder,
    public appService: AppService,
    public basicDataService: BasicDataService,
    private message: NzMessageService,
    private translate: TranslateService,
    private languageService: LocaleService,
    protected userService: AdUserService,
    public adModalService: AdModalService,
    private domSanitizer: DomSanitizer,
    protected configService: SystemConfigService,
    private route: ActivatedRoute, // private authService: AdAuthService,
    private authService: AuthService,
    public individualService: IndividualService,
  ) {
    this.language = this.languageService.currentLocale || 'zh_CN';
  }

  ngOnInit(): void {
    this.configService.getConfig().subscribe((config) => {
      this.appToken = config?.appToken;
      const allowLowcodeTenantsStr = config?.allowLowcodeTenants;
      const allowLowcodeTenants = allowLowcodeTenantsStr?.split(',') ?? [];
      const tenantId = this.userService.getUser('tenantId');
      /**
       * 设计器加载lowcode版本还是formio版本依赖：
       * 1. 环境变量中的ALL_LOWCODE_TENANTS
       * 2. 当前租户id，tenantId
       * 使用lowcode的条件：
       * 1. ALL_LOWCODE_TENANTS配置为空
       * 2. 一旦配置了，必须是配置内的租户才允许使用lowcode设计器
       */
      if (!allowLowcodeTenantsStr || allowLowcodeTenants.includes(tenantId)) {
        this.useLowcode = true;
      }
    });
    this.configService.get('lcdpDesignerUrl').subscribe((lcdpDesignerUrl) => {
      this.lcdpDesignerUrl = lcdpDesignerUrl;
    });
    // 路由选中
    this.route.queryParams.subscribe((param: CustomQueryParams & { code: string }) => {
      const { code, targetCode, uiKey, dataViewQueryCode } = param;
      this.uiKey = uiKey;
      this.dataViewQueryCode = dataViewQueryCode;
      if (code || targetCode) {
        this.handleLoadBasic(code ?? targetCode, () => {
          this.handleInterfaceDesign(this.currentBasic);
        });
      } else {
        this.handleLoadBasic();
      }
    });
    this.isBakBranch = this.userService.getUserInfo()?.branch === 'bak';
  }

  // 是否是场景化套件解决方案
  get isKITApp() {
    return this.appService.selectedApp?.appType === AppTypes.SCENARIO_KIT;
  }

  // 新增个案
  handleAddCase(data: any) {
    this.caseModalVisible = true;
    this.caseData = cloneDeep(data);
  }
  // 保存个案
  saveCase(data: any) {
    const params = {
      newDataEntryCode: data.newCode,
      newDataEntryName: data.newName,
      tenantIds: data.tenantIds,
      dataEntryCode: this.caseData.code,
    };
    this.caseData = cloneDeep(data);
    this.caseData.needLocation = true;
    this.basicDataService.copyMonitorRule(params).subscribe(
      (res) => {
        if (res.code === 0) {
          this.caseModalRef.closeLoad();
          this.message.success(this.translate.instant('dj-操作成功'));
          this.handleLoadBasic();
        }
      },
      () => {
        this.caseModalRef.closeLoad();
      },
    );
  }

  // 搜索
  handleSearch(value: any): void {
    if (value) {
      this.basicData = this.cloneData.filter(
        (d) => d.id?.includes(value) || d.code?.includes(value) || d.name?.includes(value),
      );
      this.noSearchData = this.basicData.length === 0;
    } else {
      this.basicData = this.cloneData;
      this.noSearchData = this.basicData.length === 0;
    }
  }

  // 清空搜索
  handleClearSearch(): void {
    this.searchValue = '';
    this.handleSearch('');
  }

  // 获取基础资料
  handleLoadBasic(currentCode?: string, callBack?): void {
    this.loading = true;
    const param = `?pattern=DATA_ENTRY&application=${this.appService?.selectedApp?.code}`;
    this.basicDataService.loadAppBasicReport(param).subscribe(
      (res) => {
        if (res.code === 0) {
          this.loading = false;
          this.basicData = res.data || [];
          const finded = this.basicData.find((re) => re.code === currentCode);

          if (currentCode && finded) {
            this.currentBasic = finded;
            callBack && callBack();
          }
          if (this.caseData?.needLocation) {
            this.basicData.forEach((item) => {
              if (item.code === this.caseData.newCode) {
                item.location = `id_${item.code}`;
              }
            });
            // 选中样式
            this.handleSelectBasic(this.basicData[0]);
          } else if (!!this.currentBasic) {
            const data = this.basicData.find((s) => s.code === this.currentBasic?.code);
            !!data && this.handleSelectBasic(data, true);
          }
          this.scrollToDetect();
          this.cloneData = this.basicData;
          this.searchValue && this.handleSearch(this.searchValue);
        }
      },
      () => {
        this.loading = false;
      },
    );
  }

  // 滑动到指定位置
  scrollToDetect() {
    setTimeout(() => {
      const dom = `#id_${this.caseData?.newCode || this.caseData?.code}`;
      let domEl = document.querySelector(dom);
      if (domEl) {
        domEl.scrollIntoView();
      }
    }, 10);
  }

  // 选中基础资料
  handleSelectBasic(basic: any, force?: boolean): void {
    const { code } = basic;

    if (code === this.currentBasic?.code && !force) {
      return;
    }
    basic.simpleModelCodeAndServiceCode = basic?.simpleModelCode + basic?.simpleModelServiceCode;
    this.currentBasic = basic;

    this.type = this.currentBasic?.sourceType;
    let cate = this.currentBasic.category || '';

    // 兼容支持分类导航选项
    if (cate?.includes('TREEDATA-') && this.type) {
      cate = cate.substr(9, this.currentBasic.category.length);
    }
    const attr = this.type ? `${cate}-${this.type}` : `${cate}`;
    this.currentBasic.modalTitleDescription = categroyTitleDescription.hasOwnProperty(attr)
      ? this.translate.instant(categroyTitleDescription[attr])
      : '';
  }

  // 添加
  handleAddModal(target): void {
    const { category, type } = target;
    this.type = null;
    if (type) {
      this.type = type;
    }
    this.modalTitle = this.translate.instant(type === 'MODEL_DRIVEN' ? 'dj-新增数据录入作业' : 'dj-新建数据录入');
    const attr = type ? `${category}-${type}` : category;
    this.modalTitleDescription = categroyTitleDescription.hasOwnProperty(attr)
      ? this.translate.instant(categroyTitleDescription[attr])
      : '';
    this.addCategory = category;
    this.addModal = true;
  }

  // 添加-二级窗口
  handleAddDetailModal(data: any): void {
    const { type, action } = data ?? {};
    if (action === 'add') {
      // 打开二级窗口
      this.detailType = type;
      this.addModal = false;
      setTimeout(() => {
        this.addModalDetail = true;
      }, 100);
    } else if (action === 'addCancle') {
      // 关闭新增一级窗口
      this.addModal = false;
      this.addModalDetail = false;
      this.type = this.currentBasic?.sourceType;
      this.detailType = null;
    }
  }

  handleAddModalOpen() {
    const { dataForm } = this.dataForm;
    // 兼容树型uibot
    if (this.addCategory === 'TREEDATA-SINGLE-DOCUMENT') {
      this.dataForm.changeCategories(true);
    } else {
      dataForm.get('category').disable();
    }
    dataForm.patchValue({ category: this.addCategory });

    ['code'].forEach((s) => {
      if (dataForm.get(s).disabled) {
        dataForm.get(s).enable();
      }
    });
  }

  // 关闭添加窗
  handleCloseAdd(): void {
    const { dataForm } = this.dataForm;
    this.addModal = false;
    this.addModalDetail = false;
    dataForm.reset();
    dataForm.patchValue({ pattern: 'DATA_ENTRY' });
  }

  // 添加或编辑基础资料数据
  handleAddBasic(modalFlag, otherForm?): void {
    const appCode = this.appService?.selectedApp?.code;
    const dataForm = modalFlag === 'add' ? this.dataForm.dataForm : otherForm.dataForm;
    const form = modalFlag === 'add' ? this.dataForm : otherForm;

    for (const i of Object.keys(dataForm.controls)) {
      dataForm.controls[i].markAsDirty();
      dataForm.controls[i].updateValueAndValidity({ onlySelf: modalFlag === 'add' });
    }
    if (dataForm.valid) {
      if (modalFlag === 'add') {
        // this.addLoading = true;
      }
      const activityDto = dataForm?.getRawValue();
      activityDto['lang'] = this.type === 'MODEL_DRIVEN' ? form.formLang : form.reportFormLang;
      const param = {
        ...activityDto,
        authorityPrefix: activityDto['authorityPrefix']
          ? this.isCommonApp
            ? `${
                this.individualService?.individualCaseApp
                  ? this.individualService.sourceApplicationCode
                  : this.appService?.selectedApp?.code
              }:${modalFlag === 'add' ? 'DataEntry_' : ''}${activityDto.code}`
            : `${
                this.individualService?.individualCaseApp
                  ? this.individualService.sourceApplicationCode
                  : this.appService?.selectedApp?.code
              }:basicDataEntry`
          : '',
        // version: '1.0',  // 版本号
        application: appCode,
        applicationName: this.appService?.selectedApp?.name,
        // packages: packages || [],
      };
      // 删除无用属性
      if (param.hasOwnProperty('simpleModelCodeAndServiceCode')) {
        delete param.simpleModelCodeAndServiceCode;
      }
      if (param.hasOwnProperty('simpleModelCodeAndServiceCodeLabel')) {
        delete param.simpleModelCodeAndServiceCodeLabel;
      }
      if (modalFlag === 'add' && !!param.simpleModelCode) {
        // 新增模式下把关联模型的id-用户输入的code传给后端
        // param.code = `${param.simpleModelCode}__${param.code}`;
      }
      this.addLoading = true;
      this.basicDataService.addBasicReport(param, modalFlag).subscribe(
        (res) => {
          const { name, authorityPrefix, dependOnGroundEnd, category, pattern } = param;
          if (res.code === 0) {
            if (modalFlag === 'add') {
              this.addLoading = false;
              this.addModal = false;
              this.addModalDetail = false;
            }
            // dataForm.reset();
            dataForm.patchValue({ pattern: 'DATA_ENTRY' });
            this.message.success(this.translate.instant('dj-保存成功！'));
            this.handleLoadBasic();
            if (modalFlag === 'add') {
              const code = res.data?.code;
              // 模型驱动
              if (param?.sourceType === 'MODEL_DRIVEN') {
                this.handleInterfaceDesign({
                  ...param,
                  code: code,
                });
              } else {
                // 界面设计开窗
                this.handleDesignPage({
                  name,
                  code: code,
                  authorityPrefix,
                  dependOnGroundEnd,
                  category,
                  pattern,
                });
              }
            }
          }
        },
        () => {
          this.addLoading = false;
        },
      );
    }
  }

  // 删除基础资料数据
  async handleDeleteBasic(data: any) {
    this.adModalService.confirm({
      nzTitle: this.translate.instant('dj-确认删除？'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translate.instant('dj-确定'),
      nzCancelText: this.translate.instant('dj-取消'),
      nzOnOk: () => {
        this.loading = true;
        const param = `?pattern=DATA_ENTRY&code=${data.code}&category=${data?.category}`;
        this.basicDataService.deleteBasicReport(param).subscribe(
          (res) => {
            if (res.code === 0) {
              this.loading = false;
              this.message.success(this.translate.instant('dj-删除成功！'));
              this.currentBasic = null;
              this.handleLoadBasic();
            }
          },
          () => {
            this.loading = false;
          },
        );
      },
      nzOnCancel: () => {},
    });
  }

  /**
   * 打开UI设计器
   */
  handleDataEntryDrawerOpen(type: any, sourceType: string = '', data: any = null, isEdit?: any, code?: any) {
    const application = this.appService?.selectedApp?.code;
    const language = this.languageService.currentLanguage;
    this.drawerDataEntryVisible = true;
    this.UIDesignerLoading = 'MODEL_DRIVEN' !== sourceType;
    const allow = this.authService.getAuth(AuthOperate.UPDATE);
    this.designOptions = {
      code,
      application,
      type,
      sourceType,
      language,
      editAllow: allow,
      publishAllow: allow,
      lcdpAppToken: this.appToken,
      lcdpUserToken: this.userService.getUser('iamToken'),
      isMobile: this.isMobile,
    };
    if (!!data) {
      this.designOptions.workData = this.handleWorkData(data);
    }
  }

  /**
   * 普通数据录入drawer关闭回调
   */
  handleDataEntryDrawerClose() {
    if (!this.authService.getAuth(AuthOperate.UPDATE)) {
      // 没权限 直接关闭
      this.dataEntryDrawerClose();
      return;
    }
    if (this.dataEntryWorkDesign && this.dataEntryWorkDesign.workDataHasChanged) {
      this.showConfirm = true;
      return;
    }
    // 关闭如果再loading状态 也要提示
    if (this.UIDesignerLoading) {
      // 页面正在加载中，弹出confirm
      const mod = this.adModalService.confirm({
        nzTitle: '<b>' + this.translate.instant('dj-确定关闭当前页面吗') + '</b>',
        nzContent: this.translate.instant('dj-正在保存/发布当前页面，关闭可能导致保存/发布失败'),
        nzWrapClassName: 'vertical-deleteMechanism-ability-modal',
        nzOnCancel: () => {
          mod.destroy();
        },
        nzOnOk: () => {
          this.dataEntryDrawerClose();
        },
      });
    } else {
      // 直接关闭
      this.dataEntryDrawerClose();
    }
  }

  dataEntryDrawerClose() {
    if (this.canSaveDesignDrawer) {
      this.drawerDataEntryVisible = false;
      // 这里只有lcdp的数据录入需要重新请求，比如在左上角改了名称，需要返回刷新左侧list更新名称
      const { sourceType } = this.designOptions || {};
      if ('MODEL_DRIVEN' !== sourceType) {
        // 非模型驱动的，就之前的lcdp数据录入要刷新左侧列表
        this.handleLoadBasic();
      }
    } else {
      this.showConfirm = true;
    }
    window.removeEventListener('message', () => {});
  }
  // 继续编辑
  handleContinue() {
    this.showConfirm = false;
  }

  // 保存并关闭
  async handleSureSave() {
    // const iframeDom = document.getElementById('dataRecord');
    // const iWindow = (<HTMLIFrameElement>iframeDom).contentWindow;
    // iWindow.postMessage('save', '*');
    if (!!this.dataEntryWorkDesign) {
      this.UIDesignerLoading = true;
      this.showConfirm = false;
      await this.dataEntryWorkDesign.pageDesignUpdate();
      this.UIDesignerLoading = false;
      this.drawerDataEntryVisible = false;
      return;
    }
    this.handleClose(false);
    this.saveSign = `SAVE${new Date().getTime().toString(32)}`;
    this.showConfirm = false;
  }

  /** 关闭 */
  handleClose(val) {
    this.UIDesignerLoading = !val;
  }

  // 处理驱动模型的保存并关闭
  handleCloseModelDesign(val) {
    this.canSaveDesignDrawer = true;
    this.handleClose(val);
    this.drawerDataEntryVisible = false;
    this.saveSign = '';
  }

  // 直接关闭
  handleSureClose() {
    this.drawerDataEntryVisible = false;
    // this.handleLoadBasic();
    this.showConfirm = false;
  }
  handleInterfaceDesignMobile(basic) {
    const { code, category, sourceType } = basic || {};
    this.isMobile = true;
    this.handleDataEntryDrawerOpen(category, sourceType, basic, true, code);
  }

  /**
   * 点击界面设计
   */
  handleInterfaceDesign(basic) {
    this.isMobile = basic?.isMobile;
    const { code, category, name, lang, authorityPrefix, dependOnGroundEnd, pattern, sourceType } = basic || {};
    if ('MODEL_DRIVEN' === sourceType) {
      // 模型驱动的单档双档
      this.handleDataEntryDrawerOpen(category, sourceType, basic, true, code);
    } else {
      // 到设计界面
      this.handleDesignPage({ name, lang, code, authorityPrefix, dependOnGroundEnd, category, pattern });
    }
  }

  handleWorkData(data: any): any {
    const app = this.appService?.selectedApp;
    const title = {
      en_US: `${app?.lang?.name?.['en_US'] ?? app?.name}-${data.lang?.name?.['en_US'] ?? data.name}`,
      zh_CN: `${app?.lang?.name?.['zh_CN'] ?? app?.name}-${data.lang?.name?.['zh_CN'] ?? data.name}`,
      zh_TW: `${app?.lang?.name?.['zh_TW'] ?? app?.name}-${data.lang?.name?.['zh_TW'] ?? data.name}`,
    };
    return {
      origin: data, // 原数据
      title, // 解决方案名称 + 作业名
      code: data?.code,
      category: 'DataEntry',
      authorityPrefix: data.authorityPrefix || '',
      dependOnGroundEnd: !!data?.dependOnGroundEnd,
      taskCode: data?.taskCode,
      defaultCategory: data.category,
      defaultPattern: data.pattern,
      simpleModelCode: data.simpleModelCode,
      simpleModelServiceCode: data.simpleModelServiceCode,
    };
  }

  // 界面设计: Activity: 任务, DataEntry: 数据录入, Statement: 报表, ExcelFunction: 报表函数录入,
  handleDesignPage(data: any): void {
    this.workData = this.handleWorkData(data);
    this.workVisible = true;
    this.workType = data['category'];
  }

  // 界面设计关闭
  handleDesignPageClose(): void {
    this.workData = null;
    this.workType = '';
    this.workVisible = false;
  }

  // 打开复制数据录入，进行表单初始化赋值
  handleCopyVisible(record): void {
    this.currentBasic = record;
    this.showCopyModal = true;
  }

  // 处理关闭复制弹框
  handleCloseCopy(event) {
    this.showCopyModal = false;
    if (event.reload) {
      this.handleLoadBasic();
    }
  }

  handleExtendedInfo(basic, event) {
    this.extendedInfo = basic;
    setTimeout(() => {
      this.extendedInfoRef.handleOpenExtendedInfoModal(event);
    });
  }

  handleGotoDesign($event) {
    this.modelTitle = $event.name;
    this.modelParams = {
      code: $event.code,
      basicData: $event,
      mode: 'table',
    };
    this.modelVisible = true;
  }

  handleCancel() {
    this.modelVisible = false;
  }

  handleMigrate(basic) {
    this.migrateBranchInfo = {
      sourceApplication: basic.application,
      sourceBranch: 'bak',
      targetApplication: basic.application,
      targetBranch: 'develop',
      pageDesignCodeList: [
        {
          code: basic.code,
          simpleModelCode: basic.simpleModelCode ?? '',
        },
      ],
    };
    this.isDoMigrate = true;
  }

  handleMigrateCallback() {
    this.migrateBranchInfo = null;
    this.isDoMigrate = false;
  }

  // TAG转DSL, 回溯DSL为TAG, recover为true时表示回退dsl为tag
  handleTransFormTag(data: string, recover?: boolean): void {
    let loading = false;
    this.adModalService.confirm({
      nzTitle: this.translate.instant(`dj-确认${!!recover ? '回退' : '转化'}？`),
      nzContent: this.translate.instant(`dj-${!!recover ? '回退' : '转化'}数据前，请确认是否已备份数据`),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translate.instant('dj-确定'),
      nzCancelText: this.translate.instant('dj-取消'),
      nzOkLoading: loading,
      nzOnOk: async () => {
        loading = true;
        const [err, res] = await to(this.basicDataService.transformTag({ code: data['code'], recover }).toPromise());
        if (res?.code === 0) {
          this.message.success(this.translate.instant('dj-操作成功'));
          this.handleLoadBasic();
        }
        loading = false;
      },
      nzOnCancel: () => {},
    });
  }
}
