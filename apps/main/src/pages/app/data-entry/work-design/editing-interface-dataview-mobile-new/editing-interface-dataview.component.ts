import { Component, Input, OnInit, TemplateRef, Output, EventEmitter } from '@angular/core';
import { EditingInterfaceDataViewMobileNewService } from './editing-interface-dataview.service';
import { AppService } from 'pages/apps/app.service';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { isAllow } from 'common/utils/auth';
import { AdUserService } from 'pages/login/service/user.service';
type TPageType = 'browse' | 'rule' | 'edit' | 'design';
@Component({
  selector: 'app-editing-interface-dataview-mobile-new',
  template: `
    <app-data-entry-work-design-mobile
      #dataEntryWorkDesign
      [workData]="workData"
      [headerCustomTemplate]="headerCustomTemplate"
      [modelPageType]="pageType"
      [isCheckContentChange]="true"
      (saveAndPublishLoadingChange)="handleSaveAndPublishLoadingChange($event)"
      (contentChangeWithoutSaveChange)="handleContentChangeWithoutSaveChange($event)"
    ></app-data-entry-work-design-mobile>
  `,
  styleUrls: [],
})
export class EditingInterfaceDataviewMobileNewComponent implements OnInit {
  designOptions: any;
  saveSign: any;
  appToken: any;
  workData: any;
  @Input() workCode: string; // 作业code
  @Input() pageType: TPageType; // 页面类型
  @Input() headerCustomTemplate: TemplateRef<any> | null = null; // 定制头部（头部的左侧部分）
  @Output() saveAndPublishLoadingChange: EventEmitter<boolean> = new EventEmitter(); // 当保存和发布状态变化时触发的事件，配合优化需求，保存和发布时模型驱动解决方案左侧菜单不可点击
  @Output() contentChangeWithoutSaveChange: EventEmitter<boolean> = new EventEmitter(); // 内容变更未保存状态的变化

  constructor(
    private service: EditingInterfaceDataViewMobileNewService,
    private appService: AppService,
    private languageService: LocaleService,
    protected configService: SystemConfigService,
    protected userService: AdUserService,
  ) {}

  get application() {
    return this.appService?.selectedApp?.code;
  }

  get lang() {
    return this.languageService.currentLanguage;
  }

  ngOnInit(): void {
    // this.appToken = this.configService.get('appToken').toPromise();
    // // 存储作业code
    // this.service.setWorkCode(this.workCode);
    // let type: string;
    // if (this.pageType === 'design') {
    //   type = 'SIGN-DOCUMENT';
    // } else if (this.pageType === 'edit') {
    //   type = 'TREEDATA-DOUBLE-DOCUMENT-MULTI';
    // }
    // this.designOptions = {
    //   code: this.workCode,
    //   application: this.application,
    //   type,
    //   sourceType: 'MODEL_DRIVEN',
    //   language: this.lang,
    //   editAllow,
    //   publishAllow,
    //   lcdpAppToken: this.appToken,
    //   lcdpUserToken: this.userService.getUser('iamToken'),
    //   from: 'ModelDrivenApp',
    //   headerCustomTemplate: this.headerCustomTemplate,
    // };
    this.workData = {
      code: this.workCode,
      category: 'DataEntry',
      // headerCustomTemplate: this.headerCustomTemplate,
    };
  }

  handleSaveAndPublishLoadingChange(isLoading: boolean) {
    this.saveAndPublishLoadingChange.emit(isLoading);
  }

  handleContentChangeWithoutSaveChange(value: boolean) {
    this.contentChangeWithoutSaveChange.emit(value);
  }
}
