<form nz-form [formGroup]="dataForm" [nzNoColon]="true" class="form-info login-form">
  <!-- 租户 -->
  <nz-form-item *ngIf="currentBasic?.tenantId && currentBasic?.tenantId !== 'SYSTEM'">
    <nz-form-control nzFlex="400px" [nzErrorTip]="codeErrorTpl" class="data-form-item" style="max-width: unset">
      <div nz-tooltip [nzTooltipTitle]="'dj-租户' | translate" nzTooltipTrigger="click" nzTooltipPlacement="bottom">
        <nz-input-group [adInnerLabel]="'dj-租户' | translate" [required]="true">
          <input
            nz-input
            formControlName="tenantId"
            type="text"
            (keyup)="inputChange($event, 'tenantId')"
            [placeholder]="'dj-请输入' | translate"
          />
        </nz-input-group>
      </div>
    </nz-form-control>
    <ng-template #codeErrorTpl let-control>
      <ng-container *ngIf="control.hasError('required')">{{ 'dj-租户必填' | translate }}</ng-container>
    </ng-template>
  </nz-form-item>
  <!-- 关联API -->
  <nz-form-item>
    <nz-form-control nzFlex="400px" [nzErrorTip]="simpleErrorTpl" class="data-form-item" style="max-width: unset">
      <div class="data-form-item-layout">
        <nz-input-group
          [ngClass]="{ 'form-disabled': type === 'edit' }"
          [adInnerLabel]="'dj-关联API' | translate"
          [required]="true"
          [nzSuffix]="suffixIconAPI"
        >
          <input readonly nz-input formControlName="actionId" [placeholder]="'dj-请选择' | translate" />
        </nz-input-group>
        <ng-template #suffixIconAPI>
          <i
            adIcon
            iconfont="iconkaichuang"
            aria-hidden="true"
            class="open-modal-icon window-icon iconfont"
            (click)="handleOpenAction()"
          ></i>
        </ng-template>
        <!--action开窗组件-->
        <app-action-modal
          *ngIf="isActionModalShow"
          [transferModal]="isActionModalShow"
          [transferData]="actionData"
          labelType="EspAction"
          (callBack)="handleConfirmAction($event)"
          (closeModal)="isActionModalShow = false"
        >
        </app-action-modal>
      </div>
    </nz-form-control>
    <ng-template #simpleErrorTpl let-control>
      <ng-container *ngIf="control.hasError('required')">{{ 'dj-请选择API' | translate }}</ng-container>
    </ng-template>
  </nz-form-item>
  <!--编码 作业代号去除 -->
  <nz-form-item *ngIf="type !== 'create'">
    <nz-form-control nzFlex="400px" [nzErrorTip]="codeErrorTpl" class="data-form-item" style="max-width: unset">
      <div
        nz-tooltip
        [nzTooltipTitle]="'dj-作业代号tips' | translate"
        nzTooltipTrigger="click"
        nzTooltipPlacement="bottom"
      >
        <!--这里特殊处理 已选模型 先屏蔽掉label,已避免palace的label把模型code给覆盖显示-->
        <nz-input-group
          [adInnerLabel]="'dj-作业代号' | translate"
          [required]="true"
          [nzPrefix]="type === 'create' && prefix?.length > 0 ? prefixTmpl : null"
        >
          <input
            nz-input
            formControlName="code"
            type="text"
            [maxlength]="50"
            (keyup)="inputChange($event, 'code')"
            [placeholder]="'this_is_a_sample'"
          />
        </nz-input-group>
        <ng-template #prefixTmpl>
          {{ prefix + '_' }}
        </ng-template>
      </div>
    </nz-form-control>
    <ng-template #codeErrorTpl let-control>
      <ng-container *ngIf="control.hasError('required')">{{ 'dj-请输入作业代号' | translate }}</ng-container>
      <ng-container *ngIf="control.hasError('workDesignCode')">{{
        'dj-请输入正确的作业代号' | translate
      }}</ng-container>
      <ng-container *ngIf="control.hasError('codeRepeat')">{{ 'dj-作业代号重复' | translate }}</ng-container>
    </ng-template>
  </nz-form-item>
  <!-- 作业名称 -->
  <nz-form-item>
    <nz-form-control nzFlex="400px" [nzErrorTip]="nameErrorTpl" class="data-form-item" style="max-width: unset">
      <div
        nz-tooltip
        [nzTooltipTitle]="'dj-作业名称tips' | translate"
        nzTooltipTrigger="click"
        nzTooltipPlacement="bottom"
      >
        <app-component-input
          class="lang-input"
          formControlName="name"
          [attr]="{
            code: 'name',
            needLang: true,
            lang: { value: formLang?.name },
            placeholder: '这是范例',
            maxlength: 50,
            label: 'dj-作业名称',
            innerLabel: true,
            required: true
          }"
          [value]="formLang?.name?.[('dj-LANG' | translate)] || _formData?.name"
          ngDefaultControl
          (callBack)="handlePatchLang('name', $event)"
        >
        </app-component-input>
      </div>
      <ng-template #nameErrorTpl let-control>
        <ng-container *ngIf="control.hasError('required')">{{ 'dj-请输入作业名称' | translate }}</ng-container>
      </ng-template>
    </nz-form-control>
  </nz-form-item>

  <nz-form-item>
    <div class="data-form-item-layout">
      <label nz-checkbox formControlName="dependOnGroundEnd">
        {{ 'dj-依赖地端' | translate }}
      </label>
      <label nz-checkbox formControlName="authorityPrefix" (ngModelChange)="handleChangeAuthorityPrefix($event)">
        {{ 'dj-权限控制' | translate }}
      </label>
      <!-- <ng-container formGroupName="extendFields">
        <label nz-checkbox formControlName="closeNeedConfirm">
          {{ 'dj-是否卡控' | translate }}
        </label>
      </ng-container> -->
    </div>
  </nz-form-item>

  <!-- 关联模型 -->
  <!-- <nz-form-item *ngIf="dataForm.get('authorityPrefix').value">
    <nz-form-control nzFlex="400px" [nzErrorTip]="simpleErrorTpl" class="data-form-item" style="max-width: unset">
      <div class="data-form-item-layout">
        <nz-input-group
          [ngClass]="{ 'form-disabled': simpleModelDisabled }"
          [adInnerLabel]="'dj-关联模型' | translate"
          [required]="false"
          [nzSuffix]="suffixCode"
        >
          <input
            readonly
            nz-input
            formControlName="simpleModelCodeAndServiceCodeLabel"
            [placeholder]="'dj-请选择' | translate"
          />
        </nz-input-group>
        <ng-template #suffixCode>
          <i
            adIcon
            iconfont="iconkaichuang"
            aria-hidden="true"
            class="open-modal-icon window-icon iconfont"
            (click)="handleOpenSimpleModelListModal()"
          ></i>
        </ng-template>
      </div>
    </nz-form-control>
  </nz-form-item> -->

  <!-- 关联模型 -->
  <nz-form-item *ngIf="dataForm.get('authorityPrefix').value">
    <nz-form-control nzFlex="400px" [nzErrorTip]="simpleErrorTpl" class="data-form-item" style="max-width: unset">
      <div
        class="data-form-item-layout"
        nz-tooltip
        [nzTooltipTitle]="'dj-模型驱动code描述' | translate"
        nzTooltipTrigger="click"
        nzTooltipPlacement="bottom"
      >
        <nz-input-group
          [ngClass]="{ 'form-disabled': simpleModelDisabled }"
          [adInnerLabel]="'dj-关联模型' | translate"
          [required]="false"
        >
          <input nz-input formControlName="simpleModelCode" [maxlength]="50" [placeholder]="'dj-请输入' | translate" />
        </nz-input-group>
      </div>
    </nz-form-control>
  </nz-form-item>
</form>

<!-- <app-authority-simple-model-list-modal
  *ngIf="workDesignApiDetailFormService.chooseSimpleModelVisible"
  [visible]="workDesignApiDetailFormService.chooseSimpleModelVisible"
  [simpleModelList]="workDesignApiDetailFormService.simpleModelList"
  [simpleModelCode]="dataForm.get('simpleModelCode')?.value"
  (cancel)="handleChooseSimpleModelCancel()"
  (confirm)="handleSimpleModelCodeChange($event)"
></app-authority-simple-model-list-modal> -->
