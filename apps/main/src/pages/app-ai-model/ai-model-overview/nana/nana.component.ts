import {
  Directive,
  ElementRef,
  Output,
  EventEmitter,
  HostListener,
  ViewChild,
  OnDestroy,
  OnInit,
  Inject,
  Component,
  NgZone,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { AD_AUTH_TOKEN } from 'pages/login/service/auth.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { take } from 'rxjs/operators';
import { AdUserService } from 'pages/login/service/user.service';
import { AppLayloutService } from '../app-layout/app-laylout.service';
import { AppService } from '../../../apps/app.service';
import { AiModelOverviewService } from '../services/ai-model.overview.service';

@Component({
  selector: 'app-ai-nana',
  templateUrl: './nana.component.html',
  styleUrls: ['./nana.component.less'],
})
export class AiNanaComponent implements OnInit, OnD<PERSON>roy {
  messageHandler = null;
  nanaUrl = '';
  nanaJumpLink;
  imIconFlag = true;
  appCode: string = '';
  token;

  private platformCategory: string;

  @ViewChild('nanaFrame', { static: false }) nanaFrame: ElementRef;

  constructor(
    public service: AiModelOverviewService,
    public appService: AppService,
    protected userService: AdUserService,
    public applayloutService: AppLayloutService,
    private systemConfigService: SystemConfigService,
    private sanitizer: DomSanitizer,
    private _ngZone: NgZone,
  ) {
    this.token = this.userService.getUser('iamToken');
    this.appCode = this.appService.selectedApp?.code;

    this.systemConfigService.getConfig().subscribe(({ platformCategory }) => {
      this.platformCategory = platformCategory;
    });
  }

  ngOnInit(): void {
    this.systemConfigService
      .get('nanaUrl')
      .pipe(take(1))
      .subscribe((url) => {
        this.nanaUrl = url + '?token=' + this.token + '&appCode=AIMHELPER&assistantCode=AIMHELPER';
        this.nanaJumpLink = this.sanitizer.bypassSecurityTrustResourceUrl(this.nanaUrl); // 信任该url
      });
  }

  ngOnDestroy(): void {
    window.removeEventListener('message', this.messageHandler);
    this.applayloutService.modelDetailId = '';
  }

  nanaMessageEvent(e) {
    const iframe = this.nanaFrame?.nativeElement;

    if (e.data && e.data.value === 'afterNanaContentInit') {
      const messageData = {
        changeBusiInfo: {
          assistantCode: 'AIMHELPER',
          busiInfo: {
            busiType: '801',
            id: this.applayloutService.modelDetailId,
            platformCategory: this.platformCategory,
          },
        },
      };
      iframe.contentWindow.postMessage(messageData, this.nanaUrl);

      this.service.isSwitchJudge(this.applayloutService.modelDetailId).subscribe((res) => {
        if (res.code === 0 && res.data) {
          const messageData = {
            sendMessage: { assistantCode: 'AIMHELPER' },
          };
          iframe.contentWindow.postMessage(messageData, this.nanaUrl);

          this.service.guideSessionSave(this.applayloutService.modelDetailId).subscribe();
        }
      });
    }
  }

  imHomeControl(flag) {
    this.imIconFlag = flag;
    this.applayloutService.isNanaCollapsed = flag;
  }

  // 娜娜iframe 加载完成后调用 postMessage 发送消息
  onNanaIframeLoad(): void {
    this._ngZone.runOutsideAngular((): void => {
      this.messageHandler = window.addEventListener('message', this.nanaMessageEvent.bind(this));
    });
  }
}
