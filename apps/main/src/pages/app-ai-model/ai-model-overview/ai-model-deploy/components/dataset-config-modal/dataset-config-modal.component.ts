import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { validatorForm } from 'common/utils/core.utils';
import { ModelDeployService } from '../../ai-model-deploy.service';
import { AdUserService } from 'pages/login/service/user.service';
import { SystemConfigService } from 'common/service/system-config.service';

@Component({
  selector: 'app-dataset-config-modal',
  templateUrl: './dataset-config-modal.component.html',
  styleUrls: ['./dataset-config-modal.component.less'],
})
export class DatasetConfigModelComponent implements OnInit {
  dataFormGroup: FormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
    },
  };

  appToken: string;
  loading = false;

  @Input() visible: boolean = false;
  @Input() configId = null;
  @Input() envId = null;
  @Output() handleClose: EventEmitter<any> = new EventEmitter();
  @Output() handleConfirm: EventEmitter<any> = new EventEmitter();

  constructor(
    private fb: FormBuilder,
    public translate: TranslateService,
    public service: ModelDeployService,
    private userService: AdUserService,
    protected configService: SystemConfigService,
  ) {}

  ngOnInit() {
    this.configService.get('appToken').subscribe((appToken) => {
      this.appToken = appToken;
    });
    this.loading = true;
    this.service.getDatasetOssConfig(this.envId, this.configId).subscribe((res) => {
      if (res.code === 0) {
        this.dataFormGroup = this.fb.group({
          obsShareUrl: [res.data || null, [Validators.required]],
        });

        this.loading = false;
      }
    });
  }

  async getDatasetObsConfig() {}

  handleCancel() {
    this.handleClose.emit();
  }

  handleOk() {
    const formData = this.dataFormGroup.getRawValue();
    validatorForm(this.dataFormGroup);
    if (this.dataFormGroup.valid) {
      this.handleConfirm.emit(formData.obsShareUrl);
      this.handleClose.emit();
    }
  }

  openWindow(url) {
    window.open(url + 'sso-login?userToken=' + this.userService.getUser('iamToken') + '&appToken=' + this.appToken);
  }
}
