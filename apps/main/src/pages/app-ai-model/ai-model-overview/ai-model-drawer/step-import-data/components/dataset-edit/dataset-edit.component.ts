import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  EventEmitter,
  OnInit,
  Input,
  Output,
} from '@angular/core';
import { FormBuilder, FormArray, FormGroup, ValidationErrors, ValidatorFn, Validators } from '@angular/forms';
import { NzMarks } from 'ng-zorro-antd/slider';
import { TranslateService } from '@ngx-translate/core';
import { AiModelOverviewService } from '../../../../services/ai-model.overview.service';
import { AppService } from 'pages/apps/app.service';
import { AiModelDrawerService } from '../../../ai-model-drawer.service';
import { Subscription } from 'rxjs';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { AdUserService } from 'pages/login/service/user.service';
import { SystemConfigService } from 'common/service/system-config.service';

@Component({
  selector: 'app-dataset-edit',
  changeDetection: ChangeDetectionStrategy.OnPush,
  templateUrl: './dataset-edit.component.html',
  styleUrls: ['./dataset-edit.component.less'],
})
export class DatasetEditComponent implements OnInit {
  @Input() datasetId = undefined;
  @Input() datasetConfig = null;
  @Input() datasetEditDrawerVisible: boolean = false;
  @Output() closeDrawer = new EventEmitter(); // 关闭modal

  isActionModalShow: boolean; // action开窗
  actionData: any; // 传递action数据

  isStarRocksModalShow: boolean; // starRocks开窗
  starRocksData: any;

  tabSelectIndex = 0;
  appToken: string;
  uploading: boolean;
  uploadFile: any = null;

  loading: boolean = false;
  dataFormGroup: FormGroup;
  selectedModelType: string = '0'; // 默认选中 "input"
  submitLoading = false;
  timeOptions = [
    {
      name: this.translateService.instant('dj-每天03点'),
      value: '0',
    },
    {
      name: this.translateService.instant('dj-30分钟1次'),
      value: '1',
    },
    {
      name: this.translateService.instant('dj-每2小时1次'),
      value: '2',
    },
    {
      name: this.translateService.instant('dj-1小时1次'),
      value: '3',
    },
    {
      name: this.translateService.instant('dj-每周日06点'),
      value: '4',
    },
    {
      name: this.translateService.instant('dj-每天23点'),
      value: '5',
    },
  ];
  fieldOptions = [
    {
      name: 'date',
      value: 'date',
    },
    {
      name: 'string',
      value: 'string',
    },
    {
      name: 'int',
      value: 'int',
    },
    {
      name: 'float',
      value: 'float',
    },
  ];

  allChecked = false;
  indeterminate = false;

  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translateService.instant('dj-必填'),
    },
  };

  private subscriptions: Subscription[] = [];

  constructor(
    private fb: FormBuilder,
    private translateService: TranslateService,
    private message: NzMessageService,
    private overviewService: AiModelOverviewService,
    private aiModelDrawerService: AiModelDrawerService,
    private cdr: ChangeDetectorRef,
    private appService: AppService,
    private modal: NzModalService,
    private userService: AdUserService,
    protected configService: SystemConfigService,
  ) {}

  ngOnInit(): void {
    this.configService.get('appToken').subscribe((appToken) => {
      this.appToken = appToken;
    });

    if (this.isCreateDataset) {
      this.dataFormGroup = this.fb.group({
        accessType: [this.selectedModelType, [Validators.required]],
        datasetName: [null, [Validators.required]],
        config: this.fb.group({
          interfaceName: [null, [Validators.required]],
          interfaceDesc: [null, [Validators.required]],
          timer: [null, [Validators.required]],
          dataEnv: [null, [Validators.required]],
          reqParam: [null, [Validators.required]],
          obsKey: [null],
          fileName: [null],
          obsShareUrl: [null],
        }),
        /* starRocksConfig: this.fb.group({
          tableName: [null, [Validators.required]],
          returnParams: this.fb.array([this.createParam()]),
        }), */
        ossForm: this.fb.group({
          obsShareUrl: [null, [Validators.required]],
        }),
      });
    } else {
      this.datasetDetail();
    }

    const subscription = this.dataFormGroup.get('accessType')?.valueChanges.subscribe(() => {
      this.updateInputValidators(this.dataFormGroup);
    });

    this.subscriptions.push(subscription);

    this.updateInputValidators(this.dataFormGroup);
  }

  datasetDetail() {
    // 查询页面信息
    this.dataFormGroup = this.fb.group({
      accessType: [this.datasetConfig.accessType === '3' ? '2' : this.datasetConfig.accessType, [Validators.required]],
      datasetName: [this.datasetConfig.datasetName, [Validators.required]],
      config: this.fb.group({
        interfaceName: [this.datasetConfig.config.interfaceName, [Validators.required]],
        interfaceDesc: [this.datasetConfig.config.interfaceDesc, [Validators.required]],
        timer: [this.datasetConfig.config.timer, [Validators.required]],
        dataEnv: [this.datasetConfig.config.dataEnv, [Validators.required]],
        reqParam: [this.datasetConfig.config.reqParam, [Validators.required]],
        obsKey: [this.datasetConfig.config.obsKey],
        fileName: [this.datasetConfig.config.fileName],
        obsShareUrl: [this.datasetConfig.config.obsShareUrl],
      }),
      /* starRocksConfig: this.fb.group({
        tableName: [null, [Validators.required]],
        returnParams: this.fb.array([this.createParam()]),
      }), */
      ossForm: this.fb.group({
        obsShareUrl: [this.datasetConfig.config.obsShareUrl, [Validators.required]],
      }),
    });

    if (this.datasetConfig.accessType === '3') {
      this.selectedModelType = '2';
      this.tabSelectIndex = 1;
    }

    if (this.datasetConfig.accessType === '2') {
      this.selectedModelType = '2';
      this.uploadFile = {
        name: this.datasetConfig.config.fileName,
      };
    }
  }

  get isCreateDataset() {
    return !this.datasetId;
  }

  get zeroStepDisabled() {
    return this.aiModelDrawerService.zeroStepDisabled;
  }

  getFormArray(groupName: string, arrayName: string): FormArray {
    const group = this.dataFormGroup.get(groupName) as FormGroup;
    return group.get(arrayName) as FormArray;
  }

  get accessType() {
    return this.dataFormGroup.get('accessType').value;
  }

  // 创建一项
  createParam(): FormGroup {
    return this.fb.group({
      key: ['', Validators.required],
      type: ['', Validators.required],
    });
  }

  async handleDownloadTemplate() {
    const res: any = await this.overviewService.downloadTemplate();
    if (res?.code === 0) {
      const link = document.createElement('a');
      link.href = res.data;
      link.click();
    }
  }

  beforeUpload = (file: any): boolean => {
    if (!['xls', 'xlsx', 'csv'].includes(file?.name?.split('.')?.slice(-1)?.[0])) {
      this.message.warning(this.translateService.instant('dj-文件格式有误，请重新选择'));
      return false;
    }
    const isLt100M = file.size! / 1024 / 1024 < 100;
    if (!isLt100M) {
      this.message.warning(this.translateService.instant('dj-文件大小超过限制，请重新选择'));
      return false;
    }
    this.handleImportData(file);
    return false;
  };

  // 上传文件
  async handleImportData(file: any) {
    const formData = new FormData();
    formData.append('file', file);
    this.uploading = true;

    this.aiModelDrawerService.uploadFile(formData).subscribe((res) => {
      if (res.code === 0) {
        this.uploadFile = file;
        this.dataFormGroup.get('config').patchValue(res.data);
        this.message.success(this.translateService.instant('dj-导入成功'));
      } else {
        this.removeFile();
        this.message.error(this.translateService.instant('dj-导入失败'));
      }
      this.uploading = false;
      this.cdr.markForCheck();
    });
  }

  // 移除文件
  removeFile() {
    this.uploadFile = null;
  }

  // action 开窗
  handleOpenAction(): void {
    this.isActionModalShow = true;
    this.actionData = {
      actionId: this.dataFormGroup.get('config').get('interfaceName').value || '',
      actionName: '',
      useApp: 'true',
    };
  }

  // 回填action
  handleConfirmAction(data: any): void {
    this.isActionModalShow = false;

    this.dataFormGroup.get('config').patchValue({
      interfaceName: data.actionId,
      interfaceDesc: data.actionName,
    });
  }

  // starRocks 开窗
  handleOpenStarRocks(): void {
    this.isStarRocksModalShow = true;
    this.starRocksData = {
      actionId: this.dataFormGroup.get('starRocksConfig').get('tableName').value || '',
      actionName: '',
      useApp: 'true',
    };
  }

  // 回填starRocks
  handleConfirmStarRocks(data: any): void {
    this.isStarRocksModalShow = false;

    this.dataFormGroup.get('starRocksConfig').patchValue({
      tableName: data.actionId,
    });
  }

  // 添加一项
  addParam(formGroupName, formArrayName): void {
    this.getFormArray(formGroupName, formArrayName).push(this.createParam());
  }

  // 删除一项
  removeParam(formGroupName, formArrayName, index: number): void {
    this.getFormArray(formGroupName, formArrayName).removeAt(index);
  }

  close() {
    this.closeDrawer.emit();
  }

  onRadioChange(value: string): void {
    this.selectedModelType = value;

    this.updateInputValidators(this.dataFormGroup);
  }

  preventEnter(event: KeyboardEvent): void {
    event.preventDefault(); // 阻止默认行为
  }

  updateInputValidators(itemGroup: FormGroup) {
    const accessType = itemGroup.get('accessType')?.value;
    const config = itemGroup.get('config') as FormGroup;
    const ossForm = itemGroup.get('ossForm') as FormGroup;

    // 根据 select 的值判断是否需要给 input 控件添加必填验证
    if (accessType === '0') {
      ossForm?.clearValidators();
      Object.keys(config.controls).forEach((controlName) => {
        const control = config.get(controlName);
        control?.setValidators([Validators.required]);
        //control?.updateValueAndValidity();
      });
    } else if (accessType === '2') {
      config?.clearValidators();
      if (this.tabSelectIndex === 0) {
      } else if (this.tabSelectIndex === 1) {
        Object.keys(ossForm.controls).forEach((controlName) => {
          const control = ossForm.get(controlName);
          control?.setValidators([Validators.required]);
          //control?.updateValueAndValidity();
        });
      }
    }
    config?.updateValueAndValidity();
    ossForm?.updateValueAndValidity();
  }

  tabSelectChange(params: { index: number }) {
    this.updateInputValidators(this.dataFormGroup);
  }

  onAllChecked(value) {}

  onItemChecked(id, value) {}

  submitForm() {
    this.submitLoading = true;
    if (this.accessType === '0') {
      const espForm = this.dataFormGroup.get('config') as FormGroup;
      if (
        espForm.get('dataEnv').valid &&
        espForm.get('interfaceName').valid &&
        espForm.get('interfaceDesc').valid &&
        espForm.get('timer').valid &&
        espForm.get('reqParam').valid &&
        this.dataFormGroup.get('accessType').valid &&
        this.dataFormGroup.get('datasetName').valid
      ) {
        const params = {
          id: this.datasetId,
          modelDetailId: this.aiModelDrawerService.modelDetailId,
          datasetName: this.dataFormGroup.value?.datasetName,
          accessType: this.dataFormGroup.value?.accessType,
          config: espForm.value,
        };

        this.aiModelDrawerService.saveDataset(params).subscribe(
          (res) => {
            if (res.code === 0) {
              this.message.success(
                this.isCreateDataset
                  ? this.translateService.instant('dj-新增成功')
                  : this.translateService.instant('dj-编辑成功'),
              );
              this.close();
            }
            this.submitLoading = false;
          },
          () => {
            this.submitLoading = false;
          },
        );
      } else {
        this.submitLoading = false;
        this.markAllControlsDirty(this.dataFormGroup);
      }
    } else if (this.accessType === '2' && this.tabSelectIndex === 0) {
      if (
        this.uploadFile &&
        this.dataFormGroup.get('accessType').valid &&
        this.dataFormGroup.get('datasetName').valid
      ) {
        const params = {
          id: this.datasetId,
          modelDetailId: this.aiModelDrawerService.modelDetailId,
          datasetName: this.dataFormGroup.value?.datasetName,
          accessType: this.dataFormGroup.value?.accessType,
          config: {
            obsKey: this.dataFormGroup.get('config').value.obsKey,
            fileName: this.dataFormGroup.get('config').value.fileName,
            obsShareUrl: this.dataFormGroup.get('config').value.obsShareUrl,
          },
        };

        this.aiModelDrawerService.saveDataset(params).subscribe(
          (res) => {
            if (res.code === 0) {
              this.message.success(
                this.isCreateDataset
                  ? this.translateService.instant('dj-新增成功')
                  : this.translateService.instant('dj-编辑成功'),
              );

              this.submitLoading = false;
              this.close();
            }
          },
          () => {
            this.submitLoading = false;
          },
        );
      } else {
        this.submitLoading = false;
        this.markAllControlsDirty(this.dataFormGroup);
      }
    } else if (this.accessType === '2' && this.tabSelectIndex === 1) {
      const ossForm = this.dataFormGroup.get('ossForm') as FormGroup;
      if (ossForm.valid && this.dataFormGroup.get('accessType').valid && this.dataFormGroup.get('datasetName').valid) {
        const params = {
          id: this.datasetId,
          modelDetailId: this.aiModelDrawerService.modelDetailId,
          datasetName: this.dataFormGroup.value?.datasetName,
          accessType: '3',
          config: {
            obsShareUrl: ossForm.value.obsShareUrl,
          },
        };

        this.aiModelDrawerService.saveDataset(params).subscribe(
          (res) => {
            if (res.code === 0) {
              this.message.success(
                this.isCreateDataset
                  ? this.translateService.instant('dj-新增成功')
                  : this.translateService.instant('dj-编辑成功'),
              );

              this.submitLoading = false;
              this.close();
            }
          },
          () => {
            this.submitLoading = false;
          },
        );
      } else {
        this.submitLoading = false;
        this.markAllControlsDirty(this.dataFormGroup);
      }
    }
  }

  markAllControlsDirty(form: FormGroup | FormArray) {
    Object.values(form.controls).forEach((control) => {
      if (control instanceof FormGroup || control instanceof FormArray) {
        // 对于 FormGroup 和 FormArray 直接递归标记其子控件
        this.markAllControlsDirty(control);
      } else {
        control.markAsDirty();
        control.updateValueAndValidity({ onlySelf: true });
      }
    });
  }

  openWindow(url) {
    window.open(url + 'sso-login?userToken=' + this.userService.getUser('iamToken') + '&appToken=' + this.appToken);
  }
}
