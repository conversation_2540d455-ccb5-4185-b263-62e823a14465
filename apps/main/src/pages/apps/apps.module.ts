import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AppsRoutingModule } from './apps-routing.module';
import { NzMessageModule } from 'ng-zorro-antd/message';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzInputModule } from 'ng-zorro-antd/input';
import { TranslateModule } from '@ngx-translate/core';
import { NzCardModule } from 'ng-zorro-antd/card';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzFormModule } from 'ng-zorro-antd/form';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';

import { AdEmptyModule } from 'components/ad-ui-components/ad-empty/ad-empty.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { DeleteInfoModule } from 'components/bussiness-components/delete-info/delete-info.module';

import { AppsComponent } from './apps.component';
import { AppCardComponent } from './app-card/app-card.component';
import { PublishTenantComponent } from './publish-tenant/publish-tenant.component';
import { CreateAppModule } from './create-app/create-app.module';
import { DirectiveModule } from '../../common/directive/directive.module';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { InputModule } from '../../components/form-components/input/input.module';
import { AppInfoEditModule } from 'pages/app/app-info/app-info-edit/app-info-edit.module';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { CreateAgileDataAppModule } from './create-agile-data-app/create-agile-data-app.module';
import { CreateNanaAssistantAppModule } from './create-nana-assistant-app/create-nana-assistant-app.module';
import { AuthManageModule } from '../../components/bussiness-components/layout/menu-bar/auth-manage/auth-manage.module';
import { AppsService } from './apps.service';
import { SwitchComponent } from './switch/switch.component';
import { AgGridModule } from 'ag-grid-angular';
import { SolutionCardModule } from 'components/bussiness-components/solution-card/solution-card.module';
import { SolutionEntryCardModule } from 'components/bussiness-components/solution-entry-card/solution-entry-card.module';
import { SolutionTableModule } from 'components/bussiness-components/solution-table/solution-table.module';
import { SolutionBaseInfoFormModule } from 'components/bussiness-components/solution-base-info-form/solution-base-info-form.module';
import { VisibilitySensorModule } from 'components/bussiness-components/visibility-sensor/visibility-sensor.module';
import { SolutionAntdTableModule } from 'components/bussiness-components/solution-antd-table/solution-antd-table.module';
import { NzModalModule } from 'ng-zorro-antd/modal';

@NgModule({
  imports: [
    CommonModule,
    AppsRoutingModule,
    NzMessageModule,
    NzButtonModule,
    NzGridModule,
    AdIconModule,
    NzInputModule,
    TranslateModule,
    NzCardModule,
    NzRadioModule,
    NzDividerModule,
    NzSelectModule,
    NzSpinModule,
    NzTableModule,
    NzFormModule,
    AdSelectModule,
    AdEmptyModule,

    NzToolTipModule,
    AdModalModule,
    NzDrawerModule,
    CreateAppModule,
    DeleteInfoModule,
    DirectiveModule,
    FormsModule,
    AdButtonModule,
    ReactiveFormsModule,
    InputModule,
    AppInfoEditModule,
    NzPopoverModule,
    CreateAgileDataAppModule,
    CreateNanaAssistantAppModule,
    AuthManageModule,
    AgGridModule,
    SolutionCardModule,
    SolutionEntryCardModule,
    SolutionTableModule,
    SolutionBaseInfoFormModule,
    VisibilitySensorModule,
    SolutionAntdTableModule,
  ],
  declarations: [AppsComponent, AppCardComponent, PublishTenantComponent, SwitchComponent],
  providers: [AppsService],
  exports: [AppCardComponent],
})
export class AppsModule {}
