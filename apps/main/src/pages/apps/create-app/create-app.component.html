<!--新建解决方案-->
<ad-modal
  nzClassName="add-app-modal"
  [nzWidth]="'540px'"
  [(nzVisible)]="emptyVisible"
  [nzTitle]="'dj-' + modalTitle | translate"
  [nzMaskClosable]="false"
  [nzFooter]="null"
  [nzClosable]="true"
  (nzOnCancel)="onMoadlCancel()"
  (nzAfterClose)="handleModalClose()"
>
  <ng-container *adModalContent>
    <nz-spin [nzSpinning]="saveAppLoading">
      <form nz-form [formGroup]="appForm" [nzNoColon]="true" class="form-info login-form">
        <nz-form-item>
          <nz-form-control [nzErrorTip]="'dj-请输入解决方案名称，且字符长度不超过40' | translate">
            <app-modal-input
              [attr]="{
                name: '名称',
                required: true,
                lang: appLang?.name,
                needLang: true
              }"
              [value]="appLang?.name[('dj-LANG' | translate)]"
              (callBack)="handlePatchApp('name', $event)"
              formControlName="name"
              ngDefaultControl
            ></app-modal-input>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item *ngIf="opType === 'edit'">
          <nz-form-control>
            <app-modal-input
              [attr]="{ name: '代号', required: true, readOnly: true }"
              [value]="appForm.get('code')?.value"
              formControlName="code"
              ngDefaultControl
            ></app-modal-input>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item *ngIf="opType !== 'edit'">
          <nz-form-control [nzErrorTip]="userErrorTpl">
            <app-modal-input
              [attr]="{
                name: '代号',
                required: true,
                addOnAfter: appCodeEnv,
                readOnly: opType === 'edit',
                maxlength: 10
              }"
              [value]="appForm.get('code')?.value"
              (callBack)="handlePatchApp('code', $event)"
              formControlName="code"
              ngDefaultControl
            ></app-modal-input>
            <ng-template #userErrorTpl let-control>
              <ng-container *ngIf="control.hasError('required')">{{ 'dj-请输入' | translate }}</ng-container>
              <ng-container *ngIf="control.hasError('minlength')">{{ 'dj-最少2位字符' | translate }}</ng-container>
              <ng-container *ngIf="control.hasError('maxlength')">{{ 'dj-最多10位字符' | translate }}</ng-container>
              <ng-container *ngIf="control.hasError('duplicated')">{{ 'dj-app-regular4' | translate }}</ng-container>
              <!--<ng-container *ngIf="control.hasError('duplicated')">{{
                (isInternalTenant(teamId) ? 'dj-app-regular4' : 'dj-app-regular3') | translate
              }}</ng-container>-->
            </ng-template>
          </nz-form-control>
        </nz-form-item>

        <div class="form-item-layout">
          <div class="form-item-icon">
            <nz-form-item>
              <nz-form-control
                [nzErrorTip]="'dj-必填！' | translate"
                [nzValidateStatus]="!appForm.get('iconName').value && appForm.get('iconName').dirty ? 'error' : ''"
              >
                <app-add-app-icon
                  [required]="true"
                  [label]="'dj-解决方案图标' | translate"
                  [value]="value"
                  (change)="onAppIconChange($event)"
                ></app-add-app-icon>
              </nz-form-control>
            </nz-form-item>
          </div>
          <ng-container *ngIf="!solutionCardService.isDynamicsApp(params?.appType)">
            <div *ngIf="isDataDriveApp" class="form-item-common">
              <nz-form-item>
                <nz-form-control>
                  <label nz-checkbox formControlName="commonApp">
                    <span>{{ 'dj-设置为公共解决方案' | translate }}</span>
                  </label>
                </nz-form-control>
                <span>{{ 'dj-公共解决方案的内容会被其他解决方案选择，并在发版时有特殊处理' | translate }}</span>
              </nz-form-item>
            </div>
          </ng-container>
        </div>

        <nz-form-item>
          <nz-form-control [nzErrorTip]="'dj-请输入' | translate">
            <app-textarea-input
              [innerLabel]="true"
              class="description"
              [attr]="{
                type: 'textarea',
                name: '描述',
                required: true,
                lang: appLang?.description,
                needLang: true
              }"
              [value]="appLang?.description[('dj-LANG' | translate)]"
              (callBack)="handlePatchApp('description', $event)"
              formControlName="description"
              ngDefaultControl
            ></app-textarea-input>
          </nz-form-control>
        </nz-form-item>

        <!-- 已有后端服务 -->
        <ng-container *ngIf="!solutionCardService.isDynamicsApp(params?.appType)">
          <div *ngIf="isModelDriveApp && !userIsExperience">
            <nz-form-item [class.hideForm]="opType === 'edit'">
              <nz-form-control>
                <label nz-checkbox formControlName="commonApp">
                  <span>{{ 'dj-设置为公共解决方案' | translate }}</span
                  ><span
                    nz-tooltip
                    [nzTooltipTitle]="'dj-公共解决方案的内容会被其他解决方案选择，并在发版时有特殊处理' | translate"
                    style="margin-left: 6px"
                    ><i adIcon iconfont="iconshuoming2" aria-hidden="true" class="iconfont"></i
                  ></span>
                </label>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item [class.special-row]="createType === 2">
              <nz-form-control>
                <nz-radio-group
                  formControlName="createType"
                  ngDefaultControl
                  (ngModelChange)="handleOptionChange($event)"
                >
                  <label nz-radio [nzValue]="1">
                    {{ 'dj-使用现有后端微服务' | translate }}
                  </label>
                  <label nz-radio [nzValue]="2">
                    {{ 'dj-新建后端微服务' | translate }}
                  </label>
                  <label nz-radio [nzDisabled]="disabledNoServerType" [nzValue]="3">
                    {{ 'dj-其他' | translate }}
                  </label>
                </nz-radio-group>
              </nz-form-control>
            </nz-form-item>
            <nz-form-item nz-col *ngIf="createType === 1">
              <nz-form-control
                [nzHasFeedback]="appForm.get('serviceCode').pending"
                [nzValidateStatus]="serviceCodeStatus"
                [nzValidatingTip]="('dj-正在校验' | translate) + '......'"
                [nzAutoTips]="nzAutoTips"
                [nzErrorTip]="'dj-请选择或输入' | translate"
              >
                <ad-select
                  style="width: 100%"
                  isInsideLabel
                  [label]="'dj-请输入注册在ESP的产品名称' | translate"
                  required="true"
                  nzMode="tags"
                  [nzTokenSeparators]="[',']"
                  formControlName="serviceCode"
                >
                  <ad-option *ngFor="let option of codeList" [nzValue]="option.value" [nzLabel]="option.label">
                  </ad-option>
                </ad-select>
              </nz-form-control>
            </nz-form-item>
            <!-- <nz-form-item nz-col nzSpan="3">
              <nz-form-control nzFlex="310px">
                <div class="serviceCode-set">
                  <span (click)="handleOpenModel()" class="data-form-setting">
                    {{ 'dj-设置' | translate }}
                  </span>
                </div>
              </nz-form-control>
            </nz-form-item> -->
            <!-- 新建解决方案后端 -->
            <div class="add-resource-container" *ngIf="createType === 2">
              <div class="add-button-and-tip">
                <a nz-button nzType="link" (click)="addSource()"> + {{ 'dj-添加资源' | translate }} </a>
                <span class="add-resource-tip">
                  <i adIcon [iconfont]="'iconshuoming'"></i>
                  <span *ngIf="!showErrorMessage"> {{ addResourceTip | translate }}</span>
                  <span *ngIf="showErrorMessage">
                    {{ 'dj-您还没有ECS和RDS云资源， 点此前往' | translate
                    }}<a (click)="goToCloudService()" href="javascript:void(0);" class="link-text">{{
                      'dj-智客中心' | translate
                    }}</a
                    >{{ 'dj-购买！' | translate }}
                    <i (click)="goToCloudService()" adIcon [iconfont]="'icontiaozhuanlianjie'"></i>
                  </span>
                </span>
              </div>
              <ng-container formArrayName="serverResources">
                <nz-form-item
                  [nzGutter]="{ xs: 2, sm: 1, md: 2 }"
                  [nzAlign]="'middle'"
                  [nzJustify]="'space-between'"
                  *ngFor="let resource of serverResources.controls; let i = index"
                  [formGroupName]="i"
                >
                  <nz-form-control [nzSpan]="6">
                    <app-modal-select
                      class="modal-select-wrapper"
                      [attr]="{
                        name: '环境',
                        required: true,
                        options: envList,
                        readOnly: editInitServerResourceCode.includes(resource.value?.envCode)
                      }"
                      [value]="appForm.get('envCode')"
                      (callBack)="handleEnvChange('envCode', $event, i)"
                      formControlName="envCode"
                    >
                    </app-modal-select>
                  </nz-form-control>
                  <nz-form-control [nzSpan]="8">
                    <app-modal-select
                      class="modal-select-wrapper"
                      [attr]="{
                        name: '主机',
                        required: true,
                        options: hostList[i],
                        readOnly: editInitServerResourceCode.includes(resource.value?.envCode)
                      }"
                      formControlName="hostId"
                      (callBack)="handleHostChange($event, i)"
                    >
                    </app-modal-select>
                  </nz-form-control>
                  <nz-form-control [nzSpan]="8">
                    <app-modal-select
                      class="modal-select-wrapper"
                      [attr]="{
                        name: '数据库',
                        required: true,
                        options: rdsList[i],
                        readOnly: editInitServerResourceCode.includes(resource.value?.envCode)
                      }"
                      formControlName="dbId"
                      (callBack)="handleDbChange($event, i)"
                    >
                    </app-modal-select>
                  </nz-form-control>
                  <nz-form-control [nzSpan]="1">
                    <i adIcon [iconfont]="'iconyichu'" (click)="deleteSource(i)"></i>
                  </nz-form-control>
                </nz-form-item>
              </ng-container>
              <nz-form-item *ngIf="serverResources.errors?.minLength">
                <nz-form-control>
                  <span style="color: red">{{ 'dj-' + serverResources.errors?.minLength | translate }}</span>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </ng-container>

        <!-- 高代码应用后端服务 -->
        <ng-container *ngIf="!solutionCardService.isDynamicsApp(params?.appType)">
          <div *ngIf="isHighApp && (opType === 'create' || !transferAppNotNeedService)">
            <nz-form-item>
              <nz-form-control>
                <nz-radio-group formControlName="deploymentType" ngDefaultControl>
                  <label nz-radio nzValue="backend">
                    {{ 'dj-后端开发' | translate }}
                  </label>
                  <label nz-radio nzValue="all">
                    {{ 'dj-前端和后端开发' | translate }}
                  </label>
                </nz-radio-group>
              </nz-form-control>
            </nz-form-item>
            <div class="add-resource-container">
              <div class="add-button-and-tip">
                <a nz-button nzType="link" (click)="addHighSource()"> + {{ 'dj-添加资源' | translate }} </a>
                <span class="add-resource-tip">
                  <i adIcon [iconfont]="'iconshuoming'"></i>
                  <span *ngIf="!showErrorMessage"> {{ addResourceTip | translate }}</span>
                  <span *ngIf="showErrorMessage">
                    {{ 'dj-您还没有ECS和RDS云资源， 点此前往' | translate
                    }}<a (click)="goToCloudService()" href="javascript:void(0);" class="link-text">{{
                      'dj-智客中心' | translate
                    }}</a
                    >{{ 'dj-购买！' | translate }}
                    <i (click)="goToCloudService()" adIcon [iconfont]="'icontiaozhuanlianjie'"></i>
                  </span>
                </span>
              </div>
              <ng-container formArrayName="serverResources">
                <nz-form-item
                  [nzGutter]="{ xs: 2, sm: 1, md: 2 }"
                  [nzAlign]="'middle'"
                  [nzJustify]="'space-between'"
                  class="resource-server-item"
                  *ngFor="let resource of serverResources.controls; let i = index"
                  [formGroupName]="i"
                >
                  <nz-form-control [nzSpan]="10" [nzErrorTip]="'dj-请选择' | translate">
                    <app-modal-select
                      class="modal-select-wrapper"
                      [attr]="{
                        name: '环境',
                        required: true,
                        options: envList,
                        readOnly: editInitServerResourceCode.includes(resource.value?.envCode)
                      }"
                      [value]="appForm.get('envCode')"
                      (callBack)="handleHightEnvChange('envCode', $event, i)"
                      formControlName="envCode"
                    >
                    </app-modal-select>
                  </nz-form-control>
                  <nz-form-control [nzSpan]="10" [nzErrorTip]="'dj-请选择' | translate">
                    <app-modal-select
                      class="modal-select-wrapper"
                      [attr]="{
                        name: '主机',
                        required: true,
                        options: hostList[i],
                        readOnly: editInitServerResourceCode.includes(resource.value?.envCode)
                      }"
                      formControlName="hostId"
                      (callBack)="handleHighHostChange($event, i)"
                    >
                    </app-modal-select>
                  </nz-form-control>
                  <nz-form-control [nzSpan]="2">
                    <i adIcon [iconfont]="'iconyichu'" (click)="deleteHighSource(i)"></i>
                  </nz-form-control>
                </nz-form-item>
              </ng-container>
              <nz-form-item *ngIf="serverResources.errors?.minLength">
                <nz-form-control>
                  <span style="color: red">{{ 'dj-' + serverResources.errors?.minLength | translate }}</span>
                </nz-form-control>
              </nz-form-item>
            </div>
          </div>
        </ng-container>

        <!-- 后端通过接口设置的动态表单 -->
        <ng-container *ngIf="solutionCardService.isDynamicsApp(params?.appType)">
          <ng-container *ngFor="let attr of dynamicsAttributes">
            <ng-container [ngSwitch]="attr.componentType">
              <ng-container *ngSwitchCase="'CHECKBOX'">
                <nz-form-item>
                  <nz-form-control>
                    <label nz-checkbox [nzDisabled]="attr.disabled" [formControlName]="attr.formControlName">
                      <span>{{ attr?.lang?.label?.[('dj-LANG' | translate)] }}</span>
                    </label>
                  </nz-form-control>
                </nz-form-item>
              </ng-container>
            </ng-container>
          </ng-container>
        </ng-container>
      </form>
      <div class="modal-footer">
        <button ad-button adType="default" (click)="onMoadlCancel()">
          {{ 'dj-取消' | translate }}
        </button>
        <button ad-button adType="primary" (click)="handleSaveApp()">
          {{ 'dj-确定' | translate }}
        </button>
      </div>
    </nz-spin>
  </ng-container>
</ad-modal>

<ad-modal
  [nzVisible]="repeatVisible"
  [nzTitle]="'dj-解决方案已存在' | translate"
  (nzOnCancel)="repeatVisible = false"
  [nzFooter]="null"
  nzClassName="repeat-modal"
  [nzWidth]="'460px'"
>
  <ng-container *adModalContent>
    <nz-spin [nzSpinning]="saveAppLoading">
      <p class="i-text-dark">{{ 'dj-请输入正确appToken进行校验' | translate }}</p>
      <div class="m10">
        <form nz-form [formGroup]="repeatForm" [nzNoColon]="true" class="form-info">
          <nz-form-item>
            <nz-form-control [nzErrorTip]="errorTpl">
              <input type="text" nz-input formControlName="appToken" [placeholder]="'dj-请输入' | translate" />
              <ng-template #errorTpl let-control>
                <ng-container *ngIf="control.hasError('required')">{{ 'dj-必填！' | translate }}</ng-container>
              </ng-template>
            </nz-form-control>
          </nz-form-item>
        </form>
        <div class="modal-footer">
          <button ad-button adType="default" (click)="handleCancelRepeat()">
            {{ 'dj-取消' | translate }}
          </button>
          <button ad-button adType="primary" (click)="handleSaveRepeat()">
            {{ 'dj-确定' | translate }}
          </button>
        </div>
      </div>
    </nz-spin>
  </ng-container>
</ad-modal>

<ad-modal
  nzClassName="repeat-modal"
  [(nzVisible)]="excelAppVisible"
  [nzMaskClosable]="false"
  [nzTitle]=""
  (nzOnCancel)="onExcelModalCancel()"
  [nzFooter]="null"
  [nzWidth]="'80vw'"
  (nzAfterClose)="handleExcelAppReset()"
>
  <ng-container *adModalContent>
    <app-excel-app></app-excel-app>
  </ng-container>
</ad-modal>

<app-create-progress></app-create-progress>

<app-data-target-modal
  *ngIf="serviceVisible"
  [visible]="serviceVisible"
  [tableData]="targetList"
  [operationByFront]="true"
  [newApplication]="applicationCode"
  (close)="handleCloseModel($event)"
></app-data-target-modal>

<!-- 解决方案日志 -->
<app-create-log
  *ngIf="logVisible"
  [newAppCode]="newAppCode"
  [logVisible]="logVisible"
  [isHomePage]="isHomePage"
  [activeEnvCode]="logActiveEnvCode"
  [appType]="params?.appType"
  (goAppFn)="handleGoApp($event)"
  (handleCloseModal)="handleCloseLogModal()"
></app-create-log>
