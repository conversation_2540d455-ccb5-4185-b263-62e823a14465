import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ValidationErrors } from '@angular/forms';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable, Observer, Subject } from 'rxjs';
import { AdUserService } from '../login/service/user.service';
import { GlobalService } from 'common/service/global.service';
import { BarchKeyType } from 'components/bussiness-components/layout/menu-bar/branch-manage/type';
import { ActivatedRoute } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class AppService {
  publishUrl: any;
  url: string;
  private _selectedApp: any;
  get selectedApp(): any {
    // 先从url中获取appCode，如果没有再用selectedApp中的code
    // 由于dtd引用的需求，在TTT层所有接口需要使用引用应用的appCode，为了避免大量的接口入参application判断及修改，因此在这里统一处理
    const params = this.router.snapshot.queryParams;
    if (params.viewDesignerCode && this._selectedApp) {
      this._selectedApp.code = params.viewDesignerCode;
    } else if (params?.appCode && this._selectedApp) {
      this._selectedApp.code = params.appCode;
    }
    return this._selectedApp;
  }
  set selectedApp(value: any) {
    // 切换app时才需要重置分支
    if (value?.code && this._selectedApp?.code && value?.code !== this._selectedApp?.code) {
      this.userService.setUserInfo({ branch: BarchKeyType.develop });
    }

    this._selectedApp = value;
  }

  menus: any;
  isExpend: boolean = true; // 左侧菜单是否收起
  isLoadingApp: Subject<boolean> = new Subject();
  gptAppCode: any;
  branchLoading: boolean = false; // 切换分支时loading正在切换分支，请勿关闭当前页面
  appLoading: boolean = false; // 加载app时loading
  individualAppLoading: boolean = false; // 个案加载app时loading
  autoAddParadigms: any = []; // 范式
  mdLastActiveUrl: string;
  mdCurrentUrlType: string = '';
  mdTabList = [
    {
      name: 'dj-解决方案总览',
      url: '/app/application-overview',
      urlType: 'application-overview',
    },
    {
      name: 'dj-业务搭建',
      url: '/app/business-constructor',
      urlType: 'business-constructor',
    },
    {
      name: 'dj-集成与自动化',
      url: '/app/integrated-automation',
      urlType: 'integrated-automation',
    },
    {
      name: 'dj-数据分析',
      url: '/app/data-analysis',
      urlType: 'data-analysis',
    },
    {
      name: 'dj-发版中心',
      url: '/app/release-manage',
      urlType: 'release-manage',
    },
  ];
  constructor(
    protected http: HttpClient,
    protected configService: SystemConfigService,
    private userService: AdUserService,
    public globalService: GlobalService,
    private router: ActivatedRoute,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.url = url;
      // this.url = 'http://192.168.202.32:8000';
    });
    this.configService.get('publishUrl').subscribe((url) => {
      this.publishUrl = url;
    });
    this.isLoadingApp.subscribe((isLoadingApp) => {});
    this.userService.setUserInfo({ branch: BarchKeyType.develop });
  }

  ngOnDestroy(): void {
    this.isLoadingApp.unsubscribe();
  }

  updateSelectedApp(appCode: string): Observable<any> {
    this.isLoadingApp.next(true);

    return new Observable((subscriber) => {
      if (appCode === '') {
        this.selectedApp = null;
        this.isLoadingApp.next(false);
        subscriber.next(this.selectedApp);
        this.globalService.currentAppType = null;
        return;
      }

      if (this.selectedApp?.code === appCode) {
        this.globalService.currentAppType = this.selectedApp.appType;
        this.isLoadingApp.next(false);
        subscriber.next(this.selectedApp);

        return;
      }

      // 查询是否解决方案下是否有自动添加组件的范式
      this.queryAutoAddParadigm(appCode);
      //进入解决方案数据埋点
      this.queryAccessRecord(appCode);

      this.loadAppInfo(appCode).subscribe(
        (res) => {
          this.selectedApp = res?.data || null;
          this.globalService.currentAppType = this.selectedApp.appType;
          this.isLoadingApp.next(false);
          subscriber.next(this.selectedApp);
        },
        () => {
          this.isLoadingApp.next(false);

          subscriber.error('selectedApp信息请求失败');
        },
      );
    });
  }

  /**
   * 进入解决方案数据埋点
   * @param appCode
   */
  queryAccessRecord(appCode) {
    this.getAccessRecord(appCode).subscribe(
      () => {},
      () => {},
    );
  }

  /** code校验规则 */
  codeValidator = (control) =>
    new Observable((observer: Observer<ValidationErrors | null>) => {
      // const teamId = this.userService.getUser('teamId');
      const { value = '' } = control;
      // const str = isInternalTenant(teamId)
      //   ? new RegExp(`^[a-zA-Z][a-zA-Z0-9-]+$`, 'g')
      //   : new RegExp(`^[a-zA-Z][a-zA-Z0-9-]{0,38}-${this.userService.getUser('tenantId')}$`, 'g');
      //
      const str = new RegExp(`^[a-zA-Z][a-zA-Z0-9-]+$`, 'g');
      if (!str.test(value)) {
        observer.next({
          error: true,
          duplicated: true,
        });
      } else {
        observer.next(null);
      }
      observer.complete();
    });

  /**
   * 解决方案基础模块
   */
  loadAppList(params: any): Observable<any> {
    const { condition = '', pageNum = 1, pageSize = 40, appType = '', auth = '' } = params;
    const url = `${this.url}/athena-designer/application/get?condition=${condition}&pageNum=${pageNum}&pageSize=${pageSize}&appType=${appType}&auth=${auth}`;
    return this.http.get(url);
  }

  loadIndividualAppList(params: any): Observable<any> {
    const { condition = '', pageNum = 1, pageSize = 40, appType = '', auth = '', tenantId = '' } = params;
    const url = `${this.url}/athena-designer/application/get?condition=${condition}&pageNum=${pageNum}&pageSize=${pageSize}&appType=${appType}&auth=${auth}&tenantId=${tenantId}`;
    return this.http.get(url);
  }

  loadTenantAppList(params: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/tenant/queryApplications`;
    return this.http.get(url, { params });
  }

  /**
   * 查询解决方案的过期时间
   * @param codes
   * @returns
   */
  queryExperienceOverTime(codes: string[]): Observable<any> {
    const url = `${this.url}/athena-designer/application/queryExperienceOverTime`;
    return this.http.post(url, codes);
  }

  /**
   * 从模板安装解决方案
   * @param params
   * @returns
   */
  installExampleApp(params): Observable<any> {
    return this.http.post(`${this.url}/athena-designer/application/installExampleApp`, params);
  }

  // 查询app分支详情
  loadAppBranch(app: any): Observable<any> {
    const url = `${this.url}/athena-designer/branch/${app}/all`;
    return this.http.get(url);
  }

  // app创建分支
  saveAppBranch(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/checkoutAppBranch`;
    return this.http.post(url, param);
  }

  // 复制app
  copyAgileDataApp(param): Observable<any> {
    const url = `${this.url}/athena-designer/agile/application/copy`;
    return this.http.get(url, { params: param });
  }

  // 导出app
  exportAgileDataApp(param): Observable<any> {
    const url = `${this.url}/athena-designer/agile/application/export`;
    return this.http.get(url, {
      responseType: 'blob',
      params: param,
    });
  }

  // 导入app
  importAgileDataApp(param): Observable<any> {
    const url = `${this.url}/athena-designer/agile/application/importAgileApp`;
    return this.http.post(url, param);
  }

  // 查询导入app进度
  queryImportProcessAgileDataApp(param): Observable<any> {
    const url = `${this.url}/athena-designer/agile/application/queryImportProcess`;
    return this.http.get(url, { params: param });
  }

  /**
   * 删除app进度
   * @param params
   */
  async findDeleteAppProgress(params: any): Promise<any> {
    const url = `${this.url}/athena-designer/application/queryDeleteProgress`;
    return await this.http.post(url, params).toPromise();
  }

  /**
   * 删除解决方案日志
   * @param params
   */
  async findDeleteAppLog(params: any): Promise<any> {
    const url = `${this.url}/athena-designer/application/queryDeleteLogDetail`;
    return await this.http.post(url, params).toPromise();
  }
  /**
   * 查询App详细
   * @param code
   */
  async queryAppInfo(params: any): Promise<any> {
    const url = `${this.url}/athena-designer/application/queryByCodes`;
    return await this.http.post(url, params).toPromise();
  }

  /**
   * 新的删除App
   * @param code
   */
  async deleteNewApp(code: string): Promise<any> {
    const url = `${this.url}/athena-designer/application/delete/v2/${code}`;
    return await this.http.get(url).toPromise();
  }

  // 删除App 废弃
  deleteApp(code: string): Observable<any> {
    const url = `${this.url}/athena-designer/application/delete/${code}`;
    return this.http.get(url);
  }

  // 删除当前分支下的解决方案数据
  deleteBranchApp(param: any): Observable<any> {
    const { appCode, branchName } = param;
    const url = `${this.url}/athena-designer/application/${appCode}/${branchName}`;
    return this.http.delete(url);
  }

  addApp(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/add`;
    return this.http.post(url, param);
  }

  /**
   * 创建解决方案
   * @param params
   * @returns
   */
  createSolution(params): Observable<any> {
    const url = `${this.url}/athena-designer/solution`;
    return this.http.post(url, params);
  }

  addAppV2(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/addV2`;
    return this.http.post(url, param);
  }

  batchAddServiceCode(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/servicecode/batchAdd`;
    return this.http.post(url, param);
  }

  saveApp(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/update`;
    return this.http.post(url, param);
  }
  saveAppV2(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/updateV2`;
    return this.http.post(url, param);
  }
  getUploadToken(): Observable<any> {
    const url = `${this.url}/athena-designer/file/getDmcToken`;
    return this.http.get(url);
  }
  get teamId(): string {
    return this.userService.getUser('teamId');
  }

  // 是否公共解决方案
  get isCommonApp() {
    return this.selectedApp.commonApp;
  }

  /**
   * 解决方案分支相关
   */
  // 获取所有分支
  loadAllBranch(): Observable<any> {
    const url = `${this.url}/athena-designer/branch/all`;
    return this.http.get(url);
  }

  // 切项目总体分支
  changeMainBranch(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/switchBranch`;
    return this.http.post(url, param);
  }

  // 合并分支
  mergeAppBranch(app: any, mergeRequestId: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/applyMerge/${app}/${mergeRequestId}`;
    return this.http.put(url, {});
  }

  // 获取解决方案合并状态
  loadAppMergeStatus(app: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/mergeRequest/${app}`;
    return this.http.get(url);
  }

  // 合并分支
  mergeBranch(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/mergeRequest`;
    return this.http.post(url, param);
  }

  // 关闭合并请求
  cancelMergeBranch(app: string): Observable<any> {
    const url = `${this.url}/athena-designer/archive/mergeRequest/${app}`;
    return this.http.delete(url);
  }

  // 解决冲突
  resolveConflict(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/resolveConflicts`;
    return this.http.post(url, param);
  }

  /**
   * S7插入需求 分支管理 查询分支
   */
  loadAllBranches(): Observable<any> {
    const url = `${this.url}/athena-designer/branch/getAllBranches`;
    return this.http.get(url);
  }

  /**
   * S7插入需求 分支管理 切换分支
   */
  // 切项目总体分支
  switchBranch(params: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/switchBranch`;
    return this.http.post(url, params);
  }

  // 获取租户列表
  loadTenantList(param: any): Observable<any> {
    const url = `${this.publishUrl}/athenadeployer/application/getTenant${param}`;
    return this.http.get(url);
  }

  // 校验版本
  validVersion(): Observable<any> {
    const url = `${this.url}/athena-designer/application/version/${this.selectedApp?.code}`;
    return this.http.get(url);
  }

  // 发布项目
  publishProject(param: any): Observable<any> {
    const url = `${this.publishUrl}/athenadeployer/application/tenant/deploy`;
    return this.http.post(url, param);
  }

  // 编译解决方案
  compileApp(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/compile`;
    return this.http.post(url, param);
  }

  // 查询编译日志
  loadCompileRecord(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/queryCompileLog${param}`;
    return this.http.get(url);
  }

  //复制解决方案
  copyApp(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/copy`;
    return this.http.post(url, param);
  }

  // 授权
  setAppAuthUser(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/appAuthUser`;
    return this.http.post(url, param);
  }

  // 新增分支接口
  addAppBranch(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/addAppBranch`;
    return this.http.post(url, param);
  }

  // 删除分支接口
  deleteAppBranch(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/archive/appBranch/${param}`;
    return this.http.delete(url);
  }

  // 查询app信息
  loadAppInfo(appCode: string): Observable<any> {
    const url = `${this.url}/athena-designer/application/authAppInfo/${appCode}`;
    return this.http.get(url);
  }

  // 跳转一点平台
  loadElUrl(params): Observable<any> {
    return this.http.post(`${this.url}/athena-designer/user/sso_token/login`, params);
  }

  // 登出
  logout(params): Observable<any> {
    return this.http.post(`${this.url}/athena-designer/user/logout`, params);
  }

  // 查询范式
  queryAutoAddParadigm(appCode): void {
    this.getAutoAddParadigm(appCode).subscribe(
      (res) => {
        this.autoAddParadigms = res?.data || [];
      },
      () => {
        this.autoAddParadigms = [];
      },
    );
  }

  // 查询解决方案下是否有自动添加组件的范式
  getAutoAddParadigm(appCode: any): Observable<any> {
    const url = `${this.url}/athena-designer/paradigm/atuoAddParadigm?application=${appCode}`;
    return this.http.get(url);
  }

  aiEvent(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/aihardone/aiEvent`;
    return this.http.post(url, param);
  }

  aiEventSave(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/aihardone/aiEventSave`;
    return this.http.post(url, param);
  }

  // 查询已设置的解决方案后端列表
  getServiceCodeList(): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/getServiceCodeList`;
    return this.http.get(url);
  }

  // 设置解决方案后端-新增服务
  createServiceCode(params): Observable<any> {
    return this.http.post(`${this.url}/athena-designer/modelDriverTarget/serverSource/createServiceCode`, params);
  }

  // 获取可用服务资源
  getAvailableServiceList(params?: any): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/query`;
    return this.http.get(url, { params });
  }

  // 调用esp校验后端服务是否存在
  checkServiceValid(serviceCode: string): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/validateIsRegistry?serviceCode=${serviceCode}`;
    return this.http.get(url);
  }

  // 修改解决方案-查询解决方案详情
  getApplicationDetail(applicationCode: string): Observable<any> {
    const url = `${this.url}/athena-designer/application/queryApplicationDetail?applicationCode=${applicationCode}`;
    return this.http.get(url);
  }

  // 新增解决方案
  addAppV3(params): Observable<any> {
    const url = `${this.url}/athena-designer/application/addV3`;
    return this.http.post(url, params);
  }

  // 更新解决方案
  updateAppV3(params): Observable<any> {
    const url = `${this.url}/athena-designer/application/updateV3`;
    return this.http.post(url, params);
  }

  // 高代码 新增应用
  highCodeAdd(params): Observable<any> {
    const url = `${this.url}/athena-designer/application/highCodeAdd`;
    return this.http.post(url, params);
  }

  // 高代码 更新解决方案
  highCodeEdit(params): Observable<any> {
    const url = `${this.url}/athena-designer/application/highCodeEdit`;
    return this.http.post(url, params);
  }

  // 查询已设置的解决方案后端列表
  getCustomBackendInfoList(): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/getCustomBackendInfoList`;
    return this.http.get(url);
  }

  // 查询解决方案后端创建日志
  getRecordList(appCode: any): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/getSaveServiceLog?application=${appCode}`;
    return this.http.get(url);
  }

  // 查询应用后端创建状态
  getSaveServiceStatus(appCode: any): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/getSaveServiceStatus?application=${appCode}`;
    return this.http.get(url);
  }

  // 重试
  retry(appCode: any): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/tryCreateService?application=${appCode}`;
    return this.http.get(url);
  }

  /**
   * 高代码重试
   * @param appCode
   * @returns
   */
  hightRetry(appCode: any): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/tryDeployApp?application=${appCode}`;
    return this.http.get(url);
  }

  // 重置
  reset(id: any): Observable<any> {
    const url = `${this.url}/athena-designer/iamApi/api/iam/v2/dev/app/secret/reset`;
    return this.http.post(url, { id });
  }

  // 获取解决方案后端资源
  getBackendInfoAsync(serviceCode: any, cloud: any): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/serverSource/getBackendInfo?serviceCode=${serviceCode}&&cloud=${cloud}`;
    return this.http.get(url);
  }

  // 下载源码
  downloadSourceCode(appCode: any): Observable<any> {
    const url = `${this.url}/athena-designer/modelDriverTarget/downloadSourceCode?application=${appCode}`;
    return this.http.get(url, { responseType: 'blob' });
  }

  // 数据埋点
  getAccessRecord(appCode: any): Observable<any> {
    const url = `${this.url}/athena-designer/application/accessRecord?application=${appCode}`;
    return this.http.get(url);
  }
  /**
   * 获取自动生成的侦测code
   * @param params
   */
  async getCodeByType(abbreviation: string, typeCode: string = 'default'): Promise<any> {
    const params = {
      typeCode: typeCode,
      paramMap: {
        abbreviationCode: abbreviation,
        application: this.selectedApp?.code,
      },
    };
    const url = `${this.url}/athena-designer/code/generate/standard`;
    return await this.http
      .post(url, params)
      .toPromise()
      .then((res: any) => {
        return Promise.resolve(res?.data);
      })
      .catch((e) => {
        console.error(e);
      });
  }
}
