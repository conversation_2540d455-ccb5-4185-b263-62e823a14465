import { Component, OnInit, ViewChild } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { LocaleService } from 'common/service/locale.service';
import { ApiToolService } from 'pages/api-tool/api-tool.service';
import { AdUserService } from 'pages/login/service/user.service';
import { isInternalTenant } from 'common/utils/core.utils';
import { cloneDeep } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';
import { ApplicableSceneService } from './applicable-scene.service';
import { ColumnAddCategoryComponent } from './components/column-add-category/column-add-category.component';
import { ColumnCategoryCodesComponent } from './components/column-category-codes/column-category-codes.component';
import { SINGLE_ROW_HEIGHT } from 'pages/api-tool/api-tool.setting';

@Component({
  selector: 'app-applicable-scene',
  templateUrl: './applicable-scene.component.html',
  styleUrls: ['./applicable-scene.component.less'],
  providers: [ApplicableSceneService],
})
export class ApplicableSceneComponent implements OnInit {
  visible: boolean;
  lang: string;
  tableStyle: string;
  columnDefs: any[];
  rowData: any[];
  nameMap: Map<string, string>;
  messageSpecList: any[] = [];
  teamId: string;
  defaultColDef: any = {
    minWdith: SINGLE_ROW_HEIGHT,
    suppressMenu: true,
    cellClass: 'ag-cell-wrap-text',
    suppressMovable: true,
  };

  frameworkComponents: any = {
    'column-category-codes': ColumnCategoryCodesComponent,
    'column-add-category': ColumnAddCategoryComponent,
  };

  @ViewChild('athTable') refAthTable: any;

  constructor(
    private apiToolService: ApiToolService,
    private messageService: NzMessageService,
    private translateService: TranslateService,
    private languageService: LocaleService,
    private applicableSceneService: ApplicableSceneService,
    private userService: AdUserService,
  ) {
    this.messageSpecList = this.handleGetMessageSpecList();
    this.handleNameMap(this.messageSpecList);
    this.teamId = this.userService.getUser('teamId');
  }

  get isDisabled(): boolean {
    // 鼎捷外部ISV禁用适用场景 鼎捷内部ISV可以使用适用场景
    if (isInternalTenant(this.teamId) && this.nameMap.size > 0) {
      return false;
    }
    return true;
  }

  ngOnInit(): void {
    this.columnDefs = this.handleGetColumnDefs();
    this.handleLoadViewCategory();
  }

  // 打开弹窗
  handleClickOpenModal(): void {
    if (this.isDisabled) {
      if (isInternalTenant(this.teamId)) {
        this.messageService.warning(this.translateService.instant('dj-词汇为空'));
      }
      return;
    }
    this.visible = true;
    this.lang = this.languageService.currentLanguage;
    this.handleLoadRelationByWords();
  }

  // 查询所有类型信息内容的列表并返回
  handleGetMessageSpecList(): any[] {
    // 数据: Request信息内容、Response信息内容(成功)、Response信息内容(失败)
    const { requestMessageSpec, responseMessageFailedSpec, responseMessageSuccessSpec } =
      this.apiToolService.BreadContents.apiData;
    // 数据: 排除（E）类型
    const messageSpecList = [...requestMessageSpec, ...responseMessageFailedSpec, ...responseMessageSuccessSpec].filter(
      (msg) => !['E'].includes(msg.columnType),
    );
    return messageSpecList;
  }

  // 生成词名称字典
  handleNameMap(messageSpecList: any[]): void {
    this.nameMap = new Map(
      messageSpecList.map((msg) => {
        return [msg.dataName, msg.dataDescription];
      }),
    );
  }

  // 根据词查对应的关系
  handleLoadRelationByWords(): void {
    // 词汇去重
    const param = {
      words: Array.from(new Set(this.messageSpecList.map((msg) => msg.dataName))),
    };
    // 计算表格高度
    this.handleCalcTableStyle(param.words.length);
    this.applicableSceneService.loadRelationByWords(param).subscribe(
      (res) => {
        if (res.code === 0) {
          this.rowData = res.data.map((row) => {
            const { wordCode, wordCategories } = row;
            return {
              wordCode,
              wordName: this.nameMap.get(wordCode),
              wordCategories,
              categoryCodes: [],
            };
          });
        }
      },
      () => {},
    );
  }

  // 计算表格样式
  handleCalcTableStyle(lineNum: number): void {
    if (lineNum < 10) {
      this.tableStyle = `width:100%;height:${48 * lineNum + 46}px`;
    } else {
      this.tableStyle = `width:100%;height:523px`;
    }
  }

  // 查询词条和维度关系
  handleLoadViewCategory(): void {
    this.applicableSceneService.loadViewCategory().subscribe(
      (res) => {
        const { code, data } = res;
        if (code === 0) {
          this.applicableSceneService.viewCategory = cloneDeep(data);
          this.applicableSceneService.viewCategoryTree = this.handleBuildSceneTree('', data);
          this.applicableSceneService.viewCategoryMap = new Map(
            this.applicableSceneService.viewCategoryTree.map((category) => {
              return [category.code, category];
            }) ?? [],
          );
        }
      },
      () => {},
    );
  }

  // 构建场景树
  handleBuildSceneTree(pCode: string, sceneList: any[]): any[] {
    const child = sceneList.filter((item) => item.parentCode === pCode);
    return child.map((item) => {
      const children = this.handleBuildSceneTree(item.code, sceneList);
      if (children.length > 0) {
        return {
          ...item,
          key: item.code,
          children: children,
          title: item.nameLang[this.translateService.instant('dj-LANG')] ?? item.name,
          isLeaf: false,
        };
      } else {
        return {
          ...item,
          key: item.code,
          title: item.nameLang[this.translateService.instant('dj-LANG')] ?? item.name,
          isLeaf: true,
        };
      }
    });
  }

  // 表格列对象
  handleGetColumnDefs(): any[] {
    return [
      {
        field: 'wordCode',
        sortable: false,
        filter: false,
        width: 200,
        resizable: false,
        headerName: this.translateService.instant('dj-词汇'),
      },
      {
        field: 'wordName',
        sortable: false,
        filter: false,
        width: 200,
        resizable: false,
        headerName: this.translateService.instant('dj-名称'),
      },
      {
        field: 'wordCategories',
        sortable: false,
        filter: false,
        width: 320,
        resizable: false,
        autoHeight: true,
        headerName: this.translateService.instant('dj-已有场景'),
        cellRenderer: 'column-category-codes',
      },
      {
        field: 'categoryCodes',
        sortable: false,
        filter: false,
        width: 320,
        resizable: false,
        headerName: this.translateService.instant('dj-新增场景'),
        cellRenderer: 'column-add-category',
      },
    ];
  }

  // 取消弹窗
  handleModalCancel(): void {
    this.visible = false;
  }

  // 保存弹窗内容（增量方式）
  handleModalSave(): void {
    // const { type } = this.apiToolService.BreadContents;
    // const status = type === 'query' ? 1 : 0;
    const param = {
      wordCategories: (this.rowData.filter((row) => row.categoryCodes.length > 0) ?? []).map(
        ({ wordCode, wordName, categoryCodes }) => {
          return {
            wordCode,
            wordName,
            status: 0, // （目前，词库是公有的，状态暂时无用）先都传0
            categoryCodes,
          };
        },
      ),
    };
    if (param.wordCategories.length === 0) {
      this.visible = false;
      return;
    }
    this.applicableSceneService.saveViewWord(param).subscribe(
      (res) => {
        if (res.code === 0) {
          this.messageService.success(this.translateService.instant('dj-保存成功'));
          this.visible = false;
        }
      },
      () => {
        this.messageService.error(this.translateService.instant('dj-保存失败'));
      },
    );
  }
}
