import { Observable } from 'rxjs';

export class RestfulContents {
  url: string;
  iamUrl: string;

  constructor(protected http, protected configService) {
    this.configService.get('apiMgmtUrl').subscribe((url) => {
      this.url = url;
      // this.url = 'http://10.20.86.51:32622';
    });
    this.configService.get('iamUrl').subscribe((url) => {
      this.iamUrl = url;
    });
  }
  /* 
  获取用户角色
   */
  getUseRole(param: any): Promise<any> {
    const url = `${this.iamUrl}/api/iam/v2/user/role`;
    return this.toPromise(this.http.post(url, param));
  }

  /**
   * 获取信息類別清單
   * @returns
   */
  getApiCategory(): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiCategory/Get`;
    return this.toPromise(this.http.get(url));
  }

  /* 
      查询api详情:
   */

  queryAPIDetail(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiDetail/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      进阶查询清单
   */

  querySearchConditions(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/SearchConditionList/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      进阶查询获取数据
   */

  queryAPIsByConditions(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/AdvanceSearchApiListOther/Get`;
    return this.toPromise(this.http.post(url, param));
  }


  /**
   * 标准查询总数
   * @param param 
   * @returns 
   */
  queryAdvanceSearchApiCount(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/AdvanceSearchApiCount/Get`;
    return this.toPromise(this.http.post(url, param));
  }



  /* 
      进阶查询清单 -- api设计 statusId
   */

  querySearchConditionsByDesign(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiDesignSearchConditionList/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      获取信息范本连接
   */

  queryInfoModalFile(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/MessageExampleFile/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      下载模板连接
   */

  queryDownloadSpecify(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiSpecFile/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      导出规格 apiName = api名稱 apiVersion = 版號
   */

  queryExportSpecify(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiSpecFile/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  queryStandardApiQueryList(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/StandardApiQueryList/Export`;
    return this.toPromise(this.http.post(url, param));
  }

  queryStandardApiSpecify(param): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/StandardApiQuery/Export`;
    return this.toPromise(this.http.post(url, param));
  }

  /**
   * 获取状态列表清单
   */
  getApiStates(param): Promise<any> {
    // this.url = 'http://10.20.86.98:32622';
    const url = `${this.url}/restful/standard/apimgmt/ApiStatusList/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      api送审 apiList
   */

  submitAPIApprove(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiReview/Update`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      api送审 apiList
   */

  submitAPIFix(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiComfirmed/Update`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      取回 apiList
   */

  specifyBack(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiSpecRetrieve/Update`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      api删除 apiName version
   */

  deleteAPISpecify(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiDelete/Delete`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      api审核通过 apiList version
   */

  approveAPIPass(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiExaminationPassed/Update`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      api审核回退  
   */

  approveAPIBack(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiReject/Update`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      api审核历程 apiName version
   */

  getAPICourse(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiApprovedProcess/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      api导入 apiName version
   */

  getImportSpecifyUrl(): string {
    const url = `${this.url}/restful/standard/apimgmt/ApiImport/Update`;
    return url;
  }

  /* 
      api导入 -- binary
   */

  checkImportSpecify(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiImport/Check`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
      api实际导入 -- binary
   */
  actualImportSpecify(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiImport/Update`;
    return this.toPromise(this.http.post(url, param));
  }

  /* 
    解析租户信息获取租户对应的人员name 非邮箱 
   */

  getTokenAnalyze(): Promise<any> {
    const url = `${this.iamUrl}/api/iam/v2/identity/token/analyze`;
    return this.toPromise(this.http.post(url));
  }

  /* 
    获取新增修改复制场景下的下拉数据
   */

  getEditDropdownMenu(): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/DropdownMenu/Get`;
    return this.toPromise(this.http.get(url));
  }

  saveApiDetail(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiSpec/Save`;
    return this.toPromise(this.http.post(url, param));
  }

  private toPromise(fn: any): Promise<any> {
    return new Promise((resolve): void => {
      fn.subscribe(
        (res: any): void => {
          resolve(res);
        },
        () => {
          resolve({ data: null });
        },
      );
    });
  }

  searchApiDateName(param: any): Promise<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiDateNameSearch/Get`;
    return this.toPromise(this.http.post(url, param));
  }

  ApiVersion(param: any): Observable<any> {
    const url = `${this.url}/restful/standard/apimgmt/ApiVersion/Get`;
    return this.http.post(url, param);
  }
}
