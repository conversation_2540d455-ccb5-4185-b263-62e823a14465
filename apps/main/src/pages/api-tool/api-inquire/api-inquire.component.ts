import { Component, OnDestroy, OnInit } from '@angular/core';
import { Subscription } from 'rxjs';
import { ApiToolService } from '../api-tool.service';
import { AdUserService } from 'pages/login/service/user.service';
import { EditDropdownMenu } from '../api-overview/api.mock';
import { Router, ActivatedRoute } from '@angular/router';
import { isEmpty } from 'lodash';
import { AuthService } from 'common/service/auth.service';

@Component({
  selector: 'app-api-inquire',
  templateUrl: './api-inquire.component.html',
  styleUrls: ['./api-inquire.component.less'],
})
export class ApiInquireComponent implements OnInit, OnDestroy {
  loading: boolean = false;
  type: 'query' = 'query';
  categories: any[] = [];
  activeCategory: any = null;
  activeRow: any = null;
  // rowsData: any[] = [];
  inEdit: boolean = false; // 新增 编辑复制时
  editType: 'add' | 'edit' | 'copy' | 'sub';
  menuLevel: number = 0;
  apiData: any;
  caseUntoken: boolean = false;

  activeRowSubscription: Subscription;
  breadContentsSubscription: Subscription;
  breadThreeLevelSubscription: Subscription;

  selectedIndex = 0;
  featureList: any[] = [
    { label: 'dj-标准API查询', type: 'query' },
    { label: 'dj-标准API设计', type: 'design' },
  ];

  constructor(
    private router: Router,
    private apiToolService: ApiToolService,
    protected adUserService: AdUserService,
    private route: ActivatedRoute,
    private newAuthService: AuthService,
  ) {
    this.getEditDropdownMenu();
    this.breadContentsSubscription = this.apiToolService.breadContentsSubject$.subscribe((res) => {
      this.loading = true;
      setTimeout(() => {
        this.menuLevel = res?.level;
        if (res?.level === 4 && !res.unRefresh && res.editType) {
          this.apiData = res.apiData;
          this.editType = res.editType;
          // 新增 复制 进版 建立子API
          this.inEdit = true;
          this.activeRow = { name: res.apiData.apiName, version: res.apiData.apiVersion, ...this.apiData };
          // this.queryAPIlist();
        } else {
          this.editType = null;
        }
        this.loading = false;
      }, 0);
    });
    this.breadThreeLevelSubscription = this.apiToolService.breadThreeLevelSubject$.subscribe((res) => {
      if (res && res.type === 'query') {
        this.toCategoryInit();
      }
    });
    this.activeRowSubscription = this.apiToolService.classifyRowClickSubject$.subscribe((res) => {
      if (res?.activeRow) {
        this.handleActiveRow(res);
      }
    });
    this.getApiCategory();
  }

  async getUseRole() {
    // const userInfo = this.adUserService.getUserInfo();
    // const res: any = await this.apiToolService.getUseRole({ id: userInfo.userId });
    // if (res.roles) {
    //   const matchItem = res.roles.find((one) => one.id === 'apiMdApprover' || one.id === 'apiMdCreator');
    //   if (matchItem) {
    //     this.apiToolService.setUserRole(matchItem.id);
    //   } else {
    //     sessionStorage.removeItem('role');
    //   }
    // }
    this.apiToolService.setUserRole('apiMdCreator');
  }

  async getApiCategory() {
    this.loading = true;
    const resAll: any = await this.apiToolService.getApiCategory();
    this.loading = false;
    if (resAll?.code === 0) {
      this.apiToolService.setCategories(resAll.data);
      this.categories = (resAll.data ?? []).map((one) => {
        one.active = false;
        return one;
      });
    }
  }

  // async queryAPIlist() {
  //   this.loading = true;
  //   const res: any = await this.apiToolService.queryAPIsByConditions({
  //     conditionList: [],
  //     pageSize: 10000,
  //     pageNum: 1,
  //     type: false,
  //     categoryId: this.activeCategory.id.toString(),
  //     statusId: '',
  //   });
  //   this.loading = false;
  //   if (res?.code === 0) {
  //     const resdata = res.data?.value ?? [];
  //     if (this.editType === 'copy') {
  //       resdata.push({
  //         name: this.apiData.apiName + '.copy',
  //         description: this.apiData.apiDescription,
  //         active: true,
  //       });
  //     } else if (this.editType === 'sub') {
  //       resdata.push({
  //         name: this.apiData.apiName + '.sub',
  //         description: this.apiData.apiDescription,
  //         active: true,
  //       });
  //     }
  //     this.rowsData = resdata.map((one) => {
  //       if (this.activeRow) {
  //         if (this.editType === 'copy') {
  //           one.active = one.name === this.apiData.apiName + '.copy';
  //         } else if (this.editType === 'sub') {
  //           one.active = one.name === this.apiData.apiName + '.sub';
  //         } else {
  //           one.active = one.name === this.activeRow.name && one.version === this.activeRow.version;
  //         }
  //       }
  //       return one;
  //     });
  //   }
  // }

  ngOnInit(): void {
    const allow = this.newAuthService.getAuth('update');
    if (!allow || !this.adUserService.isLogin) {
      this.featureList = [{ label: 'dj-标准API查询', type: 'query' }];
    } else {
      this.featureList = [
        { label: 'dj-标准API查询', type: 'query' },
        { label: 'dj-标准API设计', type: 'design' },
      ];
    }
    if (!this.adUserService.isLogin) {
      // 免登录场景
      this.caseUntoken = true;
      sessionStorage.removeItem('role');
    } else {
      this.getUseRole();
    }
    this.handleInit();

    // 路由选中
    this.route.queryParams.subscribe((param) => {
      const { name, categoryId, categoryName, approvedStatus, version, tenantId } = param;
      if (name && categoryId && categoryName) {
        this.menuLevel = 4;
        this.activeRow = { name, categoryId, categoryName, approvedStatus, version, tenantId };
        this.activeCategory = { id: categoryId, category: categoryName };
        // 获取类目下的 列表信息
        // this.queryAPIlist();
        this.apiToolService.searchData = [];
        this.apiToolService.setBreadContents({
          type: this.type,
          level: 4,
          apiData: { name, categoryId, categoryName, approvedStatus, version },
          tenantId,
          module: 'ApiInquireComponent',
        });
      }
    });
  }

  toLogin(): void {
    this.router.navigateByUrl('/login?returnUrl=/asset-center/api-tool/query');
  }

  handleInit(): void {
    this.menuLevel = 2;
    this.apiToolService.setBreadContents({
      type: this.type,
      level: 2,
      module: 'ApiInquireComponent',
    });
    this.activeCategory = null;
    this.activeRow = null;
  }

  // 一级面包屑
  toAPIManageInit(): void {
    if (this.caseUntoken) {
      return;
    }
    this.apiToolService.classifyRowClickSubject$.next(undefined);
    this.menuLevel = 1;
    this.apiToolService.searchData = [];
    this.apiToolService.toPage('/asset-center/api-tool', null);
    this.apiToolService.setBreadContents({
      type: this.type,
      level: 1,
      module: 'ApiInquireComponent',
    });
  }

  // 二级面包屑
  toInquireInit(action?: string): void {
    this.menuLevel = 2;
    this.activeCategory = null;
    this.activeRow = null;
    this.categories = this.categories.map((one) => {
      one.active = false;
      return one;
    });
    this.apiToolService.searchData = [];
    if (action === 'back') {
      this.apiToolService.toPage(window.location.pathname, null);
    }
    this.apiToolService.setBreadContents({
      type: this.type,
      level: 2,
      module: 'ApiInquireComponent',
    });
  }

  // 三级面包屑
  toCategoryInit(action?: string): void {
    this.menuLevel = 3;
    this.activeRow = null;
    this.apiToolService.searchData = [];
    this.handleCategoryActive(this.activeCategory, action);
  }

  handleCategoryActive(item, action?: string): void {
    this.activeCategory = item;
    this.categories = this.categories.map((one) => {
      one.active = one.id.toString() === item.id.toString();
      return one;
    });
    this.apiToolService.searchData = [];
    if (action === 'back') {
      this.apiToolService.toPage(window.location.pathname, null);
    }

    // 激活，触发三级面包屑
    this.apiToolService.setBreadContents({
      type: this.type,
      level: 3,
      class: this.activeCategory,
      module: 'ApiInquireComponent',
    });
  }

  // 四级面包屑
  handleActiveRow(res: any): void {
    // this.menuLevel = 4;
    // this.activeRow = res.activeRow;
    // this.activeCategory = { id: res.activeRow?.categoryId, category: res.activeRow.categoryName };
    // // 获取类目下的 列表信息
    // this.queryAPIlist();
    // this.apiToolService.searchData = [];
    // this.apiToolService.setBreadContents({
    //   type: this.type,
    //   level: 4,
    //   apiData: res.activeRow,
    //   tenantId: res.tenantId,
    //   module: 'ApiInquireComponent',
    // });

    const url = this.router.serializeUrl(
      this.router.createUrlTree([window.location.pathname], {
        queryParams: {
          name: res.activeRow?.name ?? res.activeRow?.apiName,
          version: res.activeRow?.version ?? res.activeRow?.apiVersion,
          approvedStatus: res.activeRow?.approvedStatus,
          categoryName: res.activeRow?.categoryName,
          categoryId: res.activeRow?.categoryId,
          tenantId: res.activeRow.tenantId,
        },
      }),
    );
    window.open(url, '_blank');
  }

  handleActiveRowChange(val: any): void {
    this.activeRow = val;
    this.editType = null;
  }

  async getEditDropdownMenu() {
    const resAll: any = await this.apiToolService.getEditDropdownMenu();
    if (resAll?.code === 0) {
      this.apiToolService.setAllDefault(resAll.data);
      this.apiToolService.setEditDropDownMenu(resAll.data.dropdownMenu);
    }
  }

  ngOnDestroy(): void {
    this.breadContentsSubscription.unsubscribe();
    this.activeRowSubscription.unsubscribe();
    this.breadThreeLevelSubscription?.unsubscribe();
  }

  handleFeatureClick(e?: any) {
    const { type } = this.featureList[e ?? this.selectedIndex] ?? {};
    // 页签跳转 服务清空逻辑
    this.apiToolService.classifyRowClickSubject$.next(undefined);
    this.menuLevel = 2;
    this.apiToolService.searchData = [];
    // 页签跳转
    this.router.navigateByUrl(this.caseUntoken ? `api-query-view` : `asset-center/api-tool/${type}`);
    this.apiToolService.setBreadContents({
      type: type,
      level: 2,
      unRefresh: type === 'design',
      module: 'ApiOverviewComponent',
    });
  }
}
