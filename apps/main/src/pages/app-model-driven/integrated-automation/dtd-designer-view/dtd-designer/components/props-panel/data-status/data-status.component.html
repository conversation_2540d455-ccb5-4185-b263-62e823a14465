<div class="app-data-status">
  <nz-spin class="loading" [nzSpinning]="loading"></nz-spin>
  <div class="header">
    <span>{{ 'dj-数据状态' | translate }}</span>
  </div>

  <section class="content">
    <form nz-form class="form" [formGroup]="stateFormGroup">
      <nz-form-item class="nz-form-item-content">
        <nz-form-control class="form-item">
          <div class="item-title">
            {{ 'dj-节点id' | translate }}
            <span class="item-required">*</span>
          </div>
          <app-modal-input
            formControlName="code"
            ngDefaultControl
            [innerLabel]="false"
            [attr]="{
              name: 'dj-节点id',
              required: true,
              readOnly: true
            }"
            style="width: 100%"
            [innerLabel]="false"
            [value]="stateFormGroup.get('code')?.value"
          >
          </app-modal-input>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="nz-form-item-content">
        <nz-form-control class="form-item" [nzErrorTip]="'dj-必填' | translate">
          <div class="item-title">
            {{ 'dj-节点名称' | translate }}
            <span class="item-required">*</span>
          </div>
          <app-modal-input
            formControlName="name"
            ngDefaultControl
            [innerLabel]="false"
            [attr]="{
              name: '节点名称',
              required: true,
              needLang: true,
              lang: lang?.name
            }"
            style="width: 100%"
            [innerLabel]="false"
            [nzDisabled]="stateFormGroup.disabled"
            [value]="stateFormGroup.get('name')?.value"
            (callBack)="handlePatchLang('name', $event)"
            (translateLoading)="handleTranslateLoading($event)"
          >
          </app-modal-input>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="nz-form-item-content">
        <nz-form-control class="form-item">
          <div class="item-title">
            {{ 'dj-条件' | translate }}
          </div>
          <app-modal-input
            formControlName="conditionExpression"
            ngDefaultControl
            [innerLabel]="false"
            [attr]="{
              name: '条件',
              required: true
            }"
            style="width: 100%"
            [innerLabel]="false"
            [nzDisabled]="stateFormGroup.disabled"
            [value]="stateFormGroup.get('conditionExpression')?.value"
            (callBack)="handlePatchLang('conditionExpression', $event)"
          >
          </app-modal-input>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item class="nz-form-item-content">
        <nz-form-control class="form-item">
          <div class="item-title">
            {{ 'dj-关联的特征集' | translate }}
          </div>
          <ad-select
            [nzShowSearch]="true"
            [nzPlaceHolder]="'dj-请选择' | translate"
            nzMode="multiple"
            ngDefaultControl
            formControlName="dataFeatureSets"
          >
            <ad-option *ngFor="let option of dataStateModalFeatureList" [nzValue]="option.code" [nzLabel]="option.name">
            </ad-option>
          </ad-select>
        </nz-form-control>
      </nz-form-item>
      <nz-form-item>
        <nz-form-control>
          <div class="form-item">
            <div class="item-title">
              {{ 'dj-变量设置' | translate }}
            </div>
            <!-- <div [ngClass]="{ 'not-allow': isReference }">
              <div class="example" (click)="handleShowExample()">{{ 'dj-示例' | translate }}</div>
              <app-state-import-json
                type="normal"
                [data]="stateFormGroup.get('variables')?.value"
                (returnData)="handleImportJson($event)"
              ></app-state-import-json>
            </div> -->
            <nz-collapse [nzBordered]="false">
              <nz-collapse-panel [nzHeader]="'dj-DTD变量' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
                <nz-spin class="i-loading" [nzSpinning]="variableLoading">
                  <app-props-variable-item
                    [variableData]="variableData"
                    [showCopy]="true"
                    [showOperation]="false"
                    [showAdd]="true"
                    [showMore]="false"
                    variableType="DTDVar"
                    [variableList]="variableList"
                    (edit)="handleEdit($event)"
                    (refreshVariable)="handleRefreshVariable()"
                  >
                  </app-props-variable-item>
                </nz-spin>
              </nz-collapse-panel>
            </nz-collapse>
          </div>
        </nz-form-control>
      </nz-form-item>
    </form>
  </section>
  <ng-container *operateAuth="{ prefix: 'update', guards: [!isReference] }">
    <div class="footer">
      <div class="save" (click)="handleSave()">
        <i adIcon iconfont="iconbaocun2" class="save-icon"></i>
        <span class="save-text">{{ 'dj-保存设置项' | translate }}</span>
      </div>
    </div>
  </ng-container>
</div>
<nz-modal
  [(nzVisible)]="isShowExample"
  [nzTitle]="'dj-示例' | translate"
  (nzOnCancel)="handleOk()"
  (nzOnOk)="handleOk()"
>
  <ng-container *nzModalContent>
    <pre>
  {{ jsonData }}  
    </pre>
  </ng-container>
</nz-modal>
