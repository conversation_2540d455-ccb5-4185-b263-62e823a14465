import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';

import { TranslateService } from '@ngx-translate/core';
import { to, validatorForm } from 'common/utils/core.utils';
import { PropsPanelApiService } from '../props-panel-api.service';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { GraphComponent } from '../../../graph/graph.component';
import { TaskListComponent } from '../../../task/task-list.component';
import { DtdDesignerViewService } from 'pages/app-model-driven/integrated-automation/dtd-designer-view/service/dtd-designer-view.service';
import { cloneDeep, delay, isEqual, pick } from 'lodash';
import { takeUntil } from 'rxjs/internal/operators/takeUntil';
import { debounceTime } from 'rxjs/internal/operators/debounceTime';
import { Subject } from 'rxjs/internal/Subject';
import { NzMessageService } from 'ng-zorro-antd/message';
import { distinctUntilChanged } from 'rxjs/operators';
import { LocaleService } from 'common/service/locale.service';
import { Graph, Node } from '@antv/x6';
import { processData } from '../../../../../../utils/utils';
import { Subscription } from 'rxjs';
import { Router, NavigationEnd } from '@angular/router';
import { filter } from 'rxjs/operators';

/**
 * @description: 获取所有前序节点
 * @param {Graph} graph 画布
 * @param {Node} startNode 当前节点
 * @return {*} 关联节点列表
 */
function getPredecessorsFromNode(graph: Graph, startNode: Node): Node[] {
  const result: Node[] = [];
  let currentLevelNodes: Node[] = [startNode];
  let previousFlag = true;

  while (true) {
    const parents: Node[] = [];
    currentLevelNodes.forEach((node) => {
      const incomingEdges = graph.getIncomingEdges(node) || [];
      incomingEdges.forEach((edge) => {
        const source = edge.getSourceCell();
        const data = source.getData();
        if (source && source.isNode() && !result.includes(source)) {
          parents.push(source);
          // 过滤掉旧的任务节点
          if (data.type === 'task' && data.taskCategory === 'workflowTask') {
            source.setData({ previousFlag });
            result.push(source);
          }
        }
      });
    });

    if (parents.length === 0) {
      break;
    }
    previousFlag = false;
    currentLevelNodes = parents;
  }

  return result;
}

@Component({
  selector: 'app-data-status',
  templateUrl: './data-status.component.html',
  styleUrls: ['./data-status.component.less'],
  providers: [],
})
export class DataStatusComponent implements OnInit, OnDestroy {
  @Input() params: any = {};
  @Input() graphComponentRef: GraphComponent;
  @Input() taskComponentRef: TaskListComponent;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();

  @Output() formChange: EventEmitter<any> = new EventEmitter();
  loading: boolean = false; // 加载状态
  nodeState: { data: any; nodeData: any };
  stateFormGroup: FormGroup;
  tabIndex: number = 0;
  dataStateModalData: any;
  dataStateModalFeatureList: any; //数据特征
  dataStateModalType: string;
  isShowExample: boolean;
  public lang: Record<
    string,
    {
      zh_CN: string;
      zh_TW: string;
      en_US: string;
    }
  > = {};
  jsonData: string;
  originDataStateData: any;
  private destory$ = new Subject();
  private flagLeave: boolean = false; // 是否离开
  translating: boolean;

  /** 直连大T,列表显示的变量 */
  variableData = [];
  /** 跨T，下拉选择的变量 */
  variableList = [];
  private routerSubscription: Subscription;

  /** 控制DTD变量列表加载 */
  variableLoading = false;
  /** 原始的变量列表，用作变更比对 */
  originVariableData = [];

  get isReference() {
    return this.params?.bcReference || this.params?.ngArguments?.bcReference;
  }

  constructor(
    public translate: TranslateService,
    private apiService: PropsPanelApiService,
    private dtdDesignerService: DtdDesignerViewService,
    private fb: FormBuilder,
    protected changeDetectorRef: ChangeDetectorRef,
    private message: NzMessageService,
    private languageService: LocaleService,
    private router: Router,
  ) {
    this.ininForm();
    this.dtdDesignerService.isGetVariableByProTask$.pipe(takeUntil(this.destory$)).subscribe((res) => {
      if (!res) return;
      setTimeout(() => {
        this.getDTDVariableData();
      });
    });
  }

  ngOnInit(): void {
    this.jsonData = JSON.stringify(
      [
        { dataType: 'STRING', name: '项目编号', key: 'project_no', hasArrayStruct: false },
        {
          dataType: 'OBJECT',
          name: '任务编号',
          fields: [
            {
              dataType: 'STRING',
              name: '编号',
              isArray: false,
              key: 'id',
            },
          ],
          key: 'task_no',
          hasArrayStruct: false,
        },
      ],
      null,
      2,
    );
    this.initData();
    this.dtdDesignerService.setContentChangeCheckObject({
      contentComponent: this,
      checkFunction: 'checkContentChangeWithoutSave',
    });
    this.getDTDVariableData();
    // 监听路由事件，当导航结束时刷新数据
    this.routerSubscription = this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        this.getDTDVariableData();
      });
  }
  ngOnChanges(change) {
    if (!change.params.firstChange && change.params.currentValue !== change.params.previousValue) {
      this.lang = {};
      this.stateFormGroup.reset();
      this.initData();
      this.getDTDVariableData();
    }
    if (change.params?.currentValue) {
      if (this.isReference) {
        this.stateFormGroup?.disable();
      } else {
        this.stateFormGroup?.enable();
      }
    }
  }
  ngOnDestroy(): void {
    this.dtdDesignerService.resetContentChangeCheckObject();
    this.destory$.next();
    this.destory$.complete();
    this.dtdDesignerService._isGetVariableByProTask$.next(false);
    if (this.routerSubscription) {
      this.routerSubscription.unsubscribe();
    }
  }
  ininForm(): void {
    this.stateFormGroup = this.fb.group({
      code: ['', [Validators.required]],
      name: ['', [Validators.required]],
      conditionExpression: [''], //条件
      dataFeatureSets: [[]], //数据特征
      lang: [this.lang],
    });
    this.stateFormGroup.valueChanges
      .pipe(takeUntil(this.destory$), distinctUntilChanged(), debounceTime(250))
      .subscribe((value) => {
        this.checkContentChangeWithoutSave((isChange) => {
          // 如果表单变动，则重置离开标识
          isChange && (this.flagLeave = false);
        });
        this.formChange.emit(this.stateFormGroup);
      });
  }
  async initData() {
    const { type, code, dataCode, name } = this.params;
    const dataStateComponentRef = this.graphComponentRef.dataStateComponentRef;
    const dataState = dataStateComponentRef?.originDataStateList?.find(
      (item) => item.dataDescription.code === dataCode,
    );

    const state = dataState?.dataStateList?.find((item: any) => item.code === code) || {};
    console.log(state);

    this.dataStateModalData = {
      dataFeatureSets: [],
      ...state,
      conditionExpression: state?.condition?.expression || '',
    };

    this.originDataStateData = { ...this.dataStateModalData, variables: this.dataStateModalData.variables || [] };
    this.dataStateModalFeatureList = dataState?.dataFeatures || [];
    this.lang = this.dataStateModalData.lang || this.lang;
    delay(() => {
      this.stateFormGroup.patchValue({
        code: this.dataStateModalData.code,
        name: this.dataStateModalData.name || '',
        conditionExpression: this.dataStateModalData.conditionExpression || '', //条件
        dataFeatureSets: this.dataStateModalData.dataFeatureSets || [], //数据特征
        lang: this.dataStateModalData.lang || this.lang,
      });
    }, 0);
    // DtdDesignerViewService将该值设置为true了，此处将flagLeave重置为false，否则会有不再校验的bug。
    this.flagLeave = false;
  }
  // 赋值lang
  handlePatchLang(key: any, data: any): void {
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang,
      };
    }
    this.stateFormGroup.patchValue({ [key]: data?.value, lang: this.lang });
    this.stateFormGroup.get(key).markAsDirty();
    this.stateFormGroup.get(key).updateValueAndValidity({ onlySelf: true });
  }
  /**
   * 是否有文案正在翻译
   * @param loading
   */
  public handleTranslateLoading(event: { loading: boolean }) {
    this.translating = event.loading;
  }
  /**
   * 保存
   * 外部可用
   */
  public async handleSave(needTips = true): Promise<any> {
    if (this.isReference) return; // 引用数据状态不保存
    if (this.translating) {
      this.message.error(this.translate.instant('dj-正在翻译多语言，请稍后再保存'));
      return false;
    }
    validatorForm(this.stateFormGroup);
    if (this.stateFormGroup.invalid) {
      return false;
    }
    const params = {
      application: this.dataStateModalData.application,
      code: this.stateFormGroup.get('code').value,
      condition: {
        expression: this.stateFormGroup.get('conditionExpression').value,
        type: this.dataStateModalData?.condition?.type,
      },
      dataCode: this.dataStateModalData.dataCode,
      dataFeatureSets: this.stateFormGroup.get('dataFeatureSets').value || [],
      name: this.stateFormGroup.get('name').value,
      lang: this.stateFormGroup.get('lang').value,
      variables: this.variableData.map((item) => ({
        ...item,
        dataType: 'DTDVar',
        varName: item.name || item.key,
        key: item.name || item.key,
        previousFlag: item.previousFlag || false,
        taskCode: item.code,
      })),
    };
    // 更新状态节点名称
    this.graphComponentRef.updateNodeName('datastate', this.dataStateModalData.code, params.name, params.lang);

    const [err, res] = await to(this.apiService.saveDataState(params).toPromise());
    if (res?.code === 0) {
      // 保存画布节点关系
      this.graphComponentRef.saveGraph(false);
      this.originDataStateData = params;
      this.originVariableData = cloneDeep(params.variables);
      this.originDataStateData.conditionExpression = params.condition.expression || '';
      if (needTips) {
        this.message.success(this.translate.instant('dj-保存成功！'));
      }
    }

    // // 刷新状态列表
    this.graphComponentRef.dataStateComponentRef?.fetchDataList();
    this.handleToProcess();
  }
  public checkContentChangeWithoutSave(callback?: Function): boolean {
    const values = pick(
      {
        ...this.stateFormGroup.getRawValue(),
        variables: this.originVariableData,
      },
      'name',
      'conditionExpression',
      'dataFeatureSets',
      'variables',
    );

    const currendValue = pick(
      {
        ...this.originDataStateData,
        variables: this.variableData,
      },
      'name',
      'conditionExpression',
      'dataFeatureSets',
      'variables',
    );
    const isChanged = !isEqual(currendValue, values);
    callback && callback(isChanged);

    return isChanged && !this.flagLeave;
  }
  handleShowExample() {
    this.isShowExample = true;
  }
  handleOk() {
    this.isShowExample = false;
  }

  /**
   * 设置离开标识
   * @param value
   */
  public setFlagLeave(value: boolean) {
    this.flagLeave = value;
  }
  /**
   * 处理变量的扩展信息
   * @param variable
   * @returns
   */
  handleExtendDataChange(variable) {
    console.log(variable);
  }
  /**
   * 处理编辑变量
   */
  handleEdit(data: { type: 'add' | 'delete' | 'update'; item: any; callBack: () => void }) {
    const { type, item, callBack } = data;
    if (type === 'add') {
      if (!this.variableData.some((variable) => variable.primaryKey === item.primaryKey)) {
        this.variableData.push(item);
      } else {
        this.message.error(this.translate.instant('dj-存在重复数据，请检查!'));
      }
    } else if (type === 'delete') {
      this.variableData = this.variableData.filter((variable) => variable.primaryKey != item.primaryKey);
    }
    // 最后，通过回调函数关闭模态框
    callBack && callBack();
  }
  /**
   * @description: 更新DTD变量数据
   * @param {*} updateAll 是否更新全部数据 false为只更新下拉数据
   * @return {*}
   */
  async getDTDVariableData(updateAll = true) {
    const graph = this.graphComponentRef.getGraph();
    const currentNode = graph.getNodes().find((node) => {
      const { type, code: nodeCode } = node.getData();
      return this.dataStateModalData.code === nodeCode && type === 'datastate';
    });
    const predecessors = currentNode ? getPredecessorsFromNode(graph, currentNode) : [];
    const params = {
      application: this.dataStateModalData.application,
      bcReference: this.dataStateModalData.bcReference || false, //适配多版本传参
      dataCode: this.dataStateModalData.code,
      name: this.dataStateModalData.name,
      taskData: predecessors.map((item) => {
        const { code, type, taskCategory, previousFlag, ngArguments } = item.getData();
        return {
          taskCode: code,
          previousFlag,
          name: ngArguments.name,
          adpType: taskCategory, //没有就给空
        };
      }),
    };
    // 引用的 需要额外处理版本号
    if (params.bcReference && this.getReferenceData()) {
      params['bcReferenceHeader'] = this.getReferenceData();
    }
    this.variableLoading = true;
    const res = await this.apiService.saveBigTVariableToData(params).toPromise();
    this.variableLoading = false;
    const { code, data } = res;
    if (code === 0) {
      this.variableList = [];
      // 遍历任务节点
      data?.taskData?.forEach((item) => {
        const { variables, previousFlag } = item;
        if (variables && variables.length > 0) {
          // 下拉选择-选择所有数据
          this.variableList.push({
            ...item,
            code: item.taskCode,
            name: item.name || item.taskCode,
            variables: variables.map((variable) => ({
              ...variable,
              code: item.taskCode,
              dataType: variable.dataType || 'String',
              adpType: item.adpType,
              primaryKey: item.taskCode + variable.key, // 前端作唯一标识
              name: variable.name || variable.key,
              hasArrayStruct: variable.hasArrayStruct || false,
              previousFlag: previousFlag || false,
            })),
          });
        }
      });
      if (updateAll) {
        // 已选择列表
        this.variableData = [];
        data?.variables?.forEach((item) => {
          this.variableData.push({
            ...item,
            code: item.taskCode,
            primaryKey: item.taskCode + item.varName, // 前端作唯一标识
            dataType: item.dataType || 'String',
            name: item.varName,
            hasArrayStruct: false,
            previousFlag: item.previousFlag || false,
          });
        });
        this.originVariableData = cloneDeep(this.variableData);
      }
    }
  }
  // 调用DTD变量保存接口
  handleToProcess() {
    let taskCodeList = [];
    const graph = this.graphComponentRef.getGraph();
    const currentNode = graph.getNodes().find((node) => {
      const { type, code: nodeCode } = node.getData();
      return this.dataStateModalData.code === nodeCode && type === 'datastate';
    });
    const outgoingNodes = graph.getNeighbors(currentNode, { outgoing: true });
    outgoingNodes.forEach((node) => {
      const data = node.getData();
      if (data.type === 'task' && data.taskCategory === 'workflowTask') {
        taskCodeList.push(data.code);
      }
    });
    const params = {
      application: this.dataStateModalData.application,
      dataCode: this.dataStateModalData.code,
      taskCodeList,
      variables: this.variableData,
    };
    this.apiService.toProcess(params).toPromise();
  }
  /**
   * @description: dtd变量列表点击添加变量时查询一次下拉列表，只更新下拉数据，不更新列表数据
   * @return {*}
   */
  handleRefreshVariable() {
    this.getDTDVariableData(false);
  }

  getReferenceData(): any {
    let headerInfo = null;
    if (this.params?.bcReference) {
      const originDataStateItem: any =
        this.graphComponentRef.dataStateComponentRef?.originDataStateList?.find(
          (item) => item.dataDescription.code === this.params.dataCode,
        ) || {};
      const dataState = originDataStateItem?.dataStateList?.find((item) => item.code === this.params.code);
      if (!!dataState) {
        headerInfo = {
          adpStatus: dataState?.adpStatus,
          adpVersion: dataState?.adpVersion,
        };
      }
    }
    return headerInfo;
  }
}
