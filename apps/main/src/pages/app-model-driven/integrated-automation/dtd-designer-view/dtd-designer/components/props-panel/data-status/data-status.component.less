.app-data-status {
  position: relative;
  width: 330px;
  height: calc(100vh - 134px);
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: normal;
  background-color: #fff;

  .loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.5);
  }

  .header {
    height: 55px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: PingFang SC;
    font-size: 15px;
    font-weight: 500;
    border-bottom: 1px solid #eeeeee;
  }
  .content {
    height: calc(100% - 36px);
    overflow-y: auto;
    padding-bottom: 16px;
    .form {
      margin-top: 12px;
      margin-bottom: 46px;
      .nz-form-item-content {
        margin-bottom: 4px;
      }
      .form-item {
        margin-bottom: 8px;
        padding: 0 16px;
        .item-title {
          color: #333;
          font-size: 13px;
          margin-bottom: 8px;
          display: inline-block;
          .item-required {
            margin-right: 4px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
          }
        }
        .example {
          color: rgb(106, 76, 255);
          cursor: pointer;
          font-size: 13px;
        }
      }
    }
  }

  .footer {
    position: sticky;
    bottom: 0;
    height: 46px;
    border-top: 1px solid #e5e5e5;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    z-index: 102;
    .save {
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #6868ae;
      padding: 5px 0;
      width: 90%;
      border-radius: 4px;
      cursor: pointer;
      .save-icon {
        color: #6868ae !important;
        font-size: 14px;
      }
      .save-text {
        color: #6868ae;
        font-size: 13px;
        margin-left: 8px;
        cursor: pointer;
      }
    }
  }

  .not-allow {
    cursor: not-allowed;
    pointer-events: none;
    filter: grayscale(100%);
    user-select: none;
    opacity: 0.5;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
  }
}
