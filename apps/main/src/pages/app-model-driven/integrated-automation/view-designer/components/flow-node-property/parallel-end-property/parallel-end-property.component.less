.parallel-end-property-root {
  width: 330px;
  height: calc(100vh - 134px);
  font-size: 14px;
  font-family: PingFang SC;
  font-weight: normal;
  // box-shadow: -4px 0px 10px 0px rgba(233, 233, 233, 0.5);
  background-color: #fff;
  .nz-form-item-content {
    margin-bottom: 6px;
  }
  .header {
    height: 55px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-family: PingFang SC;
    font-size: 15px;
    font-weight: 500;
    border-bottom: 1px solid #eeeeee;

    i {
      font-size: 12px;
    }
  }

  .content {
    height: calc(100% - 55px);
    overflow-y: auto;
    padding-bottom: 16px;

    .content-tip {
      display: flex;
      align-items: baseline;
      margin: 16px 0;
      padding: 8px 16px;
      font-size: 12px;
      color: #fd5609;
      background: rgba(240, 98, 24, 0.1);
      i.iconfont {
        flex: none;
      }
    }

    ::ng-deep .ant-tabs-content {
      min-height: 260px;
    }
  }

  &.submit-form {
    .form-item {
      margin-bottom: 8px;

      .item-title {
        color: #333;
        font-size: 13px;
        margin-bottom: 8px;
        display: inline-block;

        .item-required {
          margin-right: 4px;
          color: #ff4d4f;
          font-size: 14px;
          font-family: SimSun, sans-serif;
          line-height: 1;
        }
      }

      .hasError {
        ::ng-deep .ant-input-affix-wrapper {
          border: 1px solid #ea3d46;
        }
      }

      .hasErrorBorder {
        border: 1px solid #ea3d46;
      }

      .error {
        color: #ff4d4f;
        font-size: 12px;
      }
    }

    .form-end {
      display: flex;
      font-size: 13px;
      align-items: center;
      line-height: 30px;
      justify-content: space-between;

      .field-set,
      .iconfont {
        cursor: pointer;
        color: #6a4cff;
      }
    }
  }

  ::ng-deep nz-collapse {
    background-color: #fff;
    padding: 0px;
  }

  ::ng-deep nz-collapse-panel {
    ::ng-deep .ant-collapse-header {
      background: #f8fafd;
      padding: 8px 16px !important;
      color: #666 !important;
    }
    .ant-collapse-content {
      padding: 0 16px;
    }
  }

  ::ng-deep nz-collapse-panel .ant-collapse-content-box {
    padding: 0px;
  }

  ::ng-deep ath-tabs[navStyle='button'] {
    nz-tabs-nav {
      margin-bottom: 16px !important;
    }

    .ant-tabs-tab {
      border: 1px solid #bec3e1;
      color: #6868ae;
    }

    .ant-tabs-tab-active {
      background: #dbdaf0;
      font-weight: normal;
    }
  }
  .form-item-time {
    .item-title {
      margin-bottom: 8px;
    }
    .item-required {
      margin-right: 4px;
      color: #ff4d4f;
      font-size: 14px;
      font-family: SimSun, sans-serif;
      line-height: 1;
    }

    ::ng-deep .ant-form-item-explain.ant-form-item-explain-error {
      position: absolute;
      bottom: -21px;
    }
    .flex-form-item {
      align-items: center;
      display: flex;
      span {
        font-size: 13px;
        font-weight: 400;
      }
    }
    .api {
      font-size: 13px;
      word-break: break-all;
      cursor: pointer;
      color: #6a4cff;
    }
    ::ng-deep {
      .nz-form-item-content {
        margin-bottom: 0;
      }
      .ant-select-single:not(.ant-select-customize-input) .ant-select-selector {
        height: 30px;
      }
    }
    .custom-card-checkbox {
      font-weight: normal;
    }
  }
  .question-icon {
    margin-left: 8px;
    padding-right: 8px;
    font-size: 14px;
    cursor: pointer;
    display: inline-block;
    line-height: 17px;
    vertical-align: middle;
  }
  .custom-operate {
    background: #f8fafd;
    padding: 10px;
  }
  .data-srouce-tip {
    margin-top: 10px;
    background: #e5e6e9;
    padding: 2px 5px;
  }
  .form-nostyle-item {
    margin-bottom: 0;
  }
  .mt20 {
    margin-top: 20px;
  }

  .form-item-tip {
    height: 48px;
    background: rgba(240, 98, 24, 0.1);
    border-radius: 4px;
    display: flex;
    align-items: self-start;
    font-size: 12px;
    color: #fd5609;
    padding: 8px;
    margin: 10px;
  }
}
