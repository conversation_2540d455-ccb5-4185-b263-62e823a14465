<div class="parallel-end-property-root submit-form">
  <div class="header">
    <span>{{ 'node-并行结束' | translate }}</span>
    <i adIcon iconfont="icondanchuxiaoxiguanbi" class="iconfont" aria-hidden="true" (click)="handleClosePanel()"></i>
  </div>
  <form nz-form class="content" [formGroup]="dataFormGroup">
    <div class="form-item-tip">
      <i style="margin: 4px 4px 0 0" adIcon type="bulb" theme="outline"></i>
      <span>{{ 'dj-并行分支：设定数据进入并行分支后的处理模式，目前支持2中并行数据处理' | translate }}</span>
    </div>
    <nz-collapse [nzBordered]="false">
      <!--基本信息的面板-->
      <nz-collapse-panel [nzHeader]="'dj-基本信息' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点id-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点id' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-component-input
                [attr]="{
                  name: '节点id',
                  required: true,
                  needLang: false,
                  readOnly: true,
                  notAllowClear: true
                }"
                style="width: 100%"
                formControlName="id"
                [value]="dataFormGroup.get('id')?.value"
                ngDefaultControl
              >
              </app-component-input>
            </div>
          </nz-form-control>
        </nz-form-item>

        <nz-form-item class="nz-form-item-content">
          <nz-form-control [nzAutoTips]="errorTips">
            <!--节点名称-->
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-节点名称' | translate }}
                <span class="item-required">*</span>
              </div>
              <app-modal-input
                class="language-input"
                [attr]="{
                  name: 'name',
                  required: true,
                  lang: this.nameLang?.name,
                  needLang: true
                }"
                [innerLabel]="false"
                [value]="nameLang?.name?.[('dj-LANG' | translate)] || dataFormGroup.get('name')?.value"
                (callBack)="handlePatchApp('name', $event)"
                [formControlName]="'name'"
                ngDefaultControl
              ></app-modal-input>
            </div>
          </nz-form-control>
        </nz-form-item>
      </nz-collapse-panel>
      <nz-collapse-panel [nzHeader]="'dj-数据合并' | translate" [nzActive]="true" [nzExpandedIcon]="'caret-right'">
        <nz-form-item>
          <nz-form-control>
            <nz-radio-group formControlName="parallelGatewayEndStrategy">
              <label nz-radio nzValue="default">
                {{ 'dj-默认规则' | translate }}
                <i
                  adIcon
                  iconfont="iconshuomingwenzi"
                  class="question-icon"
                  style="margin-left: 3px"
                  nz-tooltip
                  [nzTooltipTitle]="''"
                >
                </i>
              </label>
              <label nz-radio nzValue="custom">{{ 'dj-自定义规则' | translate }}</label>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>

        <ng-container *ngIf="dataFormGroup.get('parallelGatewayEndStrategy')?.value === 'custom'">
          <div class="custom-operate">
            <div class="form-item">
              <div class="item-title">
                {{ 'dj-业务主键来源' | translate }}
                <span class="item-required">*</span>
                <div class="data-srouce-tip">{{ 'dj-默认为并行开始的数据源，本期暂不可修改' | translate }}</div>

                <nz-form-item class="form-nostyle-item mt20">
                  <nz-form-control>
                    <nz-radio-group formControlName="type">
                      <label nz-radio nzValue="mapping">{{ 'dj-UI模式' | translate }}</label>
                      <label nz-radio nzValue="script">{{ 'dj-脚本模式' | translate }}</label>
                    </nz-radio-group>
                  </nz-form-control>
                </nz-form-item>

                <div *ngIf="dataFormGroup.get('type').value === 'mapping'" class="form-item mt20">
                  <div class="item-title">
                    {{ 'dj-业务主键' | translate }}
                    <span class="item-required">*</span>
                  </div>
                  <nz-form-item>
                    <nz-form-control>
                      <ad-select
                        style="width: 100%"
                        formControlName="uniKeys"
                        [nzPlaceHolder]="'dj-请选择' | translate"
                        nzMode="multiple"
                      >
                        <ad-option
                          *ngFor="let item of unikeyList"
                          [nzLabel]="item.fieldName+'(' + item.lang?.fieldName?.['dj-LANG'|translate] + ')'"
                          [nzValue]="item.data_name"
                        ></ad-option>
                      </ad-select>
                    </nz-form-control>
                  </nz-form-item>
                </div>

                <div *ngIf="dataFormGroup.get('type').value === 'script'" class="form-item mt20">
                  <nz-input-group [nzSuffix]="suffixIconx">
                    <input readonly nz-input formControlName="script" (dblclick)="showScriptModal()" />
                  </nz-input-group>
                  <ng-template #suffixIconx>
                    <i adIcon iconfont="icongaodaima" (click)="showScriptModal('pre', i, 'requestScript')"></i>
                  </ng-template>
                </div>
              </div>
            </div>
          </div>
        </ng-container>
      </nz-collapse-panel>
    </nz-collapse>
  </form>
</div>

<!--script脚本编辑器弹窗-->
<app-script-editor
  *ngIf="scriptModalVisible"
  [scriptModal]="scriptModalVisible"
  [script]="script"
  (confirm)="handleCloseSript('confirm', $event)"
  (close)="handleCloseSript('close', $event)"
></app-script-editor>
