import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep, isEmpty, size, find } from 'lodash';
import {
  BindFormType,
  PannelTabsType,
} from 'pages/app-model-driven/integrated-automation/view-designer/config/typings';
import { ManualExecutionPropertyService } from '../../manual-execution-property.service';
import { takeUntil } from 'rxjs/operators';
import { Subject } from 'rxjs';
import { ViewApiService } from 'pages/app-model-driven/integrated-automation/view-designer/service/api.service';

@Component({
  selector: 'app-checkpoint-setting',
  templateUrl: './checkpoint-setting.component.html',
  styleUrls: ['./checkpoint-setting.component.less'],
})
export class CheckpointSettingComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  // 唯一标识节点使用，OnChanges中监听便于重新加载数据
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  // updateValueAndValidity 会触发form的改变，误走单改变的监听事件中，导致再次调用getCurrentData造成死循环
  formGroupValidityFlag = false;

  bindForm = {
    formCode: '', // 作业code
    modelCode: '', // 模型code
    serviceCode: '', // 服务code
    type: '', // 类型
  };

  // 执行人不走formgroup ,单独处理
  executor: any = {};
  // 核决层级配置
  decisionConfig: {};
  // 字段的设置
  fieldConfig: [];
  // 前置的人工节点
  preManualNodes = [];

  saveButton = true;
  submitButton = true;

  // 当前选择的是否是自定义任务卡
  get isCustomizeCard() {
    return this.bindForm?.formCode && this.bindForm?.type === BindFormType.PageView;
  }

  destroy$ = new Subject();

  constructor(
    private translate: TranslateService,
    private fb: FormBuilder,
    public service: ViewApiService,
    private manualExecutionPropertyService: ManualExecutionPropertyService,
  ) {
    this.manualExecutionPropertyService.bindFormChange$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
      if (isEmpty(data)) return;
      this.bindForm = data;
      this.fieldConfig = [];
    });
  }

  ngOnInit() {
    this.dataFormGroup = this.fb.group({
      strategyConfig: this.fb.group({}),
    });
    this.handleInit();
    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleInit();
      }
    }
  }

  // 根据输入的组装fromgroup
  handleInit(): void {
    this.executor = cloneDeep(this.data.executor);
    this.fieldConfig = cloneDeep(this.data.fieldConfig);
    this.preManualNodes = cloneDeep(this.data.preManualNodes || []);
    // bindForm的处理
    this.bindForm = this.data.bindForm;
    //  签核按钮设置的处理
    this.saveButton = find(this.data?._buttons, (button) => button.id === 'workflow-act-save');
    this.submitButton = find(this.data?._buttons, (button) => button.id === 'workflow-act-submit');
  }
  ngAfterViewInit() {
    this.getCurrentData();
  }

  // 子组件人员设定发生了改变
  handlePeopleSettingChange(data) {
    const { executor } = data;
    this.executor = executor;
    this.getCurrentData();
  }
  handleFieldSettingChange(data) {
    const { fieldConfig } = data;
    this.fieldConfig = fieldConfig;
    this.getCurrentData();
  }

  getFormValidate() {
    this.formGroupValidityFlag = true;
    for (const i of Object.keys(this.dataFormGroup?.controls)) {
      this.dataFormGroup.controls[i].markAsDirty();
      this.dataFormGroup.controls[i].updateValueAndValidity();
    }
    this.formGroupValidityFlag = false;
    let isVerificationPassed = this.dataFormGroup.valid;
    if (this.dataFormGroup.valid) {
      // 签核人设置是否符合
      if (this.executor?.source === 'personnel') {
        if (size(this.executor?.personnel) === 0) {
          isVerificationPassed = false;
        }
      } else if (this.executor?.source === 'variable') {
        if (size(this.executor?.variable) === 0) {
          isVerificationPassed = false;
        }
      } else if (this.executor?.source === 'activity') {
        if (size(this.executor?.activity) === 0) {
          isVerificationPassed = false;
        }
      } else if (this.executor?.source === 'formFiled') {
        if (size(this.executor?.formFiled) === 0) {
          isVerificationPassed = false;
        }
      } else if (this.executor?.source === 'department') {
        if (size(this.executor?.department) === 0) {
          isVerificationPassed = false;
        }
      }
    }
    return isVerificationPassed;
  }

  // 获取当前最新的数据
  getCurrentData() {
    // 混入isVerificationPassed 是否校验通过
    let isVerificationPassed = this.getFormValidate();

    let currentData = this.dataFormGroup.value;
    // 回填 executor
    currentData.executor = this.executor;
    // 回填 _buttons的处理
    currentData._buttons = [];
    // 回填 fieldConfig
    currentData.fieldConfig = this.fieldConfig;
    if (this.saveButton) {
      currentData._buttons.push({ id: 'workflow-act-save' });
    }
    if (this.submitButton) {
      currentData._buttons.push({ id: 'workflow-act-submit' });
    }
    if (this.isCustomizeCard) {
      currentData._buttons = [];
    }
    // 多语言回填
    const returnData = {
      data: Object.assign(this.data, currentData, { taskCode: currentData.id }),
      valid: isVerificationPassed,
      componentType: 'checkpointSetting',
    };
    this.changeData.emit(returnData);
    return returnData;
  }
  // 签核按钮的改变
  handelSwitchChange() {
    this.getCurrentData();
  }
}
