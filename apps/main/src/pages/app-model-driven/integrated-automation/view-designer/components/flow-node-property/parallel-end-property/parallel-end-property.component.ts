import { Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { ViewStoreService } from '../../../service/store.service';
import { debounce } from 'lodash';
import { NameValidators } from '../../../config/utils';
import { ParallelEndPropertyService } from './parallel-end-property.service';
import { ViewGraphService } from '../../../service/graph.service';

@Component({
  selector: 'app-parallel-end-property',
  templateUrl: './parallel-end-property.component.html',
  styleUrls: ['./parallel-end-property.component.less'],
  providers: [ParallelEndPropertyService],
})
export class ParallelEndPropertyComponent implements OnInit {
  // 输入的渲染数据
  @Input() data;
  @Input() nodeId;
  // 通知父组件关闭属性面板
  @Output() close: EventEmitter<any> = new EventEmitter();
  // 表单数据改变通过校验后向父组件通知更新数据
  @Output() changeData: EventEmitter<any> = new EventEmitter();
  dataFormGroup: FormGroup;
  errorTips: Record<string, Record<string, string>> = {
    default: {
      required: this.translate.instant('dj-必填'),
      pattern: this.translate.instant('dj-超长'),
    },
  };
  formGroupValidityFlag = false;
  nameLang: any;
  scriptModalVisible: boolean = false;
  script: string = '';
  unikeyList: any[] = [];

  get state() {
    return this.viewStoreService.state;
  }

  constructor(
    public translate: TranslateService,
    private fb: FormBuilder,
    public viewStoreService: ViewStoreService,
    private viewGraphService: ViewGraphService,
    private parallelEndPropertyService: ParallelEndPropertyService,
  ) {
    this.initForm();
  }

  ngOnInit() {
    this.handleInit();
    this.getCurrentData();
    this.getPageDesignFields();
    this.dataFormGroup.get('type')?.valueChanges.subscribe((value) => {
      if (!this.formGroupValidityFlag) {
        this.handleTypeChange({
          type: value,
          parallelGatewayEndStrategy: this.dataFormGroup.get('parallelGatewayEndStrategy')?.value,
        });
        this.dataFormGroup.patchValue({
          uniKeys: [],
          script: '',
        });
      }
    });
    this.dataFormGroup.get('parallelGatewayEndStrategy')?.valueChanges.subscribe((value) => {
      if (!this.formGroupValidityFlag) {
        this.handleTypeChange({
          type: this.dataFormGroup.get('type')?.value,
          parallelGatewayEndStrategy: value,
        });
        this.dataFormGroup.patchValue({
          uniKeys: [],
          script: '',
          type: value === 'custom' ? 'mapping' : '',
        });
      }
    });

    this.dataFormGroup.valueChanges.subscribe(() => {
      if (!this.formGroupValidityFlag) {
        this.getCurrentData();
      }
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('nodeId')) {
      if (!changes.nodeId.firstChange) {
        this.handleChangeValue();
      }
    }
  }

  handleInit(): void {
    const type = this.data?.config?.parallelGatewayEndConfig?.type || '';
    const parallelGatewayEndStrategy =
      this.data?.config?.parallelGatewayEndConfig?.parallelGatewayEndStrategy || 'default';
    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      type,
      parallelGatewayEndStrategy,
      uniKeys: this.data?.config?.parallelGatewayEndConfig?.uniKeys || [],
      script: this.data?.config?.parallelGatewayEndConfig?.script || '',
    });
    this.nameLang = this.data.lang;
    this.handleTypeChange({
      type,
      parallelGatewayEndStrategy,
    });
  }

  initForm(): void {
    this.dataFormGroup = this.fb.group({
      id: [null, [Validators.required]],
      name: [null, [NameValidators.trim, Validators.required, Validators.pattern('^.{0,255}$')]],
      parallelGatewayEndStrategy: ['default'],
      type: [''],
      uniKeys: [[]],
      script: '',
    });
  }

  //
  async getPageDesignFields() {
    const bindForm = this.getFirstManualNodeBindForm(); // 获取第一个人工节点绑定表单
    if (!bindForm || !bindForm?.modelCode || !bindForm?.formCode || !bindForm?.serviceCode) {
      this.unikeyList = [];
      return;
    }

    try {
      const res: any = await this.parallelEndPropertyService.getPageDesignModelFields(bindForm).toPromise();
      if (res.code === 0) {
        this.unikeyList = res.data[0].modelFileInfos || [];
      }
    } catch (error) {}
  }

  handleClosePanel(): void {
    this.close.emit();
  }

  handlePatchApp(key: any, data: any): void {
    if (data.needLang) {
      this.nameLang = {
        ...(this.nameLang || {}),
        [key]: data.lang,
      };
    }
    this.dataFormGroup.patchValue({
      [key]: data?.value,
    });
  }

  handleTypeChange({
    type,
    parallelGatewayEndStrategy,
  }: { type?: string; parallelGatewayEndStrategy?: string } = {}): void {
    if (parallelGatewayEndStrategy === 'custom' && type === 'mapping') {
      this.dataFormGroup.get('uniKeys').setValidators([Validators.required]);
    } else {
      this.dataFormGroup.get('uniKeys').setValidators(null);
    }
  }

  handleChangeValue(): void {
    this.formGroupValidityFlag = true;
    const { type, parallelGatewayEndStrategy, uniKeys, script } = this.data?.config?.parallelGatewayEndConfig || {};

    this.dataFormGroup.patchValue({
      id: this.data?.id,
      name: this.data?.name,
      type: type,
      parallelGatewayEndStrategy: parallelGatewayEndStrategy || 'default',
      uniKeys: uniKeys || [],
      script: script || '',
    });
    this.handleTypeChange({
      type,
      parallelGatewayEndStrategy,
    });
    this.formGroupValidityFlag = false;
    this.nameLang = this.data.lang;
    this.getCurrentData();
  }

  getCurrentData = debounce(
    () => {
      this.formGroupValidityFlag = true;
      for (const i of Object.keys(this.dataFormGroup?.controls)) {
        this.dataFormGroup.controls[i].markAsDirty();
        this.dataFormGroup.controls[i].updateValueAndValidity();
      }
      this.formGroupValidityFlag = false;
      const currentData = this.dataFormGroup.getRawValue();
      const isVerificationPassed = this.viewStoreService.transformVerificationPassed(this.dataFormGroup.valid);
      const returnData = {
        data: Object.assign(this.data, {
          name: currentData.name,
          lang: this.nameLang,
          config: {
            parallelGatewayEndConfig: {
              type: currentData.type,
              parallelGatewayEndStrategy: currentData.parallelGatewayEndStrategy || 'default',
              uniKeys: currentData.uniKeys || [],
              script: currentData.script || '',
            },
          },
        }),
        isVerificationPassed,
      };
      console.log(returnData, 'returnData');
      this.changeData.emit(returnData);
      return returnData;
    },
    50,
    { leading: false, trailing: true },
  );

  handleCloseSript(type, data): void {
    this.scriptModalVisible = false;

    if (type === 'confirm') {
      this.dataFormGroup.get('script').setValue(data);
    }
  }

  showScriptModal(): void {
    this.scriptModalVisible = true;
    this.script = this.dataFormGroup.get('script').value || '';
  }

  /**
   * 获取开始节点之前的第一个人工节点的bindForm
   */
  getFirstManualNodeBindForm() {
    const { currentSelectedNodeId } = this.state;
    if (!currentSelectedNodeId) return;

    // 获取开始节点的id
    const branchStartId = this.state.propertiesObj[currentSelectedNodeId]?.parallelStartId;
    if (!branchStartId) return;

    // 递归找人工节点
    const findFirstManualNode = (id) => {
      const { graph } = this.viewGraphService;
      const cell = graph.getCellById(id);
      const incomingEdges = this.viewGraphService.graph.model.getConnectedEdges(cell, { incoming: true });
      if (!incomingEdges?.length) return; // 没有入边，说明是开始节点

      const incomingEdge = incomingEdges[0];
      const sourceCell = incomingEdge.getSourceCell();
      if (!sourceCell) return;

      const sourceNodeData = sourceCell.getData();
      if (['ManualApprove', 'ManualExecution'].includes(sourceNodeData?.nodeType)) {
        return sourceCell;
      }

      return findFirstManualNode(sourceCell?.id);
    };

    const manualNode = findFirstManualNode(branchStartId);
    if (!manualNode) return;

    const { bindForm, id: nodeId } = this.state.propertiesObj[manualNode.id];

    return { ...bindForm, nodeId };
  }
}
