import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Cell, Edge, Graph, Node, Point } from '@antv/x6';
import { Stencil } from '@antv/x6-plugin-stencil';
import { SystemConfigService } from 'common/service/system-config.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { flowsGraph } from '../config/graph';
import {
  commonNodeSize,
  conditionNodeSize,
  conditionStartNodeSize,
  parallelInstanceNodeSize,
  parallelMarkNodeSize,
  handlePosition,
  nodeSpanSize,
  createUUID,
} from '../config/utils';
import {
  NodeType,
  NodeTypeName,
  ICreateNodeParams,
  TriggerNodeType,
  ITransmitData,
  ManualNodeMode,
  ModelNodes,
  VariableType,
} from '../config/typings';
import { ViewStoreService } from './store.service';
import { ViewToolsService } from './tools.service';
import { Subject, Observable } from 'rxjs';
import { initIsNodePaas } from '../config/init-graph';
import { kNodeVariableList } from 'pages/app-model-driven/utils/operation';
import { cloneDeep, delay, isEmpty, max } from 'lodash';

@Injectable()
export class ViewGraphService implements OnDestroy {
  private kMaxDeepSize: number = 5;

  graph: Graph;
  stencil: Stencil;
  graphWrapper: Element;
  isInitLoading: boolean = false;

  private _isSaveLoading: boolean = false;
  get isSaveLoading(): boolean {
    return this._isSaveLoading;
  }
  set isSaveLoading(isLoading: boolean) {
    this._isSaveLoading = isLoading;
    this._saveAndPublishLoadingChange$.next(this._isSaveLoading || this._isPublishLoading);
  }

  private _isPublishLoading: boolean = false;
  get isPublishLoading(): boolean {
    return this._isPublishLoading;
  }
  set isPublishLoading(isLoading: boolean) {
    this._isPublishLoading = isLoading;
    this._saveAndPublishLoadingChange$.next(this._isSaveLoading || this._isPublishLoading);
  }

  // 订阅-保存和发布时的loading状态变化
  private _saveAndPublishLoadingChange$ = new Subject<boolean>();
  get saveAndPublishLoadingChange$(): Observable<boolean> {
    return this._saveAndPublishLoadingChange$.asObservable();
  }

  currentNodeName: string;
  currentTransmitData: ITransmitData | null = null; // 当前通信数据
  reverse = { top: 'bottom', bottom: 'top', left: 'right', right: 'left' }; // // 反方向，用于自动连线
  showPopoverMenu: any = {
    // 浮层菜单是否显示
    isShow: false,
    popoverStyle: {},
  };
  isShowPropertiesPanel$ = new Subject();
  isShowAlert$ = new Subject();

  adpVersion$ = new Subject();
  currentSelectedVersion: any = {};
  doNotCheckStateChange$ = new Subject();

  constructor(
    protected configService: SystemConfigService,
    private translateService: TranslateService,
    private athMessageService: NzMessageService,
    private athModalService: AdModalService,
    private viewStoreService: ViewStoreService,
    private viewToolsService: ViewToolsService,
  ) {}

  // 销毁
  ngOnDestroy(): void {}

  dispose() {
    this.graph?.dispose?.();
    this.graph = undefined;
    (window as any).graph = undefined;
  }

  // 初始化画布
  initGraph(container) {
    Graph.registerRouter(
      'straightLine',
      (vertices, args, view) => {
        const BOUNCES = args.bounces || 1;
        const points = vertices.map((p) => Point.create(p));
        const sourceCorner = view.sourceBBox.getCenter();
        const targetCorner = view.targetBBox.getCenter();

        const stepx = (targetCorner.x - sourceCorner.x) / BOUNCES;
        const stepy = (targetCorner.y - sourceCorner.y) / BOUNCES;
        points.push(Point.create(sourceCorner.x, sourceCorner.y + stepy - 20));
        points.push(Point.create(sourceCorner.x + stepx, sourceCorner.y + stepy - 20));

        return points;
      },
      true,
    );
    this.graph = new Graph({
      container,
      ...flowsGraph,
      autoResize: false,
      virtual: false,
      async: false,
      panning: {
        enabled: true,
        eventTypes: ['mouseWheel', 'leftMouseDown'],
      },
    });
    this.viewToolsService.toolsInit(this.graph);
    this.viewToolsService.toolsGraphPlugin(this.graph);
    (window as any).graph = this.graph;
  }

  // 打开浮层节点菜单
  handleOpenNodeMenu(e) {
    const { clientX, clientY } = handlePosition(e);
    this.showPopoverMenu = {
      isShow: true,
      popoverStyle: {
        left: `${clientX}px`,
        top: `${clientY}px`,
        position: 'fixed',
      },
    };
  }

  // 关闭浮层节点菜单
  handleCloseNodeMenu() {
    this.showPopoverMenu = {
      isShow: false,
      popoverStyle: {},
    };
  }

  /**
   * 从浮层中拖拽节点添加到画布（已弃用）
   * 方案调整：添加节点使用点击方式，拖放方式已弃用。若启用，逻辑可参考 handleClickAddNode 方法
   */
  handleDropedNode(node) {}

  // 从浮层中点击节点添加到画布
  handleClickAddNode(data) {
    const { nodeType, isOpen } = data || {};

    // 敬请期待节点
    if (!isOpen) {
      this.athMessageService.warning(this.translateService.instant('dj-暂未开放，敬请期待'));
      return;
    }

    // 开始、结束节点不能重复添加
    const validNodeType = [
      NodeType.START_EVENT,
      NodeType.START_MANUAL,
      NodeType.START_TIMER,
      NodeType.START_FLOW,
      NodeType.END_FLOW,
    ];
    const allNodes = this.graph.getNodes();
    const validNodeIndex = allNodes.findIndex(
      (item) => validNodeType.includes(item.data.nodeType) && validNodeType.includes(nodeType),
    );
    if (validNodeIndex > -1) {
      this.athMessageService.warning(
        this.translateService.instant(NodeTypeName[nodeType]) +
          this.translateService.instant('dj-节点已存在，不能重复添加'),
      );
      return;
    }

    // 条件分支的边上不能继续添加条件分支
    // 待确认,并行分支和条件分支是否可以混合添加 -> 结论：不能混合添加
    // if (this.currentTransmitData && this.currentTransmitData?.type === 'edge') {
    //   const { sourceNode } = this.viewToolsService.toolsEdgeAndNodeByEdgeId(
    //     this.graph,
    //     this.currentTransmitData?.edgeId,
    //   );
    //   if (this.isAddNodeIsParallel(nodeType)) {
    //     if (this.isSourceNodeIsConditionOrParallel(sourceNode) || sourceNode?.data?.parentConditionStartIds?.length) {
    //       this.athMessageService.warning(this.translateService.instant('dj-分支中，不能继续添加并行分支节点'));
    //       return;
    //     }
    //   }
    // }

    // 渲染节点前，平移节点
    let sourcePosition, sourceSize, sourceNode, parentConditionStartIds;
    if (this.currentTransmitData?.edgeId) {
      const { edgeId } = this.currentTransmitData || {};
      sourceNode = this.viewToolsService.toolsEdgeAndNodeByEdgeId(this.graph, edgeId)?.sourceNode;
      sourcePosition = this.viewToolsService.toolsNodePositionByEdgeId(this.graph, edgeId).sourcePosition;
      sourceSize = this.viewToolsService.toolsNodeSizeByEdgeId(this.graph, edgeId).sourceSize;

      const dependenceBranchNode = this.getParentNode(sourceNode);

      parentConditionStartIds = (dependenceBranchNode?.data.parentConditionStartIds || [])
        .concat(this.getBranchId(dependenceBranchNode, 'start'))
        .filter((item) => item);
      if (
        [NodeType.CONDITION_BRANCH, NodeType.PARALLEL_BRANCH].includes(nodeType) &&
        parentConditionStartIds.length >= this.kMaxDeepSize
      ) {
        this.athMessageService.error(this.translateService.instant('dj-条件分支最多添加n层', { n: this.kMaxDeepSize }));
        return;
      }
    }

    let node = null;
    let branchStartId = undefined;
    // 判断不同节点类型定制化添加节点
    switch (nodeType) {
      case NodeType.START_FLOW:
        // 开始节点分为人工触发、事件触发、定时触发
        const { triggerType } = this.viewStoreService.state.originalFlowData || {};
        const startNodeType = TriggerNodeType?.[triggerType] || NodeType.START_FLOW;
        node = this.addNewNode({
          ...data,
          nodeName: this.translateService.instant(NodeTypeName[startNodeType]),
          nodeType: startNodeType,
          isSelected: false,
          isVerificationPassed: initIsNodePaas(startNodeType),
        });
        this.setPropertiesWithNodeInConditionOrParallel(node);
        break;
      case NodeType.CONDITION_BRANCH:
        // 添加条件节点
        const addConditionNode = this.addNewNode({
          ...data,
          nodeName: this.translateService.instant(NodeTypeName[NodeType.CONDITION_BRANCH_START]),
          nodeType: NodeType.CONDITION_BRANCH_START,
          isSelected: false,
          isVerificationPassed: initIsNodePaas(NodeType.CONDITION_BRANCH_START),
        });

        // 给并行开始节点添加分支按钮工具
        this.addBranchButtonTool(addConditionNode);

        const position = addConditionNode.position();

        // 分支结束节点
        const finishNode = this.addNewNode({
          ...data,
          nodeName: this.translateService.instant(NodeTypeName[NodeType.CONDITION_BRANCH_END]),
          nodeType: NodeType.CONDITION_BRANCH_END,
          isSelected: false,
          isVerificationPassed: initIsNodePaas(NodeType.CONDITION_BRANCH_END),
        });
        finishNode.position(
          position.x,
          position.y + nodeSpanSize * 3 + addConditionNode.getSize().height + conditionNodeSize.height,
        );
        branchStartId = addConditionNode.id;
        // 节点存储节点id，继续新增条件分支节点需要
        addConditionNode.setData({
          ...(addConditionNode?.data || {}),
          conditionStartId: addConditionNode.id, // 开始分支节点id
          conditionEndId: finishNode.id, // 结束分支节点id
          parentConditionStartIds,
        });
        finishNode.setData({
          ...(finishNode?.data || {}),
          conditionStartId: addConditionNode.id, // 开始分支节点id
          conditionEndId: finishNode.id, // 结束分支节点id
          parentConditionStartIds,
        });
        // 条件分支右侧面板数据中增加 conditionStartId 标记
        this.viewStoreService.setState((state) => {
          const { propertiesObj } = state;
          const conditionStartNodeObj = cloneDeep(propertiesObj[addConditionNode.id]);
          const conditionEndNodeObj = cloneDeep(propertiesObj[finishNode.id]);
          propertiesObj[addConditionNode.id] = {
            ...conditionStartNodeObj,
            conditionStartId: addConditionNode.id,
          };
          propertiesObj[finishNode.id] = {
            ...conditionEndNodeObj,
            conditionStartId: addConditionNode.id,
          };
        });

        // 渲染条件分支节点
        this.renderConditionBranchNode(addConditionNode, finishNode);

        // 基于节点/边新增
        this.handleTransmitData(data, addConditionNode, finishNode);
        break;
      case NodeType.PARALLEL_BRANCH:
        // 添加条件节点
        const parallelStartNode = this.addNewNode({
          ...data,
          nodeName: this.translateService.instant(NodeTypeName[NodeType.PARALLEL_BRANCH_START]),
          nodeType: NodeType.PARALLEL_BRANCH_START,
          isSelected: false,
          isVerificationPassed: initIsNodePaas(NodeType.PARALLEL_BRANCH_START),
        });
        // 给并行开始节点添加分支按钮工具
        this.addBranchButtonTool(parallelStartNode);

        const startNodePosition = parallelStartNode.position();

        // 分支结束节点
        const parallelEndNode = this.addNewNode({
          ...data,
          nodeName: this.translateService.instant(NodeTypeName[NodeType.PARALLEL_BRANCH_END]),
          nodeType: NodeType.PARALLEL_BRANCH_END,
          isSelected: false,
          isVerificationPassed: initIsNodePaas(NodeType.PARALLEL_BRANCH_END),
        });
        parallelEndNode.position(
          startNodePosition.x,
          startNodePosition.y + nodeSpanSize * 3 + parallelStartNode.getSize().height + parallelInstanceNodeSize.height,
        );
        branchStartId = parallelStartNode.id;
        // 节点存储节点id，继续新增条件分支节点需要
        parallelStartNode.setData({
          ...(parallelStartNode?.data || {}),
          parallelStartId: parallelStartNode.id, // 开始分支节点id
          parallelEndId: parallelEndNode.id, // 结束分支节点id
          parentConditionStartIds,
        });
        parallelEndNode.setData({
          ...(parallelEndNode?.data || {}),
          parallelStartId: parallelStartNode.id, // 开始分支节点id
          parallelEndId: parallelEndNode.id, // 结束分支节点id
          parentConditionStartIds,
        });
        // 并行分支右侧面板数据中增加 parallelStartId 标记
        this.viewStoreService.setState((state) => {
          const { propertiesObj } = state;
          const parallelStartNodeObj = cloneDeep(propertiesObj[parallelStartNode.id]);
          const parallelEndNodeObj = cloneDeep(propertiesObj[parallelEndNode.id]);
          propertiesObj[parallelStartNode.id] = {
            ...parallelStartNodeObj,
            parallelStartId: parallelStartNode.id,
          };
          propertiesObj[parallelEndNode.id] = {
            ...parallelEndNodeObj,
            parallelStartId: parallelStartNode.id,
          };
        });

        // 渲染并行分支节点
        this.renderParallelBranchNode(parallelStartNode, parallelEndNode);

        // 基于节点/边新增
        this.handleTransmitData(data, parallelStartNode, parallelEndNode);
        break;
      default:
        const newNode = this.addNewNode({
          ...data,
          nodeName: this.translateService.instant(NodeTypeName[nodeType]),
          nodeType: nodeType,
          isSelected: false,
          isVerificationPassed: initIsNodePaas(nodeType),
        });

        node = newNode;
        // 基于节点/边新增
        this.handleTransmitData(data, newNode, newNode);
        this.setPropertiesWithNodeInConditionOrParallel(node);
    }
    // 移动画布
    if (sourcePosition) {
      delay(() => {
        this.translationNode(nodeType, { sourceNode, sourcePosition, sourceSize, branchStartId }, allNodes);
      }, 10);
    }
    // 新增节点后默认选中
    delay(() => this.graph.trigger('node:click', { node }), 100);
  }

  // 添加分支的按钮Tool
  addBranchButtonTool(node: Node) {
    node.addTools({
      name: 'add-branch-button',
      args: {
        x: 0,
        y: 0,
        offset: { x: node.getSize().width / 2 - 52, y: 80 },
        cursor: 'pointer',
        onClick: ({ view }) => {
          const { nodeType, conditionEndId, parallelEndId } = node?.data || {};
          switch (nodeType) {
            case NodeType.CONDITION_BRANCH_START:
              // 新增条件分支节点
              const cell = this.graph.getCellById(conditionEndId);
              this.addConditionBranchNode(node, cell, 'common');
              // 处理条件分支节点位置
              this.positionConditionBranch(node, 'fromStart');
              break;
            case NodeType.PARALLEL_BRANCH_START:
              // 新增并行分支节点
              const cellIns = this.graph.getCellById(parallelEndId);
              this.addParallelBranchNode(node, cellIns, 'common');
              this.positionParallelBranch(node, 'fromStart');
              break;
          }
        },
      },
    });
  }

  // 获取从属分支的 Node
  getParentNode(sourceNode) {
    const preNode = this.graph.getPredecessors(sourceNode, {
      breadthFirst: true, // 按广度优先遍历
    });
    // 包含点击点在内的前序节点
    const sourceAndPreNode = preNode.concat(sourceNode);

    const branchStart = [NodeType.CONDITION_BRANCH_START, NodeType.PARALLEL_BRANCH_START];
    const branchEnd = [NodeType.CONDITION_BRANCH_END, NodeType.PARALLEL_BRANCH_END];

    const startNode = sourceAndPreNode.filter((item) => branchStart.includes(item.data.nodeType));
    const endNode = sourceAndPreNode.filter((item) => branchEnd.includes(item.data.nodeType));

    let parentStartNode = null;

    for (let i of startNode) {
      // 条件开始的节点，不在条件结束里面
      if (endNode?.findIndex((item) => this.getBranchId(item, 'start') === this.getBranchId(i, 'start')) === -1) {
        parentStartNode = i;
        break;
      }
    }
    return parentStartNode;
  }

  // 给在条件分支或并行分支中的节点，面板属性增加 conditionStartId/parallelStartId 标记
  setPropertiesWithNodeInConditionOrParallel(node: any): void {
    const sourceNode = this.graph.getPredecessors(node)[0];
    if (this.isNodeIsInCondition(sourceNode as any)) {
      this.viewStoreService.setState((state) => {
        const { propertiesObj } = state;
        const nodeObj = cloneDeep(propertiesObj[node.id]);
        propertiesObj[node.id] = {
          ...nodeObj,
          conditionStartId: (sourceNode as any)?.data?.conditionStartId,
        };
      });
    } else if (this.isNodeIsInParallel(sourceNode as any)) {
      this.viewStoreService.setState((state) => {
        const { propertiesObj } = state;
        const nodeObj = cloneDeep(propertiesObj[node.id]);
        propertiesObj[node.id] = {
          ...nodeObj,
          parallelStartId: (sourceNode as any)?.data?.parallelStartId,
        };
      });
    }
  }

  // 判断节点是否在并行分支中
  isNodeIsInCondition(sourceNode: Node<Node.Properties>): boolean {
    const { nodeType, conditionStartId, conditionEndId } = sourceNode?.data || {};
    const isInCondition =
      conditionStartId &&
      conditionEndId &&
      nodeType !== NodeType.CONDITION_BRANCH_START &&
      nodeType !== NodeType.CONDITION_BRANCH_END;
    return isInCondition;
  }

  // 判断节点是否在并行分支中
  isNodeIsInParallel(sourceNode: Node<Node.Properties>): boolean {
    const { nodeType, parallelStartId, parallelEndId } = sourceNode?.data || {};
    const isInParallel =
      parallelStartId &&
      parallelEndId &&
      nodeType !== NodeType.PARALLEL_BRANCH_START &&
      nodeType !== NodeType.PARALLEL_BRANCH_END;
    return isInParallel;
  }

  isSourceNodeIsConditionOrParallel(sourceNode: Node<Node.Properties>): boolean {
    const { conditionStartId, conditionEndId, nodeType, parallelStartId, parallelEndId } = sourceNode?.data || {};
    const isCondition = conditionStartId && conditionEndId && nodeType !== NodeType.CONDITION_BRANCH_END;
    const isParallel = parallelStartId && parallelEndId && nodeType !== NodeType.PARALLEL_BRANCH_END;
    return isCondition || isParallel;
  }

  isAddNodeIsConditionOrParallel(nodeType): boolean {
    return [NodeType.PARALLEL_BRANCH].includes(nodeType);
  }

  isAddNodeIsParallel(nodeType): boolean {
    return [NodeType.PARALLEL_BRANCH].includes(nodeType);
  }

  renderParallelBranchNode(parallelStart, parallelEnd) {
    setTimeout(() => {
      this.addParallelBranchNode(parallelStart, parallelEnd, 'default');
      this.addParallelBranchNode(parallelStart, parallelEnd, 'common');
      this.positionParallelBranch(parallelStart);
    }, 0);
  }

  positionParallelBranch(node, type?: string) {
    const outGoingEdges = this.graph.getOutgoingEdges(node);
    const outEdgesByNode: Node<Node.Properties>[] = [];
    const outEdges =
      outGoingEdges?.filter((item) => {
        const parallelNode = item.getTargetNode();
        if (this.getBranchId(parallelNode, 'start') === this.getBranchId(node, 'start')) {
          outEdgesByNode.push(parallelNode);
          return true;
        }
      }) || [];
    if (type === 'fromStart') {
      const edges = this.graph.getEdges();
      const insertNodeWidthAndSpace = nodeSpanSize + conditionNodeSize.width;
      const { x } = node.position();
      //#region 移动父级和祖级
      node.data?.parentConditionStartIds?.forEach((id) => {
        edges
          .filter((edge) => edge.getSourceCellId() === id)
          .forEach((edge) => {
            const targetNode = edge.getTargetNode();
            if (!this.graph.isSuccessor(targetNode, node)) {
              if (targetNode.position().x < x) {
                this.handleMoveConditionGroup(
                  targetNode,
                  this.getBranchId(targetNode, 'end'),
                  edges,
                  (-1 * insertNodeWidthAndSpace) / 2,
                );
              } else {
                this.handleMoveConditionGroup(
                  targetNode,
                  this.getBranchId(targetNode, 'end'),
                  edges,
                  insertNodeWidthAndSpace / 2,
                );
              }
            }
          });
      });
      //#endregion

      //#region 处理同级别的节点

      const commonExistsNode = outEdgesByNode.filter((node) => {
        const { x, y } = node.position();
        return x !== 0 && y !== 0 && node.data.parallelType !== 'default';
      });
      const commonPosNode = commonExistsNode.sort((pre, next) => (pre.position().x > next.position().x ? 1 : -1));

      const commonExistsId = commonExistsNode.map((node) => node.id);

      const maxX = this.handleGetChildrenLeftRight(
        commonPosNode[commonPosNode.length - 1],
        this.getBranchId(commonPosNode[commonPosNode.length - 1], 'end'),
        edges,
      ).right;

      outEdgesByNode.forEach((node) => {
        if (node.data.parallelType === 'default') {
          this.handleMoveConditionGroup(node, this.getBranchId(node, 'end'), edges, insertNodeWidthAndSpace / 2);
        } else if (commonExistsId.includes(node.id)) {
          this.handleMoveConditionGroup(node, this.getBranchId(node, 'end'), edges, (-1 * insertNodeWidthAndSpace) / 2);
        } else {
          node.position(maxX - insertNodeWidthAndSpace / 2 + nodeSpanSize, commonExistsNode[0].position().y);
        }
      });
      //#endregion
    } else {
      const { x, y } = node.position();

      // 计算条件节点所需横轴跨度
      const spanWidth = parallelInstanceNodeSize.width * outEdges.length + (outEdges.length - 1) * nodeSpanSize;

      // X开始位置
      const initPositionX = x + node.getSize().width / 2 - spanWidth / 2;
      let paintIndex = 0;
      outEdges?.forEach((item) => {
        const targetNode = item.getTargetNode();
        const isDefaultParallel = targetNode?.data?.parallelType === 'default';
        const index = isDefaultParallel ? outEdges.length - 1 : paintIndex;
        const positionX = (nodeSpanSize + targetNode.getSize().width) * index + initPositionX;
        const positionY = y + node.getSize().height + nodeSpanSize * 2;
        const { nodes: currentBranchNodes } = this.getNodesUtilCondition(
          targetNode,
          (node) => node?.data?.nodeType === NodeType.PARALLEL_BRANCH_END,
        );
        if (currentBranchNodes?.length > 0) {
          currentBranchNodes.forEach((node) => {
            node.position(positionX, node.position().y);
          });
        }
        targetNode.position(positionX, positionY); // x轴移动，y轴移动
        if (!isDefaultParallel) paintIndex++;
      });
    }
  }

  addParallelBranchNode(parallelStart, parallelEnd, parallelType) {
    const typeToNameMap = {
      common: this.translateService.instant('dj-并行分支'),
      default: this.translateService.instant('dj-其他条件'),
    };
    const typeToConditionMap = {
      common: this.translateService.instant('dj-请设置条件'),
      default: this.translateService.instant('dj-其他条件进入此业务流'),
    };
    const parallelNodeParams = {
      parallelType,
      nodeName: typeToNameMap[parallelType],
      nodeType: NodeType.PARALLEL_BRANCH,
      isSelected: false,
      isVerificationPassed: parallelType === 'default',
    };
    const parallelNode = this.viewToolsService.toolsCreateNode(this.graph, parallelNodeParams);
    parallelNode.setAttrByPath('condition/text', typeToConditionMap[parallelType]);
    parallelNode.setData({
      ...(parallelStart?.data ?? {}),
      ...(parallelNode?.data ?? {}),
      parallelStartId: parallelStart.id,
      parallelEndId: parallelEnd.id,
      parentConditionStartIds: parallelStart.data?.parentConditionStartIds,
    });
    this.viewToolsService.toolsAddNode(this.graph, parallelNode);
    const edgeParams = {
      sourceDirection: 'bottom',
      targetDirection: this.reverse['bottom'],
    };
    const edgesConfig = [
      {
        sourceNode: parallelStart,
        targetNode: parallelNode,
      },
      {
        sourceNode: parallelNode,
        targetNode: parallelEnd,
      },
    ];
    edgesConfig.forEach((config) => {
      this.viewToolsService.toolsAddEdgeBetweenTwoNodes(this.graph, {
        ...edgeParams,
        ...config,
      });
    });
    if (parallelType === 'common') {
      this.graph.trigger('node:click', { node: parallelNode });
    }
  }

  // 处理基于节点|边新增
  handleTransmitData(data, firstTargetNode, secondSourceNode) {
    if (!this.currentTransmitData) return;

    const { type, nodeId, edgeId, sourceDirection, targetDirection } = this.currentTransmitData || {};
    if (type === 'node') {
      const sourceNode: any = this.graph.getCellById(nodeId);
      // 添加节点间连线
      const firstAddEdgeParams = {
        sourceNode: sourceNode,
        targetNode: firstTargetNode,
        sourceDirection: sourceDirection,
        targetDirection: targetDirection,
      };
      this.viewToolsService.toolsAddEdgeBetweenTwoNodes(this.graph, firstAddEdgeParams);
      // 刷新节点工具
      this.addNodeAddTool(sourceNode);
    }

    if (type === 'edge') {
      const { edge, sourceNode, targetNode } = this.viewToolsService.toolsEdgeAndNodeByEdgeId(this.graph, edgeId);
      // 添加节点间连线
      const firstAddEdgeParams = {
        sourceNode: sourceNode,
        targetNode: firstTargetNode,
        sourceDirection: sourceDirection,
        targetDirection: this.reverse[sourceDirection],
      };
      this.viewToolsService.toolsAddEdgeBetweenTwoNodes(this.graph, firstAddEdgeParams);
      const secondAddEdgeParams = {
        sourceNode: secondSourceNode,
        targetNode: targetNode,
        sourceDirection: sourceDirection,
        targetDirection: targetDirection,
      };
      this.viewToolsService.toolsAddEdgeBetweenTwoNodes(this.graph, secondAddEdgeParams);
      // 移除边的工具和边
      this.viewToolsService.toolsClearEdgeTools(edge);
      this.graph.removeEdge(edge);

      // 特殊逻辑：基于条件分支的边新增节点后加条件标识
      // 除去条件分支，并行分支， 条件分支和并行分支用自己的conditionStartId
      if (![NodeType.PARALLEL_BRANCH, NodeType.CONDITION_BRANCH].includes(data?.nodeType)) {
        const dependenceBranchNode = this.getParentNode(sourceNode);
        if (isEmpty(dependenceBranchNode)) {
          return;
        }

        if (dependenceBranchNode?.data?.nodeType === NodeType.CONDITION_BRANCH_START) {
          const { conditionStartId, conditionEndId, conditionType, conditionSort } = dependenceBranchNode?.data || {};
          firstTargetNode.setData({
            ...(data || {}),
            conditionStartId,
            conditionEndId,
            conditionSort,
            conditionType,
          });
        } else if (dependenceBranchNode?.data?.nodeType === NodeType.PARALLEL_BRANCH_START) {
          const { parallelStartId, parallelEndId, parallelType } = dependenceBranchNode?.data || {};
          firstTargetNode.setData({
            ...(data || {}),
            parallelStartId,
            parallelEndId,
            parallelType,
          });
        }
      }
    }

    // 清空
    // this.currentTransmitData = null;
  }

  // 创建新节点
  addNewNode(data: ICreateNodeParams) {
    // 创建节点
    const node = this.viewToolsService.toolsCreateNode(this.graph, data);

    // 从节点上添加节点
    if (this.currentTransmitData) {
      const { type, nodeId, edgeId, sourceDirection } = this.currentTransmitData || {};

      // 新节点坐标：来源节点添加方向
      let sourceNode: any = null;
      if (type === 'node') {
        sourceNode = this.graph.getCellById(nodeId);
        // 移除来源节点周边 + 图标工具
        this.removeNodeAddTool(sourceNode);
      }
      if (type === 'edge') {
        const toolsData = this.viewToolsService.toolsEdgeAndNodeByEdgeId(this.graph, edgeId);
        sourceNode = toolsData.sourceNode;
      }

      const sourcePosition = sourceNode.position();
      const sourceNodeSize = sourceNode.getSize();
      const nodeSize = node.getSize();

      // 节点宽度不一致，保证在同一个垂直中心线上
      const spanWidth = (sourceNodeSize.width - nodeSize.width) / 2;
      if (sourceDirection === 'top') {
        node.position(sourcePosition.x + spanWidth, sourcePosition.y - nodeSpanSize - nodeSize.height);
      } else if (sourceDirection === 'bottom') {
        node.position(sourcePosition.x + spanWidth, sourcePosition.y + nodeSpanSize + sourceNodeSize.height);
      } else if (sourceDirection === 'left') {
        node.position(sourcePosition.x - nodeSpanSize - nodeSize.width, sourcePosition.y);
      } else if (sourceDirection === 'right') {
        node.position(sourcePosition.x + nodeSpanSize + sourceNodeSize.width, sourcePosition.y);
      }
      // 关闭节点菜单
      this.handleCloseNodeMenu();
    } else {
      // 新节点坐标：画布居中位置
      const position = this.viewToolsService.toolsGraphCenter(this.graph, this.graphWrapper);
      node.position(position.x, position.y);
    }
    this.viewToolsService.toolsAddNode(this.graph, node);
    return node;
  }

  /**
   * 添加条件节点的时候 需要移动对应垂直方向
   * @param sourceNode
   * @returns
   */
  handleNeedTranslateVertical(sourceNode, nodeType) {
    let needTranslateVertical = false;

    const specialHandleTypes = [NodeType.PARALLEL_BRANCH, NodeType.CONDITION_BRANCH];

    if (!specialHandleTypes.includes(nodeType)) {
      return false;
    }

    const preNode = this.graph.getPredecessors(sourceNode);

    // 遍历目标节点和目标节点的前序
    for (let node of [sourceNode, ...preNode]) {
      if ([NodeType.CONDITION_BRANCH_END, NodeType.PARALLEL_BRANCH_END].includes(node?.data?.nodeType)) {
        needTranslateVertical = false;
        break;
      }
      if ([NodeType.CONDITION_BRANCH_START, NodeType.PARALLEL_BRANCH_START].includes(node?.data?.nodeType)) {
        needTranslateVertical = true;
        break;
      }
    }
    return needTranslateVertical;
  }

  // 是否是同一个分支组
  getSameBranchNode(currentNode, sourceNode, needTranslateVertical) {
    if (!needTranslateVertical) {
      return false;
    }
    const dependenceBranchNode = this.getParentNode(sourceNode);

    if (dependenceBranchNode) {
      if (
        this.getBranchId(currentNode, 'start') === this.getBranchId(sourceNode, 'start') ||
        currentNode?.data.parentConditionStartIds?.includes(this.getBranchId(sourceNode, 'start')) ||
        sourceNode?.data.parentConditionStartIds?.includes(this.getBranchId(currentNode, 'start'))
      ) {
        return true;
      }
    }
    return false;
  }

  // 边新增节点，平移对应方向所有节点
  translationNode(nodeType, { sourceNode, sourcePosition, sourceSize, branchStartId }, allNodes) {
    if (!this.currentTransmitData) return;

    const { type, sourceDirection } = this.currentTransmitData || {};
    if (type === 'edge') {
      const specialHandleTypes = [NodeType.PARALLEL_BRANCH, NodeType.CONDITION_BRANCH];

      let needTranslateVertical = this.handleNeedTranslateVertical(sourceNode, nodeType);

      const nodeHeight = specialHandleTypes.includes(nodeType)
        ? conditionNodeSize.height + conditionStartNodeSize.height * 2 + nodeSpanSize * 3
        : commonNodeSize.height;

      const nodeWidth = specialHandleTypes.includes(nodeType)
        ? conditionNodeSize.width * 2 + nodeSpanSize
        : commonNodeSize.width;

      const conditionStartMap = new Map<string, number>();
      allNodes?.forEach((node) => {
        const { x, y } = node.position();
        const { width, height } = node.getSize();
        const isConditionOrParallel = !!this.getBranchId(node, 'start');
        const sameGroup = this.getSameBranchNode(node, sourceNode, needTranslateVertical);
        if (needTranslateVertical) {
          let translateX = x;
          let translateY = y;
          if (sourceDirection === 'bottom') {
            if (!isConditionOrParallel || sameGroup) {
              if (x + width / 2 < sourcePosition.x + sourceSize.width / 2) {
                translateX = x - nodeWidth / 2;
              } else if (x + width / 2 > sourcePosition.x + sourceSize.width / 2) {
                translateX = x + nodeWidth / 2;
              }
              if (y > sourcePosition.y) {
                translateY = y + nodeSpanSize + nodeHeight;
              }
            } else {
              if (conditionStartMap.has(this.getBranchId(node, 'start'))) {
                translateX = x + conditionStartMap.get(this.getBranchId(node, 'start'));
              } else {
                if (x + width / 2 < sourcePosition.x + sourceSize.width / 2) {
                  translateX = x - nodeWidth / 2;
                } else if (x + width / 2 > sourcePosition.x + sourceSize.width / 2) {
                  translateX = x + nodeWidth / 2;
                }
                if (node.data.nodeType === 'ConditionBranchStart') {
                  conditionStartMap.set(this.getBranchId(node, 'start'), translateX - x);
                }
              }
              if (y > sourcePosition.y) {
                translateY = y + nodeSpanSize + nodeHeight;
              }
            }
          } else if (sourceDirection === 'top') {
            if (x + width / 2 < sourcePosition.x + sourceSize.width / 2) {
              translateX = x - nodeWidth / 2;
            } else if (x + width / 2 > sourcePosition.x + sourceSize.width / 2) {
              translateX = x + nodeWidth / 2;
            }
            if (y < sourcePosition.y) {
              translateY = y - nodeSpanSize - nodeHeight;
            }
          } else if (sourceDirection === 'left') {
            if (y + height / 2 < sourcePosition.y + sourceSize.height / 2) {
              translateY = y - nodeHeight / 2;
            } else if (y + height / 2 > sourcePosition.y + sourceSize.height / 2) {
              translateY = y + nodeHeight / 2;
            }
            if (x < sourcePosition.x) {
              translateX = x - nodeSpanSize - nodeWidth;
            }
          } else if (sourceDirection === 'right') {
            if (y + height / 2 < sourcePosition.y + sourceSize.height / 2) {
              translateY = y - nodeHeight / 2;
            } else if (y + height / 2 > sourcePosition.y + sourceSize.height / 2) {
              translateY = y + nodeHeight / 2;
            }
            if (x > sourcePosition.x) {
              translateX = x + nodeSpanSize + nodeWidth;
            }
          }
          node.position(translateX, translateY);
        } else {
          const isSuccessor = !this.checkIsInBranch(node, branchStartId)
            ? true
            : this.graph.isSuccessor(sourceNode, node);
          if (sourceDirection === 'top' && y < sourcePosition.y) {
            node.position(x, y - nodeSpanSize - nodeHeight);
          } else if (sourceDirection === 'bottom' && y > sourcePosition.y) {
            if (isSuccessor) {
              node.position(x, y + nodeSpanSize + nodeHeight);
            }
          } else if (sourceDirection === 'left' && x < sourcePosition.x) {
            node.position(x - nodeSpanSize - nodeWidth, y);
          } else if (sourceDirection === 'right' && x > sourcePosition.x) {
            node.position(x + nodeSpanSize + nodeWidth, y);
          }
        }
      });
    }
    this.currentTransmitData = null;
  }

  // 条件分支节点渲染
  renderConditionBranchNode(startBranchNode: any, finishBranchNode: any) {
    setTimeout(() => {
      // 新增其他条件分支节点
      this.addConditionBranchNode(startBranchNode, finishBranchNode, 'other');
      // 新增第一个条件节点
      this.addConditionBranchNode(startBranchNode, finishBranchNode, 'common');
      // 处理条件分支节点位置
      this.positionConditionBranch(startBranchNode);
    }, 0);
  }

  // 删除条件分支节点
  removeConditionBranchNode(node) {
    const {
      conditionStartId: currentConditionStartId,
      conditionEndId: currentConditionEndId,
      conditionSort: currentConditionSort,
    } = node?.data || {};

    const allNodes = this.graph.getNodes();
    const conditionBranchTypeNodes =
      allNodes?.filter((_node) => {
        const { conditionStartId, conditionEndId, nodeType } = _node?.data || {};
        return (
          currentConditionStartId === conditionStartId &&
          currentConditionEndId === conditionEndId &&
          nodeType === NodeType.CONDITION_BRANCH
        );
      }) || [];

    const edges = this.graph.getEdges();
    if (conditionBranchTypeNodes?.length === 2) {
      // 当前条件只有两个条件节点，则删除整个条件分支
      const searchStartIds = [currentConditionStartId];
      const searchEndIds = [currentConditionEndId];
      const allConditionBranchNodes =
        allNodes?.filter((_node) => {
          const {
            conditionStartId,
            conditionEndId,
            parentConditionStartIds = [],
            nodeType,
            nodeId,
          } = _node?.data || {};
          if (searchStartIds.includes(conditionStartId) || searchEndIds.includes(conditionEndId)) {
            // 匹配到了条件分支节点
            return true;
          }
          if (parentConditionStartIds.includes(currentConditionStartId)) {
            // 匹配到了条件分支节点
            if (nodeType === NodeType.CONDITION_BRANCH_START) {
              searchStartIds.push(nodeId);
            }
            return true;
          }
          return false;
        }) || [];
      //连线
      const sourceConditionStartNode = allConditionBranchNodes.filter(
        (node) => node.data?.nodeType === NodeType.CONDITION_BRANCH_START,
      )?.[0];
      const sourceConditionEndNode = allConditionBranchNodes.filter(
        (node) => node.data?.nodeType === NodeType.CONDITION_BRANCH_END,
      )?.[0];
      const { sourcePort } = this.viewToolsService.toolsOneWayNodePort(this.graph, sourceConditionStartNode);
      const { targetPort } = this.viewToolsService.toolsOneWayNodePort(this.graph, sourceConditionEndNode);
      if (sourcePort && targetPort) {
        this.viewToolsService.toolsAddEdge(this.graph, sourcePort, targetPort);
      }
      //同一分支下的节点，包含嵌套的条件分支节点
      const sameBranchNextNode =
        allNodes?.filter((_node) => {
          const { conditionStartId, parentConditionStartIds } = _node?.data || {};
          return (
            this.getBranchId(sourceConditionStartNode, 'start') === conditionStartId ||
            parentConditionStartIds?.includes(this.getBranchId(sourceConditionStartNode, 'start'))
          );
        }) || [];
      this.viewToolsService.toolsRemoveMoreNodes(this.graph, [...allConditionBranchNodes, ...sameBranchNextNode]);
    } else {
      const leftRight = this.handleGetChildrenLeftRight(node, currentConditionEndId, edges);
      const nodePosition = node.position();
      const nodeSize = node.size();
      const toDeleteNodes = this.handleGetChild(node, currentConditionEndId, edges);
      this.viewToolsService.toolsRemoveMoreNodes(this.graph, toDeleteNodes);
      // 重新排序优先级
      const newConditionBranchTypeNodes = conditionBranchTypeNodes?.filter((_node) => _node.id !== node.id);
      newConditionBranchTypeNodes?.forEach((_node) => {
        const { conditionStartId, conditionEndId, conditionSort } = _node?.data || {};
        if (conditionSort > currentConditionSort) {
          const newConditionSort = conditionSort - 1;
          _node.setAttrByPath('level/text', this.translateService.instant(`dj-优先级`) + newConditionSort);
          _node.setData({ ...(_node?.data || {}), conditionSort: newConditionSort });
          // 此条件上的串联的其他节点同步水平方向移动
          allNodes.forEach((item_node) => {
            const {
              nodeType: _nodeType,
              conditionStartId: _conditionStartId,
              conditionEndId: _conditionEndId,
              conditionSort: _conditionSort,
            } = item_node?.data || {};
            if (
              conditionStartId === _conditionStartId &&
              conditionEndId === _conditionEndId &&
              conditionSort === _conditionSort &&
              _nodeType !== NodeType.CONDITION_BRANCH
            ) {
              item_node.setData({ ...(item_node?.data || {}), conditionSort: newConditionSort });
            }
          });
        }
      });
      // 布局
      const startNode = this.graph.getCellById(currentConditionStartId);
      this.layoutAfterRemove(startNode, nodePosition.x + nodeSize.width / 2, (leftRight.right - leftRight.left) / 2);
    }
  }

  /**
   * 条件分支节点、边位置计算
   * @param node
   * @param type fromStart: 添加单个条件
   * @returns
   */
  positionConditionBranch(node, type?: 'fromStart') {
    if (!node) return;
    const outGoingEdges = this.graph.getOutgoingEdges(node);
    const outEdgesByNode: Node<Node.Properties>[] = [];
    // 同一个条件分支从添加分支指向条件分支节点的边
    const outEdges =
      outGoingEdges?.filter((item) => {
        const conditionNode = item.getTargetNode();
        if (this.getBranchId(conditionNode, 'start') === this.getBranchId(node, 'start')) {
          outEdgesByNode.push(conditionNode);
          return true;
        }
      }) || [];
    if (type === 'fromStart') {
      const edges = this.graph.getEdges();
      const insertNodeWidthAndSpace = nodeSpanSize + conditionNodeSize.width;
      const { x } = node.position();
      //#region 移动父级和祖级
      node.data?.parentConditionStartIds?.forEach((id) => {
        edges
          .filter((edge) => edge.getSourceCellId() === id)
          .forEach((edge) => {
            const targetNode = edge.getTargetNode();
            if (!this.graph.isSuccessor(targetNode, node)) {
              if (targetNode.position().x < x) {
                this.handleMoveConditionGroup(
                  targetNode,
                  this.getBranchId(targetNode, 'end'),
                  edges,
                  (-1 * insertNodeWidthAndSpace) / 2,
                );
              } else {
                this.handleMoveConditionGroup(
                  targetNode,
                  this.getBranchId(targetNode, 'end'),
                  edges,
                  insertNodeWidthAndSpace / 2,
                );
              }
            }
          });
      });
      //#endregion

      //#region 处理同级别的节点
      const nodeSort = outEdgesByNode.sort((pre, next) => (pre.data.conditionSort > next.data.conditionSort ? 1 : -1));
      const commonExistsNode = nodeSort.slice(0, nodeSort.length - 2);

      const commonPosNode = commonExistsNode.sort((pre, next) => (pre.position().x > next.position().x ? 1 : -1));

      const commonExistsId = commonExistsNode.map((node) => node.id);
      const maxX = this.handleGetChildrenLeftRight(
        commonPosNode[commonPosNode.length - 1],
        this.getBranchId(commonPosNode[commonPosNode.length - 1], 'end'),
        edges,
      ).right;

      outEdgesByNode.forEach((node) => {
        if (node.data.conditionType === 'other') {
          this.handleMoveConditionGroup(node, this.getBranchId(node, 'end'), edges, insertNodeWidthAndSpace / 2);
        } else if (commonExistsId.includes(node.id)) {
          this.handleMoveConditionGroup(node, this.getBranchId(node, 'end'), edges, (-1 * insertNodeWidthAndSpace) / 2);
        } else {
          node.position(maxX - insertNodeWidthAndSpace / 2 + nodeSpanSize, commonExistsNode[0].position().y);
        }
      });
      //#endregion
    } else {
      const { x, y } = node.position();
      // 计算条件分支节点所需横轴跨度
      const widthFirst = outEdgesByNode?.find((item) => item.data.conditionSort === 1)?.position()?.x || 0;
      let widthSecond = 0;
      outEdgesByNode.sort((a, b) => a.data.conditionSort - b.data.conditionSort);
      for (let i of outEdgesByNode) {
        if (i.data?.conditionSort !== 1 && i.position()?.x !== 0) {
          widthSecond = i?.position()?.x;
          break;
        }
      }

      const nodeSpanSizeX =
        widthSecond == widthFirst ? nodeSpanSize : Math.abs(widthSecond - widthFirst) - conditionNodeSize.width;
      const nodeSpanSizeY = nodeSpanSize;

      // 计算条件节点所需横轴跨度
      const spanWidth = conditionNodeSize.width * outEdges.length + (outEdges.length - 1) * nodeSpanSizeX;
      // X开始位置
      const initPositionX = x + node.getSize().width / 2 - spanWidth / 2;
      outEdges?.forEach((item) => {
        const targetNode = item.getTargetNode();

        // 节点顺序定位节点位置坐标
        const { conditionSort } = targetNode?.data || {};
        const positionX = (nodeSpanSizeX + targetNode.getSize().width) * (conditionSort - 1) + initPositionX;
        const positionY = y + node.getSize().height + nodeSpanSizeY * 2;
        targetNode.position(positionX, positionY); // x轴移动，y轴移动
      });
    }
  }

  private layoutAfterRemove(sourceNode: Cell<Cell.Properties>, middleX: number, distance: number): void {
    const edges = this.graph.getEdges();
    edges
      .filter((edge) => edge.getSourceCellId() === sourceNode.id)
      .forEach((edge) => {
        const targetNode = edge.getTargetNode();
        const { x } = targetNode.position();
        if (x < middleX) {
          this.handleMoveConditionGroup(targetNode, this.getBranchId(sourceNode, 'end'), edges, distance);
        } else {
          this.handleMoveConditionGroup(targetNode, this.getBranchId(sourceNode, 'end'), edges, -1 * distance);
        }
      });
  }

  // 获取条件分组里的节点最右边的值
  private handleGetChildrenLeftRight(
    sourceNode: Node<Node.Properties>,
    stopId: string,
    edges: Edge<Edge.Properties>[],
  ): {
    left: number;
    right: number;
  } {
    let minLeft = sourceNode.position().x;
    let maxRight = sourceNode.position().x + sourceNode.size().width;
    const nextEdges = edges.filter((edge) => edge.getSourceCellId() === sourceNode.id);
    nextEdges.forEach((edge) => {
      const targetCell = edge.getTargetNode();
      if (
        !(
          [NodeType.CONDITION_BRANCH_END, NodeType.PARALLEL_BRANCH_END].includes(targetCell.data.nodeType) &&
          stopId === targetCell.id
        )
      ) {
        const res = this.handleGetChildrenLeftRight(targetCell, stopId, edges);
        minLeft = Math.min(minLeft, res.left);
        maxRight = Math.max(maxRight, res.right);
      }
    });
    return {
      left: minLeft,
      right: maxRight,
    };
  }

  // 移动一个条件组
  private handleMoveConditionGroup(
    sourceNode: Node<Node.Properties>,
    stopId: string,
    edges: Edge<Edge.Properties>[],
    translateX: number,
    moved: Record<string, boolean> = {},
  ): void {
    if (!moved[sourceNode.id]) {
      const sourcePos = sourceNode.position();
      sourceNode.position(sourcePos.x + translateX, sourcePos.y);
      moved[sourceNode.id] = true;
    }
    const nextEdges = edges.filter((edge) => edge.getSourceCellId() === sourceNode.id);
    nextEdges.forEach((edge) => {
      const targetCell = edge.getTargetNode();
      if (
        !(
          [NodeType.CONDITION_BRANCH_END, NodeType.PARALLEL_BRANCH_END].includes(targetCell.data.nodeType) &&
          stopId === targetCell.id
        )
      ) {
        this.handleMoveConditionGroup(targetCell, stopId, edges, translateX, moved);
      }
    });
  }

  private handleGetChild(
    sourceNode: Node<Node.Properties>,
    stopId: string,
    edges: Edge<Edge.Properties>[],
    list: Node<Node.Properties>[] = [],
  ): Node<Node.Properties>[] {
    list.push(sourceNode);
    const nextEdges = edges.filter((edge) => edge.getSourceCellId() === sourceNode.id);
    nextEdges.forEach((edge) => {
      const targetCell = edge.getTargetNode();
      if (
        !(
          [NodeType.CONDITION_BRANCH_END, NodeType.PARALLEL_BRANCH_END].includes(targetCell.data.nodeType) &&
          stopId === targetCell.id
        )
      ) {
        this.handleGetChild(targetCell, stopId, edges, list);
      }
    });
    return list;
  }

  private getBranchId(node: Cell<Cell.Properties>, type: 'start' | 'end'): string {
    if (!node) return undefined;
    if (
      [NodeType.PARALLEL_BRANCH, NodeType.PARALLEL_BRANCH_START, NodeType.PARALLEL_BRANCH_END].includes(
        node.data.nodeType,
      )
    ) {
      return type === 'start' ? node.data.parallelStartId : node.data.parallelEndId;
    }
    if (
      [NodeType.CONDITION_BRANCH, NodeType.CONDITION_BRANCH_START, NodeType.CONDITION_BRANCH_END].includes(
        node.data.nodeType,
      )
    ) {
      return type === 'start' ? node.data.conditionStartId : node.data.conditionEndId;
    }
    return undefined;
  }

  private checkIsInBranch(node: Cell<Cell.Properties>, branchStartId?: string): boolean {
    if (!node) return false;
    if (branchStartId) {
      if (
        [
          NodeType.CONDITION_BRANCH,
          NodeType.CONDITION_BRANCH_START,
          NodeType.CONDITION_BRANCH_END,
          NodeType.PARALLEL_BRANCH,
          NodeType.PARALLEL_BRANCH_START,
          NodeType.PARALLEL_BRANCH_END,
        ].includes(node.data.nodeType)
      ) {
        return (
          node.data.parentConditionStartIds?.includes(branchStartId) ||
          (node.data.conditionStartId || node.data.parallelStartId) === branchStartId
        );
      }
    }
    return (node.data.conditionStartId || node.data.parallelStartId) === branchStartId;
  }

  /**
   * 新增条件分支节点
   * @param startBranchNode 分支开始节点
   * @param finishBranchNode 分支结束节点
   * @param conditionType 目标节点类型
   */
  addConditionBranchNode(startBranchNode, finishBranchNode, conditionType) {
    // 获取节点相连的边
    const edges = this.graph.getOutgoingEdges(startBranchNode);
    const edgesNum = edges?.length || 0;

    // 默认节点名称
    let conditionNodeName;
    let conditionSort;
    if (conditionType === 'other') {
      conditionNodeName = this.translateService.instant(`dj-其他条件`);
      conditionSort = edgesNum + 1;
    } else {
      // conditionNodeName = this.translateService.instant(`dj-条件序号`, { num: edgesNum });
      conditionNodeName = this.translateService.instant(`dj-条件分支`);
      conditionSort = edgesNum + 1;
      // 先把其他条件的顺序调整到最低优先级
      edges.forEach((item) => {
        const target = item.getTargetNode();
        const { nodeId, conditionStartId, conditionEndId, conditionType } = target?.data || {};
        if (conditionType === 'other') {
          const newConditionSort = edgesNum + 1;
          target.setAttrByPath('level/text', this.translateService.instant(`dj-优先级`) + newConditionSort);
          target.setData({ ...(target?.data || {}), conditionSort: newConditionSort });
          // 同步条件面板优先级配置
          this.viewStoreService.setState((state) => {
            state.propertiesObj[nodeId].sort = newConditionSort;
          });

          // 此条件上的串联的其他节点同步更新优先级
          const allNodes = this.graph.getNodes();
          allNodes.forEach((item_node) => {
            const {
              nodeType: _nodeType,
              conditionStartId: _conditionStartId,
              conditionEndId: _conditionEndId,
              conditionSort: _conditionSort,
            } = item_node?.data || {};
            if (
              conditionStartId === _conditionStartId &&
              conditionEndId === _conditionEndId &&
              edgesNum === _conditionSort &&
              _nodeType !== NodeType.CONDITION_BRANCH
            ) {
              item_node.setData({ ...(item_node?.data || {}), conditionSort: newConditionSort });
            }
          });

          // 新增条件优先级补位
          conditionSort = edgesNum;
        }
      });
    }
    // 新增条件节点
    const createNodeParams = {
      nodeName: conditionNodeName,
      nodeType: NodeType.CONDITION_BRANCH,
      isSelected: false,
      isVerificationPassed: conditionType === 'other',
    };
    const conditionNode = this.viewToolsService.toolsCreateNode(this.graph, createNodeParams);
    conditionNode.setAttrByPath('level/text', this.translateService.instant(`dj-优先级`) + conditionSort);
    conditionNode.setAttrByPath(
      'condition/text',
      this.translateService.instant(conditionType === 'other' ? 'dj-其他条件进入此业务流' : 'dj-请设置条件'),
    );
    conditionNode.setData({
      ...(startBranchNode?.data || {}),
      ...(conditionNode?.data || {}),
      conditionType: conditionType, // 条件分支节点类型 common | other
      conditionSort: conditionSort, // 优先级
      conditionStartId: startBranchNode.id, // 开始分支节点id（所属条件分支标识）
      conditionEndId: finishBranchNode.id, // 结束分支节点id（所属条件分支标识）
      parentConditionStartIds: startBranchNode.data?.parentConditionStartIds,
    });
    this.viewToolsService.toolsAddNode(this.graph, conditionNode);
    // 同步条件面板优先级配置
    this.viewStoreService.setState((state) => {
      state.propertiesObj[conditionNode.id].sort = conditionSort;
    });

    const firstAddEdgeParams = {
      sourceNode: startBranchNode,
      targetNode: conditionNode,
      sourceDirection: 'bottom',
      targetDirection: this.reverse['bottom'],
    };
    this.viewToolsService.toolsAddEdgeBetweenTwoNodes(this.graph, firstAddEdgeParams);
    const secondAddEdgeParams = {
      sourceNode: conditionNode,
      targetNode: finishBranchNode,
      sourceDirection: 'bottom',
      targetDirection: this.reverse['bottom'],
    };
    this.viewToolsService.toolsAddEdgeBetweenTwoNodes(this.graph, secondAddEdgeParams);
    // 新增条件分支时，默认选中条件节点
    if (conditionType === 'common') {
      this.graph.trigger('node:click', { node: conditionNode });
      // setTimeout(() => {
      //   this.graph.trigger('node:click', { node: conditionNode });
      // }, 300);
    }
  }

  // 连接桩是否可见
  setNodePortsVisible(node, visbile) {
    const allPorts = node?.getPorts() || [];
    if (!visbile) {
      allPorts.forEach((port) => {
        node.setPortProp(port.id, 'attrs/circle/style/visibility', 'hidden');
      });
      return;
    }

    const { nodeType } = node?.data || {};

    // 已作为入线的连接桩，不显示
    const groups: any[] = this.viewToolsService.toolsNodeIncomingPortGroups(this.graph, node);
    const ports = allPorts.filter((port) => !groups?.includes(port.group));

    switch (nodeType) {
      case NodeType.START_FLOW:
      case NodeType.START_MANUAL:
      case NodeType.START_EVENT:
      case NodeType.START_TIMER:
        // 开始节点只有一个 bottom 连接桩，如果存在出线，不显示连接桩
        const startOutgoingEdges = this.graph.getOutgoingEdges(node);
        if (!startOutgoingEdges?.length) {
          ports.forEach((port) => {
            if (port.group === 'bottom') {
              node.setPortProp(port.id, 'attrs/circle/style/visibility', 'visible');
            }
          });
        }
        break;
      case NodeType.CONDITION_BRANCH_START:
      case NodeType.CONDITION_BRANCH:
      case NodeType.END_FLOW:
        break;
      default:
        // 如果存在出线，不显示连接桩
        const outgoingEdges = this.graph.getOutgoingEdges(node);
        if (!outgoingEdges?.length) {
          ports.forEach((port) => {
            node.setPortProp(port.id, 'attrs/circle/style/visibility', 'visible');
          });
        }
    }
  }

  // 设置复制、删除按钮显示隐藏
  setNodeTool(node: Node, visible: boolean) {
    if (!visible) {
      ['copy-node-button', 'delete-node-button', 'node-tooltip'].forEach((tool: string) => {
        node.removeTool(tool);
      });
    } else {
      const {
        nodeId,
        nodeType,
        conditionStartId,
        conditionEndId,
        conditionType,
        condition,
        queryFields,
        parallelStartId,
        parallelEndId,
        parallelType,
      } = node?.data || {};
      const copyNodeBtn = {
        name: 'copy-node-button',
        args: {
          x: '100%',
          y: 0,
          offset: { x: 0, y: -20 },
          cursor: 'pointer',
          onClick: ({ cell }) => {
            // 添加节点编号和类型
            const nodeJson = cell.toJSON();
            nodeJson.id = createUUID();
            const cloneNode = this.graph.parseJSON([nodeJson])[0] as Node;
            cloneNode.position(cell.getPosition().x + nodeSpanSize + cell.getSize().width, cell.getPosition().y);
            cloneNode.setData({ ...cloneNode.data, nodeId: cloneNode.id });
            // 设置复制节点的边框默认样式
            this.viewToolsService.toolsNodeStyle(cloneNode, 'none');
            // 设置复制节点的复制、删除按钮不可见
            this.setNodeTool(cloneNode, false);
            // 设置复制节点的链接桩不可见
            this.setNodePortsVisible(cloneNode, false);
            // 添加节点
            this.viewToolsService.toolsAddNode(this.graph, cloneNode, nodeId);
          },
        },
      };
      const deleteNodeBtn = {
        name: 'delete-node-button',
        args: {
          x: '100%',
          y: 0,
          offset: { x: 0, y: -20 },
          cursor: 'pointer',
          onClick: () => {
            this.athModalService.confirm({
              nzTitle: this.translateService.instant('dj-确定删除么？'),
              nzBodyStyle: { height: '160px' },
              nzOkText: this.translateService.instant('dj-确定'),
              nzOnOk: () => {
                this.removeGraphNode(node);
              },
              nzOnCancel: () => {},
            });
          },
        },
      };
      const conditionList = condition?.length ? JSON.parse(condition) : [];
      const conditionQueryFields = queryFields?.length ? JSON.parse(queryFields) : [];

      const nodeTooltip = {
        name: 'node-tooltip',
        args: {
          cell: node,
          position: node.getPosition(),
          conditionList: conditionList || [],
          conditionQueryFields: conditionQueryFields || [],
          translateService: {
            instant: (key) => this.translateService.instant(key),
          },
        },
      };

      switch (nodeType) {
        case NodeType.START_FLOW:
        case NodeType.START_EVENT:
        case NodeType.START_TIMER:
        case NodeType.START_MANUAL:
        case NodeType.END_FLOW:
          break;
        case NodeType.CONDITION_BRANCH_START:
        case NodeType.PARALLEL_BRANCH_START:
          if (node.hasTool('add-branch-button')) {
            node.removeTool('add-branch-button');
            this.addBranchButtonTool(node);
          } else {
            this.addBranchButtonTool(node);
            const nodes = this.graph.getNodes();
            nodes.forEach((n) => {
              if (n.position().y > node.position().y) {
                n.position(n.position().x, n.position().y + 100);
              }
            });
          }
          break;
        case NodeType.CONDITION_BRANCH_END:
        case NodeType.PARALLEL_BRANCH_END:
          break;
        case NodeType.CONDITION_BRANCH:
          if (conditionType === 'common') {
            node.addTools([deleteNodeBtn]);
          }
          if (conditionList?.length) {
            node.addTools([nodeTooltip]);
          }
          break;
        case NodeType.PARALLEL_BRANCH:
          if (parallelType === 'common') {
            node.addTools([deleteNodeBtn]);
          }
          if (conditionList?.length) {
            node.addTools([nodeTooltip]);
          }
          break;
        default:
          // 分支条件中的节点，只显示删除，不显示复制
          if ((conditionStartId && conditionEndId) || (parallelStartId && parallelEndId)) {
            node.addTools([deleteNodeBtn]);
          } else {
            node.addTools([copyNodeBtn, deleteNodeBtn]);
          }
      }
    }
  }

  // 删除节点
  removeGraphNode(node) {
    const { nodeType, conditionStartId, conditionEndId, parallelStartId, parallelEndId } = node?.data || {};

    if (conditionStartId && conditionEndId) {
      if (nodeType === NodeType.CONDITION_BRANCH) {
        this.removeConditionBranchNode(node);
      } else {
        const { sourcePort, targetPort } = this.viewToolsService.toolsOneWayNodePort(this.graph, node);
        this.viewToolsService.toolsAddEdge(this.graph, sourcePort, targetPort);
        this.viewToolsService.toolsRemoveNode(this.graph, node);
      }
    } else if (parallelStartId && parallelEndId) {
      if (nodeType === NodeType.PARALLEL_BRANCH) {
        this.handleRemoveParallelBranch(node);
      } else {
        const { sourcePort, targetPort } = this.viewToolsService.toolsOneWayNodePort(this.graph, node);
        this.viewToolsService.toolsAddEdge(this.graph, sourcePort, targetPort);
        this.viewToolsService.toolsRemoveNode(this.graph, node);
      }
    } else if ([NodeType.MANUAL_APPROVE, NodeType.MANUAL_EXECUTION].includes(nodeType)) {
      this.viewToolsService.toolsRemoveNode(this.graph, node);
    } else {
      this.viewToolsService.toolsRemoveNode(this.graph, node);
    }
  }

  // 处理并行分支的删除操作
  handleRemoveParallelBranch(node) {
    const {
      nodeType,
      parallelStartId: currentParallelStartId,
      parallelEndId: currentParallelEndId,
      conditionSort: currentConditionSort,
    } = node?.data ?? {};
    const allNodes = this.graph.getNodes();
    const parallelBranchTypeNodes =
      allNodes?.filter((_node) => {
        const { parallelStartId, parallelEndId, nodeType } = _node?.data || {};
        return (
          currentParallelStartId === parallelStartId &&
          currentParallelEndId === parallelEndId &&
          nodeType === NodeType.PARALLEL_BRANCH
        );
      }) || [];
    const edges = this.graph.getEdges();
    if (parallelBranchTypeNodes?.length === 2) {
      // 当前条件只有两个条件节点，则删除整个条件分支
      const searchStartIds = [currentParallelStartId];
      const searchEndIds = [currentParallelEndId];
      const allParallelBranchNodes =
        allNodes?.filter((_node) => {
          const { parallelStartId, parallelEndId, parentConditionStartIds = [], nodeType, nodeId } = _node?.data || {};
          if (searchStartIds.includes(parallelStartId) || searchEndIds.includes(parallelEndId)) {
            // 匹配到了条件分支节点
            return true;
          }
          if (parentConditionStartIds.includes(currentParallelStartId)) {
            // 匹配到了条件分支节点
            if (nodeType === NodeType.PARALLEL_BRANCH_START) {
              searchStartIds.push(nodeId);
            }
            return true;
          }
          return false;
        }) || [];
      //连线
      const sourceParallelStartNode = allParallelBranchNodes.filter(
        (node) => node.data?.nodeType === NodeType.PARALLEL_BRANCH_START,
      )?.[0];
      const sourceParallelEndNode = allParallelBranchNodes.filter(
        (node) => node.data?.nodeType === NodeType.PARALLEL_BRANCH_END,
      )?.[0];
      const { sourcePort } = this.viewToolsService.toolsOneWayNodePort(this.graph, sourceParallelStartNode);
      const { targetPort } = this.viewToolsService.toolsOneWayNodePort(this.graph, sourceParallelEndNode);
      if (sourcePort && targetPort) {
        this.viewToolsService.toolsAddEdge(this.graph, sourcePort, targetPort);
      }
      //同一分支下的节点，包含嵌套的条件分支节点
      const sameBranchNextNode =
        allNodes?.filter((_node) => {
          const { parallelStartId, parentConditionStartIds } = _node?.data || {};
          return (
            this.getBranchId(sourceParallelStartNode, 'start') === parallelStartId ||
            parentConditionStartIds?.includes(this.getBranchId(sourceParallelStartNode, 'start'))
          );
        }) || [];
      this.viewToolsService.toolsRemoveMoreNodes(this.graph, [...allParallelBranchNodes, ...sameBranchNextNode]);
    } else {
      const leftRight = this.handleGetChildrenLeftRight(node, currentParallelEndId, edges);
      const nodePosition = node.position();
      const nodeSize = node.size();
      const toDeleteNodes = this.handleGetChild(node, currentParallelEndId, edges);
      this.viewToolsService.toolsRemoveMoreNodes(this.graph, toDeleteNodes);
      // 重新排序优先级
      const newParallelBranchTypeNodes = parallelBranchTypeNodes?.filter((_node) => _node.id !== node.id);
      newParallelBranchTypeNodes?.forEach((_node) => {
        const { parallelStartId, parallelEndId, conditionSort } = _node?.data || {};
        if (conditionSort > currentConditionSort) {
          const newConditionSort = conditionSort - 1;
          _node.setAttrByPath('level/text', this.translateService.instant(`dj-优先级`) + newConditionSort);
          _node.setData({ ...(_node?.data || {}), conditionSort: newConditionSort });
          // 此条件上的串联的其他节点同步水平方向移动
          allNodes.forEach((item_node) => {
            const {
              nodeType: _nodeType,
              parallelStartId: _parallelStartId,
              parallelEndId: _parallelEndId,
              conditionSort: _conditionSort,
            } = item_node?.data || {};
            if (
              parallelStartId === _parallelStartId &&
              parallelEndId === _parallelEndId &&
              conditionSort === _conditionSort &&
              _nodeType !== NodeType.PARALLEL_BRANCH
            ) {
              item_node.setData({ ...(item_node?.data || {}), conditionSort: newConditionSort });
            }
          });
        }
      });
      // 布局
      const startNode = this.graph.getCellById(currentParallelStartId);
      this.layoutAfterRemove(startNode, nodePosition.x + nodeSize.width / 2, (leftRight.right - leftRight.left) / 2);
    }

    // if (nodeType === NodeType.PARALLEL_BRANCH) {
    //   const branchStartNode = this.getPrevOrNextNode(node, true);
    //   const { nodes: branchNodes, targetNode: branchEndNode } = this.getNodesUtilCondition(
    //     node,
    //     (n) => n?.data?.nodeType === NodeType.PARALLEL_BRANCH_END,
    //   );
    //   const outEdges = this.graph.getOutgoingEdges(branchStartNode);

    //   const edgeParams = {
    //     sourceDirection: 'bottom',
    //     targetDirection: this.reverse['bottom'],
    //   };
    //   if (outEdges?.length <= 2) {
    //     const branchParentNode = this.getPrevOrNextNode(branchStartNode, true);
    //     const allParallelNodes = this.getParallelAllNodes(branchStartNode);
    //     const branchPackNode = this.getPrevOrNextNode(allParallelNodes[allParallelNodes.length - 1]);
    //     this.viewToolsService.toolsRemoveMoreNodes(this.graph, allParallelNodes);
    //     this.viewToolsService.toolsAddEdgeBetweenTwoNodes(this.graph, {
    //       sourceNode: branchParentNode,
    //       targetNode: branchPackNode,
    //       ...edgeParams,
    //     });
    //   } else {
    //     this.viewToolsService.toolsRemoveMoreNodes(this.graph, [...branchNodes, node]);
    //     this.positionParallelBranch(branchStartNode);
    //   }
    // } else {
    //   const { sourcePort, targetPort } = this.viewToolsService.toolsOneWayNodePort(this.graph, node);
    //   this.viewToolsService.toolsAddEdge(this.graph, sourcePort, targetPort);
    //   this.viewToolsService.toolsRemoveNode(this.graph, node);
    // }
  }

  /**
   * @function 从起始节点开始获取包括起始节点,终止节点的并行分支的所有节点
   * @param {Node<Node.Properties>} 起始节点
   * @return {Node<Node.Properties>} 并行分支节点集合
   */
  getParallelAllNodes(startNode: Node<Node.Properties>): Node<Node.Properties>[] {
    const outEdges = this.graph.getOutgoingEdges(startNode);
    let res: Node<Node.Properties>[] = [];
    let parallelEndNode: Node<Node.Properties>;
    for (let i = 0; i < outEdges.length; i++) {
      const branchStart = outEdges[i].getTargetNode();
      const { nodes, targetNode } = this.getNodesUtilCondition(
        branchStart,
        (n) => n?.data?.nodeType === NodeType.PARALLEL_BRANCH_END,
      );
      res = res.concat([...nodes, branchStart]);
      if (!parallelEndNode) parallelEndNode = targetNode;
    }
    return [...res, startNode, parallelEndNode];
  }

  /**
   * @function 获取某个节点的前置节点或后置节点
   * @param {Node.Properties} node 基准节点
   * @param {boolean} prevOrNext 前置或后置标记 true = 前置
   * @return {Node<Node.Properties> | null} 查找的节点
   */
  getPrevOrNextNode(node: Node<Node.Properties>, prevOrNext: boolean = false): Node<Node.Properties> | null {
    let res: Node<Node.Properties> = null;
    if (prevOrNext) {
      const inEdges = this.graph.getIncomingEdges(node);
      if (inEdges?.length > 0) {
        res = inEdges[0].getSourceNode();
      }
    } else {
      const outEdges = this.graph.getOutgoingEdges(node);
      if (outEdges?.length > 0) {
        res = outEdges[0].getTargetNode();
      }
    }
    return res;
  }

  /**
   * @function 获取两个节点间的节点,必须单线
   * @param {Node<Node.Properties>} sourceNode 起始节点
   * @param {(node: Node<Node.Properties>) => boolean} conditionFn 终止条件
   * @return {{ nodes: Node<Node.Properties>[], targetNode: Node<Node.Properties> }} 节点直接的所有节点集合
   */
  getNodesUtilCondition(
    sourceNode: Node<Node.Properties>,
    conditionFn: (node: Node<Node.Properties>) => boolean,
  ): { nodes: Node<Node.Properties>[]; targetNode: Node<Node.Properties> } {
    const res: Node<Node.Properties>[] = [];
    let currentNode = sourceNode;
    while (currentNode && !conditionFn(currentNode)) {
      const tempNode = this.getPrevOrNextNode(currentNode);
      if (!conditionFn(tempNode)) {
        res.push(tempNode);
      }
      currentNode = tempNode;
    }
    return {
      nodes: res,
      targetNode: currentNode,
    };
  }

  // 节点周边 + 图标工具
  addNodeAddTool(node) {
    // 移除工具
    this.removeNodeAddTool(node);

    const nodeId = node.id;
    // 已存在出线，不展示图标工具
    const outgoingEdges = this.graph.getOutgoingEdges(node);
    if (outgoingEdges?.length) {
      return;
    }

    // 已连线的方向
    const directions = this.viewToolsService.toolsNodePortGroups(this.graph, node);
    const nodeSize = node.getSize();
    // 上
    const top = {
      name: 'node-add-node-button',
      args: {
        x: nodeSize.width / 2,
        y: -16,
        onClick: ({ e }: any) => {
          this.currentTransmitData = { type: 'node', sourceDirection: 'top', targetDirection: 'bottom', nodeId };
          this.handleOpenNodeMenu(e);
          // 取消节点编辑
          node.removeTool('node-text-editor');
        },
      },
    };
    // 下
    const bottom = {
      name: 'node-add-node-button',
      args: {
        x: nodeSize.width / 2,
        y: nodeSize.height + 16,
        onClick: ({ e }: any) => {
          this.currentTransmitData = { type: 'node', sourceDirection: 'bottom', targetDirection: 'top', nodeId };
          this.handleOpenNodeMenu(e);
          // 取消节点编辑
          node.removeTool('node-text-editor');
        },
      },
    };
    // 左
    const left = {
      name: 'node-add-node-button',
      args: {
        x: -16,
        y: nodeSize.height / 2,
        onClick: ({ e }: any) => {
          this.currentTransmitData = { type: 'node', sourceDirection: 'left', targetDirection: 'right', nodeId };
          this.handleOpenNodeMenu(e);
          // 取消节点编辑
          node.removeTool('node-text-editor');
        },
      },
    };
    // 右
    const right = {
      name: 'node-add-node-button',
      args: {
        x: nodeSize.width + 16,
        y: nodeSize.height / 2,
        onClick: ({ e }: any) => {
          this.currentTransmitData = { type: 'node', sourceDirection: 'right', targetDirection: 'left', nodeId };
          this.handleOpenNodeMenu(e);
          // 取消节点编辑
          node.removeTool('node-text-editor');
        },
      },
    };

    switch (node.data?.nodeType) {
      case NodeType.END_FLOW:
      case NodeType.CONDITION_BRANCH_START:
      case NodeType.CONDITION_BRANCH:
        // 不处理
        break;
      case NodeType.CONDITION_BRANCH_END:
        bottom.args.x = conditionStartNodeSize.width / 2;
        bottom.args.y = conditionStartNodeSize.height + 16;
        if (!directions.includes('bottom')) node.addTools([{ ...bottom }]);
        break;
      case NodeType.PARALLEL_BRANCH_END:
        bottom.args.x = parallelMarkNodeSize.width / 2;
        bottom.args.y = parallelMarkNodeSize.height + 16;
        if (!directions.includes('bottom')) node.addTools([{ ...bottom }]);
        break;
      default:
        if (!directions.includes('top')) node.addTools([{ ...top }]);
        if (!directions.includes('bottom')) node.addTools([{ ...bottom }]);
        if (!directions.includes('left')) node.addTools([{ ...left }]);
        if (!directions.includes('right')) node.addTools([{ ...right }]);
    }
  }

  // 移除节点周边 + 图标工具
  removeNodeAddTool(node) {
    node.removeTool('node-add-node-button');
  }

  // 连线添加新增按钮工具
  edgeAddButtonTool(edge) {
    const { sourceNode } = this.viewToolsService.toolsEdgeAndNodeByEdgeId(this.graph, edge);
    const { nodeType } = sourceNode?.data || {};

    // 添加条件节点出线不展示工具
    if ([NodeType.CONDITION_BRANCH_START, NodeType.PARALLEL_BRANCH_START].includes(nodeType)) {
      return;
    }

    // 显示连线删除按钮
    edge.addTools([
      {
        name: 'edge-add-node-button',
        args: {
          distance: 0.33,
          onClick: ({ cell, e }: any) => {
            const { sourcePortMetaData, targetPortMetaData } = this.viewToolsService.toolsPortMetaDataByEdge(edge);
            this.currentTransmitData = {
              type: 'edge',
              sourceDirection: sourcePortMetaData.group,
              targetDirection: targetPortMetaData.group,
              edgeId: cell.id,
            };
            this.handleOpenNodeMenu(e);
          },
        },
      },
    ]);
  }

  // 连线添加删除按钮工具
  edgeRemoveButtonTool(edge) {
    // 显示连线删除按钮
    edge.addTools([
      {
        name: 'button-remove',
        args: {
          distance: 0.66,
          onClick: ({ view }: any) => {
            this.athModalService.confirm({
              nzTitle: this.translateService.instant('dj-确定删除么？'),
              nzBodyStyle: { height: '160px' },
              nzOkText: this.translateService.instant('dj-确定'),
              nzOnOk: () => {
                // 点击删除边
                this.graph.removeCells([view.cell]);
              },
              nzOnCancel: () => {},
            });
          },
        },
      },
    ]);
  }

  // 获取所有前序人工节点
  getAllPredecessorsNodes(node) {
    const predecessorsNodes = this.graph.getPredecessors(node);
    const { propertiesObj } = this.viewStoreService.state;
    const preManualNodes =
      predecessorsNodes
        ?.filter((i) => {
          const { nodeType } = i?.data || {};
          return [NodeType.MANUAL_APPROVE, NodeType.MANUAL_EXECUTION, NodeType.MANUAL_NOTIFICATION].includes(nodeType);
        })
        ?.map((j) => {
          const { nodeId, nodeType, nodeName } = j?.data || {};

          return {
            nodeId,
            nodeName,
            nodeType,
            id: propertiesObj[nodeId]?.id,
          };
        }) || [];

    return preManualNodes;
  }

  // 获取所有前序人工节点绑定的模型
  getAllPredecessorsNodesMode(node) {
    const preManualNodesMode = [];
    const cacheNodesMode = new Map();
    const predecessorsNodes = this.graph.getPredecessors(node);
    const preManualNodeIds =
      predecessorsNodes
        ?.filter((i) => {
          const { nodeType } = i?.data || {};
          return [NodeType.MANUAL_APPROVE, NodeType.MANUAL_EXECUTION, NodeType.MANUAL_NOTIFICATION].includes(nodeType);
        })
        ?.map((j) => {
          const { nodeId } = j?.data || {};
          return nodeId;
        }) || [];

    const { propertiesObj } = this.viewStoreService.state;
    preManualNodeIds?.forEach((k) => {
      const { modelCode, serviceCode } = propertiesObj?.[k]?.bindForm || {};
      const { id } = propertiesObj?.[k] || {};
      if (!modelCode || !serviceCode) return;
      const unique = modelCode + serviceCode;
      if (cacheNodesMode.has(unique)) return;
      preManualNodesMode.push({
        modelCode,
        serviceCode,
        nodeId: id,
      });
      cacheNodesMode.set(unique, 0);
    });

    // 基于modeCode和serviceCode去重
    // const mapMode = [...new Map(_preManualNodesMode.map((item) => [item.unique, item]))].map((item) => item[1]);
    // const preManualNodesMode: ManualNodeMode[] =
    //   mapMode?.map((item) => ({ modelCode: item.modelCode, serviceCode: item.serviceCode, nodeId: item.nodeId })) || [];

    // 自定义任务卡也作为模型可选
    const cachePageViewCode = new Map();
    preManualNodeIds?.forEach((id) => {
      const { pageView, id: properId } = propertiesObj?.[id] ?? {};
      const { formCode, type } = propertiesObj?.[id]?.bindForm || {};
      if (type === 'pageView') {
        if (cachePageViewCode.has(formCode)) return;
        preManualNodesMode.push({
          code: formCode,
          pageViewCode: formCode,
          name: pageView?.lang?.name?.[this.translateService.instant('dj-LANG')],
          nodeId: properId,
        });
        cachePageViewCode.set(formCode, 0);
      }
    });

    return preManualNodesMode;
  }

  /**
   * 获取数据节点-获取所有前序的 「获取数据」、「获取单条数据」、「新增数据」
   * @param node
   */
  getPreGetDataNodesMode(node) {
    const predecessorsNodes = this.graph.getPredecessors(node);
    const { propertiesObj } = this.viewStoreService.state;
    const preGetDataNodes =
      predecessorsNodes
        ?.filter((i) => {
          const { nodeType } = i?.data || {};
          return [NodeType.DATA_GET, NodeType.DATA_GET_MULTI, NodeType.DATA_ADD].includes(nodeType);
        })
        ?.map((j) => {
          const { nodeId, nodeName } = j?.data || {};
          return { nodeId, nodeName, id: propertiesObj[nodeId]?.id };
        }) || [];
    return preGetDataNodes;
  }

  // 获取条件节点的优先级
  getConditionNodesLevel(node) {
    const { nodeType, conditionStartId, conditionEndId } = node?.data || {};
    const conditionNodesLevel = [];
    if (nodeType === NodeType.CONDITION_BRANCH) {
      const allNodes = this.graph.getNodes();
      allNodes.forEach((_node) => {
        const {
          nodeId: _nodeId,
          nodeName: _nodeName,
          nodeType: _nodeType,
          conditionStartId: _conditionStartId,
          conditionEndId: _conditionEndId,
          conditionSort: _conditionSort,
          conditionType: _conditionType,
        } = _node?.data || {};
        if (
          _nodeType === NodeType.CONDITION_BRANCH &&
          _conditionStartId === conditionStartId &&
          _conditionEndId === conditionEndId &&
          _conditionType === 'common'
        ) {
          conditionNodesLevel.push({
            nodeId: _nodeId,
            nodeName: _nodeName,
            nodeType: _nodeType,
            nodeSort: _conditionSort,
          });
        }
      });
    }

    // 排序
    conditionNodesLevel.sort(function (a, b) {
      return a.nodeSort - b.nodeSort;
    });

    return conditionNodesLevel;
  }

  getParallelBranchNodeExcludeDefault(node) {
    const { nodeType, parallelStartId, parallelEndId } = node?.data || {};
    const parallelBranchNodes = [];
    if (nodeType === NodeType.PARALLEL_BRANCH) {
      const allNodes = this.graph.getNodes();
      allNodes.map((_node) => {
        const {
          nodeId: _nodeId,
          nodeName: _nodeName,
          nodeType: _nodeType,
          parallelStartId: _parallelStartId,
          parallelEndId: _parallelEndId,
          parallelType: _parallelType,
        } = _node?.data || {};
        if (
          _nodeType === NodeType.PARALLEL_BRANCH &&
          _parallelStartId === parallelStartId &&
          _parallelEndId === parallelEndId &&
          _parallelType === 'common'
        ) {
          parallelBranchNodes.push({
            nodeId: _nodeId,
            nodeName: _nodeName,
            nodeType: _nodeType,
          });
        }
      });
    }
    return parallelBranchNodes;
  }

  // 处理保存的数据
  public handleFlowDataParams(transfer: boolean = true) {
    const { originalFlowData, propertiesObj } = this.viewStoreService.state;
    const { processId } = originalFlowData || {};

    const cells = this.graph.toJSON().cells;
    const allNodes = cells?.filter((i) => i.shape !== 'edge') || [];
    const allLinks = cells?.filter((i) => i.shape === 'edge') || [];

    const configNodes =
      Object.entries(propertiesObj)
        ?.filter((k) => k?.[0] !== processId)
        ?.map((l) => {
          const config: any = l?.[1] || {};
          return config;
        }) || [];
    const configLinks =
      allLinks?.map((m) => {
        return {
          id: m.id,
          fromId: propertiesObj?.[m.source.cell]?.id,
          toId: propertiesObj?.[m.target.cell]?.id,
        };
      }) || [];
    const params = {
      ...originalFlowData,
      ...propertiesObj[processId],
      flowGraph: {
        nodes: allNodes,
        links: allLinks,
      },
      processConfig: {
        ...(propertiesObj[processId]?.processConfig || {}),
        nodes: configNodes,
        links: configLinks,
      },
    };
    if (params.mechanismVariables?.length && transfer) {
      // 这里是因为接口的数据结构不一样，所以转一层，保持和自定义变量一致，这样前端的业务代码里改动较少
      params.mechanismVariables = params.mechanismVariables.map((e) => ({
        actionId: e.actionId,
        dataType: e.dataType,
        description: e.description,
        paramCode: e.varName,
        paramName: e.paramName,
      }));
    }
    return params;
  }

  /**
   * 获取所有的可配制成节点变量的数据
   * @param options
   * @returns
   */
  getNodesVariable(options: { type: 'all' | 'pre'; node?: Node<Node.Properties> }) {
    const { type, node } = options;
    const hasNodeType = kNodeVariableList;
    const allNodes = type === 'all' ? this.graph.getNodes() : this.graph.getPredecessors(node);
    const {
      originalFlowData: { code },
      propertiesObj,
    } = this.viewStoreService.state;
    const nodeIds = allNodes?.filter((i) => hasNodeType.includes(i?.data?.nodeType))?.map((j) => j?.data?.nodeId) || [];
    return (propertiesObj?.[code]?.nodeVariables || []).filter((e) => nodeIds.includes(e._nodeId));
  }

  /**
   * 获取所有节点中有model的
   * @param getData
   * @returns
   */
  getAllNodesHasModel(getData: { type: 'all' | 'pre'; repeat?: boolean; node?: Node<Node.Properties> | null }) {
    const { type, node } = getData;
    const hasNodeType = [
      NodeType.MANUAL_APPROVE, // 人工签核
      NodeType.MANUAL_EXECUTION, // 人工关卡
      NodeType.DATA_ADD, // 新增数据
      NodeType.DATA_UPDATE, // 更新数据
      NodeType.DATA_GET, // 获取单条数据
      NodeType.DATA_GET_MULTI, // 获取多条数据
      NodeType.DATA_DELETE, // 删除分支
      NodeType.DATA_GROUPING, // 数据分组
      NodeType.CONDITION_BRANCH, // 条件开始
      NodeType.PARALLEL_BRANCH, // 并行分支
    ];
    const allNodes = type === 'all' ? this.graph.getNodes() : this.graph.getPredecessors(node);
    return this.handleModelByNodes({ hasNodeType, allNodes });
  }

  /**
   * 检测获取流程中只有部分节点有模型
   * type: 检查前序节点还是当前flow所有节点
   * repeat：是否过滤掉重复的模型节点
   */
  getFlowPartHasModelNodes(getData: { type: 'all' | 'pre'; repeat?: boolean; node?: Node<Node.Properties> | null }) {
    const { type, node } = getData;
    const hasNodeType = [
      NodeType.MANUAL_APPROVE, // 人工签核
      NodeType.MANUAL_EXECUTION, // 人工关卡
      NodeType.DATA_ADD, // 新增数据
      NodeType.DATA_UPDATE, // 更新数据
      NodeType.DATA_GET, // 获取单条数据
      NodeType.DATA_GET_MULTI, // 获取多条数据
    ];
    const allNodes = type === 'all' ? this.graph.getNodes() : this.graph.getPredecessors(node);
    return this.handleModelByNodes({ hasNodeType, allNodes });
  }

  handleModelByNodes(data) {
    const { hasNodeType, allNodes } = data;
    const setModelNode = [];
    const { propertiesObj } = this.viewStoreService.state;
    // 获取所有含有model模型节点
    const allModelNodeIds =
      allNodes
        ?.filter((i) => hasNodeType.includes(i?.data?.nodeType))
        ?.map((j) => {
          const { nodeId } = j?.data || {};
          return nodeId;
        }) || [];

    allModelNodeIds?.forEach((k) => {
      const { modelCode, serviceCode } = propertiesObj?.[k]?.bindForm || {};
      if (modelCode && serviceCode) {
        setModelNode.push({
          modelCode,
          serviceCode,
          noteType: propertiesObj?.[k].type,
          noteSubType: propertiesObj?.[k]._nodeType,
          nodeId: propertiesObj?.[k].id,
          unique: modelCode + serviceCode,
        });
      }
    });
    const map = new Map();
    for (const item of setModelNode) {
      if (!map.has(item.unique)) {
        map.set(item.unique, item);
      }
    }
    return Array.from(map.values());
  }

  handleModelNodeVariable(data: { processId?: string; newBindForm?: any }) {
    const { processId, newBindForm = {} } = data;
    const {
      propertiesObj,
      originalFlowData: { allModelList, code },
    } = this.viewStoreService.state;

    const allNodesId =
      this.graph
        .getNodes()
        .filter((e) => ModelNodes.includes(e?.data?.nodeType))
        ?.map((j) => {
          const { nodeId } = j?.data || {};
          return nodeId;
        }) || [];

    const allModelNodes = [];
    const map = new Map();
    [code, ...allNodesId]?.forEach((k) => {
      const { modelCode, serviceCode } = processId === k ? newBindForm : propertiesObj?.[k]?.bindForm || {};
      if (modelCode && serviceCode && !map.has(modelCode + serviceCode)) {
        map.set(modelCode + serviceCode, modelCode + serviceCode);
        allModelNodes.push({
          modelCode,
          serviceCode,
          varName: allModelList?.find((e) => e.code === modelCode)?.name || '',
          dataType: VariableType.MODEL,
          uniqueKey: `${modelCode}&${serviceCode}`,
        });
      }
    });
    return allModelNodes;
  }
}
