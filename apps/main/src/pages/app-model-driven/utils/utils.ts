import { omit } from 'lodash';
import { EType, NodeType, VariableType } from '../integrated-automation/view-designer/config/typings';
import { EPerspective } from './operation';
import { nodeCanAddToOptions } from 'pages/app-model-driven/utils/operation';
import { TranslateService } from '@ngx-translate/core';

/** 检测是不是资源视角 */
export const checkResourcePerspective = (addSoureType?: string): boolean => {
  return addSoureType === EPerspective.RESOURCEPERSPECTIVE;
};

export const dataTypeToVariableType = (dataType: string) => {
  const map: Map<string, VariableType> = new Map([
    ['modelVariable', VariableType.MODEL],
    ['customVariable', VariableType.STRING],
    ['systemVariable', VariableType.PROCESS],
    ['processVariable', VariableType.PROCESS], // 做兼容
    ['nodeVariable', VariableType.NODE],
    ['businessObjectVariable', VariableType.BUSINESS_OBJECT],
    ['mechanismVariable', VariableType.Mechanism],
    ['dtdVariable', VariableType.DTD],
  ]);
  return map.get(dataType);
};

export const variableTypeToDataType = (variableType: VariableType) => {
  const map: Map<VariableType, string> = new Map([
    [VariableType.MODEL, 'modelVariable'],
    [VariableType.STRING, 'customVariable'],
    [VariableType.INTEGER, 'customVariable'],
    [VariableType.DECIMAL, 'customVariable'],
    [VariableType.BOOLEAN, 'customVariable'],
    [VariableType.DATETIME, 'customVariable'],
    [VariableType.PROCESS, 'systemVariable'],
    [VariableType.NODE, 'nodeVariable'],
    [VariableType.BUSINESS_OBJECT, 'businessObjectVariable'],
    [VariableType.Mechanism, 'mechanismVariable'],
    [VariableType.DTD, 'dtdVariable'],
  ]);
  return map.get(variableType);
};

export const dataTypeToValueType = (variableType: VariableType) => {
  const map: Map<VariableType, string> = new Map([
    [VariableType.MODEL, 'modelVariable'],
    [VariableType.STRING, 'customVariable'],
    [VariableType.INTEGER, 'customVariable'],
    [VariableType.DECIMAL, 'customVariable'],
    [VariableType.BOOLEAN, 'customVariable'],
    [VariableType.DATETIME, 'customVariable'],
    [VariableType.PROCESS, 'processVariable'],
    [VariableType.NODE, 'nodeVariable'],
    [VariableType.BUSINESS_OBJECT, 'businessObjectVariable'],
    [VariableType.Mechanism, 'mechanismVariable'],
    [VariableType.DTD, 'dtdVariable'],
  ]);
  return map.get(variableType);
};

/**
 * 检测模型字段有没有某个完整的path
 * @param path
 * @param data
 * @returns
 */
export const checkModelHasValuePath = (path: string, data: any[]): boolean => {
  let exists = false;
  const checkHasPath = (list: any[]) => {
    loop: for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.key === path) {
        exists = true;
        break loop;
      }
      if (item.children?.length) {
        checkHasPath(item.children);
      }
    }
  };
  checkHasPath(data);
  return exists;
};

/**
 * 查出模型字段是制定path的对象
 * @param path
 * @param data
 * @returns
 */
export const matchModelPath = (path: string, data: any[]): any => {
  let ret: any = undefined;
  const find = (list: any[]) => {
    loop: for (let i = 0; i < list.length; i++) {
      const item = list[i];
      if (item.key === path) {
        ret = item;
        break loop;
      }
      if (item.children?.length) {
        find(item.children);
      }
    }
  };
  find(data);
  return ret;
};

/**
 * 拼接完整的path
 * @param list
 * @param parentKey
 * @param index
 * @returns
 */
export const ergodicList = (list: any[], parentKey: string[], index: number = 1): any[] => {
  const prefix = parentKey.slice(1).join('.');
  const returnList = list.map((item) => {
    const key = (prefix ? prefix + '.' : prefix) + item.key;
    return {
      ...item,
      key: key,
      children: item.children?.length ? ergodicList(item.children, key.split('.').slice(index), index + 1) : undefined,
    };
  });
  return returnList;
};

/**
 * 处理数据，用于 解决 【模型驱动解决方案】提示保存校验机制异常 的问题
 * @param data
 */
export const processData = (data: any, t: TranslateService) => {
  const patchList = ['modelCode', 'formCode', 'actionId', 'serviceCode', 'type'];
  patchList.forEach((item) => {
    if (!Reflect.has(data.bindForm, item)) data.bindForm[item] = null;
    if (data.bindForm.type === undefined) data.bindForm['type'] = EType.MODEL;
  });
  if (data.businessObjectVariables === undefined) data.businessObjectVariables = [];
  if (data.mechanismVariables === undefined) data.mechanismVariables = [];
  // 这里是因为接口的数据结构不一样，所以转一层，保持和自定义变量一致，这样前端的业务代码里改动较少
  data.mechanismVariables = data.mechanismVariables.map((e) => ({
    actionId: e.actionId,
    dataType: e.dataType,
    description: e.description,
    varName: e.paramCode,
    paramName: e.paramName,
  }));
  if (data.daemon === undefined) data.daemon = false;
  if (data.dtdVariable === undefined) data.dtdVariable = [];
  if (data.customVariables === undefined) data.customVariables = [];
  if (data.systemVariable === undefined) data.systemVariable = [];
  if (data.dueDateTimeDistance === undefined) data.dueDateTimeDistance = null;
  if (data.adpStatus === undefined) data.adpStatus = null;
  if (data.adpVersion === undefined) data.adpVersion = null;
  if (data.personInCharge === undefined) {
    data.personInCharge = {
      type: 'default',
      personnel: [],
      source: 'personnel',
    };
  }
  if (data.processConfig.enableCustomProjectCard === undefined) data.processConfig.enableCustomProjectCard = false;
  if (data.processConfig.projectDescription === undefined) data.processConfig.projectDescription = '';
  if (!data.merge) data.merge = false;
  if (!data.planEndTime) data.planEndTime = { settingType: 'no' };
  data.flowGraph.links?.forEach((link) => {
    if (link.tools) link.tools.name = null;
    if (link.router?.name !== 'straightLine') link.router.name = 'straightLine';
  });
  data.flowGraph.nodes?.forEach((node) => {
    if (node.tools) node.tools.name = null;
  });
  const nodes = data.processConfig.nodes || [];
  nodes.forEach((node) => {
    if (node.lang?.name) {
      node.name = node.lang.name[t.instant('dj-LANG')];
    }
    // 人工签核
    if (node._nodeType === NodeType.MANUAL_APPROVE) {
      if (node.decisionConfig.levelType === undefined) node.decisionConfig.levelType = null;
      if (node.isHideKey === undefined) node.isHideKey = false;
      if (node.decisionConfig.level === undefined) node.decisionConfig.level = null;
      if (!node.planEndTime) node.planEndTime = { settingType: 'no' };
      if (node.executor?.variable) {
        node.executor.variable = node.executor.variable.map((item) => {
          if (item.variableSuffix === undefined) item.variableSuffix = null;
          return item;
        });
      }
    }
    // 人工关卡
    if (node._nodeType === NodeType.MANUAL_EXECUTION) {
      if (!node.planEndTime) node.planEndTime = { settingType: 'no' };
      if (node.executor?.variable) {
        node.executor.variable = node.executor.variable.map((item) => {
          if (item.variableSuffix === undefined) item.variableSuffix = null;
          return item;
        });
      }
      if (node.isHideKey === undefined) node.isHideKey = false;
      if (node.fieldConfig === undefined) node.fieldConfig = null;
    }
    // 新增节点｜｜ 更新节点
    if (node._nodeType === NodeType.DATA_ADD || node._nodeType === NodeType.DATA_UPDATE) {
      node.fieldInfos = node.fieldInfos.map((item) => {
        if (item.fieldName === undefined) item.fieldName = null;
        if (item.modelCode === undefined) item.modelCode = null;
        if (item.serviceCode === undefined) item.serviceCode = null;
        return item;
      });
    }
    // 获取多条
    if (node._nodeType === NodeType.DATA_GET_MULTI) {
      if (node.prod === undefined) node.prod = null;
      if (node.dataView === undefined) node.dataView = null;
    }
    if (node.type === 'DataDeal' && node._nodeType === 'grouping') {
      //数据分组数据格式, 数据格式变了，为了适配老数据
      if (!Reflect.has(node, 'groupingQueryConditionScript')) {
        node.groupingQueryConditionScript = node.queryConditionScript;
        delete node.queryConditionScript;
      }
      if (!Reflect.has(node, 'groupingConditionList')) {
        if (node.conditionList?.length) node.groupingConditionList = [node.conditionList];
        else node.groupingConditionList = [];
        delete node.conditionList;
      }
      if (!Reflect.has(node, 'groupingQueryConditions')) {
        if (node.queryConditions?.length) node.groupingQueryConditions = [node.queryConditions];
        else node.groupingQueryConditions = [];
        delete node.queryConditions;
      }
      if (node.groupDataSource?.dataSourceSuffix === undefined) node.groupDataSource.dataSourceSuffix = null;
    }
    // 并行分支
    if (node._nodeType === NodeType.PARALLEL_BRANCH) {
      if (node.queryConditionScript === undefined) node.queryConditionScript = null;
    }
    // 手工发起
    if (node._nodeType === NodeType.START_PROJECT) {
      if (node.task === undefined) node.task = {};
    }
    // 结束流程
    if (node._nodeType === NodeType.END_FLOW) {
      if (node._events.length === 1) {
        node._events.push({ type: 0 });
      }
      node._events = node._events?.map((item, i) => {
        if (item.modelCode === undefined) item.modelCode = null;
        if (item.serviceCode === undefined) item.serviceCode = '';
        if (item.actionId === undefined) item.actionId = '';
        if (item.callBackType === undefined) item.callBackType = i === 0 ? 'complete' : 'abort';
        if (item.requestScript === undefined) item.requestScript = null;
        return item;
      });
    }
  });
  const flowGraphNodes = data.flowGraph.nodes || [];
  flowGraphNodes.forEach((node) => {
    const configNode = nodes.find((e) => e._nodeId === node.id);
    if (configNode && node.data.nodeName) {
      node.data.nodeName = configNode.name;
      const nodeTypeMap = {
        [NodeType.CONDITION_BRANCH_START]: 'node-条件分支开始',
        [NodeType.CONDITION_BRANCH_END]: 'node-条件分支结束',
        [NodeType.PARALLEL_BRANCH_START]: 'node-并行分支开始',
        [NodeType.PARALLEL_BRANCH_END]: 'node-并行分支结束',
      };
      if (nodeTypeMap[node.data.nodeType]) {
        node.attrs.label.text = t.instant(nodeTypeMap[node.data.nodeType]);
      } else if (node.attrs.label.text) {
        node.attrs.label.text = configNode.name || node.attrs.label.text;
      }
      if ([NodeType.CONDITION_BRANCH, NodeType.PARALLEL_BRANCH].includes(node.data.nodeType)) {
        if ('other' === node.data.conditionType) {
          node.attrs.condition.text = t.instant('dj-其他条件进入此业务流');
        } else if ('default' === node.data.parallelType) {
          node.attrs.condition.text = t.instant('dj-其他条件进入此业务流');
        }
        if (node.data.conditionSort !== undefined) {
          node.attrs.level.text = t.instant('dj-优先级') + node.data.conditionSort;
        }
      }
    }
  });
  const nodeVariables = nodes
    .filter((e) => nodeCanAddToOptions(e._nodeType))
    .map((e) => ({
      _nodeId: e._nodeId,
      nodeId: e.id,
      varName: e.name,
      name: e.name,
      dataType: 'Node',
    }));
  data.nodeVariables = nodeVariables;
};

/**
 * 比较新旧数据时，剔除不参与比较的数据
 * @param originData
 * @param params
 * @returns
 */
export const omitFrontData = (originData, params) => {
  // 去除最外层
  let _originData = omit(originData, [
    'modelVariables',
    'nodeVariables',
    'allModelList',
    '_isValidPassed',
    'flowGraph',
  ]);
  let _params = omit(params, ['modelVariables', 'nodeVariables', 'allModelList', '_isValidPassed', 'flowGraph']);

  // 去除links
  _originData.processConfig = omit(_originData.processConfig, ['links']);
  _params.processConfig = omit(_params.processConfig, ['links']);

  // 去除每个节点中不比较的字段
  _originData.processConfig.nodes = _originData.processConfig.nodes?.map((item) => {
    item = omit(item, ['processId', '_isValidPassed']);
    if (item._nodeType === NodeType.START_PROJECT) {
      item = omit(item, ['pageView']);
    }
    if (item._nodeType === NodeType.CONDITION_BRANCH) {
      item = omit(item, ['conditionType']);
    }
    if (item._nodeType === NodeType.PARALLEL_BRANCH) {
      item = omit(item, ['parallelType']);
    }
    if (item._nodeType === NodeType.MANUAL_APPROVE || item._nodeType === NodeType.MANUAL_EXECUTION) {
      item = omit(item, ['taskCode', 'transferButton', 'preManualNodes']);
      item.bindForm = omit(item.bindForm, ['type']);
    }
    return item;
  });
  _params.processConfig.nodes = _params.processConfig.nodes?.map((item) => {
    item = omit(item, ['processId', '_isValidPassed', 'preGetNodesMode', 'preManualNodes', 'preModelNodes']);
    if (item._nodeType === NodeType.START_PROJECT) {
      item = omit(item, ['descriptionLang', 'pageView']);
    }
    if (item._nodeType === NodeType.MANUAL_APPROVE || item._nodeType === NodeType.MANUAL_EXECUTION) {
      item = omit(item, ['taskCode', 'transferButton', 'preManualNodes']);
      item.bindForm = omit(item.bindForm, ['type']);
    }
    if (item._nodeType === NodeType.CONDITION_BRANCH) {
      item = omit(item, [
        'conditionType',
        'conditionNodesLevel',
        'preManualNodesMode',
        'modelCodeSel',
        'conditionSortList',
      ]);
    }
    if (item._nodeType === NodeType.PARALLEL_BRANCH) {
      item = omit(item, [
        'parallelType',
        'conditionNodesLevel',
        'preManualNodesMode',
        'modelCodeSel',
        'conditionSortList',
      ]);
    }
    return item;
  });
  return { _originData, _params };
};
