import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { AppService } from 'pages/apps/app.service';

@Injectable()
export class MdWebapiService {
  adesignerUrl: string;
  private _dataEntryList: any[];

  constructor(
    protected configService: SystemConfigService,
    protected http: HttpClient,
    private appService: AppService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
    });
  }

  get dataEntryList() {
    return this._dataEntryList;
  }

  setDataEntryList(type: 'QUERY' | 'DELETE', value?: any[]): void {
    if (type === 'QUERY') {
      this._dataEntryList = value;
    }
    if (type === 'DELETE') {
      this._dataEntryList = null;
    }
  }

  /**
   * 接口获取业务对象内容
   * @param appCode
   * @returns
   */
  getBusinessObjectMenuList(appCode): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/businessDir/queryList`, {
      application: appCode,
    });
  }

  /**
   * 局部刷新资源视角的树
   * @param param
   * @returns
   */
  getResourceObjectMenuList(param: { application: string; type: string }): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/resourceTree/queryListOfSourceRefresh`, param);
  }

  /**
   * 接口获取业务对象内容 资源视角
   * @param appCode
   * @returns
   */
  getBusinessObjectMenuListResource(appCode): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/resourceTree/queryListOfSource`, {
      application: appCode,
    });
  }

  /**
   * 接口获取仪表盘
   * @param appCode
   * @returns
   */
  getInstrumentPaneMenuList(appCode): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/dashBoard/queryList`, {
      application: appCode,
      pattern: 'STATEMENT',
    });
  }

  /**
   * 局部刷新接口获取业务对象内容
   * @param appCode
   * @returns
   */
  refreshBusinessObjectMenu(searchParams?: object): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/businessDir/part/refresh`, {
      ...searchParams,
    });
  }

  /** 删除业务对象 */
  postDeleteBusinessObject(payload: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/businessDir/delete`;
    return this.http.post(url, payload);
  }

  // 检查是否被绑定(视图，模型)
  checkBindRelation(params: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/tbb/checkBindRelation`;
    return this.http.post(url, params);
  }

  /** 删除数据视图 */
  postDeleteDataView(payload: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataView/deleteDataView`;
    return this.http.post(url, payload);
  }

  /** 删除事件 */
  postDeleteEvent(payload: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/event/eventDelete`;
    return this.http.post(url, payload);
  }

  /**
   * 删除服务编排
   * @param code
   * @returns
   */
  deleteSO(code: string): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/template/id/${code}`;
    return this.http.delete(url);
  }

  // 根据appCode获取服务编排
  loadServiceOrchestration(param: { applicationCode: string; businessCode: string }): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/template/all`;
    return this.http.get(url, { params: param });
  }

  /**
   * 删除数据侦测
   * @param code
   * @returns
   */
  deleteTreeDetect(code: string): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/monitorRule/deleteMonitorRule/${code}`;
    return this.http.get(url);
  }

  /** 数据视图设置为默认视图 */
  postUpdateDataViewDefault(payload: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataView/updateDataViewDefaultView`;
    return this.http.post(url, payload);
  }

  // 仪表盘增改
  addBasicReport(param: any, flag: any): Observable<any> {
    const urlFlag = flag === 'edit' ? 'modifyBaseInfo' : 'addByPattern';
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/${urlFlag}`;
    return this.http.post(url, param);
  }

  // 删除仪表盘
  deleteBasicReport(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/deleteByCode${param}`;
    return this.http.delete(url, {});
  }

  // 删除流程
  postRemoveProcess(params: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/process/removeProcess`;
    return this.http.post(url, params);
  }

  // 根据作用code查询作业信息
  getActivityByCode(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/getActivityByCode?code=${param.code}&application=${param.application}`;
    return this.http.get(url);
  }

  // 新增个案
  copyMonitorRule(param): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/template/copyTemplate`;
    return this.http.post(url, param);
  }

  // 复制侦测
  monitorRuleCopy(params: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/task/monitorRule/copy`;
    return this.http.post(url, params);
  }
  /** 查询方案生成作业 */
  generatePageDesignByQueryPlan(params): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/generatePageDesignByQueryPlan`;
    return this.http.post(url, Object.assign({}, params, { application: this.appService?.selectedApp?.code }));
  }

  /** 检测是否已经生成作业 */
  checkIsAutoGenerateBefore(params): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/dataView/checkIsAutoGenerateBefore`;
    return this.http.get(url, {
      params,
    });
  }

  /**
   * 删除资源视角下的模型
   * @param params
   * @returns
   */
  deleteOfResource(params: { code: string; serviceCode: string; application: string }): Promise<any> {
    const url = `${this.adesignerUrl}/athena-designer/modelDriver/modelAssign/deleteOfResource`;
    return this.http
      .get(url, {
        params,
      })
      .toPromise() as Promise<any>;
  }

  /** 删除前检查 */
  checkTableRelated(params: { code: string; serviceCode: string; application: string }): Promise<any> {
    const payload = {
      ...params,
    };
    const url = `${this.adesignerUrl}/athena-designer/pageDesign/checkTableRelated`;
    return this.http.post(url, payload).toPromise();
  }

  // 查询当前解决方案下的基础资料
  loadAppBasic(param: any): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/activityConfigs/getActivityListByPatternAndApplication${param}`;
    return this.http.get(url);
  }
}
