<ng-container *operateAuth="{ prefix: 'create' }">
  <a
    nz-dropdown
    *ngIf="showAddMenu"
    class="more-menu-icon-add"
    [class]="{
      'show-one-icon-add': showAddMenu && !showOperatorMenu,
      'show-two-icon-add': showAddMenu && showOperatorMenu
    }"
    nzTrigger="hover"
    nzOverlayClassName="menu-operator-overlay"
    [nzDropdownMenu]="menuAdd"
  >
    <i adIcon type="plus" theme="outline" style="font-weight: 500"></i>
  </a>
  <nz-dropdown-menu #menuAdd="nzDropdownMenu">
    <app-menu-popover-custom [menuPopover]="menuPopover" [menuItem]="menuItem"></app-menu-popover-custom>
  </nz-dropdown-menu>
</ng-container>

<ng-container *ngIf="showOperatorMenu">
  <a
    class="more-menu-icon"
    nzTrigger="hover"
    nzOverlayClassName="menu-operator-overlay"
    nz-dropdown
    [nzDropdownMenu]="menuDrop"
  >
    <i adIcon type="more" theme="outline" style="font-weight: 500"></i>
  </a>
  <nz-dropdown-menu #menuDrop="nzDropdownMenu">
    <app-menu-popover-custom [menuPopover]="operationPopover" [menuItem]="menuItem"></app-menu-popover-custom>
  </nz-dropdown-menu>
</ng-container>

<ng-container *operateAuth="{ prefix: 'create' }">
  <span
    *ngIf="showFastAddButton"
    class="more-menu-icon-add"
    [class]="{
      'show-one-icon-add': showFastAddButton && !showOperatorMenu,
      'show-two-icon-add': showFastAddButton && showOperatorMenu
    }"
    (click)="onFastAdd($event)"
  >
    <i adIcon type="plus" theme="outline" style="font-weight: 500"></i>
  </span>
</ng-container>

<ng-container *ngIf="showExchangePageDesign">
  <span class="more-menu-icon-exchange" (click)="onExchangePageDesignType($event)">
    <i adIcon aria-hidden="true" [iconfont]="iconfontType" class="exchange-menu-icon"></i>
  </span>
</ng-container>
