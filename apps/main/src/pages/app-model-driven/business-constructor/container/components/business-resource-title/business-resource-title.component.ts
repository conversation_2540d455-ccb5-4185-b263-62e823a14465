import { Component, Input, OnInit } from '@angular/core';
import { AddBusinessResourceSub } from 'pages/app-model-driven/utils/operation';
import { AdUserService } from 'pages/login/service/user.service';

@Component({
  selector: 'app-business-resource-title',
  templateUrl: './business-resource-title.component.html',
  styleUrls: ['./business-resource-title.component.less'],
})
export class BusinessResourceTitleComponent implements OnInit {
  searchModalShow: boolean = false;
  menuItem = {
    type: 'global',
  };
  menuPopover = AddBusinessResourceSub;

  isTenantActive = false; // 租户级 开发平台 是否激活

  constructor(private adUserService: AdUserService) {}

  ngOnInit() {
    this.updateTenant();
  }

  handleSearch() {
    this.searchModalShow = true;
  }

  updateTenant() {
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }
}
