import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { Router } from '@angular/router';
import { LocaleService } from 'common/service/locale.service';
import { AppModelDrivenService } from 'pages/app-model-driven/business-constructor/service/app-model-driven.service';
import { AppService } from 'pages/apps/app.service';
import { debounce } from 'lodash';
import { cloneDeep } from 'common/utils/core.utils';

const menuObjectEum = {
  modelDesign: '业务对象',
  model: '业务对象',
  dataView: '查询方案',
  pageDesign: '界面',
  homeWork: '作业',
  event: '事件',
  businessProcess: '流程',
  detect: '侦测',
  service: '服务',
};

const tabList = [
  {
    id: '',
    name: 'dj-全部',
  },
  {
    id: 'modelDesign',
    name: 'dj-业务对象',
  },
  {
    id: 'dataView',
    name: 'dj-查询方案',
  },
  {
    id: 'pageDesign',
    name: 'dj-界面',
  },
  {
    id: 'homeWork',
    name: 'dj-作业',
  },
  {
    id: 'event',
    name: 'dj-事件',
  },
  {
    id: 'service',
    name: 'dj-服务',
  },
  {
    id: 'detect',
    name: 'dj-侦测',
  },
  {
    id: 'businessProcess',
    name: 'dj-流程',
  },
];
@Component({
  selector: 'app-md-search-modal',
  templateUrl: './md-search-modal.component.html',
  styleUrls: ['./md-search-modal.component.less'],
})
export class MdSearchModalComponent implements OnInit {
  @Input() modalShow;
  @Output() closeModal = new EventEmitter();
  searchValue: string;
  menuList: Array<any> = [];
  businessObjectMenu: Array<any> = [];
  menuObjectEum = menuObjectEum;
  handleSearchDebounce;
  tabList: Array<any> = tabList;
  tabId: string = '';
  constructor(
    private languageService: LocaleService,
    private appModelDrivenService: AppModelDrivenService,
    private router: Router,
    public appService: AppService,
  ) {}

  ngOnInit() {
    this.handleInit();
  }

  /**
   * 初始化
   */
  handleInit() {
    this.handleSearchDebounce = debounce(this.handleSearch, 1000);
    this.businessObjectMenu = cloneDeep(this.appModelDrivenService.businessObjectMenu || []);
  }

  /**
   * 关闭modal
   */
  handleCloseAdd() {
    this.closeModal.emit(false);
  }

  /**
   * 搜索
   * @param value
   */
  handleSearch(value) {
    this.searchValue = value.trim();
    this.handleSearchMenu();
  }

  /**
   * 执行搜索逻辑
   */
  handleSearchMenu() {
    if (!this.searchValue) {
      this.menuList = [];
      return;
    }
    const menuList = [];
    menuList.push(...this.handleSearchObjectMenu());
    this.menuList = menuList;
  }

  /**
   * 搜索业务对象menu
   * @returns
   */
  handleSearchObjectMenu() {
    const currentLang = this.languageService?.currentLanguage || 'zh_CN';
    let menuList = [];
    this.businessObjectMenu.forEach((item) => {
      const businessDirTree = !!this.tabId
        ? item.businessDirTree.filter((subItem) => subItem.type === this.tabId)
        : item.businessDirTree;
      businessDirTree?.forEach((node) => {
        if (node.type === 'model') {
          // 业务对象-模型设计
          const firstMenuName = item.lang?.name?.[currentLang] || item.businessName;
          const secondMenuName = node.businessSubName;
          let menuTitle = [];
          if (firstMenuName.includes(this.searchValue) && !secondMenuName.includes(this.searchValue)) {
            menuTitle = [firstMenuName];
            menuList.push({
              menuTitle,
              type: node.type,
              path: node.path,
            });
            return;
          }
          if (secondMenuName.includes(this.searchValue)) {
            menuTitle = [firstMenuName, secondMenuName];
            menuList.push({
              menuTitle,
              type: node.type,
              path: node.path,
            });
            return;
          }
        } else if (node.type === 'homeWork') {
          // 作业
          for (const subNode of node.businessDirTree) {
            const firstMenuName = item.lang?.name?.[currentLang] || item.businessName;
            const secondMenuName = node.lang?.name?.[currentLang] || node.businessSubName;
            const thirdMenuName = subNode.lang?.name?.[currentLang] || subNode.businessSubName;
            let menuTitle = [];
            if (secondMenuName.includes(this.searchValue) && !thirdMenuName.includes(this.searchValue)) {
              menuTitle = [firstMenuName, secondMenuName];
              menuList.push({
                menuTitle,
                type: node.type,
                path: subNode.businessDirTree[0].path,
              });
              break;
            }
            if (thirdMenuName.includes(this.searchValue)) {
              menuTitle = [firstMenuName, secondMenuName, thirdMenuName];
              menuList.push({
                menuTitle,
                type: node.type,
                path: subNode.businessDirTree[0].path,
              });
            }
          }
        } else {
          // 视图/事件/流程
          for (const subNode of node.businessDirTree) {
            const firstMenuName = item.lang?.name?.[currentLang] || item.businessName;
            const secondMenuName = node.lang?.name?.[currentLang] || node.businessSubName;
            const thirdMenuName = subNode.lang?.name?.[currentLang] || subNode.businessSubName;
            let menuTitle = [];
            if (thirdMenuName.includes(this.searchValue)) {
              menuTitle = [firstMenuName, secondMenuName, thirdMenuName];
              menuList.push({
                menuTitle,
                type: node.type,
                path: subNode.path,
              });
            }
          }
        }
      });
    });
    return menuList;
  }

  /**
   *  清空搜索
   */
  handleClearSearch() {
    this.searchValue = '';
    this.menuList = [];
  }

  /**
   * 切换tab
   * @param event
   * @returns
   */
  handleTabSearch(event) {
    const id = tabList[event.index].id;
    this.tabId = id;
    this.handleSearchMenu();
  }

  /**
   * 跳转
   * @param item
   * @returns
   */
  handleLinkTo(item) {
    this.router.navigate([`/app/business-constructor/${item.path}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
    this.handleCloseAdd();
  }

  /**
   * 搜索中的高亮
   * @param item
   * @returns
   */
  handleHighlightField(item: any): string {
    if (!this.searchValue) {
      return item;
    }
    const regex = new RegExp('(' + this.searchValue + ')', 'gi');
    return item?.replace(regex, '<span class="highlight">$1</span>');
  }
}
