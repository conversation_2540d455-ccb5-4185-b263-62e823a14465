import { Component, EventEmitter, Input, OnD<PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { GlobalService } from 'common/service/global.service';
import { CaseModalComponent } from 'components/bussiness-components/case-modal/case-modal.component';
import { AppModelDrivenService } from 'pages/app-model-driven/business-constructor/service/app-model-driven.service';
import { ICheckPageDesign } from 'pages/app/data-entry/work-design/basic-data/components/backup-branch/backup.types';
import { AppService } from 'pages/apps/app.service';
import { Observable, Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { MdWebapiService } from 'pages/app-model-driven/service/md-webapi.service';
import { cloneDeep } from 'lodash';
import { ServiceArrangeComponent } from 'pages/app-model-driven/business-constructor/service-arrange/service-arrange.component';
import { DataFormComponent } from 'pages/app/data-entry/components/data-form/data-form.component';
import { validatorForm } from 'common/utils/core.utils';
import { ModelDesignService } from 'pages/app/data-entry/services/model-design.service';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import {
  GenerateViewType,
  GenerateBusinessAddSourcetype,
} from 'pages/app/data-entry/components/homework-modal/homework-modal.tool';
import { AiModelComponent } from 'components/page-design/entries/model-designer-new/components/ai-model/ai-model.component';

@Component({
  selector: 'app-business-modal-component',
  templateUrl: './business-modal-component.component.html',
  styleUrls: ['./business-modal-component.component.less'],
})
export class BusinessModalComponentComponent implements OnInit, OnDestroy {
  @Input() routerComponent: any;

  clickMenuBackDataSubscription: Subscription;
  fastAddBackDataSubscription: Subscription;
  // 业务对象开窗属性
  businessObjectModalVisible = new EventEmitter<any>();
  // 浏览视图开窗属性
  browseViewModalVisible = new EventEmitter<any>();
  // 新建事件开窗
  eventModalVisible = new EventEmitter<any>();
  // 服务编排开窗
  soModalVisible = new EventEmitter<any>();
  // 新建侦测开窗
  detectModelVisible = new EventEmitter<any>();
  // 新建流程开窗
  flowModalData: any = {};
  createFlowModalVisible: boolean = false;
  // 数据侦测开窗
  createDetectModalVisible: boolean = false;
  detectModalData: any = {};
  // 数据侦测开窗
  detectDrawer: boolean = false;
  detectData: any = {};

  // 作业新增或编辑的弹窗是否显示
  isShowJobModal = false;
  // 分类导航作业新增或编辑的弹窗是否显示
  isShowNavigateJobModal = false;
  // 二级作业新增或编辑的弹窗是否显示
  isShowJobSubModal = false;
  // 作业弹窗的数据
  jobModalData: any = {};
  // 分类导航作业弹窗的数据
  navigateJobModalData: any = {};
  operationLoading: boolean = false; // 删除loading

  reportModalShow: boolean = false; // 新增编辑仪表盘modal是否展示
  reportItem: object;

  deleteBusinessObjectData: any;
  deleteBusinessObjectVisible: boolean = false; // 是否删除业务对象

  addApiModal: boolean = false; // 新增api模型弹窗
  addSourceType: string | undefined; // 新增api是不是资源视角下的

  isDoMigrate: boolean = false; // 是否开始备份
  migrateBranchInfo: ICheckPageDesign = null; // 迁移分支信息

  // 服务编排-个案
  caseModalVisible: boolean = false;
  caseData: any = undefined;
  @ViewChild('caseModalRef') caseModalRef: CaseModalComponent;
  @ViewChild('modelDesignDataFormAdd') modelDesignDataFormAdd: DataFormComponent;
  @ViewChild('aiModelComponent') aiModelComponent: AiModelComponent;

  // 服务编排-入参设置
  paramsSettingModal: boolean = false;
  paramsSettingData: any = {};
  // 服务编排 - drawer
  soVisible: boolean = false;
  soData: any = {};

  // ai 创建模型
  aiDrawerVisible: boolean = false;
  // ai 创建的结果
  aiResult: any = undefined;
  // ai 创建业务对象
  aiModelVisible: boolean = false;

  // 资源视角下-type选择
  isTypeSelectModalShow: boolean = false;
  // 资源视角下，pop新增的备份数据
  cloneData: any;

  // 业务对象
  handleBusinessObjectModalVisible = (): Observable<any> => this.businessObjectModalVisible.asObservable();
  // 作业新增中间选择弹窗
  bussinessAddMidViewModelVisible = new EventEmitter<any>();
  handleBussinessAddMidViewModelVisible = (): Observable<any> => this.bussinessAddMidViewModelVisible.asObservable();
  // 作业和查询方案新建开窗
  planAndWorkViewModalVisible = new EventEmitter<any>();
  handlePlanAndWorkViewModalVisible = (): Observable<any> => this.planAndWorkViewModalVisible.asObservable();

  // 浏览视图
  handleBrowseViewModalVisible = (): Observable<any> => this.browseViewModalVisible.asObservable();

  // 事件
  handleEventModalVisible = (): Observable<any> => this.eventModalVisible.asObservable();

  // 服务编排
  handleSoModalVisible = (): Observable<any> => this.soModalVisible.asObservable();

  // 新增/编辑模型的title
  modalTitle: string = '';
  // 新增/编辑模型的modal
  modelModal: boolean = false;

  // 是否正在添加模型
  public modalAdding: boolean = false;

  // 是否显示删除模型的确认框
  public deleteModelVisible: boolean = false;
  public modelName: string;
  public modelData: any;

  constructor(
    public globalService: GlobalService,
    public appService: AppService,
    public appModelDrivenService: AppModelDrivenService,
    public translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    private router: Router,
    private modal: AdModalService,
    private message: NzMessageService,
    private mdWebapiService: MdWebapiService,
    private modelDesignService: ModelDesignService,
  ) {}

  ngOnInit(): void {
    this.clickMenuBackDataSubscription = this.appModelDrivenService.clickMenuBackData$.subscribe((data) => {
      this.handleClickMenuOperation(data);
    });
    this.fastAddBackDataSubscription = this.appModelDrivenService.fastAddBackData$.subscribe((data) =>
      this.handleFastAddOperation(data),
    );
  }

  ngOnDestroy() {
    this.clickMenuBackDataSubscription?.unsubscribe();
    this.fastAddBackDataSubscription?.unsubscribe();
  }

  /**
   * 处理通过menu的popover点击事件
   * @param data
   */
  handleClickMenuOperation(data) {
    const cloneData = cloneDeep(data);
    const { item, subItem } = cloneDeep(cloneData) || {};
    // 操作
    const itemAction = subItem?.action || item?.action || '';
    switch (itemAction) {
      case 'add':
        this.handleAddAction(cloneData);
        break;
      case 'edit':
        this.handleEditAction(cloneData);
        break;
      case 'delete':
        this.handleDeleteAction(cloneData);
        break;
      case 'default':
        this.handleDefaultAction(cloneData);
        break;
      case 'migrate':
        this.handleMigrateAction(cloneData);
        break;
      case 'case': // 服务编排-个案
        this.handleCaseAction(cloneData);
        break;
      case 'params': // 服务编排-入参设置
        this.handleParamsSettingAction(cloneData);
        break;
      default:
      // 默认
    }
  }

  /**
   * 服务编排-入参设置
   * @param daya
   */
  async handleParamsSettingAction(data: any): Promise<void> {
    const so = await this.getServiceBy(data.menuItem.businessCode, data.menuItem.businessSubCode);
    if (!so) return;
    this.paramsSettingData = Object.assign(
      {
        actionId: so.id,
        label: 'StartServiceComposerAction',
      },
      { extendsData: data },
    );
    this.paramsSettingModal = true;
  }

  /**
   * 服务编排-个案
   * @param data
   */
  async handleCaseAction(data: any): Promise<void> {
    const so = await this.getServiceBy(data.menuItem.businessCode, data.menuItem.businessSubCode);
    if (!so) return;
    this.caseData = Object.assign({}, so, { code: so.id }, { extendsData: data });
    this.caseModalVisible = true;
  }

  /**
   * 获取指定的服务编排的信息
   * @param businessCode
   * @param id
   * @returns
   */
  private async getServiceBy(businessCode: string, id: string): Promise<any> {
    const appCode = this.activatedRoute.snapshot.queryParams.appCode;
    const { code, data } = await this.mdWebapiService
      .loadServiceOrchestration({
        businessCode: businessCode,
        applicationCode: appCode,
      })
      .toPromise();
    if (code === 0) {
      return data.find((e) => e.objectId === id);
    }
    return undefined;
  }

  handleBussinessAddMidModelSelect(info: any) {
    this.planAndWorkViewModalVisible.emit(info);
  }

  handleAddPlanOrWork(type: GenerateViewType, info: any) {
    this.bussinessAddMidViewModelVisible.emit({
      visible: true,
      data: {
        application: info?.application ?? this.appService?.selectedApp?.code,
        businessCode: info?.businessCode,
        modelCode: info?.modelId,
        serviceCode: info?.serviceCode,
        modelType: info?.modelType,
      },
      extraData: {
        viewType: type,
        perspective: this.appModelDrivenService.businessObjectPerspective
          ? GenerateBusinessAddSourcetype.BUSINESS_OBJECT
          : GenerateBusinessAddSourcetype.RESOURCE,
      },
    });
  }
  /**
   * 处理menu中快速创建事件
   */
  handleFastAddOperation(data: any = {}) {
    const cloneInfo = cloneDeep(data);
    const { menu: cloneData } = cloneInfo;
    // 菜单类型
    const itemType = cloneData?.type || '';
    switch (itemType) {
      case 'event': // 事件
        const param = {
          businessCode: cloneData.businessCode,
          visible: true,
          ...this.patchResourceKey(),
        };
        if (!this.isResourcePerspective()) {
          const currentMenu = this.appModelDrivenService.businessObjectMenu?.find(
            (item) => item.businessCode === cloneData.businessCode,
          );
          param['modelId'] = currentMenu?.modelId;
          param['serviceCode'] = currentMenu?.serviceCode;
        }
        this.eventModalVisible.emit(param);
        break;
      case 'businessProcess': // 集成与自动化流程设计
        this.flowModalData = Object.assign({}, cloneData, this.patchResourceKey());
        this.createFlowModalVisible = true;
        break;
      case 'instrument-pane': // 仪表盘
        this.handleAddAndEditReport();
        break;
      case 'homeWork':
        this.handleAddPlanOrWork(GenerateViewType.BUSINESS, cloneInfo.parent);
        break;
      case 'dataView': // 新增查询方案
        if (this.isResourcePerspective()) {
          this.handleAddQueryPlan(undefined, undefined, undefined);
        } else {
          const currentMenu = this.appModelDrivenService.businessObjectMenu?.find(
            (item) => item.businessCode === cloneData.businessCode,
          );
          this.handleAddQueryPlan(currentMenu.modelId, currentMenu.businessCode, currentMenu.serviceCode);
        }
        break;
      case 'service':
        this.soModalVisible.emit({
          data: !this.isResourcePerspective() ? data : {},
        });
        break;
      case 'detect':
        this.detectModalData = { businessCode: cloneData.businessCode, ...this.patchResourceKey() };
        this.createDetectModalVisible = true;
        break;
      case 'modelDesign': // 新增模型
        this.handleShowTypeSelect(cloneData);
        break;
      default:
      // 默认
    }
  }
  // 新建业务、视图等逻辑
  handleAddAction(data) {
    const { item, subItem, menuItem } = data || {};
    const itemType = subItem?.type || item?.type || '';
    switch (itemType) {
      case 'bussinessApi': // 新增业务对象 - API
        this.addApiModal = true;
        this.addSourceType = undefined;
        break;
      case 'bussinessCommon': // 新增业务对象 - 通用
        this.businessObjectModalVisible.emit({
          visible: true,
          modelType: 'bussinessCommon',
        });
        break;
      case 'ai':
        this.aiDrawerVisible = true;
        this.aiModelComponent?.getGptLimit();
        break;
      case 'basic': // 业务对象 - 基础档
      case 'param': // 业务对象 - 参数档
      case 'transaction': // 业务对象 - 业务档
        this.businessObjectModalVisible.emit({
          visible: true,
          modelType: itemType,
        });
        break;
      case 'SIGN-DOCUMENT': // 作业 - 单档多栏
      case 'DOUBLE-DOCUMENT-FORM': // 作业 - 单档
      case 'DOUBLE-DOCUMENT': // 作业 - 双档
      case 'DOUBLE-DOCUMENT-MULTI': // 作业 - 多档
        if (!menuItem.modeId) {
          const businessObj = this.appModelDrivenService.businessObjectMenu?.find(
            (e) => e.businessCode === menuItem.businessCode,
          );
          menuItem.modelId = businessObj?.modelId;
        }
        this.jobModalData = {
          category: itemType,
          operateType: 'create',
          businessCode: menuItem.businessCode,
          menuItem: Object.assign({}, menuItem, this.patchResourceKey()),
        };
        this.isShowJobModal = true;
        break;
      case 'TREEDATA-SINGLE-DOCUMENT': // 分类导航作业 - 单档多栏
      case 'TREEDATA-DOUBLE-DOCUMENT-FORM': // 分类导航作业 - 单档
      case 'TREEDATA-DOUBLE-DOCUMENT': // 分类导航作业 - 双档
      case 'TREEDATA-DOUBLE-DOCUMENT-MULTI': // 分类导航作业 - 多档
        if (!menuItem.modeId) {
          const businessObj = this.appModelDrivenService.businessObjectMenu?.find(
            (e) => e.businessCode === menuItem.businessCode,
          );
          menuItem.modelId = businessObj?.modelId;
        }
        this.navigateJobModalData = {
          category: itemType,
          operateType: 'create',
          businessCode: menuItem.businessCode,
          menuItem: Object.assign({}, menuItem, this.patchResourceKey()),
        };
        this.isShowNavigateJobModal = true;
        break;
      case 'browse': // 数据视图 - 浏览视图
      case 'searchView': // 数据视图 - 数据查询视图
        this.handleSearchView(menuItem, itemType);
        break;
      case 'pageDesign':
        this.handleAddPlanOrWork(GenerateViewType.BUSINESS, data.menuItem);
        break;
      case 'dataView':
        this.handleAddQueryPlan(menuItem.modelId, menuItem.businessCode, menuItem.serviceCode);
        break;
      case 'event': // 事件
        const param = {
          businessCode: menuItem.businessCode,
          visible: true,
          ...this.patchResourceKey(),
        };
        if (!this.isResourcePerspective()) {
          param['modelId'] = menuItem.modelId;
          param['serviceCode'] = menuItem.serviceCode;
        }
        this.eventModalVisible.emit(param);
        break;
      case 'flow': // 集成与自动化流程设计
        this.flowModalData = Object.assign({}, menuItem, this.patchResourceKey());
        this.createFlowModalVisible = true;
        break;
      case 'service':
        this.soModalVisible.emit({
          data: !this.isResourcePerspective()
            ? menuItem.businessDirTree.find((e) => e.type === itemType) ?? {
                businessCode: menuItem.businessCode,
              }
            : {},
        });
        break;
      case 'detect':
        this.detectModalData = { businessCode: menuItem.businessCode, ...this.patchResourceKey() };
        this.createDetectModalVisible = true;
        break;
      case 'modelPop':
        this.handleShowTypeSelect(cloneDeep(data));
        break;
      default:
      // 默认
    }
  }

  /**
   * 处理 浏览视图/数据查询视图
   * @param menuItem
   * @param itemType
   * @returns
   */
  handleSearchView(menuItem: any, itemType: string): void {
    if (!this.isResourcePerspective()) {
      // 对象视角
      // 业务对象层新增
      if (menuItem.type === 'businessConstructor') {
        this.browseViewModalVisible.emit({
          visible: true,
          extraData: {
            simpleModelCode: menuItem.modelId,
            businessCode: menuItem.businessCode,
            serviceCode: menuItem.serviceCode,
            viewType: itemType,
          },
        });
        return;
      }
      // 数据视图层新增
      if (menuItem.type === 'dataView') {
        const menuList = this.appModelDrivenService.businessObjectMenu;
        const currentMenu = menuList.find((item) => item.businessCode === menuItem.businessCode);
        this.browseViewModalVisible.emit({
          visible: true,
          extraData: {
            simpleModelCode: currentMenu.modelId,
            businessCode: currentMenu.businessCode,
            serviceCode: currentMenu.serviceCode,
            viewType: itemType,
          },
        });
      }
    } else {
      // 资源视角
      this.browseViewModalVisible.emit({
        visible: true,
        extraData: {
          viewType: itemType,
        },
      });
    }
  }

  /**
   * 显示类别选择框
   * @param data
   */
  handleShowTypeSelect(data: any): void {
    this.isTypeSelectModalShow = true;
    this.cloneData = data;
  }

  // 编辑业务、视图等逻辑
  handleEditAction(data) {
    const { item, subItem, menuItem } = data || {};
    const itemType = subItem?.type || item?.type || menuItem.type || '';
    switch (itemType) {
      case 'businessConstructor': // 业务对象
        this.businessObjectModalVisible.emit({
          visible: true,
          modelType: menuItem.modelType,
          businessCode: menuItem.businessCode,
        });
        break;
      case 'pageDesign': // 作业
        if (menuItem.category.startsWith('TREEDATA-')) {
          // 编辑分类导航作业
          this.navigateJobModalData = {
            operateType: 'edit',
            businessCode: menuItem.businessCode,
            code: menuItem.businessSubCode,
            ...this.patchResourceKey(),
          };
          this.isShowNavigateJobModal = true;
        } else {
          this.jobModalData = {
            operateType: 'edit',
            businessCode: menuItem.businessCode,
            code: menuItem.businessSubCode,
            ...this.patchResourceKey(),
          };
          this.isShowJobSubModal = true;
        }

        break;
      case 'dataView': // 视图
        const menuList = this.appModelDrivenService.businessObjectMenu;
        const currentMenu = menuList.find((item) => item.businessCode === menuItem.businessCode);
        if (this.isResourcePerspective()) {
          this.browseViewModalVisible.emit({
            visible: true,
            data: {
              code: menuItem.businessSubCode,
              name: menuItem.businessSubName,
              description: menuItem.description,
              lang: menuItem.lang,
              modelId: menuItem.modelId || currentMenu.modelId,
              serviceCode: menuItem.serviceCode || currentMenu.serviceCode,
            },
            extraData: {
              simpleModelCode: menuItem.modelId || currentMenu.modelId,
              businessCode: menuItem.businessCode,
              serviceCode: menuItem.serviceCode,
              viewType: menuItem.viewType,
            },
          });
        } else {
          this.browseViewModalVisible.emit({
            visible: true,
            data: {
              code: menuItem.businessSubCode,
              name: menuItem.businessSubName,
              description: menuItem.description,
              lang: menuItem.lang,
              modelId: menuItem.modelId ?? null,
              serviceCode: menuItem.serviceCode ?? null,
            },
            extraData: {
              simpleModelCode: currentMenu.modelId,
              businessCode: menuItem.businessCode,
              serviceCode: menuItem.serviceCode,
              viewType: menuItem.viewType,
            },
          });
        }

        break;
      case 'businessProcessItem': // 集成与自动化流程设计
        this.flowModalData = {
          processId: menuItem.businessSubCode,
          businessCode: menuItem.businessCode,
          ...this.patchResourceKey(),
        };
        this.createFlowModalVisible = true;
        break;
      case 'instrumentPaneItem': // 仪表盘
        this.handleAddAndEditReport(menuItem);
        break;

      case 'serviceItem': {
        // 服务编排-编辑
        const component = this.routerComponent as ServiceArrangeComponent;
        component.appSoFlow.handleEdit();
        break;
      }
      default:
      // 默认
    }
  }

  // 删除业务、视图等逻辑
  handleDeleteAction(data) {
    const { item, subItem, menuItem } = data || {};
    const itemType = subItem?.type || item?.type || menuItem.type || '';
    switch (itemType) {
      case 'businessConstructor': // 业务对象
        this.deleteBusinessObject(itemType, menuItem);
        break;
      case 'pageDesign': // 作业
        this.deleteJobsObject({ code: menuItem.businessSubCode, businessCode: menuItem.businessCode });
        break;
      case 'model':
        this.deleteModel(itemType, menuItem);
        break;
      case 'dataView': // 数据视图
        this.deleteDataView(itemType, menuItem);
        break;
      case 'eventItem': //事件
        this.deleteEvent(itemType, menuItem);
        break;
      case 'businessProcessItem': // 集成与自动化流程设计
        this.deleteProcess(itemType, menuItem);
        break;
      case 'serviceItem': // 服务编排
        this.deleteService(itemType, menuItem);
        break;
      case 'detectItem': // 数据侦测
        this.deleteDetect(itemType, menuItem);
        break;
      default:
        // 默认
        break;
    }
  }

  // 默认视图等逻辑
  handleDefaultAction(data) {
    const { item, subItem, menuItem } = data || {};
    const itemType = subItem?.type || item?.type || '';
    switch (itemType) {
      case 'dataView': // 数据视图
        this.defaultDataView(itemType, menuItem);
        break;
      default:
      // 默认
    }
  }

  // 删除业务对象
  deleteBusinessObject(type, data) {
    this.deleteBusinessObjectData = data;
    this.deleteBusinessObjectVisible = true;
  }

  /**
   * 删除业务对象
   * @param e
   */
  async deleteBusinessObjectOk(e) {
    const params = {
      type: 'table',
      code: this.deleteBusinessObjectData.modelId,
      serviceCode: this.deleteBusinessObjectData.serviceCode,
      entrance: 'delBusinessDir',
    };
    const returnData: any = await this.checkBindRelationBeforeDelete(params);
    this.operationLoading = false;
    if (returnData?.data) {
      this.modal.confirm({
        nzTitle: this.translateService.instant('dj-当前业务对象已被绑定，是否继续？'),
        nzOkText: this.translateService.instant('dj-确定'),
        nzOnOk: () => {
          this.handleDeleteBusinessObject('businessConstructor', this.deleteBusinessObjectData);
          this.cancelDeletBusinessObject();
        },
        nzOnCancel: () => {
          this.cancelDeletBusinessObject();
        },
      });
    } else {
      this.handleDeleteBusinessObject('businessConstructor', this.deleteBusinessObjectData);
      this.cancelDeletBusinessObject();
    }
  }

  /**
   * 取消删除业务对象
   * @param e
   */
  cancelDeletBusinessObject() {
    this.deleteBusinessObjectData = {};
    this.deleteBusinessObjectVisible = false;
  }

  /**
   * 确定业务对象
   * @param e
   */
  handleDeleteBusinessObject(type, data) {
    this.appModelDrivenService.loadingPage = true;
    const params = { type, businessCode: data.businessCode };
    this.mdWebapiService.postDeleteBusinessObject(params).subscribe(
      (res) => {
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-删除成功'));
          this.appModelDrivenService.updateBusinessObjectMenuRefresh({
            businessCode: data.businessCode,
            menuType: 'businessConstructor',
            operatorType: 'delete',
            deleteId: data.businessCode,
          });
        } else {
          this.message.error(res.data);
        }
        this.appModelDrivenService.loadingPage = false;
      },
      () => {
        this.appModelDrivenService.loadingPage = false;
      },
    );
  }

  deleteJobsObject(data) {
    this.modal.confirm({
      nzTitle: this.translateService.instant('dj-确认删除？'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translateService.instant('dj-确定'),
      nzCancelText: this.translateService.instant('dj-取消'),
      nzOnOk: () => {
        this.operationLoading = true;
        const param = `?pattern=DATA_ENTRY&code=${data.code}`;
        this.mdWebapiService.deleteBasicReport(param).subscribe(
          (res) => {
            if (res.code === 0) {
              this.operationLoading = false;
              this.message.success(this.translateService.instant('dj-删除成功！'));
              this.appModelDrivenService.updateBusinessObjectMenuRefresh({
                businessCode: data.businessCode,
                menuType: 'pageDesign',
                operatorType: 'delete',
                deleteId: data.code,
              });
            }
          },
          () => {
            this.operationLoading = false;
          },
        );
      },
      nzOnCancel: () => {},
    });
  }

  deleteModel(item: any, menuItem: any) {
    const name = menuItem.lang?.name?.[this.translateService.instant('dj-LANG')] || menuItem.businessSubName;
    this.modelName = name;
    this.modelData = menuItem;
    this.deleteModelVisible = true;
  }

  // 删除数据视图
  deleteDataView(type, data) {
    if (data.viewType === 'searchView') {
      this.modal.confirm({
        nzTitle: this.translateService.instant('dj-查询方案有可能被界面关联使用，请谨慎删除'),
        nzClassName: 'searchview-save-tips',
        nzOkText: this.translateService.instant('dj-继续删除'),
        nzOnOk: () => {
          this.handleDeleteDataView(type, data);
        },
        nzOnCancel: () => {},
      });
    } else {
      this.modal.confirm({
        nzContent: this.translateService.instant('dj-确定删除查询方案'),
        nzOkLoading: this.operationLoading,
        nzBodyStyle: {
          height: '160px',
        },
        nzOnOk: async () => {
          this.operationLoading = true;
          const params = {
            type: 'view',
            code: data.businessSubCode,
            serviceCode: data.serviceCode,
          };
          const returnData: any = await this.checkBindRelationBeforeDelete(params);
          this.operationLoading = false;
          if (returnData?.data) {
            this.modal.confirm({
              nzTitle: this.translateService.instant('dj-当前查询方案已被绑定，是否继续？'),
              nzOkText: this.translateService.instant('dj-确定'),
              nzOnOk: () => {
                this.handleDeleteDataView(type, data);
              },
              nzOnCancel: () => {},
            });
          } else {
            this.handleDeleteDataView(type, data);
          }
        },
      });
    }
  }

  // 删除事件
  deleteEvent(type, data) {
    this.modal.confirm({
      nzContent: this.translateService.instant('dj-确定删除事件'),
      nzOkLoading: this.operationLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: async () => {
        this.handleDeleteEvent(type, data);
      },
    });
  }

  deleteService(type, data) {
    this.modal.confirm({
      nzContent: this.translateService.instant('dj-确定删除服务编排'),
      nzOkLoading: this.operationLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: async () => {
        this.handleDeleteService(type, data);
      },
    });
  }

  deleteDetect(type, data) {
    this.modal.confirm({
      nzContent: this.translateService.instant('dj-确定删除侦测'),
      nzOkLoading: this.operationLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: async () => {
        this.handleDeleteDetect(type, data);
      },
    });
  }

  // 检测业务对象|数据视图是否被绑定
  async checkBindRelationBeforeDelete(params) {
    try {
      this.operationLoading = true;
      const returnData = await this.mdWebapiService.checkBindRelation(params).toPromise();
      this.operationLoading = false;
      return returnData;
    } catch (error) {
      this.operationLoading = false;
      this.message.error(error.message);
      return null;
    }
  }

  handleDeleteDataView(type, data) {
    this.operationLoading = true;
    const params = { code: data.businessSubCode };
    this.mdWebapiService.postDeleteDataView(params).subscribe(
      (res) => {
        this.operationLoading = false;
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-删除成功'));
          this.appModelDrivenService.updateBusinessObjectMenuRefresh({
            businessCode: data.businessCode,
            menuType: 'dataView',
            operatorType: 'delete',
            deleteId: data.businessSubCode,
          });
        } else {
          this.message.error(res.data);
        }
      },
      () => {
        this.operationLoading = false;
      },
    );
  }

  handleDeleteEvent(type, data) {
    this.operationLoading = true;
    const params = { id: data.businessSubCode };
    this.mdWebapiService.postDeleteEvent(params).subscribe(
      (res) => {
        this.operationLoading = false;
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-删除成功'));
          this.appModelDrivenService.updateBusinessObjectMenuRefresh({
            businessCode: data.businessCode,
            menuType: 'event',
            operatorType: 'delete',
            deleteId: data.businessSubCode,
          });
        } else {
          this.message.error(res.data);
        }
      },
      () => {
        this.operationLoading = false;
      },
    );
  }

  handleDeleteService(type, data) {
    this.operationLoading = true;
    this.mdWebapiService.deleteSO(data.businessSubCode).subscribe(
      (res) => {
        this.operationLoading = false;
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-删除成功'));
          this.appModelDrivenService.updateBusinessObjectMenuRefresh({
            businessCode: data.businessCode,
            menuType: 'service',
            operatorType: 'delete',
            deleteId: data.businessSubCode,
          });
        } else {
          this.message.error(res.data);
        }
      },
      () => {
        this.operationLoading = false;
      },
    );
  }

  handleDeleteDetect(type, data) {
    this.operationLoading = true;
    this.mdWebapiService.deleteTreeDetect(data.businessSubCode).subscribe(
      (res) => {
        this.operationLoading = false;
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-删除成功'));
          this.appModelDrivenService.updateBusinessObjectMenuRefresh({
            businessCode: data.businessCode,
            menuType: 'detect',
            operatorType: 'delete',
            deleteId: data.businessSubCode,
          });
        } else {
          this.message.error(res.data);
        }
      },
      () => {
        this.operationLoading = false;
      },
    );
  }

  // 删除流程
  deleteProcess(type, data) {
    this.modal.confirm({
      nzContent: this.translateService.instant('dj-是否确定删除该业务流？'),
      nzOkLoading: this.operationLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: (): void => {
        this.operationLoading = true;
        const params = { processId: data.businessSubCode };
        this.mdWebapiService.postRemoveProcess(params).subscribe(
          (res) => {
            this.operationLoading = false;
            if (res.code === 0) {
              this.message.success(this.translateService.instant('dj-删除成功'));
              this.appModelDrivenService.updateBusinessObjectMenuRefresh({
                businessCode: data.businessCode,
                menuType: 'businessProcess',
                operatorType: 'delete',
                deleteId: data.businessSubCode,
              });
            } else {
              this.message.error(res.data);
            }
          },
          () => {
            this.operationLoading = false;
          },
        );
      },
    });
  }

  // 设置默认视图
  defaultDataView(type, data) {
    this.modal.confirm({
      nzContent: this.translateService.instant('dj-确定设为默认查询方案'),
      nzOkLoading: this.operationLoading,
      nzBodyStyle: {
        height: '160px',
      },
      nzOnOk: (): void => {
        this.operationLoading = true;
        const params = {
          modelId: data.modelId,
          productCode: data.serviceCode,
          dataViewCode: data.businessSubCode,
          defaultView: true,
        };
        this.mdWebapiService.postUpdateDataViewDefault(params).subscribe(
          (res) => {
            this.operationLoading = false;
            if (res.code === 0) {
              this.message.success(this.translateService.instant('dj-设置成功'));
              this.appModelDrivenService.updateBusinessObjectMenuRefresh({
                businessCode: data.businessCode,
                menuType: 'dataView',
                operatorType: 'default',
                deleteId: data.businessSubCode,
              });
            } else {
              this.message.error(res.data);
            }
          },
          () => {
            this.operationLoading = false;
          },
        );
      },
    });
  }

  // 业务对象创建|编辑回调
  finishBusinessObjectModal(businessData) {
    // modelPath 字段，创建返回，编辑不返回
    const { businessCode, modelPath, isUpdateBusinessData } = businessData || {};
    this.appModelDrivenService.updateBusinessObjectMenuRefresh({
      businessCode,
      menuType: 'businessConstructor',
      operatorType: modelPath ? 'add' : 'edit',
      navigateUrl: modelPath,
      isUpdateBusinessData,
    });
  }

  // 浏览视图创建|编辑回调
  finishedBrowseViewModal(viewData) {
    const { businessCode, addDataViewResponse } = viewData || {};
    const { dataViewPath } = addDataViewResponse || {};
    const params: any = {
      businessCode,
      menuType: 'dataView',
      operatorType: dataViewPath ? 'add' : 'edit',
      navigateUrl: dataViewPath,
    };
    if (params.operatorType === 'edit') {
      params.editId = viewData.code;
    }
    this.appModelDrivenService.updateBusinessObjectMenuRefresh(params);
  }

  // 取消流程开窗
  closeFlowModal(): void {
    this.flowModalData = {};
    this.createFlowModalVisible = false;
  }

  closeDetectModal(info?: any): void {
    this.createDetectModalVisible = false;
    const { businessCode } = this.detectModalData || {};
    this.detectModalData = undefined;
    if (info) {
      this.detectData = {
        businessCode,
        code: this.appService?.selectedApp?.code,
        ...this.patchResourceKey(),
      };
      this.detectDrawer = true;
    }
  }

  // 流程创建成功回调
  finishedFlowModal(flowData): void {
    this.createFlowModalVisible = false;
    const { businessCode, processPath, menuOperateType } = flowData || {};
    const params: any = {
      businessCode,
      menuType: 'businessProcess',
      operatorType: menuOperateType,
      navigateUrl: processPath,
    };
    this.appModelDrivenService.updateBusinessObjectMenuRefresh(params);
  }

  // 新增或编辑更新左侧的菜单
  handleUpdateMenuJobModal(data): void {
    this.appModelDrivenService.updateBusinessObjectMenuRefresh(data);
  }

  // 事件创建|编辑回调
  finishEventModal(eventData) {
    // eventPath 字段，创建返回，编辑不返回
    const { businessCode, eventPath } = eventData || {};
    this.appModelDrivenService.updateBusinessObjectMenuRefresh({
      businessCode,
      menuType: 'event',
      operatorType: eventPath ? 'add' : 'edit',
      navigateUrl: eventPath,
    });
  }

  /**
   * 新增编辑报表modal
   * @param menuItem
   */
  handleAddAndEditReport(menuItem?: object) {
    this.reportModalShow = true;
    this.reportItem = menuItem;
  }
  /**
   * 关闭报表modal
   * @param e
   */
  closeReportModal(e) {
    this.reportItem = {};
    this.reportModalShow = false;
  }

  handleDeleteReport(menuItem) {
    this.modal.confirm({
      nzTitle: this.translateService.instant('dj-确认删除？'),
      nzWrapClassName: 'vertical-center-modal',
      nzOkText: this.translateService.instant('dj-确定'),
      nzOnOk: () => {
        this.appModelDrivenService.handleDeleteReport(menuItem);
      },
      nzOnCancel: () => {},
    });
  }

  closeJobModal(): void {
    this.isShowJobModal = false;
    this.isShowJobSubModal = false;
  }

  closeNavigateJobModal(): void {
    this.isShowNavigateJobModal = false;
  }

  handleMigrateAction(data) {
    const application = this.appService?.selectedApp?.code;
    const { businessSubCode, modelId } = data?.menuItem ?? {};
    this.migrateBranchInfo = {
      sourceApplication: application,
      sourceBranch: 'bak',
      targetApplication: application,
      targetBranch: 'develop',
      pageDesignCodeList: [
        {
          code: businessSubCode,
          simpleModelCode: modelId ?? '',
        },
      ],
    };
    this.isDoMigrate = true;
  }

  handleMigrateCallback() {
    this.migrateBranchInfo = null;
    this.isDoMigrate = false;
  }

  // 新增查询方案
  handleAddQueryPlan(simpleModelCode, businessCode, serviceCode) {
    this.browseViewModalVisible.emit({
      visible: true,
      extraData: {
        simpleModelCode,
        businessCode,
        serviceCode,
        viewType: 'queryPlan',
        ...this.patchResourceKey(),
      },
    });
  }

  handleSoCancel(): void {
    this.soModalVisible.emit(null);
  }

  // 新增服务编排
  handleSoOk(values: any): void {
    const { form: item, businessCode } = values;
    const { objectId, tenantId, id, lang } = item;
    this.soData = {
      id: objectId,
      businessCode,
      tenantId,
      objectId,
      tempId: id,
      lang: lang,
      appCode: this.appService?.selectedApp?.code,
      ...this.patchResourceKey(),
    };
    this.soVisible = true;
    this.handleSoCancel();
  }

  // 新增个案
  saveCase(data: any): void {
    const params = {
      tenantIds: data.tenantIds,
      objectId: this.caseData.objectId,
      newTemplateCode: data.newCode,
      newTemplateName: data.newName,
    };
    this.mdWebapiService.copyMonitorRule(params).subscribe(
      (res) => {
        if (res.code === 0) {
          this.caseModalRef.closeLoad();
          this.message.success(this.translateService.instant('dj-操作成功'));
          const {
            extendsData: { menuItem },
          } = this.caseData;
          this.appModelDrivenService.updateBusinessObjectMenuRefresh({
            businessCode: menuItem.businessCode,
            menuType: 'service',
            operatorType: 'add',
          });
        }
        this.caseData = undefined;
      },
      () => {
        this.caseModalRef.closeLoad();
        this.caseData = undefined;
      },
    );
  }

  /**
   * 服务编排入参设置
   */
  handleSaveActionSuccess(): void {
    this.paramsSettingModal = false;
    this.paramsSettingData = {};
  }

  /**
   * 选择了类别
   * @param type
   */
  handleSelectType(type: 'bussinessApi' | 'bussinessCommon'): void {
    this.isTypeSelectModalShow = false;
    if (type === 'bussinessCommon') {
      this.modalTitle = this.translateService.instant('dj-新增实体模型');
      this.modelModal = true;
    } else if (type === 'bussinessApi') {
      this.addApiModal = true;
      this.addSourceType = this.patchResourceKey().addSourceType;
    } else if (type === 'ai') {
      this.aiDrawerVisible = true;
      this.aiModelComponent?.getGptLimit();
    }
  }

  //关闭新增模型的modal
  handleAddClose(): void {
    this.modelModal = false;
  }

  // 保存新增的模型
  handleAddSure(): void {
    const { dataForm, formLang } = this.modelDesignDataFormAdd;
    validatorForm(dataForm);
    if (!dataForm.valid) return;
    const dataFormValue = dataForm.getRawValue();
    const params = Object.assign(dataFormValue, {
      lang: formLang,
      ...this.patchResourceKey(),
    });
    params.modelType = 'basic';
    const seen = new Set();
    const hasDuplicate = params.customProperties?.some((item) => {
      const value = item['key'];
      if (seen.has(value)) {
        return true;
      }
      seen.add(value);
      return false;
    });
    if (hasDuplicate) {
      this.message.error(this.translateService.instant('dj-存在重复的自定义属性'));
      return;
    }
    const customProperties = {};
    params.customProperties.forEach((item) => {
      customProperties[item.key] = item.value;
    });
    params.customProperties = customProperties;
    this.modalAdding = true;
    this.modelDesignService.addModelDesign$.next({
      params,
      cb: (res) => {
        this.modalAdding = false;
        if (res.code === 0) {
          this.message.success(this.translateService.instant('dj-保存成功！'));
          this.handleAddClose();
          this.appModelDrivenService.updateBusinessObjectMenuRefresh({
            businessCode: undefined,
            menuType: 'modelDesign',
            operatorType: 'add',
            navigateUrl: res.data?.modelPath,
          });
        }
      },
    });
  }

  /**
   * 是不是资源视角
   * @returns
   */
  private isResourcePerspective(): boolean {
    return this.appModelDrivenService.tabIndex === 1;
  }

  // 资源视角下需要 ‘addSourceType’这个key
  private patchResourceKey() {
    return this.appModelDrivenService.getAddSourcetype();
  }

  // 取消删除模型
  handleCancelDeleteModel(): void {
    this.deleteModelVisible = false;
    this.modelName = undefined;
    this.modelData = undefined;
  }

  handleDeleteModel = async (): Promise<void> => {
    const params = {
      code: this.modelData.businessSubCode,
      serviceCode: this.modelData.serviceCode,
      businessCode: this.modelData.businessCode,
      application: this.appService?.selectedApp?.code,
    };
    const checkRes = await this.mdWebapiService.checkTableRelated(params);
    if (checkRes.code !== 0) return;
    const res = await this.mdWebapiService.deleteOfResource(params);
    if (res.code === 0) {
      this.handleCancelDeleteModel();
      this.message.success(this.translateService.instant('dj-删除成功'));
      this.appModelDrivenService.updateBusinessObjectMenuRefresh({
        businessCode: params.businessCode,
        menuType: 'modelDesign',
        operatorType: 'delete',
        deleteId: params.code,
      });
    }
  };

  /**
   * TODO: 新的创建作业之后的处理，待处理
   */
  handleNewAddBusiness(info: any) {
    this.handleUpdateMenuJobModal(info);
    console.log(
      '%c 🚀 [Neovim AutoGR Log]: path = src/pages/app-model-driven/business-constructor/container/components/business-modal-component/business-modal-component.component.ts, scope = BusinessModalComponentComponent.handleNewAddBusiness, info = ',
      'color: orangered; font-weight: bold;',
      info,
    );
  }

  /**
   * TODO: 新的生成查询方案之后的处理，待处理
   */
  handleNewQueryPlan(info: any) {
    this.finishedBrowseViewModal(info);
    console.log(
      '%c 🚀 [Neovim AutoGR Log]: path = src/pages/app-model-driven/business-constructor/container/components/business-modal-component/business-modal-component.component.ts, scope = BusinessModalComponentComponent.handleNewQueryPlan, info = ',
      'color: orangered; font-weight: bold;',
      info,
    );
  }

  handleDrawerClose() {
    this.aiDrawerVisible = false;
    this.aiResult = undefined;
    this.aiModelComponent?.reset();
  }

  handleAIBack(param: any) {
    this.aiResult = param;
    this.aiModelVisible = true;
  }

  handleAiCallback({ status, url }: any) {
    const { msgId } = this.aiResult;
    this.aiModelComponent?.updateMessageStatus?.(msgId, status);
    if (url) {
      this.aiModelVisible = false;
      this.appModelDrivenService.updateBusinessObjectMenuRefresh({
        businessCode: undefined,
        menuType: 'modelDesign',
        operatorType: 'add',
        navigateUrl: url,
      });
    }
  }

  handleAiCancel() {
    this.aiModelVisible = false;
  }
}
