<!-- 业务视图 -->
<app-business-object-modal
  *ngIf="handleBusinessObjectModalVisible"
  [infoVisible]="handleBusinessObjectModalVisible"
  (finished)="finishBusinessObjectModal($event)"
></app-business-object-modal>
<!-- 新建查询方案 -->
<app-new-query-plan-modal
  [infoVisible]="handlePlanAndWorkViewModalVisible"
  (submit)="handleNewQueryPlan($event)"
></app-new-query-plan-modal>
<!--新建作业-->
<app-new-homework-modal
  [infoVisible]="handlePlanAndWorkViewModalVisible"
  (fastGenerateQueryPlan)="handleNewQueryPlan($event)"
  (submit)="handleNewAddBusiness($event)"
></app-new-homework-modal>
<app-homework-middle-modal
  [infoVisible]="handleBussinessAddMidViewModelVisible"
  (callback)="handleBussinessAddMidModelSelect($event)"
></app-homework-middle-modal>
<!-- 浏览视图 -->
<app-dataview-info-modal
  [infoVisible]="handleBrowseViewModalVisible"
  (finished)="finishedBrowseViewModal($event)"
></app-dataview-info-modal>

<!-- 事件 -->
<app-event-modal
  [infoVisible]="handleEventModalVisible"
  [showModelSelect]="true"
  (finished)="finishEventModal($event)"
></app-event-modal>

<!--作业的新增或编辑的弹窗-->
<app-md-job-modal
  *ngIf="isShowJobModal || isShowJobSubModal"
  [data]="jobModalData"
  [isShowModal]="isShowJobModal"
  [isShowSubModal]="isShowJobSubModal"
  prefix="DataEntry"
  (close)="closeJobModal()"
  (updateMenu)="handleUpdateMenuJobModal($event)"
></app-md-job-modal>

<!-- 分类导航作业的新增或编辑的弹窗 -->
<app-md-navigate-job-modal
  *ngIf="isShowNavigateJobModal"
  [data]="navigateJobModalData"
  [isShowModal]="isShowNavigateJobModal"
  (close)="closeNavigateJobModal()"
></app-md-navigate-job-modal>

<!-- 集成与自动化 -->
<app-md-create-flow-modal
  *ngIf="createFlowModalVisible"
  [type]="'flow'"
  [sourceData]="flowModalData"
  (closeModal)="closeFlowModal()"
  (finished)="finishedFlowModal($event)"
></app-md-create-flow-modal>

<!-- 仪表盘 -->
<app-md-report-modal
  *ngIf="reportModalShow"
  [reportItem]="reportItem"
  [modalShow]="reportModalShow"
  (closeModal)="closeReportModal($event)"
></app-md-report-modal>

<!-- 数据侦测 -->
<app-md-create-flow-modal
  *ngIf="createDetectModalVisible"
  [sourceData]="detectModalData"
  [type]="'monitor'"
  (closeModal)="closeDetectModal($event)"
></app-md-create-flow-modal>

<app-delete-confirm
  [deleteBusinessObjectData]="deleteBusinessObjectData"
  [isVisible]="deleteBusinessObjectVisible"
  *ngIf="deleteBusinessObjectVisible"
  (ok)="deleteBusinessObjectOk($event)"
  (cancel)="cancelDeletBusinessObject()"
>
</app-delete-confirm>

<!-- API模型模型对象 -->
<app-api-model
  [addSourceType]="addSourceType"
  [transferModal]="addApiModal"
  (submitModal)="finishBusinessObjectModal($event); addApiModal = false"
  (closeModal)="addApiModal = false"
>
</app-api-model>

<!-- 作业迁移 -->
<app-backup-branch
  *ngIf="isDoMigrate"
  [backupConfig]="migrateBranchInfo"
  (callback)="handleMigrateCallback()"
></app-backup-branch>

<!-- 新增/编辑服务编排 -->
<app-service-orchestration-modal
  [infoVisible]="handleSoModalVisible"
  (onCancel)="handleSoCancel()"
  (onOk)="handleSoOk($event)"
></app-service-orchestration-modal>

<!-- 个案开窗 -->
<app-case-modal
  *ngIf="caseModalVisible"
  #caseModalRef
  [caseData]="caseData"
  [caseModal]="caseModalVisible"
  [mode]="'service'"
  (saveModal)="saveCase($event)"
  (closeModal)="caseModalVisible = false"
>
</app-case-modal>

<!--入参设置开窗-->
<ad-modal
  *ngIf="paramsSettingModal"
  nzClassName="action-modal"
  [nzWidth]="'800px'"
  [nzVisible]="paramsSettingModal"
  [nzTitle]="'so-入参设置' | translate"
  [nzFooter]="null"
  (nzOnCancel)="paramsSettingModal = false"
>
  <ng-container *adModalContent>
    <app-action-detail
      class="action-detail"
      [isTenantActive]="isTenantActive"
      [serviceComposerIdDisabled]="true"
      [activatedNode]="paramsSettingData"
      (savedSuccess)="handleSaveActionSuccess($event)"
    >
    </app-action-detail>
  </ng-container>
</ad-modal>

<!-- type选择界面 -->
<app-type-select-modal
  *ngIf="isTypeSelectModalShow"
  group="resource"
  [transferModal]="isTypeSelectModalShow"
  (selectType)="handleSelectType($event)"
  (closeModal)="isTypeSelectModalShow = false"
>
</app-type-select-modal>

<ad-modal
  nzClassName="dialog-modal2"
  [nzTitle]="nzModalTitle"
  [nzWidth]="'520px'"
  [(nzVisible)]="modelModal"
  [nzFooter]="null"
  [nzClosable]="false"
>
  <ng-template #nzModalTitle>
    <i
      adIcon
      [iconfont]="'iconexplain'"
      class="question-icon"
      nzTooltipTrigger="hover"
      nz-tooltip
      [nzTooltipTitle]="'dj-模型设计tips' | translate"
    ></i>
    {{ modalTitle }}
  </ng-template>
  <ng-container *adModalContent>
    <nz-spin [nzSpinning]="false">
      <data-other-service-form #modelDesignDataFormAdd type="ADD"></data-other-service-form>
      <div class="modal-footer">
        <button ad-button adType="default" (click)="handleAddClose()">
          {{ 'dj-取消' | translate }}
        </button>
        <button ad-button adType="primary" [nzLoading]="modalAdding" (click)="handleAddSure()">
          {{ 'dj-确定' | translate }}
        </button>
      </div>
    </nz-spin>
  </ng-container>
</ad-modal>

<nz-drawer
  [nzBodyStyle]="{ overflow: 'auto' }"
  [nzMaskClosable]="false"
  [nzWidth]="'88%'"
  [nzVisible]="soVisible"
  nzWrapClassName="so-drawer"
  [nzTitle]="'so-服务编排' | translate"
  (nzOnClose)="soVisible = false"
>
  <ng-container *nzDrawerContent>
    <app-service-orchestration-drawer
      (onSave)="appModelDrivenService.updateBusinessObjectMenuRefresh($event)"
      [params]="soData"
    ></app-service-orchestration-drawer>
  </ng-container>
</nz-drawer>

<nz-drawer
  [nzBodyStyle]="{ overflow: 'auto' }"
  [nzMaskClosable]="false"
  [nzWidth]="'88%'"
  [nzVisible]="detectDrawer"
  nzWrapClassName="so-drawer"
  [nzTitle]="'dj-数据侦测' | translate"
  (nzOnClose)="detectDrawer = false"
>
  <ng-container *nzDrawerContent>
    <app-detect-drawer
      [params]="detectData"
      (onSave)="appModelDrivenService.updateBusinessObjectMenuRefresh($event)"
    ></app-detect-drawer>
  </ng-container>
</nz-drawer>

<app-delete-model-confirm
  [title]="'dj-您确定删除该模型' | translate"
  [comfirmTitle]="'dj-删除模型提示' | translate"
  [confirmError]="'dj-模型名称保持一致' | translate"
  [deleteTips]="'dj-删除模型警示' | translate"
  [ok]="handleDeleteModel"
  [applyName]="modelName"
  [isVisible]="deleteModelVisible"
  (cancel)="handleCancelDeleteModel()"
>
</app-delete-model-confirm>

<app-ai-model
  scene="business"
  #aiModelComponent
  [ngStyle]="{ display: aiDrawerVisible ? 'block' : 'none' }"
  (close)="handleDrawerClose()"
  (callback)="handleAIBack($event)"
></app-ai-model>

<app-ai-business-object-model
  [businessObjectPerspective]="appModelDrivenService.businessObjectPerspective"
  [aiResult]="aiResult"
  [visible]="aiModelVisible"
  (callback)="handleAiCallback($event)"
  (cancel)="handleAiCancel()"
>
</app-ai-business-object-model>
