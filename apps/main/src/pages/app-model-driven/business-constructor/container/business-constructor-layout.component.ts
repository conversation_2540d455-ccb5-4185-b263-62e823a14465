import { Component, OnInit, HostListener, OnD<PERSON>roy } from '@angular/core';
import { AppService } from '../../../apps/app.service';
import { Subscription } from 'rxjs';
import { NavigationEnd, Router } from '@angular/router';
import { GlobalService } from 'common/service/global.service';
import { AppModelDrivenService } from '../service/app-model-driven.service';
import { MdWebapiService } from 'pages/app-model-driven/service/md-webapi.service';
import { TranslateService } from '@ngx-translate/core';
import { filter, debounceTime } from 'rxjs/operators';

@Component({
  selector: 'app-business-constructor-layout',
  templateUrl: './business-constructor-layout.component.html',
  styleUrls: ['./business-constructor-layout.component.less'],
})
export class BusinessConstructorLayoutComponent implements OnInit, OnDestroy {
  isMenuzCollapsed: boolean = false;
  appCode: string = '';

  menuCollapsedSubscription: Subscription;
  menuListSubscription: Subscription;
  eventSubscribe$: Subscription;
  routerSubscribe$: Subscription;

  // 激活的路由组件
  routerComponent: any;

  get tabIndex() {
    return this.appModelDrivenService.tabIndex;
  }

  constructor(
    public router: Router,
    public globalService: GlobalService,
    public appService: AppService,
    public appModelDrivenService: AppModelDrivenService,
    public translateService: TranslateService,
    public mdWebApi: MdWebapiService,
  ) {}

  ngOnInit() {
    this.router.events
      .pipe(
        filter((event) => event instanceof NavigationEnd),
        debounceTime(300),
      )
      .subscribe(() => {
        if (!this.appModelDrivenService.businessObjectPerspective) {
          this.appModelDrivenService.updateResourcePerspectiveExpandKeys(window.location.pathname);
        }
      });
    this.globalService.isAppPage = true;
    this.appService.mdCurrentUrlType = 'business-constructor';
    this.menuCollapsedSubscription = this.globalService?.menuCollapsedSubject$.subscribe(
      (data) => {
        this.isMenuzCollapsed = data;
      },
      (error) => {},
    );
  }

  ngAfterViewInit() {
    const win = window as any;
    win.requestIdleCallback(
      () => {
        const param = `?pattern=DATA_ENTRY&application=${this.appService?.selectedApp?.code}`;
        this.mdWebApi.loadAppBasic(param).subscribe(
          (res) => {
            if (res.code === 0) {
              this.mdWebApi.setDataEntryList('QUERY', res.data || []);
            }
          },
          () => {},
        );
      },
      { timeout: 3000 },
    );
  }

  ngOnDestroy(): void {
    this.globalService.isAppPage = false;
    this.menuCollapsedSubscription?.unsubscribe();
    this.eventSubscribe$?.unsubscribe();
    this.routerSubscribe$?.unsubscribe();
    this.mdWebApi.setDataEntryList('DELETE');
  }

  onCollapsedChange(val): void {
    this.appService.isExpend = !val;
    this.appModelDrivenService.sidebarWidth = val ? 0 : 200;
    this.globalService.setMenuCollapsed(val);
  }

  // 监听鼠标按下事件，开始拖动
  startDrag(event: MouseEvent): void {
    event.preventDefault();
    this.appModelDrivenService.isReSize = true;
  }

  // 监听鼠标移动事件，处理拖动效果
  @HostListener('document:mousemove', ['$event'])
  onMouseMove(event: MouseEvent): void {
    const t = null;
    if (!this.appModelDrivenService.isReSize) {
      return;
    }
    const mouseX = event.clientX;
    const windowWidth = window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth;
    const maxSideBarWidth = windowWidth / 2;
    let sidebarWidth = mouseX - 16;
    let _sidebarWidth = sidebarWidth < 200 ? 200 : sidebarWidth > maxSideBarWidth ? maxSideBarWidth : sidebarWidth;
    this.appModelDrivenService.sidebarWidth = _sidebarWidth;
  }

  // 监听鼠标释放事件，停止拖动
  @HostListener('document:mouseup', ['$event'])
  onMouseUp(): void {
    if (this.appModelDrivenService.isReSize) {
      this.appModelDrivenService.isReSize = false;
    }
  }

  /**
   * 获取当前激活的路由组件实例
   * @param component
   */
  handleActivate(component: any): void {
    this.routerComponent = component;
  }

  /**
   * 设置tabs激活的key
   * @param index
   */
  handleUpdateTabIndex(index: number): void {
    this.appModelDrivenService.tabIndex = index;
    if (!this.appModelDrivenService.businessObjectPerspective) {
      this.appModelDrivenService.updateResourcePerspectiveExpandKeys(window.location.pathname);
    }
  }
}
