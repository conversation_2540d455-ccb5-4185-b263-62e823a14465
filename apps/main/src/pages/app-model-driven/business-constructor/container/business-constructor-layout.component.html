<!-- 带左侧menu的路由 -->
<nz-spin class="nz-spin-content" [nzSpinning]="appModelDrivenService.loadingPage" nzTip="Loading...">
  <nz-layout class="app-md-layout-content">
    <nz-sider
      nzCollapsible
      [nzCollapsedWidth]="0"
      [ngClass]="{ 'expend-panel': !isMenuzCollapsed }"
      [nzZeroTrigger]="expendBtnTmpl"
      [(nzCollapsed)]="isMenuzCollapsed"
      (nzCollapsedChange)="onCollapsedChange($event)"
      class="intro-app-step-1"
      [style.width.px]="appModelDrivenService.sidebarWidth"
      [style.maxWidth.px]="appModelDrivenService.sidebarWidth"
      [style.minWidth.px]="appModelDrivenService.sidebarWidth"
      style="flex: auto"
    >
      <div class="menu-title">
        {{ 'dj-业务搭建' | translate }}
      </div>
      <div class="menu-body" [ngClass]="{ 'content-save-loading': appModelDrivenService.contentSaveLoading }">
        <ad-tabs
          [navStyle]="'button'"
          [nzSelectedIndex]="tabIndex"
          (nzSelectedIndexChange)="handleUpdateTabIndex($event)"
          class="tree-tabs"
          fontSize="12px"
        >
          <ad-tab [nzTitle]="'dj-按对象' | translate">
            <ng-container *ngIf="tabIndex === 0">
              <app-business-object-title></app-business-object-title>
              <app-business-object-menu [subRouteData]="appModelDrivenService.subRouteData"></app-business-object-menu>
            </ng-container>
          </ad-tab>
          <ad-tab [nzTitle]="'dj-按资源' | translate">
            <ng-container *ngIf="tabIndex === 1">
              <app-business-resource-title></app-business-resource-title>
              <app-business-resource-menu
                [subRouteData]="appModelDrivenService.subRouteData"
              ></app-business-resource-menu>
            </ng-container>
          </ad-tab>
        </ad-tabs>
      </div>
    </nz-sider>
    <div
      class="resize-line"
      [ngClass]="{ 'is-resize': appModelDrivenService.isReSize }"
      (mousedown)="startDrag($event)"
    ></div>
    <nz-content class="content-box" [ngClass]="{ 'expend-menu-panel': !isMenuzCollapsed, 'is-api-manage': false }">
      <router-outlet (activate)="handleActivate($event)"></router-outlet>
    </nz-content>
  </nz-layout>
</nz-spin>

<ng-template #expendBtnTmpl>
  <i
    adIcon
    [iconfont]="isMenuzCollapsed ? 'iconcaidanzhankai1' : 'iconcaidanshouqiicon1'"
    class="expend-btn iconfont"
  ></i>
</ng-template>

<app-business-modal-component [routerComponent]="routerComponent"></app-business-modal-component>
