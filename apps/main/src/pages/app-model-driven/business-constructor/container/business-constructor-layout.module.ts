import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { TranslateModule } from '@ngx-translate/core';
import { BusinessConstructorLayoutComponent } from './business-constructor-layout.component';
import { MenuOperatorComponent } from './components/menu-operator/menu-operator.component';
import { BusinessObjectTitleComponent } from './components/business-object-title/business-object-title.component';
import { BusinessObjectMenuComponent } from './components/business-object-menu/business-object-menu.component';
import { MdJobModalComponent } from './components/md-job-modal/md-job-modal.component';
import { MdSearchModalComponent } from './components/md-search-modal/md-search-modal.component';
import { MenuPopoverCustomComponent } from './components/menu-popover-custom/menu-popover-custom.component';

import { DataviewInfoModalModule } from '../../../app/data-entry/components/dataview-info-modal/dataview-info-modal.module';
import { EventModalModule } from '../event/components/event-modal/event-modal.module';
import { MdCreateFlowModalModule } from '../../integrated-automation/flow-list/md-create-flow-modal/md-create-flow-modal.module';
import { RouterModule } from '@angular/router';
import { NzLayoutModule } from 'ng-zorro-antd/layout';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzEmptyModule } from 'ng-zorro-antd/empty';
import { AdEmptyModule } from 'components/ad-ui-components/ad-empty/ad-empty.module';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { WorkDesignDetailFormModule } from '../../../app/data-entry/components/work-design-detail-form/work-design-detail-form.module';
import { NzFormModule } from 'ng-zorro-antd/form';
import { InputModule } from '../../../../components/form-components/input/input.module';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { BusinessObjectModalModule } from './components/business-object-modal/business-object-modal.module';

import { NzInputModule } from 'ng-zorro-antd/input';
import { AdTabsModule } from 'components/ad-ui-components/ad-tabs/ad-tabs.module';
import { DeleteConfirmModule } from './components/delete-confirm/delete-confirm.module';
import { TypeSelectModalComponent } from './components/business-object-title/type-select-modal/type-select-modal.component';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { ApiModelModule } from 'components/bussiness-components/api-model/api-model.module';
import { DirectiveModule } from 'common/directive/directive.module';
import { AuthManageModule } from '../../../../components/bussiness-components/layout/menu-bar/auth-manage/auth-manage.module';
import { WorkDesignApiDetailFormModule } from 'pages/app/data-entry/components/work-design-api-detail-form/work-design-api-detail-form.module';
import { WorkTypeSelectModalModule } from 'pages/app/data-entry/components/work-type-select-modal/work-type-select-modal.module';
import { HomeworkMiddleModalModule } from 'pages/app/data-entry/components/homework-middle-modal/homework-modal.module';
import { BackupBranchModule } from 'pages/app/data-entry/work-design/basic-data/components/backup-branch/backup-branch.module';
import { BusinessResourceTitleComponent } from './components/business-resource-title/business-resource-title.component';
import { ServiceOrchestrationModalComponent } from './components/service-orchestration-modal/service-orchestration-modal.component';
import { SoFlowModule } from 'pages/app/dtd/service-orchestration/so-flow/so-flow.module';
import { CaseModalModule } from 'components/bussiness-components/case-modal/case-modal.module';
import { ActionDetailModule } from 'pages/app/dtd/action/action-detail/action-detail.module';
import { BusinessModalComponentComponent } from './components/business-modal-component/business-modal-component.component';
import { DataEntryModule } from 'pages/app/data-entry/data-entry.module';
import { BusinessResourceMenuComponent } from './components/business-resource-menu/business-resource-menu.component';
import { ServiceOrchestrationDrawerComponent } from './components/service-orchestration-drawer/service-orchestration-drawer.component';
import { PublishButtonModule } from 'components/bussiness-components/module-publish-button/module-publish-button.module';
import { DetectDrawerComponent } from './components/detect-drawer/detect-drawer.component';
import { DetectModule } from '../../../app/dtd/detect/detect.module';
import { DeleteModelConfirmModule } from './components/delete-model-confirm/delete-model-confirm.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { AiModelModule } from 'components/page-design/entries/model-designer-new/components/ai-model/ai-model.module';
import { AiBusinessObjectModelModule } from './components/ai-business-object-model/ai-business-object-model.module';
import { MdNavigateJobModalComponent } from './components/md-navigate-job-modal/md-navigate-job-modal.component';
import { MdSimpleModelListModalComponent } from './components/md-navigate-job-modal/components/md-simple-model-list-modal/md-simple-model-list-modal.component';
import { AthenaTableModule } from 'components/bussiness-components/athena-table/athena-table.module';
import { QueryPlanModalModule } from '../../../app/data-entry/components/query-plan-modal/query-plan-modal.module';
import { HomeworkModalModule } from '../../../app/data-entry/components/homework-modal/homework-modal.module';

@NgModule({
  declarations: [
    BusinessConstructorLayoutComponent,
    MenuOperatorComponent,
    BusinessObjectTitleComponent,
    BusinessObjectMenuComponent,
    MdJobModalComponent,
    MdSearchModalComponent,
    MenuPopoverCustomComponent,
    TypeSelectModalComponent,
    BusinessResourceTitleComponent,
    ServiceOrchestrationModalComponent,
    BusinessModalComponentComponent,
    BusinessResourceMenuComponent,
    ServiceOrchestrationDrawerComponent,
    DetectDrawerComponent,
    MdNavigateJobModalComponent,
    MdSimpleModelListModalComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    NzEmptyModule,
    AdEmptyModule,
    TranslateModule,
    AdButtonModule,
    DataviewInfoModalModule,
    EventModalModule,
    MdCreateFlowModalModule,
    RouterModule,
    NzLayoutModule,
    NzSpinModule,
    NzMenuModule,
    AdIconModule,
    AdModalModule,
    NzToolTipModule,
    AdSelectModule,
    NzDropDownModule,
    WorkDesignDetailFormModule,
    NzFormModule,
    InputModule,
    NzCheckboxModule,
    BusinessObjectModalModule,
    NzInputModule,
    AdTabsModule,
    DeleteConfirmModule,
    NzGridModule,
    ApiModelModule,
    AuthManageModule,
    DirectiveModule,
    WorkDesignApiDetailFormModule,
    WorkTypeSelectModalModule,
    HomeworkMiddleModalModule,
    BackupBranchModule,
    NzDrawerModule,
    SoFlowModule,
    CaseModalModule,
    ActionDetailModule,
    DataEntryModule,
    PublishButtonModule,
    DetectModule,
    DeleteModelConfirmModule,
    AiModelModule,
    AiBusinessObjectModelModule,
    AthenaTableModule,
    QueryPlanModalModule,
    HomeworkModalModule,
  ],
})
export class BusinessConstructorLayoutModule {}
