import { Component, Input, OnInit } from '@angular/core';
import { CotnentTbbService } from './content-tbb.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { AdUserService } from 'pages/login/service/user.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { LocaleService } from 'common/service/locale.service';
import { AppService } from 'pages/apps/app.service';
import { ActivatedRoute } from '@angular/router';

@Component({
  selector: 'app-content-tbb',
  templateUrl: './content-tbb.component.html',
  styleUrls: ['./content-tbb.component.less'],
  providers: [CotnentTbbService],
})
export class ContentTbbComponent implements OnInit {
  @Input() reportData: any;

  isv: string = 'lowCode';
  tbbReportUrl: string;
  reportUrl: SafeResourceUrl;
  reportLoading: boolean = true;
  routeParams: any;
  historyModalProps: HistoryModalProps = {
    transferModal: false,
    code: '',
    collection: 'activityConfigs',
  };
  paneMenuItem: any;
  serviceUrl: string;
  constructor(
    protected configService: SystemConfigService,
    protected userService: AdUserService,
    public appService: AppService,
    private languageService: LocaleService,
    private domSanitizer: DomSanitizer,
    private activatedRoute: ActivatedRoute,
    private service: CotnentTbbService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.serviceUrl = url;
    });
    this.configService.get('tbbReportUrl').subscribe((tbbReportUrl) => {
      this.tbbReportUrl = tbbReportUrl;
    });
  }

  ngOnInit() {
    this.handleReportUrl();
  }

  /**
   *
   * @param params
   */
  handleReportUrl() {
    const appCode = this.appService?.selectedApp?.code;
    const token = this.userService.getUser('iamToken');
    const language = this.languageService?.currentLanguage || 'zh_CN';
    let reportUrl = `${this.tbbReportUrl}/index.html?#/sso-login?dwLang=${language}&appCode=${appCode}&dashboardId=${this.reportData.resCode}&designMode=edit&isv=${this.isv}&appType=${this.appService.selectedApp?.appType}`;
    this.reportLoading = true;
    this.service.getTbbInnerToken().subscribe(
      (res) => {
        this.reportLoading = false;
        if (res.code === 0) {
          const tbbInnerToken = res.data;
          reportUrl += `&innerToken=${tbbInnerToken}&lowCodeUrl=${this.serviceUrl}`;
          this.reportUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(reportUrl);
          this.reportLoading = false;
        }
      },
      () => {
        this.reportLoading = false;
      },
    );
  }

  openModifyHistoryModal() {
    this.historyModalProps.code = this.reportData.code;
    this.historyModalProps.transferModal = true;
  }
}
