import { Component, OnInit, AfterViewInit } from '@angular/core';
import { Router, ActivatedRoute } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { FormBuilder, Validators } from '@angular/forms';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { AppService } from '../../apps/app.service';
import { IntroService } from 'common/service/intro/intro.service';
import { AppInfoService } from 'pages/app/app-info/app-info.service';
import { overviewTotal } from '../utils/operation';
import { AppTypes } from '../../app/typings';
import { GlobalService } from 'common/service/global.service';
import _ from 'lodash';
import { AdUserService } from 'pages/login/service/user.service';
import { TenantService } from 'pages/login/service/tenant.service';

@Component({
  selector: 'app-md-application-overview',
  templateUrl: './application-overview.component.html',
  styleUrls: ['./application-overview.component.less'],
  providers: [AppInfoService],
})
export class ApplicationOverviewComponent implements OnInit, AfterViewInit {
  loading: boolean;
  app: any; // 解决方案数据
  overviewTotal: any[];
  favoriteData: any[] = [];
  dataEntryData: any[] = [];
  allData: any;
  allFavorite: any;

  appInfoVisible: boolean;
  appForm: any;
  lang: any;
  /**
   * 缓存接口的解决方案信息
   */
  cacheApplicationInfo;

  targetList: any[] = [];
  serviceVisible: boolean = false;

  logVisible: boolean = false;
  codeList: any[] = [];

  isTenantActive = false; // 租户级 开发平台 是否激活

  showCustom = false; // 定制配置
  enableOpenVscode: boolean = false; // 是否开启打开vscode
  get isModelDriveApp() {
    return GlobalService.appType === AppTypes.MODEL_DRIVEN;
  }

  get highCodeApp() {
    return AppTypes.HIGH_CODE;
  }

  appType = AppTypes.MODEL_DRIVEN; // 应用类型
  /**
   * 是不是体验用户 + 体验环境
   */
  get userIsExperience() {
    return this.tenantService.isEduAndisExperience();
  }

  developerUrl: string; // 开发者门户
  hiddenMenuByEnv: boolean = false; //某些环境需要隐藏菜单，读取配置项
  constructor(
    public appService: AppService,
    private router: Router,
    private activatedRoute: ActivatedRoute,
    public translateService: TranslateService,
    private message: NzMessageService,
    private fb: FormBuilder,
    private languageService: LocaleService,
    private appInfoService: AppInfoService,
    private introService: IntroService,
    private adUserService: AdUserService,
    protected configService: SystemConfigService,
    private tenantService: TenantService,
  ) {
    this.configService.getConfig().subscribe((config: any) => {
      this.developerUrl = config.developerUrl;
      this.hiddenMenuByEnv = config.hiddenMenuByEnv === 'true';
    });
    this.configService.get('enableOpenVscode').subscribe((val) => {
      this.enableOpenVscode = val;
    });
  }

  async ngOnInit() {
    this.updateTenant();
    this.app = this.appService?.selectedApp;
    this.overviewTotal = overviewTotal;
    this.appForm = this.fb.group({
      code: [this.app?.code || null, [Validators.required]],
      name: [this.app?.name || null, [Validators.required, Validators.pattern('^.{0,40}$')]],
      description: [this.app?.description || null, [Validators.required]],
    });

    if (this.isModelDriveApp) {
      this.cacheApplicationInfo = await this.queryApplicationDetail();
      this.appForm.addControl('serviceCode', this.fb.control(null, Validators.required));

      // 获取已有解决方案后端
      this.getServiceCodeList();
    }
    if (!!this.app?.code) {
      this.appForm.get('code').disable();
    }
    this.handleLoadOverView();
    this.getTargetList();
  }

  async queryApplicationDetail() {
    const response = await this.appService.getApplicationDetail(this.appService?.selectedApp?.code).toPromise();
    if (response.code === 0 && response.data) {
      return response.data;
    }
    return null;
  }

  ngAfterViewInit() {
    this.introService.play('appStep');
  }

  getServiceCodeList() {
    this.appService.getServiceCodeList().subscribe(
      (res) => {
        if (res.code === 0) {
          this.codeList = res.data?.map((item) => {
            return {
              label: item,
              value: item,
            };
          });
        }
      },
      (error) => {},
    );
  }
  // 获取总览数据
  handleLoadOverView(): void {
    this.loading = true;
    this.appInfoService.loadMAppOverView(this.appService?.selectedApp?.code).subscribe(
      (res) => {
        if (res.code === 0) {
          this.allData = res.data;
          this.overviewTotal.forEach((module) => {
            module.children.forEach((item) => {
              item.total = res.data?.applicationOverView?.[item.type] || 0;
            });
          });
          this.dataEntryData = res.data?.dataEntryList || [];
          this.handleLoadFavorite();
          this.checkInitHandleCustom();
        }
      },
      () => {
        this.handleLoadFavorite();
      },
    );
  }

  // 跳转开发门户的部署专项页面
  goPortal() {
    window.open(this.developerUrl, '_blank');
  }

  // 查看日志
  getRecord() {
    this.logVisible = true;
  }

  handleCloseModal() {
    this.logVisible = false;
  }

  // 下载源码
  downloadCode() {
    this.appService.downloadSourceCode(this.appService?.selectedApp?.code).subscribe(
      (res) => {
        const link = document.createElement('a');
        const blob = new Blob([res], { type: 'application/octet-stream' });
        link.setAttribute('href', window.URL.createObjectURL(blob));
        link.setAttribute('download', this.translateService.instant('dj-源码') + '.zip');
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      },
      (err) => {},
    );
  }

  // 打开前端代码
  openFrontCode() {
    const tenantId = this.adUserService.getUser('tenantId'); // 可能后边有用
    const token = this.adUserService.getUser('iamToken');
    const appCode = this.appService?.selectedApp?.code;
    const route = 'init';

    const vscodeProtocol =
      `@vscodeinit&route=${route}&env=@env&version=@version&fileId=@fileId` + `&token=${token}&code=${appCode}`;

    const encodedUrl = encodeURIComponent(vscodeProtocol);
    window.open(`/open-vscode?url=${encodedUrl}`, '_blank');
  }

  // 获取关注
  handleLoadFavorite(): void {
    this.appInfoService.loadFavorite(this.appService?.selectedApp?.code).subscribe(
      (res) => {
        if (res.code === 0) {
          this.allFavorite = res.data || [];
          this.favoriteData = res.data || [];
          this.dataEntryData.forEach((item) => {
            if (this.favoriteData.find((fav) => item.code === fav.code)) {
              item.favorited = true;
            }
          });
          this.loading = false;
        }
      },
      () => {
        this.loading = false;
      },
    );
  }

  // 关注操作
  handleFavorite(params: any): void {
    this.appInfoService.saveCollect(params).subscribe(
      (res) => {
        this.message.success(this.translateService.instant('dj-关注成功！'));
        this.allFavorite = this.allFavorite || [];
        this.allFavorite.unshift(params);
        this.favoriteData = this.allFavorite;
        this.dataEntryData.forEach((item) => {
          if (item.code === params.code) {
            item.favorited = true;
          }
        });
      },
      () => {},
    );
  }

  // 取消关注
  handleCancelFavorite(params: any): void {
    this.appInfoService.cancelCollect(params).subscribe(
      (res) => {
        this.message.success(this.translateService.instant('dj-取消关注成功！'));
        this.allFavorite = (this.allFavorite || []).filter((item) => item.code !== params.code);
        this.favoriteData = this.allFavorite;
        this.dataEntryData.forEach((item) => {
          if (item.code === params.code) {
            item.favorited = false;
          }
        });
      },
      () => {},
    );
  }

  // 作业点击跳转
  handleLinkModule(event) {
    if (event.type === 'data-entry' || event.type === 'DATA_ENTRY') {
      const { businessCode, code, category } = event.work;
      let type = category == 'SIGN-DOCUMENT' ? 'design' : 'browse';

      const navigateUrl = `page-design/${businessCode}/${code}/${type}`;
      this.appService.mdLastActiveUrl = '';
      this.router.navigate([`/app/business-constructor/${navigateUrl}`], {
        queryParams: {
          appCode: this.appService?.selectedApp?.code,
        },
      });
    }
  }

  validVersion(): void {
    this.appService.validVersion().subscribe();
  }

  handleSetApp() {
    this.appForm.reset();
    this.appForm.patchValue({
      code: this.app?.code,
      name: this.app?.name,
      description: this.app?.description,
    });
    this.getTargetList();
    this.handleLang(this.app);
    this.appInfoVisible = true;
  }
  // 处理国际化
  handleLang(data: any): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const format = (key: any): any => {
      const lang = {
        zh_CN: data?.lang?.[key]?.zh_CN || '',
        zh_TW: data?.lang?.[key]?.zh_TW || '',
        en_US: data?.lang?.[key]?.en_US || '',
      };
      if (!lang[language]) {
        lang[language] = data[key] || '';
      }
      return lang;
    };
    this.lang = {
      ...(data?.lang || {}),
      name: format('name'),
      description: format('description'),
    };
  }

  // 赋值
  handlePatchApp(key: any, data: any): void {
    this.appForm.patchValue({ [key]: data?.value });
    if (data.needLang) {
      this.lang = {
        ...(this.lang || {}),
        [key]: data.lang?.value,
      };
    }
  }

  onVisibleChange(visible) {
    this.appInfoVisible = visible;
  }

  // 保存
  handleSaveApp(): void {
    for (const i of Object.keys(this.appForm?.controls)) {
      this.appForm.controls[i].markAsDirty();
      this.appForm.controls[i].updateValueAndValidity();
    }
    if (this.appForm.valid) {
      const values = this.appForm.getRawValue();
      const param = {
        ...(values || {}),
        lang: this.lang,
      };

      // 如果是模型驱动解决方案 调用新的修改解决方案的接口
      if (this.isModelDriveApp) {
        // 针对模型驱动解决方案重组下入参
        const temp = _.omit(param, ['createType', 'serviceCode', 'envCode', 'hostId']);
        const paramV2 = {
          application: {
            ...temp,
          },
          ...values,
        };
        this.appService.saveAppV2(paramV2).subscribe((res) => {
          this.successCallback(param);
        });
        return;
      }
      this.appService.saveApp(param).subscribe((res) => {
        if (res.code === 0) {
          this.successCallback(param);
        }
      });
    }
  }
  successCallback(param) {
    const selectedApp = {
      ...(this.appService?.selectedApp || {}),
      ...param,
    };
    this.appService.selectedApp = selectedApp;
    this.app = selectedApp;
    // this.message.success(this.translateService.instant('dj-保存成功'));
    this.appInfoVisible = false;
  }
  // 打开解决方案后端开窗
  handleOpenModel() {
    this.serviceVisible = true;
    // this.getTargetList(true);
  }

  // 获取已经设置了的解决方案后端
  getTargetList() {
    this.appInfoService.postTargetList().subscribe((res) => {
      if (res.code === 0) {
        this.targetList = res?.data.map((item) => {
          return item.serviceCode;
        });
        if (this.isModelDriveApp) {
          this.appForm.patchValue({
            serviceCode: this.targetList,
          });
        }
      }
    });
  }

  // 关闭解决方案后端开窗
  handleCloseModel(e) {
    this.serviceVisible = false;
    // this.getTargetList(false);
  }

  updateTenant() {
    this.isTenantActive = !!this.adUserService.getUser('isTenantActive');
  }

  // 定制配置
  handleCustom(): void {
    this.showCustom = true;
  }

  // 初始化时是否需要打开定制配置
  checkInitHandleCustom() {
    if (this.activatedRoute?.snapshot?.queryParams?.isHandleCustom) this.handleCustom();
  }

  /**
   * 进入发布
   */
  goHighCode() {
    let params: any = { appCode: this.appService?.selectedApp?.code, extensionApp: 'highCode' };
    const url = this.router.serializeUrl(this.router.createUrlTree(['app'], { queryParams: params }));
    window.open(url, '_blank');
  }

  // 跳转到 开放与依赖
  handleJumpOpennessAndDependency(navigateUrl: string) {
    this.router.navigate([`/app/application-overview/${navigateUrl}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }
}
