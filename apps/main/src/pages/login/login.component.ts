import { Component, OnInit, AfterViewInit, OnDestroy } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Validators, FormBuilder, FormGroup } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { Observable } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { HttpClient } from '@angular/common/http';
import { AdAuthService } from './service/auth.service';
import { AdUserService } from './service/user.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TenantService } from './service/tenant.service';
import { concatMap } from 'rxjs/operators';
import { to, validatorForm } from 'common/utils/core.utils';
import { HomeIntroService } from 'common/service/intro/home-intro/home-intro.service';
import { IndividualService } from 'pages/individual/individual.service';
import { LoginType, AreaPhoneConfig } from './config';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.less'],
})
export class LoginComponent implements OnInit, AfterViewInit, OnDestroy {
  LoginType = LoginType;
  AreaPhoneConfig = AreaPhoneConfig;
  validateForm: FormGroup;
  returnUrl: string;
  // 是否自動導頁, 用來控制是否顯示租戶清單.
  // private isAutoForward = false;
  passowrdVisible: boolean = false;
  serverErrorMessage: string = null;
  url: string;
  digiwincloudUrl: string;
  digiwinGitUrl: string;
  clientId: string;
  // 是否达到推荐分辨率
  isRecommendedResolution: boolean = screen.width < 1920 && screen.height < 1080;
  currTenantList = []; // 租户清单列表
  submitLoading: boolean = false;
  tenantActivated: boolean = true;
  showTenant: boolean = false;
  consoleUrl: string = 'https://console-test.digiwincloud.com.cn/';
  athenaUrl: string;
  athenaDesignerUrl: string;
  tenantDesignerUrl: string;
  tbbLoginUrl: string;
  _consoleUrl: string = '';
  isAgreement: boolean = true;
  btnCanClick: boolean = false;

  isTenantLogin: boolean = false; // 是否是租户级登陆
  cloud: string;
  platformCategory: any;
  envAlias: string;
  iamUrl: string;
  currentYear: number = new Date().getFullYear();
  /**
   * 是否第一次发送验证码, 切换登录方式重置
   */
  firstSendSmsCode: boolean = true;
  /**
   * 发送验证码倒计时句柄
   */
  sendSmsCodeTimer: number = null;
  /**
   * 发送验证码倒计时
   */
  smsCounter: number = 60;
  /**
   * 登录方式, 默认邮箱
   */
  loginType: LoginType = LoginType.EMAIL;

  get currentLang() {
    return this.translateService.currentLang;
  }

  get athenaLogoSrc() {
    if (this.platformCategory === 'TENANT') {
      if (this.cloud === 'MICROSOFT') {
        if (this.currentLang === 'zh_CN') {
          // 租户级微软中文
          return `assets/img/login/tenant-logo-micro-zh_CN-left.svg`;
        } else {
          // 租户级微软繁体英文
          return `assets/img/login/tenant-logo-micro-zh_TW-left.svg`;
        }
      } else {
        if (this.currentLang === 'zh_CN') {
          // 租户级华为中文
          return `assets/img/login/tenant-logo-zh_CN-left.svg`;
        } else {
          // 租户级华为繁体英文
          return `assets/img/login/tenant-logo-zh_TW-left.svg`;
        }
      }
    } else if (this.individualService.individualCase) {
      if (this.cloud === 'MICROSOFT') {
        if (this.currentLang === 'zh_CN') {
          return `assets/img/login/individual-logo-micro-zh_CN-left.svg`;
        } else {
          return `assets/img/login/individual-logo-micro-zh_TW-left.svg`;
        }
      }
      if (this.currentLang === 'zh_CN') {
        return `assets/img/login/individual-logo-zh_CN-left.svg`;
      } else {
        return `assets/img/login/individual-logo-zh_TW-left.svg`;
      }
    } else {
      if (this.cloud === 'MICROSOFT') {
        if (this.currentLang === 'zh_CN') {
          // 普通微软中文
          return `assets/img/login/logo-micro-zh_CN-left.svg`;
        } else {
          // 普通微软繁体英文
          return `assets/img/login/logo-micro-zh_TW-left.svg`;
        }
      } else {
        if (this.currentLang === 'zh_CN') {
          // 普通华为中文
          return `assets/img/login/logo-zh_CN-left.svg`;
        } else {
          // 普通华为繁体英文
          return `assets/img/login/logo-zh_TW-left.svg`;
        }
      }
    }
  }

  get userUseAgreement() {
    // 鼎捷雅典娜用户使用协议
    if (this.cloud === 'MICROSOFT') {
      if (this.currentLang === 'zh_TW') {
        return 'https://market.digiwincloud.com/assets/policy/dwca-user-tw-zh_tw.html';
      } else {
        return 'https://market.digiwincloud.com/assets/policy/dwca-user-tw-zh_cn.html';
      }
    } else {
      if (this.currentLang === 'zh_TW') {
        return 'https://market.digiwincloud.com.cn/assets/policy/dwca-user-cn-zh_tw.html';
      } else {
        return 'https://market.digiwincloud.com.cn/assets/policy/dwca-user-cn-zh_cn.html';
      }
    }
  }

  get containerPolicy() {
    // 鼎捷云平台容器服务使用协议
    if (this.cloud === 'MICROSOFT') {
      if (this.currentLang === 'zh_TW') {
        return 'https://market.digiwincloud.com/assets/policy/legal-serverless-tw-zh_tw.html';
      } else {
        return 'https://market.digiwincloud.com/assets/policy/legal-serverless-tw-zh_cn.html';
      }
    } else {
      if (this.currentLang === 'zh_TW') {
        return 'https://market.digiwincloud.com.cn/assets/policy/legal-serverless-cn-zh_tw.html';
      } else {
        return 'https://market.digiwincloud.com.cn/assets/policy/legal-serverless-cn-zh_cn.html';
      }
    }
  }

  get privacyPolicy() {
    // 隐私政策
    if (this.cloud === 'MICROSOFT') {
      if (this.currentLang === 'zh_TW') {
        return 'https://market.digiwincloud.com/assets/policy/dwc-privacy-tw-zh_tw.html';
      } else {
        return 'https://market.digiwincloud.com/assets/policy/dwc-privacy-tw-zh_cn.html';
      }
    } else {
      if (this.currentLang === 'zh_TW') {
        return 'https://market.digiwincloud.com.cn/assets/policy/dwc-privacy-cn-zh_tw.html';
      } else {
        return 'https://market.digiwincloud.com.cn/assets/policy/dwc-privacy-cn-zh_cn.html';
      }
    }
  }

  /**
   * 发送验证码按钮是否可以点击
   */
  get sendSMBtnEnable() {
    return (
      this.loginType === LoginType.PHONE && this.sendSmsCodeTimer === null && this.validateForm.get('userId')?.valid
    );
  }

  get sendSMBtnText() {
    if (this.firstSendSmsCode) {
      return this.translateService.instant('dj-login-获取验证码');
    } else {
      if (this.sendSmsCodeTimer === null) {
        return this.translateService.instant('dj-login-重新获取');
      } else {
        return `${this.translateService.instant('dj-login-重新获取')}(${this.smsCounter})`;
      }
    }
  }

  /**
   * 不同登录类型，一些页面上展示的不同信息
   */
  get loginTypeInfo() {
    switch (this.loginType) {
      case LoginType.EMAIL: {
        return {
          switchIcon: 'iconshouji-xian',
          switchText: this.translateService.instant('dj-login-手机验证码登入'),
        };
      }
      case LoginType.PHONE: {
        return {
          switchIcon: 'iconyonghu-xian',
          switchText: this.translateService.instant('dj-login-账号登入'),
        };
      }
      default: {
        return {
          switchIcon: 'iconyonghu-xian',
          switchText: this.translateService.instant('dj-login-账号登入'),
        };
      }
    }
  }

  constructor(
    private http: HttpClient,
    private fb: FormBuilder,
    private loginService: AdAuthService,
    private route: ActivatedRoute,
    private router: Router,
    private translateService: TranslateService,
    private configService: SystemConfigService,
    private tenantService: TenantService,
    private userService: AdUserService, // private i18n: NzI18nService
    private message: NzMessageService,
    public individualService: IndividualService,
  ) {
    this.configService.getConfig().subscribe((config: any) => {
      const {
        adesignerUrl,
        digiwincloudUrl,
        clientId,
        consoleUrl,
        cloud,
        platformCategory,
        envAlias,
        iamUrl,
        athenaUrl,
        tenantDesignerUrl,
        athenaDesignerUrl,
        tbbLoginUrl,
      } = config;

      this.digiwinGitUrl = 'https://athena-devops-gitlab.digiwincloud.com.cn';
      this.url = adesignerUrl;
      this.digiwincloudUrl = digiwincloudUrl;
      this.clientId = clientId;
      this._consoleUrl = consoleUrl;
      this.athenaUrl = athenaUrl;
      this.tenantDesignerUrl = tenantDesignerUrl;
      this.athenaDesignerUrl = athenaDesignerUrl;
      this.tbbLoginUrl = tbbLoginUrl;
      this.cloud = cloud;
      // this.platformCategory = 'TENANT';
      // this.cloud = 'MICROSOFT';
      this.platformCategory = platformCategory;
      this.envAlias = envAlias;
      this.iamUrl = iamUrl;
    });
  }

  ngOnInit(): void {
    this.returnUrl =
      this.route.snapshot.queryParamMap.get('returnUrl') ??
      (this.individualService?.individualCase ? '/individual/apps' : '/'); // 登入後的導頁

    this.validateForm = this.fb.group({
      userId: [null, [Validators.required]],
      password: [null, [Validators.required]],
      remember: [true],
      autoLogin: [false],
      agreeAgreement: [true],
    });

    if (this.loginService.isLoggedIn === true) {
      const userInfo = this.userService.getUserInfo();
      if (userInfo.tenantSid) {
        // // 如果已經 login 了, 直接導頁.
        this.afterLogin();
        return;
      }
    }

    this.validateForm.valueChanges.subscribe((res): void => {
      this.serverErrorMessage = null;
      if (this.validateForm.valid) {
        this.btnCanClick = !!res.agreeAgreement;
      } else {
        this.btnCanClick = false;
      }
    });
  }

  ngAfterViewInit(): void {}

  /**
   * 点击登录
   */
  async submitForm() {
    this.serverErrorMessage = null;

    if (!this.isAgreement) {
      return;
    }

    if (this.validateForm.invalid) {
      validatorForm(this.validateForm);
      return;
    }

    const { userId: name, password, verificationCode } = this.validateForm.getRawValue();

    this.submitLoading = true;

    const result = await this.getUniqueId().toPromise();
    const deviceId = result?.data;
    const identityType = this.loginType === LoginType.PHONE ? 'verificationCode' : 'query';
    const [err, res]: any = await to(
      this.loginJM({ name, password, verificationCode, deviceId, identityType }).toPromise(),
    );

    if (err) {
      this.serverErrorMessage = err?.error?.message;
      this.submitLoading = false;
      return;
    }

    if (res?.code === 0) {
      HomeIntroService.initShowHomeIntro();
      console.log(res);
      this.loginNomal(res);
    }
  }

  /**
   * 登录后根据returnUrl跳转对应页
   */
  private afterLogin(): void {
    this.userService.isLoggedIn$.next(true);
    this.returnUrl = this.returnUrl || (this.individualService?.individualCase ? '/individual/apps' : '/');
    setTimeout(() => {
      this.router.navigateByUrl(this.returnUrl);
    }, 0);
  }

  /**
   * 登录接口
   * @param param
   * @returns
   */
  login(param: any): Observable<any> {
    const url = `${this.url}/athena-designer/user/v2/login`;
    return this.http.post(url, param);
  }

  /**
   * 获取设备唯一id
   * @returns
   */
  getUniqueId(): Observable<any> {
    const url = `${this.iamUrl}/api/iam/v2/device/id`;
    return this.http.get(url);
  }

  /**
   * 密码可见性
   */
  passowrdVisibleChange(): void {
    this.passowrdVisible = !this.passowrdVisible;
  }

  /**
   * 登入加密.
   * param userConfig 登入的資訊.
   * return 返回Observable.
   */
  loginJM(userConfig: any): Observable<any> {
    return this.loginService.getLoginApiBody(userConfig).pipe(
      concatMap((apiBody) => {
        return this.login(apiBody);
      }),
    );
  }

  handleBack() {
    this.showTenant = false;
    this.submitLoading = false;
  }

  /**
   * 选择租户
   * @param tenant
   */
  async handleSelectTenant(tenant) {
    const { tenantId, tenantName, tenantSid, testTenant, experience } = tenant;
    this.userService.setUserInfo({ tenantId, tenantName, tenantSid, testTenant, experience });
    await this.tenantService.tokenRefreshTenant(tenantName, tenantId, tenantSid, testTenant, experience).toPromise();
    this.afterLogin();
  }

  /**
   * 登录后获取租户清单、设置userInfo
   * @param res
   * @returns
   */
  async loginNomal(res) {
    const userData = {
      agreeAgreement: res.data.agreeAgreement, // #46816
      tenantId: res.data.defaultTenantId,
      tenantName: res.data.defaultTenantName,
      iamToken: res.data.iamToken,
      userId: res.data.userId,
      state: res.data.state,
      token: res.data.token,
      userName: res.data.userName,
      name: res.data.name,
      role: res.data.role,
      // currentBranch: res.data?.currentBranch || '',
    };
    this.consoleUrl = `${this._consoleUrl}/sso-login?userToken=${res.data.iamToken}`;
    this.loginService.setLogined(userData);

    // 获取租户列表
    let [err, lists] = await to(this.tenantService.getTenants().toPromise());
    // 获取个案租户列表
    if (this.platformCategory !== 'TENANT') {
      const [individualErr, individualLists] = await to(this.tenantService.getIndividualCaseTenants().toPromise());
      // 如果在个案设计器内，重新赋值
      if (this.individualService.individualCase) {
        err = individualErr;
        lists = individualLists;
      }
    }

    if (err) {
      this.showTenant = true;
      this.tenantActivated = false;
      return;
    }

    if (lists) {
      this.currTenantList = lists;
      if (this.currTenantList?.length > 0) {
        this.tenantActivated = true;

        // 只有一个租户则直接使用
        if (this.currTenantList.length === 1) {
          this.showTenant = false;
          const { tenantName, tenantId, tenantSid, testTenant, experience } = this.currTenantList[0];
          const [err, res] = await to(
            this.tenantService.tokenRefreshTenant(tenantName, tenantId, tenantSid, testTenant, experience).toPromise(),
          );

          this.afterLogin();
          return;
        }
        this.showTenant = true;
      } else {
        this.showTenant = true;
        this.tenantActivated = false;
      }
    }
  }

  handleAgreement(isAgreement) {
    this.isAgreement = isAgreement;
  }
  get noSubmit() {
    if (this.loginType === LoginType.EMAIL) {
      return this.validateForm.get('userId').invalid || this.validateForm.get('password')?.invalid;
    } else if (this.loginType === LoginType.PHONE) {
      return this.validateForm.get('userId').invalid || this.validateForm.get('verificationCode')?.invalid;
    }
  }

  handlesLinkTenant(isTenantActive: boolean) {
    const url = isTenantActive ? this.tenantDesignerUrl : this.athenaDesignerUrl;
    window.open(url, '_blank');
  }

  handlesLinkOther(type: any): void {
    const url = type === 'console' ? this._consoleUrl : this.athenaUrl;
    window.open(url, '_blank');
  }

  handleJumpToTbb(): void {
    window.open(this.tbbLoginUrl, '_blank');
  }

  //api免登录快速链接
  handleJumpToAPI(): void {
    this.router.navigateByUrl('/api-query-view');
  }

  handleSendSmsCode() {
    // this.loginService.sendSmsCodeService()
  }

  resetSendSmsCodeTimer() {
    if (this.sendSmsCodeTimer) {
      clearInterval(this.sendSmsCodeTimer);
      this.sendSmsCodeTimer = null;
      this.smsCounter = 60;
    }
  }

  /**
   * 发送验证码
   */
  sendSMCode() {
    const userId = this.validateForm.get('userId').value;
    if (!userId || !this.sendSMBtnEnable) {
      return;
    }
    const currentPhoneArea = this.validateForm.get('currentPhoneArea').value;
    const areaPhoneInfo = AreaPhoneConfig.find((item) => item.key === currentPhoneArea);
    if (!areaPhoneInfo.reg.test(userId)) {
      this.message.error(this.translateService.instant('dj-login-手机号格式不正确'));
      return;
    }
    this.sendSmsCodeTimer = window.setInterval(() => {
      this.smsCounter--;
      if (this.smsCounter <= 1) {
        this.resetSendSmsCodeTimer();
      }
    }, 1000);
    this.loginService.sendSmsCodeService({ userId, currentPhoneArea }).subscribe(
      (res) => {
        if (res.code === 200) {
          if (this.firstSendSmsCode) {
            this.firstSendSmsCode = false;
          }
        } else {
          this.resetSendSmsCodeTimer();
        }
      },
      () => {
        this.resetSendSmsCodeTimer();
      },
    );
  }

  rebuildForm(loginType: LoginType) {
    switch (loginType) {
      case LoginType.EMAIL: {
        this.validateForm.removeControl('verificationCode');
        this.validateForm.removeControl('currentPhoneArea');
        this.validateForm.addControl('password', this.fb.control(null, [Validators.required]));
        break;
      }
      case LoginType.PHONE: {
        this.validateForm.removeControl('password');
        this.validateForm.addControl('verificationCode', this.fb.control(null, [Validators.required]));
        this.validateForm.addControl('currentPhoneArea', this.fb.control('86', [Validators.required]));
        break;
      }
      default:
        break;
    }
  }

  /**
   * 切换登录方式
   */
  switchLoginType() {
    switch (this.loginType) {
      case LoginType.EMAIL: {
        this.rebuildForm(LoginType.PHONE);
        this.loginType = LoginType.PHONE;
        break;
      }
      case LoginType.PHONE: {
        this.rebuildForm(LoginType.EMAIL);
        this.loginType = LoginType.EMAIL;
        break;
      }
      default:
        break;
    }
  }

  ngOnDestroy(): void {
    this.resetSendSmsCodeTimer();
  }
}
