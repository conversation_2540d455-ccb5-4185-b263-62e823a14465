import { Component, EventEmitter, Input, Output } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { AdUserService } from '../service/user.service';
import { TenantService } from '../service/tenant.service';

@Component({
  selector: 'app-select-tenant',
  templateUrl: './select-tenant.component.html',
  styleUrls: ['./select-tenant.component.less'],
})
export class SelectTenantComponent {
  @Input() activated: boolean;
  @Input() tenantList: Array<any>;
  @Input() consoleUrl: string;
  @Output() backCallback = new EventEmitter();
  @Output() selectCallback = new EventEmitter();

  constructor(
    private route: ActivatedRoute,
    private userService: AdUserService,
    private tenantService: TenantService,
  ) {}

  handleBack(): void {
    this.backCallback.emit();
  }

  async handleSelectTenant(tenant) {
    const msgkey = this.route.snapshot.queryParamMap.get('msgkey');
    if (msgkey) {
      const { tenantId, tenantName, tenantSid, testTenant, experience } = tenant;
      await this.tenantService.tokenRefreshTenant(tenantName, tenantId, tenantSid, testTenant, experience).toPromise();
      this.navigateToVscode();
      return;
    }
    this.selectCallback.emit(tenant);
  }

  // 外侧入口如果是vscode,需要在这里进行vscode 跳转
  navigateToVscode() {
    const iamToken = this.userService.getUser('iamToken');
    const authorization = this.userService.getUser('token');
    const vscodeProtocol = `vscode://Digiwin-Webdpt.vscode-digiwin/sso?token=${iamToken}&authorization=${authorization}`;
    window.location.href = vscodeProtocol;
  }
}
