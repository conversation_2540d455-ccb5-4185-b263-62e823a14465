import { Injectable } from '@angular/core';
import { isEmpty } from 'lodash';
import { AdUserService } from 'pages/login/service/user.service';
import * as Paho from 'paho-mqtt';
import { Client, ConnectionOptions, Message, MQTTError } from 'paho-mqtt';
import { MqttApiService } from './mqtt-api.service';
import { BroadcastService } from './broadcast.service';
import { differenceInMinutes } from 'date-fns';
import { SystemConfigService } from 'common/service/system-config.service';
import { LocaleService } from 'common/service/locale.service';
import { take } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';

@Injectable({
  providedIn: 'root',
})
export class MqttNotificationService {
  private mqttClient: Client;
  private host: string;
  private port: number;
  private path: string;
  private userName: string;
  private password: string;
  private mqttTopicSys: string;
  private mqttTopicUser: string;
  private mqttTopicTenant: string;
  private mqttTopicTenantUser: string;


  isLoggedIn =
    typeof this.userService.getUser('isLoggedin') === 'boolean' ? this.userService.getUser('isLoggedin') : null;

  public noticeInfo: any = {};

  constructor(
    private configService: SystemConfigService,
    private userService: AdUserService,
    private languageService: LocaleService,
    private mqttApiService: MqttApiService,
    private broadcastService: BroadcastService,
    private translateService: TranslateService
  ) {
    this.getMqttTopic();
  }

  getMqttTopic(): void {
    this.mqttApiService.queryTopic().subscribe(res => {
      if (res.code === 0) {
        this.mqttTopicSys = res.data.SYS;
        this.mqttTopicUser = res.data.USER;
        this.mqttTopicTenant = res.data.TENANT;
        this.mqttTopicTenantUser = res.data.TENANT_USER;
        this.initDataByConfig();
        this.monitorLoginStatus();
      }
    });
  }

  /**
   * 通过配置项初始化数据
   */
  initDataByConfig(): void {
    this.configService.getConfig().pipe(take(1)).subscribe((config: any): void => {
      if (config.enableMqtt === 'true' && config?.mqttUrl && config?.mqttPort) {
        this.host = config?.mqttUrl;
        this.port = Number(config.mqttPort);
        this.path = config?.mqttPath ?? '/ws';
        this.userName = config?.mqttUserName ?? '';
        this.password = config?.mqttPwd ?? '';
        if (this.isLoggedIn) {
          this.initMqtt();
        }
      }
    });
  }

  /**
   * 根据登陆状态初始化MQTT连接/断开
   */
  monitorLoginStatus() {
    this.userService.isLoggedIn$.subscribe((data) => {
      if (data) {
        this.initMqtt();
      } else {
        this.disconnect();
      }
    });
  }

  /**
   * 初始化mqtt实例
   */
  private initMqtt(): void {
    const { host, port, path } = this;
    this.mqttClient = new Paho.Client(
      host.replace('ws://', '').replace('wss://', ''),
      port,
      path,
      this.generateClientId(),
    );
    this.connect();
  }

  /**
   * generateClientId
   * @returns
   */
  private generateClientId(): string {
    const { tenantId, userId } = this.userService.getUserInfo();
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 100);
    return `client$00$${tenantId}$00$${userId}$00$${language}$00$${timestamp}$00$${random}`;
  }

  private connect(): void {
    if (isEmpty(this.mqttClient) || this.mqttClient.isConnected()) {
      return;
    }
    const { host, userName, password } = this;
    const options: ConnectionOptions = {
      timeout: 3,
      keepAliveInterval: 30,
      userName,
      password,
      reconnect: true,
      onSuccess: (): void => {
        const { tenantId, userId } = this.userService.getUserInfo();
        let topics = [this.mqttTopicSys];
        if (this.mqttTopicTenant.endsWith(tenantId)) {
          topics.push(this.mqttTopicTenant);
        }
        if (this.mqttTopicTenantUser.endsWith(userId)) {
          topics.push(this.mqttTopicTenantUser);
        }
        if (this.mqttTopicUser.endsWith(userId)) {
          topics.push(this.mqttTopicUser);
        }
        topics.forEach(topic => {
          this.mqttClient.subscribe(topic, { qos: 1 });
        });
      },
      onFailure: (error: Paho.MQTTError): void => {
        console.error('mqtt connect failure ' + error?.errorMessage);
      },
    };
    if (host.startsWith('wss:')) {
      options.useSSL = true;
    }
    this.mqttClient.connect(options);
    this.mqttClient.onConnectionLost = (e) => this.onConnectionLost(e);
    this.mqttClient.onMessageArrived = (e) => this.onMessageArrived(e);
  }

  /**
   * 连接丢失
   * @param error
   */
  private onConnectionLost(error: MQTTError): void {

  }

  /**
   * 消息到达
   * @param message
   */
  private onMessageArrived(message: Message): void {
    this.sendMessage(JSON.parse(message?.payloadString));
  }

  /**
   * 断开连接
   */
  public disconnect(): void {
    if (isEmpty(this.mqttClient) || !this.mqttClient.isConnected()) {
      return;
    }
    this.mqttClient.disconnect();
  }

  getTimeDifference(validTime, invalidTime) {
    const totalMinutes = differenceInMinutes(new Date(invalidTime), new Date(validTime));
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    return `${hours}${this.translateService.instant('dj-小时')}${minutes}${this.translateService.instant('dj-分钟')}`;
  }

  /**
   * 查询有效的升级通知列表
   */
  queryValidList() {
    this.mqttApiService.queryValid().subscribe((res) => {
      if (res.data.length > 0) {
        this.sendMessage(res.data);
        return;
      }
      this.sendMessage(null);
    });
  }

  /**
   * 标记升级通知为已读
   */
  noPromptMessage(code) {
    this.mqttApiService.noPrompt({ code }).subscribe(
      (res) => {
        // 跨页签处理通知
        this.broadcastService.removeOneMessage(code);
        // this.broadcastService.sendMessage(null);
      },
      (err) => {
        console.log(err);
      },
    );
  }

  /**
   * 当前页签处理消息通知
   * @param message 通知信息
   */
  sendMessage(message) {
    this.broadcastService.messageArrived$.next(message);
  }
}
