import { Component, Inject, Input, OnInit, SimpleChanges, Output, EventEmitter } from '@angular/core';
import microApp from '@micro-zoe/micro-app';
import { AD_AUTH_TOKEN } from 'pages/login/service/auth.service';
import { AdUserService } from 'pages/login/service/user.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { LocaleService } from 'common/service/locale.service';
import { DigiMiddlewareAuthApp } from 'common/config/app-auth-token';
import { environment } from 'environments/environment';
import { AuthResources } from 'common/types/auth.types';
import { AuthService } from 'common/service/auth.service';
import { Subject } from 'rxjs';
import { BusinessShareInfo } from './type';
import { MicroAppLanguageSyncService } from 'common/service/microapp-language-sync.service';
import { BusinessShareConsumerPreloadShareService } from '../business-share-consumer-preload/business-share-consumer-preload-share.service';
import { IndividualService } from 'pages/individual/individual.service';

@Component({
  selector: 'app-business-share-consumer',
  templateUrl: './business-share-consumer.component.html',
  styleUrls: ['./business-share-consumer.component.less'],
  providers: [MicroAppLanguageSyncService],
})
export class BusinessShareConsumerComponent implements OnInit {
  @Input() businessShareInfo: BusinessShareInfo = null;
  @Input() isPreload: boolean = true;
  @Output() afterDestroy = new EventEmitter<string>();

  businessShareRef: any = null; // 分享模块子应用实例

  url = 'http://localhost:3000';
  appName = 'business-share';
  appPath = '/business-share';
  packageName = 'athena-designer-core';
  microAppData = {
    currentLanguage: this.languageService?.currentLanguage || 'zh_CN',
    digiMiddlewareAuthApp: DigiMiddlewareAuthApp,
    userInfo: this.userService.getUserInfo(),
    sessionStorage: sessionStorage,

    auth: {
      resourcePrefix: AuthResources.APPLICATION, // 权限资源类型
      permission: this.newAuthService?.authOriginData || [], // 具体权限
    },
  };

  isLoading = true;

  destroy$ = new Subject();

  get isShow() {
    return this.isPreload || this.businessShareConsumerPreloadShareService?.isAthenaDesignerCorePreloadDestroyed;
  }

  constructor(
    private userService: AdUserService,
    private newAuthService: AuthService,
    private languageService: LocaleService,
    private configService: SystemConfigService,
    private microLangSyncService: MicroAppLanguageSyncService,
    public businessShareConsumerPreloadShareService: BusinessShareConsumerPreloadShareService,
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
    private individualService: IndividualService,
  ) {
    // const activeApps = microApp.getActiveApps();
    // if (activeApps?.includes(this.appName)) {
    //   microApp.reload(this.appName);
    // }
    if (this.individualService.individualCase) {
      // 分享模块个案路由
      this.appPath = this.individualService.individualCaseAppCode
        ? `/individual/business-share?appCode=${this.individualService.individualCaseAppCode}`
        : '/individual/business-share';
    }
    // const activeApps = microApp.getActiveApps();
    // if (activeApps?.includes(this.appName)) {
    //   microApp.reload(this.appName);
    // }
    this.microLangSyncService.syncLanguage(this.appName);
    if (environment.production) {
      // 生产环境使用当前域名
      this.url = `${location.origin}/${this.packageName}`;
    }
    // 优先加载localStorage中配置的url
    const localStorageUrl = localStorage.getItem('micro-app-lowcode');
    if (localStorageUrl) {
      this.url = localStorageUrl;
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('businessShareInfo')) {
      this.updateBusinessShareInfoToMicroApp();
    }
  }

  ngOnInit(): void {
    this.init();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.afterDestroy.emit(this.appName);
  }

  init(): void {
    if (!this.isPreload) this.businessShareConsumerPreloadShareService.setIsShowAthenaDesignerCorePreload(false);

    this.configService.getConfig().subscribe((config) => {
      microApp.setData(this.appName, {
        config,
      });
    });
    microApp.setData(this.appName, {
      authToken: this.authToken,
      userInfo: this.userService.getUserInfo(),
    });

    // 将sessionStrage中数据传入子应用
    const sessionStorageData = {};
    Object.keys(sessionStorage).forEach((key) => {
      try {
        sessionStorageData[key] = JSON.parse(sessionStorage.getItem(key));
      } catch (error) {
        sessionStorageData[key] = sessionStorage.getItem(key);
      }
    });

    // 将localStorage中数据传入子应用
    const localStorageData = {};
    Object.keys(localStorage).forEach((key) => {
      try {
        localStorageData[key] = JSON.parse(localStorage.getItem(key));
      } catch (error) {
        localStorageData[key] = localStorage.getItem(key);
      }
    });

    microApp.setData(this.appName, {
      sessionStorage: sessionStorageData,
      localStorage: localStorageData,
    });

    this.updateBusinessShareInfoToMicroApp();
  }

  updateBusinessShareInfoToMicroApp(): void {
    microApp.setData(this.appName, {
      businessShareInfo: this.businessShareInfo,
    });
  }

  handleCreate(): void {
    console.log('BusinessShareConsumerComponent 创建了');
  }

  handleBeforeMount(): void {
    console.log('BusinessShareConsumerComponent 即将被渲染');
  }

  handleMount(): void {
    console.log('BusinessShareConsumerComponent 已经渲染完成');
    microApp.router.push({ name: this.appName, path: this.appPath }).then(() => {
      this.isLoading = false;
    });
  }

  handleUnmount(): void {
    console.log('BusinessShareConsumerComponent 卸载了');
  }

  handleError(): void {
    console.log('BusinessShareConsumerComponent 加载出错了');
  }

  handleDataChange(e: CustomEvent): void {
    console.log(`来自子应用 ${this.appName} 的数据:`, e.detail.data);
    const { type, data } = e.detail.data;

    // 更新子应用组件实例
    if (type === 'updateBusinessShareRef') {
      this.businessShareRef = data;
    }
  }

  // 操作分享组件Ref上的方法
  handleBussinessShareRefMethod(method: string, ...args: any[]): any {
    console.log('handleBusinessShareRefMethod', method, args);

    const { current = null } = this.businessShareRef;

    if (!current || !current[method] || typeof current[method] !== 'function') return;

    return current[method](...args);
  }
}
