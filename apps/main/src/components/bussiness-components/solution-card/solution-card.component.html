<nz-spin
  [nzSpinning]="
    loading ||
    data?.deleteStatus === kAppStatus.DELETING ||
    [kAppStatus.CREATING, kAppStatus.UPDATING].includes(data?.appStatus)
  "
  [nzTip]="spinTip"
>
  <section class="solution-card" (click)="handleAppClick()">
    <div class="solution-card-body">
      <div class="img-wrap">
        <div
          [class]="data.appType === 12 ? 'agile-img-box' : 'img-box'"
          [style]="data.appType !== 12 ? backgroundColor : ''"
        >
          <img [src]="imgSrc" alt="" />
        </div>
      </div>
      <div class="info">
        <div
          class="title-wrap"
          [ngStyle]="{
            'margin-right': showAuth ? '63px' : 0
          }"
        >
          <div class="title-left">
            <h3
              class="title line-one"
              nz-tooltip
              [nzTooltipPlacement]="'topLeft'"
              [nzTooltipTitle]="data.lang?.name?.['dj-LANG'|translate] || data.name || data.code"
            >
              {{ data.lang?.name?.[('dj-LANG' | translate )] || data.name || data.code}}
            </h3>
            <div *ngIf="data?.tag?.sourceComponent === 'BC'" class="bc-app">
              {{ 'dj-数智应用设计器' | translate }}
            </div>
          </div>

          <div *ngIf="data?.commonApp" class="common">
            {{ 'dj-公共' | translate }}
          </div>
        </div>
        <p class="code line-one" nz-tooltip [nzTooltipPlacement]="'topLeft'" [nzTooltipTitle]="data.code">
          {{ 'dj-代号' | translate }}：{{ data.code }}
        </p>
        <p
          class="description line-one"
          nz-tooltip
          [nzTooltipPlacement]="'topLeft'"
          [nzTooltipTitle]="data.lang?.description?.['dj-LANG'|translate] || data.description "
        >
          {{ 'dj-描述' | translate }}：{{ data.lang?.description?.['dj-LANG'|translate] || data.description }}
        </p>
      </div>
    </div>
    <div class="solution-card-footer">
      <span class="type">
        <i adIcon [iconfont]="kAppIconfontsType[data.appType]" style="font-size: 14px; margin-right: 4px"></i>
        {{ appType | translate }}
        <span *ngIf="data.deleteStatus === kAppStatus.DELETE_FAIL" class="delete-fail">{{
          'dj-删除失败，请重新删除！' | translate
        }}</span>
        <span
          *ngIf="data.deleteStatus !== kAppStatus.DELETE_FAIL && data.appStatus === kAppStatus.CREATE_FAIL"
          class="delete-fail"
          >{{ 'dj-创建失败，请删除后重试！' | translate }}</span
        >
        <span
          *ngIf="data.deleteStatus !== kAppStatus.DELETE_FAIL && data.appStatus === kAppStatus.UPDATE_FAIL"
          class="delete-fail"
          >{{ 'dj-更新失败，请稍后重试！' | translate }}</span
        >
      </span>
      <div class="app-bottom-operate-right">
        <i
          adIcon
          class="high-icon"
          nz-tooltip
          [nzTooltipPlacement]="'topLeft'"
          [nzTooltipTitle]="'dj-发布' | translate"
          *ngIf="showHighCodeByAppTypes?.includes(data?.appType) && !hiddenMenuByEnv"
          [iconfont]="'icongaodaima1'"
          (click)="handleEnterHighApp($event)"
        ></i>
        <i
          *ngIf="showMore"
          class="icon"
          adIcon
          iconfont="iconnenglikashezhi1"
          nz-tooltip
          [nzTooltipPlacement]="'topLeft'"
          [nzTooltipTitle]="'dj-更多操作' | translate"
          nz-popover
          [nzPopoverContent]="moreContentTemplate"
          [nzPopoverTrigger]="'hover'"
          [nzPopoverPlacement]="'bottom'"
          (click)="handleMoreClick($event)"
        ></i>
      </div>
    </div>
    <app-auth-cooperate-enter
      *ngIf="showAuth"
      class="cooperate-enter-box"
      [roles]="data.roles"
      [resourceId]="data.code"
      [enterType]="kAuthResources.APPLICATION"
      [appName]="data.lang?.name?.['dj-LANG'|translate] || data.name || data.code"
      [enterTypeExtra]="'homePage'"
    ></app-auth-cooperate-enter>
  </section>
</nz-spin>
