import {
  ChangeDetectorRef,
  Component,
  HostListener,
  Inject,
  Input,
  OnChanges,
  OnInit,
  SimpleChanges,
} from '@angular/core';
import { PreviewModalService } from '../service/preview-modal.service';
import { ITemplateParams } from '../service/types';
import { AD_AUTH_TOKEN } from 'pages/login/service/auth.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { LocaleService } from 'common/service/locale.service';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { AppService } from '../../../../pages/apps/app.service';

@Component({
  selector: 'component-preview-content',
  templateUrl: './preview-content.component.html',
  styleUrls: ['../preview-modal.less'],
})
export class PreviewContentComponent implements OnInit, OnChanges {
  @Input() params: ITemplateParams; // 请求预览数据需要的入参
  // abi类型需要特殊处理：
  @Input() isAbi: boolean;
  @Input() hiddenPreviewLeft: boolean = false;
  @Input() hiddenPreviewOperation: boolean = false;
  @Input() token: string;

  // iframe 预览
  loading: boolean = true;
  iframeUrl: SafeResourceUrl = '';
  extraParams: any = {
    key: null,
    userToken: null,
    dwLang: null,
    routerLink: 'preview-dynamic-activity',
    appCode: '',
  };
  athenaUrl: string;

  constructor(
    public service: PreviewModalService,
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
    private language: LocaleService,
    private config: SystemConfigService,
    private domSanitizer: DomSanitizer,
    private cdr: ChangeDetectorRef,
    public appService: AppService,
  ) {
    this.extraParams.userToken = this.authToken.iamToken;
    this.extraParams.dwLang = this.language.currentLanguage || 'zh_CN';
    this.extraParams.appCode = this.appService.selectedApp.code;
    this.config.get('athenaUrl').subscribe((url: string): void => {
      this.athenaUrl = url;
    });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.token.currentValue && changes.token.currentValue !== changes.token.previousValue) {
      // 更换token重新加载iframe
      this.extraParams.userToken = changes.token.currentValue || this.authToken.iamToken;
      if (this.extraParams.key) {
        this.iframeUrl = null;
        this.loading = true;
        this.cdr.detectChanges();
        const t = setTimeout(() => {
          clearTimeout(t);
          this.generateUrl();
          this.cdr.detectChanges();
        });
      }
    }
  }

  ngOnInit() {
    this.init();
  }

  @HostListener('window:message', ['$event'])
  handleMessage(event: MessageEvent): void {
    if (event.origin === this.athenaUrl) {
      if (event.data === 'end-loading') {
        this.loading = false;
      }
    }
  }

  init() {
    this.service.getTemplateData(this.params).subscribe(
      (res) => {
        this.extraParams.key = res.data.key;
        this.generateUrl();
      },
      () => {},
    );
  }

  generateUrl() {
    this.iframeUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(
      `${this.athenaUrl}/sso-login?${new URLSearchParams({
        ...this.params,
        ...this.extraParams,
      })}`,
    );
  }
}
