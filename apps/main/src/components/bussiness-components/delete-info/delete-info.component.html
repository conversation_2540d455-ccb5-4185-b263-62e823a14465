<!--确认删除弹窗-->
<ad-modal
  [(nzVisible)]="isVisible"
  [nzTitle]="title | translate"
  (nzOnCancel)="handleCancel()"
  [nzFooter]="null"
  nzClassName="compile-modal"
  [nzWidth]="'460px'"
>
  <ng-container *adModalContent>
    <p class="i-text-dark">{{ 'dj-删除解决方案' | translate }}{{ applyName }}</p>
    <div class="m10">
      <!-- <input nz-input type="text" placeholder="请输入" [(ngModel)]="inputValue" /> -->
      <form nz-form [formGroup]="appForm" [nzNoColon]="true" class="form-info">
        <nz-form-item>
          <nz-form-control [nzErrorTip]="errorTpl">
            <input
              type="text"
              nz-input
              formControlName="inputValue"
              [placeholder]="'dj-请输入' | translate"
              (paste)="onPaste($event)"
            />
            <ng-template #errorTpl let-control>
              <ng-container *ngIf="control.hasError('required')">{{ 'dj-必填！' | translate }}</ng-container>
              <ng-container *ngIf="control.hasError('confirm')">
                {{ 'dj-解决方案名称保持一致' | translate }}
              </ng-container>
            </ng-template>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-control>
            <p class="i-text-warning">{{ 'dj-删除警示' | translate }}</p>
          </nz-form-control>
        </nz-form-item>
      </form>
      <div class="modal-footer">
        <button ad-button adType="default" (click)="handleCancel()">
          {{ 'dj-取消' | translate }}
        </button>
        <button ad-button adType="primary" (click)="handleOk()">
          {{ 'dj-确定' | translate }}
        </button>
      </div>
    </div>
  </ng-container>
</ad-modal>
<!--删除日志弹窗-->
<app-delete-app-log
  [isLogVisible]="isLogVisible"
  [appInfo]="appInfo"
  (updateStatus)="updateStatus($event)"
></app-delete-app-log>
