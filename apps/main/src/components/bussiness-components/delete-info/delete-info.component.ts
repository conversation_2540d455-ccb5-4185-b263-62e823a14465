import { Component, OnInit, Input, Output, EventEmitter } from '@angular/core';
import { FormBuilder, Validators, FormControl, FormGroup } from '@angular/forms';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { TranslateService } from '@ngx-translate/core';
import { AppTypes } from 'pages/app/typings';
@Component({
  selector: 'app-delete-info',
  templateUrl: './delete-info.component.html',
  styleUrls: ['./delete-info.component.less'],
})
export class DeleteInfoComponent implements OnInit {
  appForm!: FormGroup;
  @Input() isVisible: boolean = false; // 确认删除弹窗是否显示
  isLogVisible: boolean = false; // 删除解决方案日志弹窗是否显示
  @Input() title: string = 'dj-删除确认'; //标题
  @Input() applyName: string = ''; //解决方案名称
  @Input() applyNameArr: Array<string>; //解决方案名称
  @Input() appCode: string = ''; //解决方案code
  @Input() appType: number; //解决方案type
  appInfo;
  @Output() ok: EventEmitter<any> = new EventEmitter(); //确定
  @Output() newOk: EventEmitter<any> = new EventEmitter(); //新确定
  @Output() cancel: EventEmitter<any> = new EventEmitter(); //取消
  constructor(private modal: AdModalService, private fb: FormBuilder) {}
  inputValue: string = '';
  ngOnInit() {
    this.appForm = this.fb.group({
      inputValue: [null, [Validators.required, this.confirmationValidator]],
    });
  }
  onPaste(event: ClipboardEvent): void {
    event.preventDefault(); // 阻止粘贴事件的默认行为
  }
  /**
   * 确认删除弹窗--取消
   */
  handleCancel() {
    this.appForm.reset();
    for (const key in this.appForm.controls) {
      if (this.appForm.controls.hasOwnProperty(key)) {
        this.appForm.controls[key].markAsPristine();
        this.appForm.controls[key].updateValueAndValidity();
      }
    }
    this.cancel.emit();
  }
  /**
   * 确认删除弹窗--ok
   */
  handleOk() {
    for (const i of Object.keys(this.appForm?.controls)) {
      this.appForm.controls[i].markAsDirty();
      this.appForm.controls[i].updateValueAndValidity();
    }
    this.appInfo = {
      appName: this.applyName,
      appCode: this.appCode,
    };
    if (this.appForm.valid) {
      // 1.0 2.0走新删除，其他走老删除
      if (
        [
          AppTypes.DTD,
          AppTypes.MODEL_DRIVEN,
          AppTypes.DATA_VIEW,
          AppTypes.HIGH_CODE,
          AppTypes.BUSINESS_DOMAIN_CENTER,
        ].includes(this.appType)
      ) {
        this.cancel.emit();
        this.isLogVisible = true;
      } else {
        this.ok.emit(this.appForm.controls.inputValue.value);
        this.appForm?.reset();
      }
    }
  }
  confirmationValidator = (control: FormControl): { [s: string]: boolean } => {
    if (!control.value) {
      return { required: true };
    } else if (!this.applyNameArr.includes(control.value)) {
      return { confirm: true, error: true };
    }
    return {};
  };
  /**
   * 更新app状态
   * @param status
   */
  updateStatus(status) {
    this.isLogVisible = false;
    this.appForm?.reset();
    if (status === 'normal') {
      return;
    }
    this.newOk.emit(status);
  }
}
