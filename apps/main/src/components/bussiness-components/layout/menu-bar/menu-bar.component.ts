import { Component, OnInit, OnDestroy, Output, EventEmitter } from '@angular/core';
import { NavigationStart, Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { Subscription, Subject, forkJoin, Observable } from 'rxjs';
import { NzI18nService } from 'ng-zorro-antd/i18n';
import { AdUserService } from 'pages/login/service/user.service';
import { AdAuthService } from 'pages/login/service/auth.service';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { TranslateService } from '@ngx-translate/core';
import { AppService } from '../../../../pages/apps/app.service';
import { TenantService } from '../../../../pages/login/service/tenant.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SystemConfigService } from 'common/service/system-config.service';
import { GlobalService } from 'common/service/global.service';
import { cloneDeep, delay, find, isEmpty } from 'lodash';
import { IntroService } from 'common/service/intro/intro.service';
import { AppTypes } from 'pages/app/typings';
import { LayoutService } from '../layout.service';
import { tenantNotAllowedToPublishList } from 'common/config/common.config';
import { AuthService } from '../../../../common/service/auth.service';
import { AuthPanelRoleEnum, AuthPanelTypeEnum } from '../../../../common/config/auth.config';
import { AuthOperate, AuthResources } from '../../../../common/types/auth.types';
import { AuthManageService } from './auth-manage/auth-manage.service';
import { LocaleService, SUPPORT_LOCALE } from 'common/service/locale.service';
import microApp from '@micro-zoe/micro-app';
import { IndividualService } from 'pages/individual/individual.service';
import { filter, take } from 'rxjs/operators';
import { SolutionCardService } from 'app/service/solution-card.service';
import { LanguageCacheKey } from 'common/utils/constant';
import { IsPrivatizationService } from 'common/service/is-privatization.service';
@Component({
  selector: 'app-menu-bar',
  templateUrl: './menu-bar.component.html',
  styleUrls: ['./menu-bar.component.less'],
})
export class MenuBarComponent implements OnInit, OnDestroy {
  AuthResources = AuthResources;
  loadingAuth = false;
  @Output() clickEvent = new EventEmitter();
  currentTenant: string; // 当前租户名称
  currentTenantId: string; // 当前租户id
  currTenantList = []; // 租户清单列表
  currIndividualTenantList = []; // 个案租户清单列表
  // 是否展示租户切换
  tenantShow: boolean = true;
  currentUser = { name: '', id: '' };
  defaultLanguage: string = 'zh_CN';
  defaultLanguageLabel: string;
  languageList: Array<any> = [];
  private subscription: Subscription = new Subscription();
  destroy$ = new Subject();
  metadata: any[];
  langList: any;
  languageMode: any;
  languageName: any;
  confirmModal?: any; // 确认删除机制参数
  langTimeout: any;
  appToken: string;
  lcdpDesignerUrl: any; // 数据录入ui设计器前缀url
  interfaceConfigurationToolURL = ''; // 跳转界面配置工具固定URL
  athenaUrl: string;
  consoleUrl: string;
  tbbLoginUrl: string;
  developerPortalUrl: string;
  cloud: string;
  envAlias: string;
  exportJson: any = {};

  appInfoVisible: boolean;
  appFormData: any = {};
  showRelease: boolean;

  dtdJumpSetting: any;
  routeEventSub: Subscription;
  // ['application-overview', 'business-constructor', 'integrated-automation', 'release-manage']
  mdTabList: any = []; // 这块是模型驱动解决方案的tab list 会在 updateTenant 后 处理数据
  aiModelTabList: any = [];

  isApp = false; // 当前是否在 app 路由内
  isUserPermissionTenant = false; // 是否具有 租户级 权限（按当前逻辑，如果有 租户级 权限，那就 直接 具有 租户级跳转链接）

  isHideDeployer = false; // 是否隐藏发版管理入口（临时插单需求）
  tenantDesignerUrl: any;
  athenaDesignerUrl: any;
  platformCategory: string;

  // 租户级 开发平台 是否激活
  get isTenantActive() {
    return this.userService.getUser('isTenantActive') || false;
  }
  // TODO: 测试代码，待删除
  localeLists = [
    {
      locale: SUPPORT_LOCALE.ZH_CN,
      title: '简体中文',
    },
    {
      locale: SUPPORT_LOCALE.ZH_TW,
      title: '繁体中文',
    },
    {
      locale: SUPPORT_LOCALE.EN_US,
      title: '英文',
    },
  ];

  get isTTTApp() {
    return this.globalService.currentAppType === AppTypes.COLLECT_DATA_V2;
  }

  // 是否是场景化套件解决方案 -随心控
  get isKITApp() {
    return this.appService.selectedApp?.appType === AppTypes.SCENARIO_KIT;
  }

  get isASSISTANTApp() {
    return this.appService.selectedApp?.appType === AppTypes.NANA_ASSISTANT;
  }

  get selectedApp() {
    return this.appService.selectedApp;
  }

  // 独立解决方案-头部menus菜单
  get solutionHeaderMenus() {
    return this.globalService.standaloneTopMenus || [];
  }

  get isShowMenuCenter() {
    // 不展示中间菜单的界面路由
    const urls = ['app', 'ai-assistance', 'ai-three', 'asa-designer-web'];
    const url = this.router.url.split('?')[0];
    const routeSplit = url.split('/')[1];

    return urls.includes(routeSplit) && !!this.isTenantActive;
  }

  // 是否是独立解放方案
  get isStandaloneSolution() {
    return this.router.url.startsWith('/standalone-solution');
  }

  constructor(
    public globalService: GlobalService,
    public router: Router,
    private userService: AdUserService,
    private authService: AdAuthService,
    private tenantService: TenantService,
    private i18n: NzI18nService,
    private languageService: LocaleService,
    public appService: AppService,
    private modal: AdModalService,
    public translateService: TranslateService,
    private messageService: NzMessageService,
    private layoutService: LayoutService,
    protected configService: SystemConfigService,
    public introService: IntroService,
    public authComService: AuthService,
    private authManageService: AuthManageService,
    private route: ActivatedRoute,
    public individualService: IndividualService,
    private solutionCardService: SolutionCardService,
    private isPrivatizationService: IsPrivatizationService,
  ) {
    this.configService.get('tbbLoginUrl').subscribe((url) => {
      this.tbbLoginUrl = url;
    });
    this.configService.get('developerPortalUrl').subscribe((url) => {
      this.developerPortalUrl = url;
    });
    this.configService.getConfig().subscribe((config: any) => {
      this.athenaUrl = config?.athenaUrl;
      this.consoleUrl = config?.consoleUrl;
      this.tenantDesignerUrl = config?.tenantDesignerUrl;
      this.athenaDesignerUrl = config?.athenaDesignerUrl;
      this.cloud = config?.cloud;
      this.envAlias = config?.envAlias;
      this.platformCategory = config?.platformCategory;
    });

    this.routeEventSub = this.router.events.subscribe((event) => {
      if (event instanceof NavigationStart) {
        if (event.navigationTrigger === 'popstate') {
          // 如果是浏览器的前进或后退按钮触发的导航
          this.handleNavigation(event?.url);
        }
      }
      if (event instanceof NavigationEnd) {
        this.updateIsApp();
        this.updateTenant();
      }
    });

    // 查询个案设计器权限
    if (this.platformCategory !== 'TENANT') {
      this.individualService.loadIndividualCaseAuth().subscribe(
        () => {},
        () => {},
      );
    }

    this.isPrivatizationService.loadIsPrivatization();

    // 查询热门解决方案
    this.solutionCardService.queryAllSolutionCards().subscribe(
      (res) => {
        if (res.code === 0) {
          this.solutionCardService.allSolutionCards = res?.data ?? [];
        }
      },
      () => {},
    );
  }

  async ngOnInit() {
    this.authManageService.loadSuperRoleData();
    this.isHideDeployer = tenantNotAllowedToPublishList.includes(this.userService.getUser('tenantId'));

    this.updateIsApp();
    this.updateTenant();
    // #46816
    // const agreeAgreement = this.userService.getUser('agreeAgreement') || false;
    // if (!agreeAgreement) this.logout();

    // 临时解决sso登陆时携带参数和跳转的页面会丢失从而跳转到首页的问题，后续在sso优化项中处理
    setTimeout(() => {
      this.guardRoute();
    }, 1000);

    this.langList = {
      zh_CN: 'dj-简体',
      zh_TW: 'dj-繁体',
      en_US: 'English',
    };
    // this.currentUser.name = this.userService.getUser('userName') ?? this.userService.getUser('name');
    // this.currentUser.id = this.userService.getUser('userId');
    this.currentUser = this.userService.getUserInfo();

    this.currentTenantId = this.userService.getUser('tenantSid');
    await this.initLanguage();
    this.tenantShow = this.currentTenantId && this.currentTenantId !== '0';
    this.currentTenant = this.userService.getUser('tenantName');

    this.initTenant();
    forkJoin([this.getAppToken(), this.getLcdpDesignerUrl()]).subscribe(() => {
      this.genInterfaceConfigurationToolURL();

      this.dtdJumpSetting = {
        appToken: this.appToken,
        userToken: this.userService.getUser('iamToken'),
        language: this.languageService.currentLocale,
      };
    });

    this.exportJson = {
      lcdpAppToken: this.userService.getUser('token'),
      lcdpUserToken: this.userService.getUser('iamToken'),
      language: this.languageService.currentLocale,
    };
  }

  isRouteActive(route) {
    if (route === '/') {
      return this.router.url === route || this.router.url === '/tenant';
    }
    if (route === '/apps') {
      // /apps与/app如果使用startsWith会造成混乱，这里单独把/apps拎出来判断
      return this.router.url === route || this.router.url === `/tenant${route}`;
    }

    return this.router.url.startsWith(route) || this.router.url.startsWith(`/tenant${route}`);
  }

  updateIsApp() {
    this.isApp = /(^(?=\/app\/))|(^\/app$)|(^(?=\/asa-designer-web\/))|(^\/asa-designer-web$)/.test(location.pathname);
  }

  getAppToken() {
    return new Observable((observer) => {
      this.configService.get('appToken').subscribe((appToken) => {
        this.appToken = appToken;
        observer.next(appToken);
        observer.complete();
      });
    });
  }

  getLcdpDesignerUrl() {
    return new Observable((observer) => {
      this.configService.get('lcdpDesignerUrl').subscribe((lcdpDesignerUrl) => {
        this.lcdpDesignerUrl = lcdpDesignerUrl;
        observer.next(lcdpDesignerUrl);
        observer.complete();
      });
    });
  }

  genInterfaceConfigurationToolURL() {
    const token = this.userService.getUser('iamToken');
    const language = this.languageService.currentLocale;
    let url = `${this.lcdpDesignerUrl}/sso-login`;
    url += `?userToken=${token}&appToken=${this.appToken}&language=${language}&routerLink=/initial-design/embed/interface-configuration`;
    this.interfaceConfigurationToolURL = url;
  }

  /**
   * 没有选择租户跳转到login
   * @returns
   */
  guardRoute() {
    const userInfo = this.userService.getUserInfo();
    if (!userInfo.tenantSid) {
      setTimeout(() => {
        localStorage.clear();
        sessionStorage.clear();
        this.router.navigateByUrl('/login').then(() => {
          location.reload();
        });
      }, 0);
      return;
    }
  }

  /**
   * 初始化租户清单
   */
  initTenant(): void {
    this.tenantService.currTenantList$.subscribe((lists): void => {
      this.currTenantList = lists;
      console.log(this.currTenantList);
    });
    this.tenantService.currIndividualTenantList$.subscribe((lists): void => {
      this.currIndividualTenantList = lists;
      console.log(this.currIndividualTenantList);
    });
  }
  /**
   * 有操作權限的租戶清單-選擇一個進行切換租戶刷新token.
   */
  changeTenant(tenant: any): void {
    this.appService.appLoading = true;
    const jump = () => {
      localStorage.clear();
      // sessionStorage.removeItem('selectedApp');
      if ('' === this.router.url || '/' === this.router.url) {
        window.location.reload();
      } else {
        if (!this.individualService.individualCase) {
          this.router.navigateByUrl('/');
        } else {
          this.router.navigateByUrl('/individual/apps');
        }
        delay(() => {
          window.location.reload();
        }, 20);
      }
    };
    this.subscription.add(
      this.tenantService
        .tokenRefreshTenant(tenant.tenantName, tenant.tenantId, tenant.tenantSid, tenant.testTenant, tenant.experience)
        .subscribe(
          (): void => {
            this.appService.appLoading = false;
            //
            this.currentTenant = this.userService.getUser('tenantName');
            // this.clientService.generateClientId();
            jump();
          },
          (): void => {
            this.appService.appLoading = false;
            this.unsubscribe();
            this.authService.logout(true, this.isTenantActive);
          },
        ),
    );
  }

  ngOnDestroy(): void {
    clearTimeout(this.langTimeout);
    this.destroy$.next();
    this.destroy$.complete();
    this.routeEventSub.unsubscribe();
  }

  showModelDrivenAppBar() {
    const appMenu = [
      'app/application-overview',
      'app/business-constructor',
      'app/integrated-automation',
      'app/data-analysis',
      'app/release-manage',
      'app/mechanism-encapsulation',
    ];
    const publishMenu = ['app-publish', 'application-management', 'deploy-project', 'build-center/build-task'];
    return (
      this.appService.selectedApp?.appType === AppTypes.MODEL_DRIVEN &&
      [...appMenu, ...publishMenu].some((item) => this.router.url.includes(item))
    );
  }

  showAiModelAppBar() {
    const appMenu = ['app/model-overview'];
    return (
      this.appService.selectedApp?.appType === AppTypes.AI_MODEL &&
      appMenu.some((item) => this.router.url.includes(item))
    );
  }

  logout(): void {
    const params = {
      name: this.userService.getUser('userId'),
    };
    this.appService.logout(params).subscribe(
      (res) => {
        if (res.code === 0) {
          this.authService.logout(true, this.isTenantActive);
          localStorage.clear();
          this.appService.selectedApp = {};
          sessionStorage.removeItem('role');
        }
      },
      () => {},
    );
  }

  /**
   * 是否是租户超管
   */
  isSuperAdmin() {
    return this.authManageService.isSuper;
  }
  /**
   * 打开超级管理员
   */
  openAuthPanel() {
    let data = {
      enterType: AuthResources.TENANT,
      roles: this.authManageService.superData,
    };
    this.authComService.openAuthPanel(AuthPanelTypeEnum.SuperAdmin, data);
  }

  /**
   * 初始化语言设置
   */
  private async initLanguage() {
    const tenantsRes = await this.layoutService.queryUserTenants().toPromise();
    const tenantItem = find(tenantsRes.data, (tenant) => tenant.sid === this.currentTenantId);
    this.defaultLanguage = tenantItem?.defaultLanguage || 'zh_CN';
    this.languageMode = this.defaultLanguage;
    this.languageName = this.langList[this.defaultLanguage];
    this.languageList = [
      {
        value: 'en_US',
        label: 'English',
      },
      {
        value: 'zh_CN',
        label: '简体',
      },
      {
        value: 'zh_TW',
        label: '繁體',
      },
    ];
    this.defaultLanguageLabel = this.languageList.find((d) => d.value === this.defaultLanguage).label;
    const sessionLanguage = sessionStorage.getItem(LanguageCacheKey);
    this.languageService.switchLocale(this.defaultLanguage as SUPPORT_LOCALE);
    // 如果获取到的语言不一样，就强行刷洗一下页面
    if (sessionLanguage !== this.defaultLanguage) {
      const snapShouldReuseRoute = this.router.routeReuseStrategy.shouldReuseRoute;
      const snapOnSameUrlNavigation = this.router.onSameUrlNavigation;
      this.router.routeReuseStrategy.shouldReuseRoute = () => false;
      this.router.onSameUrlNavigation = 'reload';
      this.router.navigateByUrl(this.router.url, { replaceUrl: true, skipLocationChange: true }).finally(() => {
        this.router.routeReuseStrategy.shouldReuseRoute = snapShouldReuseRoute;
        this.router.onSameUrlNavigation = snapOnSameUrlNavigation;
      });
    }
  }

  /**
   * 在開啟多頁簽模式下, 沒有觸發 OnDestroy, 所以在選擇完租戶後, 手動 unsubscribe(),
   * 因為選擇完租戶後, 如果正常,會進行導頁, 如果不正常,會進行登出並導向 login 頁.
   */
  private unsubscribe(): void {
    this.subscription.unsubscribe();
  }

  // 回到首页
  handleGoHome(): void {
    if (this.individualService.individualCase) {
      this.router.navigateByUrl('/individual/apps');
    } else {
      if (this.isTenantActive) return;
      this.router.navigateByUrl('/');
    }
  }

  updateTenant() {
    // 2.0应用且auth=false，表示该应用没有发版权限
    const mdAuth = this.selectedApp?.auth && this.selectedApp?.appType === 5;

    this.isUserPermissionTenant = !!this.authService?.userPermissionMap?.get('TenantLcdp')?.query;
    this.mdTabList = [
      {
        name: 'dj-总览',
        type: 'application-overview',
        activeType: ['application-overview'],
      },
      {
        name: 'dj-业务搭建',
        type: 'business-constructor',
        activeType: ['business-constructor'],
      },
      {
        name: 'dj-业务自动化',
        type: 'integrated-automation',
        activeType: ['integrated-automation'],
      },
      {
        name: 'dj-数据分析',
        type: 'data-analysis',
        activeType: ['data-analysis'],
      },
      {
        name: 'dj-机制封装',
        type: 'mechanism-encapsulation',
        activeType: ['mechanism-encapsulation'],
      },
      {
        name: 'dj-发布', //发布常显，不走权限
        type: 'app-publish',
        activeType: ['app-publish', 'application-management', 'deploy-project', 'build-center/build-task'],
      },
    ];
    this.aiModelTabList = [
      {
        name: 'dj-我的模型',
        type: 'model-overview',
      },
    ];
    //发布常显，不走权限
  }

  handlesLinkTenant(isTenantActive: boolean) {
    const token = this.userService.getUser('iamToken');
    const language = this.languageService?.currentLanguage || 'zh_CN';

    const url = isTenantActive
      ? `${this.tenantDesignerUrl}/sso-login?userToken=${token}&dwLang=${language}&routerLink=${encodeURIComponent(
          'apps',
        )}`
      : `${this.athenaDesignerUrl}/sso-login?userToken=${token}&dwLang=${language}`;

    window.open(url, '_blank');
  }

  handlesLinkOther(type: any): void {
    const url = type === 'console' ? this.consoleUrl : this.athenaUrl;
    const token = this.userService.getUser('iamToken');
    const language = this.languageService?.currentLocale || 'zh_CN';
    window.open(`${url}/sso-login?userToken=${token}&dwLang=${language}`);
  }

  // 调整
  handleJumpDtd(): void {
    const url = this.router.serializeUrl(this.router.createUrlTree(['/tools/dtd-designer'], {}));
    window.open(url, '_blank');
  }
  handleJumpBackground(): void {
    const url = this.router.serializeUrl(
      this.router.createUrlTree(['/manage-background/background-management/background-management-main'], {}),
    );
    window.open(url, '_blank');
  }
  // 跳转工具
  // handleJumpTool(): void {
  //   const url = this.router.serializeUrl(
  //     this.router.createUrlTree(['/tools/initial-design/embed/interface-configuration'], {
  //       queryParams: { ...this.exportJson },
  //     }),
  //   );
  //   window.open(url, '_blank');
  // }

  handleLastUrl(type): void {
    // mdCurrentUrlType 主要给业务搭建用 其他tab的初始化状态下可以不存值
    // 当前选中业务搭建，并且再次点击业务搭建
    if (this.appService.mdCurrentUrlType === type && type === 'business-constructor') {
      return;
    }
    // 记录上次业务搭建选择的内容
    if (this.appService.mdCurrentUrlType === 'business-constructor') {
      this.appService.mdLastActiveUrl = this.router.url;
    }
    // 上次不是业务搭建，本次点击也不是业务搭建，清空记录的url
    if (this.appService.mdCurrentUrlType !== 'business-constructor' && type !== 'business-constructor') {
      this.appService.mdLastActiveUrl = '';
    }
    this.appService.mdCurrentUrlType = type;
    // 之前不是业务业务搭建， 现在点击业务搭建
    this.router.navigate([`/app/${type}`], {
      queryParams: {
        appCode: this.appService?.selectedApp?.code,
      },
    });
  }

  handleNavigation(url) {
    const menuList = [
      'application-overview',
      'business-constructor',
      'data-analysis',
      'integrated-automation',
      'release-manage',
    ];
    const selectItem = menuList.filter((item) => url.includes(item));
    if (!isEmpty(selectItem)) {
      this.appService.mdCurrentUrlType = selectItem?.[0];
    }
  }

  handleSetApp(): void {
    this.appInfoVisible = true;
    this.appFormData = cloneDeep(this.selectedApp);
  }

  handleAppEdit(data?: any) {
    if (data) {
      this.appService.selectedApp = Object.assign(this.selectedApp, data);
    }
    this.appInfoVisible = false;
    this.appFormData = {};
  }

  handleJump(type: string) {
    const userInfo = JSON.parse(sessionStorage.getItem('AdUserInfo'));
    const { iamToken = '' } = userInfo;
    const token = this.userService.getUser('iamToken');
    const language = this.languageService?.currentLocale || 'zh_CN';
    switch (type) {
      case 'ZhiKeCenter':
        // 跳转智客中心
        window.open(`${this.developerPortalUrl}/sso-login?userToken=${iamToken}`, '_blank');
        break;
      case 'courseCenter':
        // 跳转课程中心
        // window.open(`https://z0lxpczot6u.feishu.cn/wiki/HrlqwFmvgipkxVkJGRCcLow6n6k?from=from_copylink`, '_blank');
        window.open(
          `${this.developerPortalUrl}/sso-login?userToken=${token}&dwLang=${language}&routerLink=/course-center`,
          '_blank',
        );
        break;
      case 'developerDocumentation':
        // 跳转开发文档
        // window.open(
        //   `${this.developerPortalUrl}/sso-login?userToken=${iamToken}&routerLink=${encodeURIComponent(
        //     'community/list/forum',
        //   )}`,
        //   '_blank',
        // );
        window.open(
          `${this.developerPortalUrl}/sso-login?userToken=${token}&dwLang=${language}&routerLink=/dev-doc/index`,
          '_blank',
        );
        break;
      case 'forum':
        // 跳转论坛
        // window.open(
        //   `${this.developerPortalUrl}/sso-login?userToken=${iamToken}&routerLink=${encodeURIComponent(
        //     'knowledge-base/content?documentId=ATHENA&fileId=D000000084',
        //   )}`,
        //   '_blank',
        // );
        this.linkToForum();
        break;
      case 'home':
        // 智客中心首页
        window.open(
          `${this.developerPortalUrl}/sso-login?userToken=${token}&dwLang=${language}&routerLink=/home`,
          '_blank',
        );
        break;
      default:
        break;
    }
  }

  private getLinkForumConfig() {
    if (this.cloud === 'MICROSOFT') {
      return {
        appId: '2f2d57e0141e40b89efa0cfc2b48a2da',
        callbackUrl: 'https://dev-forum.digiwincloud.com',
      };
    } else {
      if (this.envAlias === 'PROD') {
        return {
          appId: '2ea237d5cf0e494489b86bc555edff4f',
          callbackUrl: 'https://dev-forum.digiwincloud.com.cn',
        };
      }
      return {
        appId: '5fe70bd5b863424d81b51b2302eee00d',
        callbackUrl: 'https://dev-forum-test.digiwincloud.com.cn',
      };
    }
  }

  /**
   * 跳转至论坛
   */
  private async linkToForum() {
    const config = this.getLinkForumConfig();
    const result = await this.layoutService
      .authorize({
        ...config,
        scope: 'user_read',
        state: '',
      })
      .toPromise();
    const url = `${config.callbackUrl}?code=${result.data.code}`;
    window.open(url, '_blank');
  }

  handleJumpToNotice(type) {
    const url = this.router.serializeUrl(this.router.createUrlTree(['help-center/upgrade-log']));
    window.open(url, '_blank');
  }

  handleJumpTo(): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const token = this.userService.getUser('iamToken');
    const tenantId = this.userService.getUser('tenantSid');
    window.open(
      `${this.tbbLoginUrl}/index.html#/sso-login?userToken=${token}&dwLang=${language}&routerKey=${tenantId}&platform=lcdp`,
      '_blank',
    );
  }

  linkUrl(url) {
    return decodeURIComponent(url);
  }

  /**
   * 路由跳转（兼容hash模式与browser模式）
   * @param prefix
   * @param url
   */
  handleGoUrl(prefix, menu) {
    const { route, routeMode } = menu;
    const name = this.globalService.standaloneMicroappsNames[this.selectedApp?.appType];
    const { microAppStateMap } = this.globalService;
    const info = microAppStateMap.get(name);

    if (routeMode === 'hash') {
      if (info?.keepAliveShow) {
        const innerRoute = route.replace('#', '');
        info.router.push({ path: innerRoute });
      } else {
        this.router.navigateByUrl(`${prefix + route}?appCode=${this.selectedApp.code}`);
      }
    }
  }

  /**
   * 跳转个案设计器
   */
  handleJumpToIndividual(): void {
    const url = this.router.serializeUrl(this.router.createUrlTree(['individual/apps']));
    window.open(url, '_blank');
  }

  /**
   * 是否激活
   * @param tab
   * @returns
   */
  ModelDrivenAppActive(tab) {
    return tab.activeType?.findIndex((item) => this.router.url.startsWith('/app/' + item)) > -1;
  }
}
