<div class="menu-bar">
  <div class="left">
    <div class="logo" [ngClass]="{ 'logo-microSoft': cloud === 'MICROSOFT' }" (click)="handleGoHome()">
      <img
        [src]="
          cloud === 'MICROSOFT' ? 'assets/img/layout/athena_logo-zh_TW.png' : 'assets/img/layout/athena_logo-zh_CN.png'
        "
        alt=""
      />
    </div>
    <nz-divider nzType="vertical"></nz-divider>
    <div *ngIf="!isTTTApp" class="title" (click)="handleGoHome()">
      <ng-conrainer *ngIf="!individualService.individualCase">
        {{ 'dj-开发平台' | translate | envTranslate }}
      </ng-conrainer>
      <ng-container *ngIf="individualService.individualCase">
        {{ 'dj-个案设计器' | translate }}
      </ng-container>
    </div>

    <div
      class="appList"
      *ngIf="
        [1, 6, 11, 12].includes(globalService['currentAppType']) ||
        isKITApp ||
        showModelDrivenAppBar() ||
        appService.selectedApp?.appTypes?.includes(9)
      "
    >
      <div
        class="app selected-app"
        *ngIf="(globalService['isAppPage'] && !!appService.selectedApp?.name) || showModelDrivenAppBar()"
      >
        <span
          class="app-name"
          nz-tooltip
          nzTooltipPlacement="bottom"
          [nzTooltipTitle]="appService.selectedApp?.lang?.name?.['dj-LANG'|translate] || appService.selectedApp.name"
        >
          {{ appService.selectedApp?.lang?.name?.['dj-LANG'|translate] || appService.selectedApp.name }}</span
        >
        <div *ngIf="!isTenantActive" class="publish-tag" [ngClass]="{ published: appService.selectedApp.published }">
          {{ (appService.selectedApp.published ? 'dj-已发布' : 'dj-未发布') | translate }}
        </div>
        <app-auth-cooperate-enter
          class="cooperate-enter-box"
          [resourceId]="appService.selectedApp.code"
          [enterType]="AuthResources.APPLICATION"
        ></app-auth-cooperate-enter>
      </div>
    </div>
    <div class="appList" *ngIf="isASSISTANTApp">
      <div class="app selected-app" *ngIf="!!appService.selectedApp?.name">
        <span class="app-name">
          {{ appService.selectedApp?.lang?.name?.['dj-LANG'|translate] || appService.selectedApp.name }}</span
        >
        <div class="publish-tag" [ngClass]="{ published: appService.selectedApp.published }">
          {{ (appService.selectedApp.published ? 'dj-已发布' : 'dj-未发布') | translate }}
        </div>
        <app-auth-cooperate-enter
          class="cooperate-enter-box"
          [enterTypeExtra]="'NaNaApp'"
          [resourceId]="appService.selectedApp.code"
          [enterType]="AuthResources.APPLICATION"
        ></app-auth-cooperate-enter>
      </div>
    </div>
    <ng-container *ngIf="isTTTApp || isKITApp || [11].includes(globalService['currentAppType'])">
      <ng-container *operateAuth="{ prefix: 'create' }">
        <i class="header-icon" adIcon iconfont="iconsheding" aria-hidden="true" (click)="handleSetApp()"></i>

        <!-- <i
          class="intro-app-step-4 header-icon"
          adIcon
          iconfont="iconpublish"
          aria-hidden="true"
          (click)="showRelease = true; introService.play('appReleaseStep', 300)"
        ></i> -->
      </ng-container>
    </ng-container>
  </div>

  <div
    class="center"
    *ngIf="
      !individualService.individualCase &&
      router.url.split('?')[0].split('/')[1] !== 'app' &&
      !['ai-assistance', 'ai-three', 'asa-designer-web'].includes(router.url.split('?')[0].split('/')[1]) &&
      !isStandaloneSolution
    "
  >
    <nav class="nav-links">
      <a *ngIf="!isTenantActive" routerLink="/" [ngClass]="{ 'router-link-active': isRouteActive('/') }">
        <span>{{ 'dj-首页' | translate }}</span>
      </a>

      <a routerLink="/apps" [ngClass]="{ 'router-link-active': isRouteActive('/apps') }">
        <span>{{ 'dj-解决方案中心' | translate }}</span>
      </a>
      <a
        *ngIf="!isTenantActive"
        routerLink="/asset-center"
        [ngClass]="{ 'router-link-active': isRouteActive('/asset-center') }"
      >
        <span>{{ 'dj-资产中心' | translate }}</span>
      </a>
      <a
        *ngIf="!isTenantActive"
        routerLink="/template-center"
        [ngClass]="{ 'router-link-active': isRouteActive('/template-center') }"
      >
        <span>{{ 'dj-模板中心' | translate }}</span>
      </a>
      <a
        *ngIf="!isTenantActive"
        routerLink="/data-center"
        [ngClass]="{ 'router-link-active': isRouteActive('/data-center') }"
      >
        <span>{{ 'dj-数据标准' | translate }}</span>
      </a>
      <!-- <a routerLink="/deployer" routerLinkActive="router-link-active"> -->
      <a
        routerLink="/devOps-center"
        *ngIf="!isTenantActive && !isHideDeployer"
        [ngClass]="{ 'router-link-active': isRouteActive('/devOps-center') || isRouteActive('/cdm') }"
      >
        <span>{{ 'dj-运维中心' | translate }}</span>
      </a>
      <!-- <a
        *ngIf="!isTenantActive"
        class="tool-link"
        nz-dropdown
        [nzDropdownMenu]="tools"
        [nzBackdrop]="true"
        nzTrigger="click"
        [ngClass]="{ 'router-link-active': router.url.startsWith('/tools') }"
      >
        <span>{{ 'dj-工具中心' | translate }}</span>
        <i adIcon class="iconfont" type="down"></i>
      </a> -->
      <!-- <nz-dropdown-menu #tools="nzDropdownMenu">
        <ul nz-menu nzSelectable class="tool-menu"> -->
      <!--<li nz-menu-item>
            <a routerLink="/tools/currency" routerLinkActive="active">
              <span>{{ 'dj-通用配置' | translate }}</span>
            </a>
          </li>-->
      <!-- <li nz-menu-item>
            <span class="jump-tool" (click)="handleJumpTool()">{{ 'dj-界面配置工具' | translate }}</span>
          </li> -->
      <!-- <li nz-menu-item>
            <span class="jump-tool" (click)="handleJumpDtd()">{{ 'dj-DTD规划工具' | translate }}</span>
          </li>
        </ul>
      </nz-dropdown-menu> -->

      <a
        class="tool-link"
        nz-dropdown
        [nzDropdownMenu]="helpCenter"
        [nzBackdrop]="true"
        nzTrigger="click"
        [ngClass]="{ 'router-link-active': router.url.startsWith('/help-center') }"
      >
        <span>{{ 'dj-帮助中心' | translate }}</span>
        <i adIcon class="iconfont" type="down"></i>
      </a>
      <nz-dropdown-menu #helpCenter="nzDropdownMenu">
        <ul nz-menu nzSelectable class="tool-menu">
          <li nz-menu-item>
            <span class="jump-tool" (click)="handleJump('courseCenter')">{{ 'dj-课程中心' | translate }}</span>
          </li>
          <li nz-menu-item>
            <span class="jump-tool" (click)="handleJump('developerDocumentation')">{{
              'dj-开发文档' | translate
            }}</span>
          </li>
          <li nz-menu-item>
            <span class="jump-tool" (click)="handleJump('forum')">{{ 'dj-论坛' | translate }}</span>
          </li>
          <li nz-menu-item>
            <span class="jump-tool" routerLink="/help-center/upgrade-log">{{ 'dj-系统通知' | translate }}</span>
          </li>
        </ul>
      </nz-dropdown-menu>
    </nav>
  </div>

  <div
    class="center"
    *ngIf="
      individualService.individualCase &&
      !individualService.isIndividualCaseApp(router.url) &&
      !['ai-assistance', 'ai-three', 'asa-designer-web'].includes(router.url.split('?')[0].split('/')[1])
    "
  >
    <nav class="nav-links">
      <a routerLink="/individual/apps" [ngClass]="{ 'router-link-active': isRouteActive('/individual/apps') }">
        <span>{{ 'dj-解决方案中心' | translate }}</span>
      </a>
      <a
        *ngIf="!isTenantActive"
        routerLink="/individual/data-center"
        [ngClass]="{ 'router-link-active': isRouteActive('/individual/data-center') }"
      >
        <span>{{ 'dj-数据中心' | translate }}</span>
      </a>
      <a
        *ngIf="!isTenantActive"
        routerLink="/individual/asset-center"
        [ngClass]="{ 'router-link-active': isRouteActive('/individual/asset-center') }"
      >
        <span>{{ 'dj-资产中心' | translate }}</span>
      </a>
      <a
        *ngIf="!isTenantActive"
        routerLink="/individual/template-center"
        [ngClass]="{ 'router-link-active': isRouteActive('/individual/template-center') }"
      >
        <span>{{ 'dj-模板中心' | translate }}</span>
      </a>
      <a
        routerLink="/individual/devOps-center"
        *ngIf="!isTenantActive && !isHideDeployer"
        [ngClass]="{ 'router-link-active': isRouteActive('/individual/devOps-center') }"
      >
        <span>{{ 'dj-运维中心' | translate }}</span>
      </a>
    </nav>
  </div>

  <div class="center model-driven-app-bar" *ngIf="showModelDrivenAppBar()">
    <nav class="nav-links">
      <a
        *ngFor="let tab of mdTabList"
        (click)="handleLastUrl(tab.type)"
        [ngClass]="{ 'router-link-active': ModelDrivenAppActive(tab) }"
      >
        <span>{{ tab.name | translate }}</span>
      </a>
    </nav>
  </div>

  <div class="center model-driven-app-bar" *ngIf="showAiModelAppBar()">
    <nav class="nav-links">
      <a
        *ngFor="let tab of aiModelTabList"
        [ngClass]="{ 'router-link-active': router.url.startsWith('/app/' + tab.type) }"
      >
        <span>{{ tab.name | translate }}</span>
      </a>
    </nav>
  </div>

  <!-- 独立解放方案，暂用于tbb调试 -->
  <div class="center" *ngIf="isStandaloneSolution">
    <nav class="nav-links">
      <a
        *ngFor="let menu of solutionHeaderMenus"
        [ngClass]="{ 'router-link-active': router.url.startsWith('/standalone-solution' + menu.route) }"
        (click)="handleGoUrl('/standalone-solution', menu)"
      >
        <span>{{ menu.name }}</span>
      </a>
      <ng-container *operateAuth="{ prefix: 'create' }">
        <a
          routerLink="standalone-solution/app-publish"
          [queryParams]="{ appCode: selectedApp?.code }"
          [ngClass]="{ 'router-link-active': router.url.startsWith('/standalone-solution/app-publish') }"
        >
          {{ 'dj-解决方案发布' | translate }}
        </a>
      </ng-container>
    </nav>
  </div>

  <!--分支列表-->
  <div class="right">
    <!-- 友情链接 -->
    <div
      *ngIf="!isTTTApp && !(isApp || individualService.isIndividualCaseApp(router.url))"
      class="dropdown other-links"
    >
      <a nz-dropdown [nzDropdownMenu]="linkMenu" [nzBackdrop]="true" nzTrigger="click">
        <span class="tenant">
          <i adIcon iconfont="iconlianjie" aria-hidden="true" class="iconfont"> </i>
        </span>
        <span [ngStyle]="{ marginLeft: '2px' }">{{ 'dj-友情链接' | translate }}</span>
      </a>
      <nz-dropdown-menu #linkMenu="nzDropdownMenu">
        <ul nz-menu nzSelectable class="language-set">
          <!-- 非个案设计器中的友情链接 -->
          <ng-container *ngIf="!individualService.individualCase">
            <li
              nz-menu-item
              nz-tooltip
              nzTooltipPlacement="left"
              [nzTooltipTitle]="'dj-终端客户配置设计器' | translate"
              *ngIf="!isTenantActive"
              (click)="handlesLinkTenant(true)"
            >
              {{ 'dj-租户设计器' | translate }}
            </li>
            <li nz-menu-item *ngIf="isTenantActive" (click)="handlesLinkTenant(false)">
              {{ 'dj-开发平台' | translate }}
            </li>
            <li
              nz-menu-item
              nz-tooltip
              nzTooltipPlacement="left"
              [nzTooltipTitle]="'dj-新智能工作模式，工作更轻松、简单、高效' | translate"
              (click)="handlesLinkOther('athena')"
            >
              {{ 'dj-智能入口' | translate }}
            </li>
            <li
              nz-menu-item
              nz-tooltip
              nzTooltipPlacement="left"
              [nzTooltipTitle]="'dj-管理本租户的IAM用户、解决方案授权' | translate"
              (click)="handlesLinkOther('console')"
            >
              {{ 'dj-云控制台' | translate }}
            </li>
            <li
              nz-menu-item
              nz-tooltip
              nzTooltipPlacement="left"
              [nzTooltipTitle]="'dj-图形化、视觉化的数据分析与数据自助式服务' | translate"
              (click)="handleJumpTo()"
            >
              {{ 'dj-敏捷BI-Tip Biu BI' | translate }}
            </li>
            <li nz-menu-item (click)="handleJump('home')">
              {{ 'dj-智客中心' | translate }}
            </li>
            <!-- 个案设计器入口 -->
            <li
              *ngIf="individualService.individualAuth"
              nz-menu-item
              nz-tooltip
              nzTooltipPlacement="left"
              [nzTooltipTitle]="'dj-标准应用二次开发设计器' | translate"
              (click)="handleJumpToIndividual()"
            >
              {{ 'dj-个案设计器' | translate }}
            </li>
          </ng-container>
          <!-- 个案设计器中的友情链接 -->
          <ng-container *ngIf="individualService.individualCase">
            <li nz-menu-item (click)="handlesLinkTenant(false)">
              {{ 'dj-开发平台' | translate }}
            </li>
            <li
              nz-menu-item
              nz-tooltip
              nzTooltipPlacement="left"
              [nzTooltipTitle]="'dj-新智能工作模式，工作更轻松、简单、高效' | translate"
              (click)="handlesLinkOther('athena')"
            >
              {{ 'dj-智能入口' | translate }}
            </li>
          </ng-container>
        </ul>
      </nz-dropdown-menu>
    </div>

    <app-branch-manage
      *ngIf="(isApp || individualService.isIndividualCaseApp(router.url)) && appService.selectedApp"
      [appCode]="appService?.selectedApp?.code"
    ></app-branch-manage>

    <!--用户-->
    <div *ngIf="!isTTTApp" class="dropdown user">
      <a
        class="user"
        nz-dropdown
        [nzDropdownMenu]="userMenu"
        [nzClickHide]="true"
        nzTrigger="click"
        nzOverlayClassName="user-menu"
      >
        <div class="profile">{{ currentUser?.userName?.slice(-2) }}</div>
        <div class="user-info">
          <div>{{ currentUser.name }}</div>
          <div>{{ currentTenant }}</div>
        </div>
      </a>
      <nz-dropdown-menu #userMenu="nzDropdownMenu">
        <div class="user-base-info">
          <div class="profile">{{ currentUser?.userName?.slice(-2) }}</div>
          <div class="name">{{ currentUser.name }}</div>
        </div>
        <ul class="tenants">
          <li
            *ngFor="let tenantItem of !individualService.individualCase ? currTenantList : currIndividualTenantList"
            [ngClass]="{ 'tenant-selected': tenantItem.tenantSid === currentTenantId }"
            appDebounce
            (debounceClick)="changeTenant(tenantItem)"
          >
            <i adIcon type="check" *ngIf="tenantItem.tenantSid === currentTenantId"></i>
            <span>{{ tenantItem.tenantName }}</span>
          </li>
        </ul>

        <ul nz-menu class="manage-wrapper">
          <li nz-menu-item appDebounce *ngIf="isSuperAdmin()" (click)="openAuthPanel()">
            <i adIcon iconfont="iconchaojiguanliyuan" aria-hidden="true" class="iconfont"> </i>
            {{ 'dj-超级管理' | translate }}
          </li>
          <li *ngIf="!individualService.individualCase" nz-menu-item>
            <span (click)="handleJumpBackground()">
              <i adIcon iconfont="iconcaigouhoutaiguanli" aria-hidden="true" class="iconfont manage"></i
              >{{ 'dj-后台管理' | translate }}
            </span>
          </li>
          <li nz-menu-item appDebounce (debounceClick)="logout()">
            <i adIcon iconfont="icondengchu-xuanting" aria-hidden="true" class="iconfont"> </i>
            {{ 'dj-登出' | translate }}
          </li>
        </ul>
      </nz-dropdown-menu>
    </div>
  </div>
</div>

<app-info-edit
  *ngIf="appInfoVisible"
  [visible]="appInfoVisible"
  [formData]="appFormData"
  (callback)="handleAppEdit($event)"
></app-info-edit>

<app-handle-release
  *ngIf="showRelease"
  [visible]="showRelease"
  [disableTenantSelection]="false"
  (cancel)="showRelease = false"
></app-handle-release>
<app-auth-panel *ngIf="authComService.authPanelData.visual"> </app-auth-panel>
