.menu-bar {
  display: flex;
  align-items: center;
  height: 48px;
  list-style: none;
  color: #000000;

  .left {
    display: flex;
    color: #000000;
    align-items: center;
    font-size: 14px;
    line-height: 22px;

    .logo {
      font-size: 20px;
      font-weight: bold;
      padding-left: 24px;
      cursor: pointer;
      display: flex;
      align-items: center;

      img {
        width: 112px;
        height: 17px;
      }
    }

    .logo-microSoft {
      img {
        width: 82px;
      }
    }

    .title {
      font-weight: 500;
      text-align: left;
      line-height: 22px;
      height: 21px;
      cursor: pointer;
      white-space: nowrap;
    }

    .appList {
      white-space: nowrap;

      .app {
        padding: 4px 0;
        margin-left: 10px;
        // margin-right: 24px;
      }

      .selected-app {
        width: auto;
        display: flex;
        align-items: flex-end;
        font-size: 14px;
        font-weight: 500;
        color: #6a4cff;
        // white-space: nowrap;

        .kit-icon {
          color: #000000;
          font-size: 16px;
          padding: 4px 0 0 8px;
        }

        .publish-tag {
          height: 20px;
          margin-left: 10px;
          padding: 0px 12px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: #ffffff;
          font-size: 12px;
          font-weight: 500;
          border-radius: 10px;
          background-color: rgba(250, 205, 145, 1);
        }

        .published {
          background-color: #85c700;
        }
      }

      .app-name {
        max-width: 100px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      .set-config {
        color: #6a4cff;
        position: relative;

        &::after {
          position: absolute;
          bottom: 0;
          left: 50%;
          margin-left: -12px;
          width: 24px;
          height: 3px;
          content: '';
          border-radius: 2px;
          background-color: #6a4cff;
        }
      }
    }

    .header-icon {
      margin-left: 10px;
    }

    ::ng-deep .ant-divider-vertical {
      height: 15px;
      background: #666666;
      top: 1px;
      margin: 0 10px;
      line-height: 22px;
    }
  }

  .center {
    position: relative;
    top: 2px;
    left: 30px;

    .nav-links {
      display: flex;

      & > a {
        position: relative;
        display: block;
        border-bottom: 3px solid rgba(0, 0, 0, 0);
        font-size: 13px;
        font-family: PingFangSC, PingFangSC-Regular;
        color: #333333;
        padding: 4px 12px;
        // margin: 0 12px;
      }

      .router-link-active {
        font-size: 14px;
        // font-family: PingFangSC, PingFangSC-Medium;
        font-weight: 500;
        color: #6a4cff;

        &::after {
          position: absolute;
          content: ' ';
          bottom: 0;
          left: 50%;
          margin-left: -12px;
          width: 24px;
          height: 3px;
          border-radius: 2px;
          background-color: #6a4cff;
        }
      }

      .tool-link {
        display: flex;
        align-items: center;

        // position: relative;
        .iconfont {
          margin-left: 4px;
          // position: absolute;
          // right: -15px;
        }
      }
    }
  }

  .right {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    color: #000000;
    padding-right: 24px;
    font-size: 12px;
    position: absolute;
    right: 0;

    > div {
      display: inline-block;

      &:not(:first-child) {
        margin-left: 15px;
      }
    }

    .tenant {
      padding: 1px 4px;
      background: #fff;
      border-radius: 50%;

      i {
        font-size: 12px;
        color: #6a4cff;
      }
    }

    .bell {
      position: relative;
      cursor: pointer;

      img {
        width: 1rem;
        margin-top: -1px;
      }

      app-badge {
        position: absolute;
        top: -0.3rem;
        right: -0.6rem;
      }

      span {
        background: red;
        color: #fff;
        width: 1rem;
        height: 1rem;
        display: inline-block;
        text-align: center;
        font-size: 0.9em;
        line-height: 1rem;
        border-radius: 50%;
        position: absolute;
        top: -0.3rem;
        right: -0.6rem;
      }
    }

    .dropdown {
      a {
        display: flex;
        color: #8e93b4;
        align-items: center;

        .anticon {
          margin-left: 3px;
        }
      }
    }

    .logout {
      a {
        color: #8e93b4;
      }
    }
  }

  .user {
    display: flex;
    align-items: center;

    .profile {
      font-size: 12px;
      color: #fff;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 30px;
      width: 30px;
      margin-right: 4px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      word-break: break-all;
      background-color: #1890ff;
      margin-right: 5px;
    }

    .user-info {
      font-size: 11px;
    }
  }
}

::ng-deep .user-menu {
  width: 200px;
  background-color: #fff;
  border-radius: 4px;
  outline: none;
  box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

.user-base-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 10px 0;

  .profile {
    font-size: 12px;
    color: #fff;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 30px;
    width: 30px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    word-break: break-all;
    background-color: #1890ff;
  }

  .name {
    font-size: 12px;
    padding-top: 5px;
    color: #8e93b4;
  }
}

// @media (min-width: 1380px) {
//   .menu-bar {
//     justify-content: center;
//   }
// }

// @media (min-width: 1220px) and (max-width: 1379px) {
//   .menu-bar {
//     justify-content: flex-start;
//   }
//   .center {
//     margin-left: 22%;
//   }
//   .model-driven-app-bar.center {
//     margin-left: 38%;
//   }
// }

// @media (max-width: 1219px) {
//   .menu-bar {
//     justify-content: flex-start;
//   }
//   .center {
//     margin-left: 18%;
//   }
//   .model-driven-app-bar.center {
//     margin-left: 38%;
//   }
// }

li.tenantdrop {
  font-size: 14px;

  i {
    color: #1890ff;
    font-size: 14px;
    margin-right: 5px;
    margin-top: 4px;
    display: inline-block;
    width: 14px;
    position: absolute;
  }

  span.tenantdrop-name {
    font-size: 14px;
    padding-left: 1.25rem;
    color: #5b5b66;
  }
}

li.link {
  color: #6a4cff;
  font-size: 16px;
}

::ng-deep li.ant-dropdown-menu-item {
  font-size: 16px;
  letter-spacing: 0;
  padding: 8px 14px;
}

.search-box input::placeholder {
  color: #8e93b4;
}

.search-tip-title {
  font-size: 10px;
  color: #5b5b66;
  letter-spacing: 0;
}

.search-tip-item {
  font-size: 12px;
  color: #34404b;
  letter-spacing: 0;
}

::ng-deep .language-set {
  .ant-dropdown-menu-item-selected {
    color: rgba(0, 0, 0, 0.65);
    background: transparent;
  }

  .active-lang {
    color: #6a4cff;
    background-color: #eef0ff;
  }

  .branch-operate {
    display: flex;
    justify-content: space-around;
    width: 100%;

    &:hover {
      .delete-operate {
        visibility: visible;

        .setIcon {
          font-size: 13px;
          color: #333333;

          &:hover {
            color: #6a4cff;
          }
        }
      }
    }

    .branch-name {
      width: calc(100% - 32px);
    }

    .delete-operate {
      visibility: hidden;
      width: 32px;
      padding-left: 16px;
    }
  }

  .hidden {
    &:hover {
      .delete-operate {
        visibility: hidden;
      }
    }
  }

  .branch-manage {
    display: flex;
    justify-content: space-around;
    width: 100%;

    .branch-name {
      width: calc(100% - 32px);
    }

    .new-operate {
      width: 32px;
      padding-left: 16px;
      font-size: 15px;
      color: #333333;

      &:hover {
        color: #6a4cff;
      }
    }
  }
}

::ng-deep .switch-branch-modal {
  .ant-modal-close {
    display: none;
  }
}

.tool-menu {
  a,
  .jump-tool {
    font-size: 13px;
    font-family: PingFangSC, PingFangSC-Regular;
    color: #333333;
  }

  .active {
    font-size: 14px;
    color: #6a4cff;
  }
}

.form-info {
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;

  ::ng-deep .ant-form-item-has-error .ant-form-item-explain {
    padding-left: 20px;
  }

  ::ng-deep .ant-form-item-control-input-content {
    align-items: baseline;
    display: flex;
  }

  ::ng-deep .center-control .ant-form-item-control-input-content {
    align-items: center;
  }

  ::ng-deep .question-icon {
    padding-right: 8px;
    font-size: 14px;
    cursor: pointer;
  }

  ::ng-deep .row-wrap {
    .ant-form-item {
      font-size: 13px;
      color: #333333;
      margin-bottom: 16px;
    }

    .ant-form-item-label {
      font-weight: Normal;
      height: 32px;
      line-height: 1;

      > label {
        font-size: 13px;
        color: #333333;
        height: 32px;
        line-height: 32px;
        width: 60px;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.ant-form-item-required::before {
          display: contents;
        }
      }
    }

    .lang-input {
      width: 100%;

      .input-container,
      .ant-input-affix-wrapper,
      .ant-input {
        width: 100%;
      }
    }
  }
}

.menu-bar-box {
  max-height: 90vh;
  overflow-y: auto;
}

.tenants {
  border-top: 1px solid rgba(0, 0, 0, 0.06);
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  max-height: 185px;
  overflow-y: auto;
  margin: 0;

  li {
    padding: 5px 12px;
    cursor: pointer;
    font-size: 13px;

    &:hover {
      background-color: #eef0ff;
    }
  }
}

.tenant-selected {
  background-color: #eef0ff;
  color: #2012d9;
}

.manage-wrapper {
  margin: 0;

  li {
    padding: 5px 12px;
    cursor: pointer;
    font-size: 14px;
    &:hover {
      background-color: #eef0ff;
    }
  }

  .iconfont {
    margin-right: 5px;
  }

  .manage {
    margin-right: 1px;
    font-size: 18px;
    color: #000;
    transform: translate(-1px, 3px);
  }
}

::ng-deep {
  svg {
    #iconcaigouhoutaiguanli {
      path {
        fill: inherit !important;
      }
    }
  }
}

.cooperate-enter-box {
  margin-left: 8px;
}
