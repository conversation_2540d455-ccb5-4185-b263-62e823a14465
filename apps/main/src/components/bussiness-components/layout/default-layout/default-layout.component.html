<nz-spin
  [nzSpinning]="
    isGobalLoading || appService.branchLoading || appService.appLoading || appService.individualAppLoading
  "
  [nzTip]="appService.branchLoading ? branchLoadingTip : ''"
>
  <div class="layout gpt-icon-drag-container" (click)="hideAuthPanel()">
    <div class="layout-menu" *ngIf="router.url !== '/tools/dtd-designer'">
      <app-menu-bar></app-menu-bar>
    </div>
    <app-mqtt-notification></app-mqtt-notification>
    <app-notification
      *ngIf="notificationParam.visible"
      [visible]="notificationParam.visible"
      [title]="notificationParam.title"
      [content]="notificationParam.content"
      (close)="handleNotificationClose()"
    ></app-notification>
    <nz-layout
      class="layout-content"
      [ngClass]="{
        'dtd-tool-layout-content': router.url === '/tools/dtd-designer',
        'model-design-layout-content': isModelDesignBackground
      }"
    >
      <nz-content>
        <router-outlet></router-outlet>
      </nz-content>
    </nz-layout>
    <!--暂时隐藏gpt（敏数解决方案不展示）-->
    <!-- <app-gpt *ngIf="appService.selectedApp && !isAgileDataApp" [appCode]="appService.selectedApp.code"></app-gpt> -->
    <app-collaborate-tip *ngIf="appService.selectedApp && !isAgileDataApp && !isAiModelApp" [appCode]="appService.selectedApp.code"></app-collaborate-tip>
    <!-- ai智能体 -->
    <app-ai-agent-manage></app-ai-agent-manage>
  </div>
</nz-spin>
<!-- 新手引导步骤条 -->
<app-intro-steps
  *ngIf="modelDrivenIntroService.showModelDrivenIntroStep"
  [introSteps]="modelDrivenIntroService.introSteps"
  [currentIntroStep]="modelDrivenIntroService.currentIntroStep"
></app-intro-steps>

<!-- <app-business-share-consumer-preload></app-business-share-consumer-preload> -->
