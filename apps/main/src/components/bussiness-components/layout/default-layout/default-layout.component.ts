import { AfterV<PERSON>w<PERSON>hecked, After<PERSON><PERSON>w<PERSON>nit, ChangeDetector<PERSON>ef, Component, OnDestroy, OnInit } from '@angular/core';
import { NavigationEnd, NavigationStart, Router, Event, ActivationEnd, ChildActivationEnd } from '@angular/router';
import { AppService } from 'pages/apps/app.service';
import { en_US, NzI18nInterface, NzI18nService, zh_CN, zh_TW } from 'ng-zorro-antd/i18n';
import { Subscription } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { AppTypes } from 'pages/app/typings';
import { AuthService } from '../../../../common/service/auth.service';
import { TenantService } from 'pages/login/service/tenant.service';
import { ModelDrivenIntroService } from 'common/service/intro/model-driven-intro/model-driven-intro.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { LocaleService } from 'common/service/locale.service';
import { AdUserService } from 'pages/login/service/user.service';
import { CollaborateService } from 'app/service/collaborate.service';
import { LayoutService } from '../layout.service';
import { GlobalService } from 'common/service/global.service';
import { NzNotificationService } from 'ng-zorro-antd/notification';

@Component({
  selector: 'app-default-layout',
  templateUrl: './default-layout.component.html',
  styleUrls: ['./default-layout.component.less'],
})
export class DefaultLayoutComponent implements OnInit, OnDestroy, AfterViewInit, AfterViewChecked {
  routerEventsSubscription: Subscription;
  deployerUrl: string;
  isLoadReleaseSession: boolean = false;
  branchLoadingTip: string;

  // 当前解决方案的类型：2->数据收集型 1：驱动型  undefined->老解决方案，表示驱动型
  appType: any;
  isGobalLoading: boolean = false;

  isModelDesignBackground: boolean = false;

  // 是否是场景化套件解决方案
  get isAgileDataApp() {
    return [AppTypes.AGILE_DATA, AppTypes.AGILE_QUESTIONS].includes(this.appService.selectedApp?.appType);
  }

  get isAiModelApp() {
    return this.appService.selectedApp?.appType === AppTypes.AI_MODEL;
  }

  /**
   * 提醒参数
   */
  notificationParam: {
    visible: boolean;
    title: string;
    content: string;
  } = {
    visible: false,
    title: '',
    content: '',
  };

  language: string;
  platformCategory: any; // 用户类别：SYSTEM -> 开发平台 ，TENANT -> 租户平台

  constructor(
    private languageService: LocaleService,
    public router: Router,
    public appService: AppService,
    private i18n: NzI18nService,
    protected adUserService: AdUserService,
    protected configService: SystemConfigService,
    private translateService: TranslateService,
    private cdr: ChangeDetectorRef,
    public authService: AuthService,
    private tenantService: TenantService,
    public modelDrivenIntroService: ModelDrivenIntroService,
    private layoutService: LayoutService,
    private globalService: GlobalService,
    private collaborateService: CollaborateService,
    private notification: NzNotificationService,
  ) {
    this.routerEventsSubscription = this.router.events.subscribe((event: Event) => {
      if (event instanceof NavigationStart) {
        // 敏捷数据解决方案存在离开路由守卫，跳过执行 loading
        if (this.isAgileDataApp) return;
        if (this.router.url.includes('app/integrated-automation/dtd-designer-view')) return;
        this.isGobalLoading = true;
      }
      if (event instanceof NavigationEnd || event instanceof ActivationEnd || event instanceof ChildActivationEnd) {
        this.isGobalLoading = false;
        this.initUrlChange();
      }
    });
    this.branchLoadingTip = this.translateService.instant('dj-正在切换分支，请勿关闭当前页面');
  }

  ngOnInit(): void {
    const url = this.router.url;
    const token = this.adUserService.getUser('iamToken');

    this.initLanguage();
    this.configService.get('athenaDeployUrl').subscribe((athenaDeployUrl) => {
      this.deployerUrl = athenaDeployUrl + `/sso-login?userToken=${token}`;
    });

    this.appType = this.appService.selectedApp?.appType;
    /**
     * 租户体验权限到期提醒
     */
    if (this.tenantService.isEduAndisExperience()) {
      this.tenantService.getExperienceExpires().subscribe((res) => {
        if (res.code === 0) {
          const notifications = res.data ?? {};
          const notificationKeys = Object.keys(notifications);
          if (notificationKeys?.length > 0) {
            const content = notificationKeys.map((key) => notifications[key]?.[this.language]).join('');
            this.notificationParam = {
              visible: true,
              title: this.translateService.instant('dj-租户体验权限到期提醒'),
              content,
            };
          }
        }
      });
    }

    this.configService.getConfig().subscribe((config: any) => {
      const { platformCategory } = config;
      this.platformCategory = platformCategory;
    });

    // 组户级开发平台处理
    if (this.adUserService.isLogin && this.platformCategory === 'TENANT') {
      this.adUserService.setUserInfo({ isTenantLogin: true }); // 是否是租户级登陆
      this.adUserService.setUserInfo({ isTenantActive: true }); // 租户级状态是否激活
    }

    this.adUserService.isLoggedIn$.subscribe((isLoggedIn) => {
      if (isLoggedIn && this.platformCategory === 'TENANT') {
        this.adUserService.setUserInfo({ isTenantLogin: true }); // 是否是租户级登陆
        this.adUserService.setUserInfo({ isTenantActive: true }); // 租户级状态是否激活
      }
    });

    /**
     * 创建协同socket连接
     */
    this.collaborateService.createCollaborateSocket();

    // 获取独立设计器注册信息
    this.queryStandaloneDesigners();
  }

  ngAfterViewInit(): void {
    // 解决方案快过期提醒
    this.solutionTimeoutTip();
  }

  ngAfterViewChecked(): void {
    this.cdr.detectChanges();
  }

  initUrlChange(): void {
    const url = this.router.url;
    // this.changeReleaseSession(url);
    this.initLanguage();
    this.changeModelDesignRoute(url);
  }

  // changeReleaseSession(url: string): void {
  //   if (url.includes('deployer/release') && !this.isLoadReleaseSession) {
  //     this.releaseService.loadFromSession();
  //     this.isLoadReleaseSession = true;
  //   } else if (!url.includes('deployer/release') && this.isLoadReleaseSession) {
  //     this.releaseService.removeSession();
  //     this.isLoadReleaseSession = false;
  //   }
  // }

  /**
   * 初始化语言设置
   */
  private initLanguage(): void {
    const language = this.adUserService.getUser('acceptLanguage') || this.languageService.currentLocale;
    this.i18n.setLocale(this.switchLanguage(language));
    this.languageService.switchLocale(language);
  }

  switchLanguage(type: string): NzI18nInterface {
    switch (type) {
      case 'zh_CN':
        return zh_CN;
      case 'zh_TW':
        return zh_TW;
      case 'en_US':
        return en_US;
      default:
        return zh_CN;
    }
  }

  changeModelDesignRoute(url) {
    this.isModelDesignBackground = false;
    if (url.includes('/app/application-overview') || url === '/' || url === '/apps') {
      this.isModelDesignBackground = true;
    }
  }

  /**
   * 控制面板点击其他地方让其消失
   */
  hideAuthPanel() {
    if (this.authService.authPanelData?.visual) {
      this.authService.closeAuthPanel();
    }
  }
  ngOnDestroy(): void {
    this.routerEventsSubscription.unsubscribe();
  }

  /**
   * 关闭提示信息
   */
  handleNotificationClose(): void {
    this.notificationParam = {
      visible: false,
      title: '',
      content: '',
    };
  }

  /**
   * 获取所有独立设计器注册信息
   */
  async queryStandaloneDesigners() {
    try {
      const res: any = await this.layoutService.queryStandaloneDesigners().toPromise();
      if (res.code === 0) {
        this.globalService.standaloneDesigners = res.data;
      }
    } catch (error) {}
  }

  /**
   * 解决方案快过期提醒
   */
  solutionTimeoutTip() {
    this.layoutService.querySolutionTimeout().subscribe((res: any) => {
      const currentLocale = this.languageService.currentLanguage;
      const keys = Object.keys(res.data);
      if (keys.length === 0) return;
      keys.forEach((key) => {
        this.notification.blank(this.translateService.instant('dj-友情提示'), res.data[key][currentLocale], {
          nzPlacement: 'topRight',
          nzDuration: 0,
        });
      });
    });
  }
}
