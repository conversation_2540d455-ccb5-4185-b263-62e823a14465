import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import { AppService } from '../../../pages/apps/app.service';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { ActionModalService } from './action-modal.service';
import { isNotNone } from 'common/utils/core.utils';

@Component({
  selector: 'app-action-modal',
  templateUrl: './action-modal.component.html',
  styleUrls: ['./action-modal.component.less'],
  providers: [ActionModalService],
})
export class ActionModalComponent implements OnInit {
  @Input() transferModal: boolean;
  @Input() transferData: any; // selectType: 是否显示type
  @Input() confirmTip: any;
  @Input() labelType: any;
  @Input() favouriteCode: string;
  @Input() applicationCodeProxy: string;

  @Output() callBack = new EventEmitter();
  @Output() closeModal = new EventEmitter();

  loading: boolean;
  searchValue: any; // 搜索关键字
  actionTypeData: any; // action type数据
  allActions: any[] = []; // 所有action数据

  selectedActionId: any; // 选中actionId
  selectedActionName: any; // 选择actionName
  selectedActionType: any; // 选择action类型
  selectedApplication: any; // 选择action所属解决方案

  actionPageNum: any; // action列表页码
  actionPageSize: number = 100; // action列表步长
  actionTotal: any; // action总数

  columnDefs: any[];

  selectedIndex: number = 0;
  tabs: any[] = [
    { label: 'dj-当前应用', value: 'curApp' },
    { label: 'dj-应用共享', value: 'appShare' },
    { label: 'dj-全局', value: 'global' },
  ];

  constructor(
    public actionService: ActionModalService,
    public service: AppService,
    private translate: TranslateService,
    private message: NzMessageService,
    private modal: AdModalService,
    private cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this.handleInitTabs();
    this.columnDefs = [
      {
        headerName: 'actionId',
        field: 'actionId',
        filter: false,
        sortable: false,
        width: 150,
        tooltipValueGetter: (params) => {
          return params.value || ' ';
        },
      },
      {
        headerName: 'actionName',
        field: 'actionName',
        filter: false,
        sortable: false,
        width: 90,
        tooltipValueGetter: (params) => {
          return params.value || ' ';
        },
      },
      {
        headerName: 'label',
        field: 'label',
        filter: false,
        sortable: false,
        width: 60,
        tooltipValueGetter: (params) => {
          return params.value || ' ';
        },
      },
    ];
    this.handleInit();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.favouriteCode?.currentValue) {
      this.actionService.favouriteCode = this.favouriteCode;
      this.actionService.headerCofig = { templateId: this.favouriteCode };
    }
    if (changes.applicationCodeProxy?.currentValue) {
      this.actionService.applicationCodeProxy = this.applicationCodeProxy;
    }
  }

  // 选择action开窗
  handleInit(): void {
    if (!!this.transferData['selectType']) {
      this.handleLoadActionType();
    } else {
      this.searchValue = '';
      this.selectedActionId = this.transferData['actionId'];
      this.selectedActionName = this.transferData['actionName'];
      this.selectedActionType = this.transferData['actionType'];
      let param;
      if (!this.labelType) {
        param = {
          condition: this.searchValue || '',
          pageNum: this.actionPageNum || 1,
        };
      } else {
        param = {
          condition: this.searchValue || '',
          pageNum: this.actionPageNum || 1,
          labelType: this.labelType,
        };
      }
      this.handleLoadAction(param);
    }
  }

  /**
   * 初始化tab标签
   */
  handleInitTabs(): void {
    if (!this.showGlobalTab()) {
      this.tabs.splice(-1, 1);
    }
  }

  // 左侧: 查询action类型
  handleLoadActionType(): void {
    if (this.transferData?.selectOptions) {
      this.actionTypeData = [...this.transferData?.selectOptions];
    } else {
      this.actionService.loadActionType().subscribe((res) => {
        if (res.code === 0) {
          this.actionTypeData = res.data || [];
        }
      });
    }
    this.handleChangeType(this.labelType);
  }

  // 修改action类型
  handleChangeType(val: any): void {
    this.actionPageNum = 1;
    const param: any = {
      labelType: val,
      condition: this.searchValue || '',
      pageNum: this.actionPageNum,
    };
    this.handleLoadAction(param);
  }

  handlePaginationChanged(event) {
    if (event.type === 'page-number') {
      this.actionPageNum = event.value;
      this.handleChangeActionNumOrSize();
    } else if (event.type === 'page-size') {
      this.actionPageSize = event.value;
      this.handleChangeActionNumOrSize();
    }
  }

  // action分页
  handleChangeActionNumOrSize(): void {
    let param;
    if (!this.labelType) {
      param = {
        condition: this.searchValue || '',
        pageNum: this.actionPageNum,
      };
    } else {
      param = {
        condition: this.searchValue || '',
        pageNum: this.actionPageNum,
        labelType: this.labelType,
      };
    }
    this.handleLoadAction(param);
  }

  // 模糊匹配
  handleSearchAction(): void {
    let param;
    if (!!this.searchValue) {
      this.actionPageNum = 1;
    }
    if (!this.labelType) {
      param = {
        condition: this.searchValue || '',
        pageNum: this.actionPageNum || 1,
      };
    } else {
      param = {
        condition: this.searchValue || '',
        pageNum: this.actionPageNum || 1,
        labelType: this.labelType,
      };
    }
    this.handleLoadAction(param);
  }

  // 查询action
  handleLoadAction(paramStr: any): void {
    this.loading = true;
    let params: any = {
      type: this.tabs[this.selectedIndex]?.value,
      condition: paramStr?.condition,
      pageNum: paramStr?.pageNum,
      pageSize: this.actionPageSize,
      needEsp: this.showGlobalTab(),
    };
    if (this.showActionType() && isNotNone(this.labelType)) {
      params = {
        ...params,
        label: this.labelType,
      };
    }

    if (this.service.selectedApp?.code) {
      params = {
        ...params,
        appCode: this.service.selectedApp?.code,
      };
    }

    this.actionService.loadActions(params).subscribe(
      (res) => {
        this.loading = false;
        if (res.code === 0 && params.type === this.tabs[this.selectedIndex]?.value) {
          this.allActions = res.data?.data || [];
          this.actionTotal = res.data?.total || 0;
          this.actionPageNum = res.data?.curPageNum || 0;
          this.allActions.map((item) => {
            item.checked = this.selectedActionId === item.actionId;
            return item;
          });
          // setTimeout(() => {
          //   const child = document.querySelector('.ant-pagination')?.children;
          //   const dom = document.querySelector('.page-number');
          //   if (!!dom) {
          //     dom['style'].left = `${child?.[0]?.['offsetLeft'] - 60}px`;
          //   }
          //   this.cd.detectChanges();
          // }, 10);
        }
        this.cd.detectChanges();
      },
      () => {
        this.loading = false;
      },
    );
  }

  handleRowSelected(event) {
    if (event.data?.checked) {
      this.handleSelectAction(event.data);
    }
  }

  // 选择action
  handleSelectAction(data: any): void {
    this.selectedActionId = data['actionId'];
    this.selectedActionName = data['actionName'];
    this.selectedActionType = data['label'];
    this.selectedApplication = data['application'];
  }

  // 关闭action
  handleCloseSelect(): void {
    this.selectedActionId = '';
    this.selectedActionName = '';
    this.selectedActionType = '';
    this.selectedApplication = '';
    this.actionTotal = 0;
    this.actionPageNum = 1;
    this.closeModal.emit();
  }

  // 确认action
  handleChangeAction(): void {
    if (!!this.selectedActionId) {
      const toDo = (): void => {
        this.actionTotal = 0;
        this.actionPageNum = 1;
        if (!!this.selectedActionId && !this.selectedActionType) {
          this.selectedActionType = (this.allActions.find((s) => s.actionId === this.selectedActionId) || {}).label;
        }
        this.callBack.emit({
          actionId: this.selectedActionId || '',
          actionName: this.selectedActionName || '',
          actionType: this.selectedActionType || '',
          application: this.selectedApplication || '',
        });
      };
      if (this.confirmTip) {
        this.modal.confirm({
          nzTitle: this.translate.instant('dj-确认修改action？'),
          nzWrapClassName: 'vertical-center-modal',
          nzOkText: this.translate.instant('dj-确定'),
          nzOnOk: () => {
            toDo();
          },
          nzOnCancel: () => {},
        });
      } else {
        toDo();
      }
    }
  }

  onFirstDataRendered(params) {
    params.api.sizeColumnsToFit();
  }

  handleSelectedIndexChange(e: any): void {
    this.searchValue = null;
    this.actionPageNum = 1;
    // this.actionPageSize = 100;
    if (this.showActionType()) {
      this.labelType = null;
    }
    this.handleSearchAction();
  }

  /**
   * 显示action类型
   * @returns
   */
  showActionType(): boolean {
    return [1, 2].includes(this.selectedIndex) && !!this.transferData['selectType'];
  }

  /**
   * 显示全局的Tab
   * @returns
   */
  showGlobalTab(): boolean {
    return !!this.transferData['selectType'] || this.labelType === 'EspAction';
  }
}
