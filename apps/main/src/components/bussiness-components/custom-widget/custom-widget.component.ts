import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { GlobalService } from 'common/service/global.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { AppTypes } from 'pages/app/typings';
import { AppService } from 'pages/apps/app.service';
import { AdUserService } from 'pages/login/service/user.service';

@Component({
  selector: 'app-custom-widget',
  templateUrl: './custom-widget.component.html',
  styleUrls: ['./custom-widget.component.less'],
})
export class CustomWidgetComponent implements OnInit {
  @Input() workType: any;
  @Input() taskPattern: any;
  @Input() codeTip: any;
  @Input() disableCustom: boolean;
  @Output() switchStandard = new EventEmitter<any>();
  @Output() maskClick = new EventEmitter<any>();
  MODEL_DRIVEN = AppTypes.MODEL_DRIVEN;
  enableOpenVscode: boolean;

  constructor(
    private userService: AdUserService,
    public appService: AppService,
    public globalService: GlobalService,
    private systemConfigService: SystemConfigService,
  ) {
    this.systemConfigService.get('enableOpenVscode').subscribe((val) => {
      this.enableOpenVscode = val === 'true';
    });
  }

  ngOnInit(): void {}

  handleSwitch(event: MouseEvent): void {
    event.stopPropagation();
    event.preventDefault();
    this.switchStandard.emit();
  }

  handleMaskClick(event: MouseEvent): void {
    event.stopPropagation();
    event.preventDefault();
    this.maskClick.emit();
  }

  openSource(event: MouseEvent): void {
    event.stopPropagation();
    event.preventDefault();
    // 打开源码，参数携带
    const route = 'manage';
    const tenantId = this.userService.getUser('tenantId');
    const token = this.userService.getUser('iamToken');
    const appCode = this.appService?.selectedApp?.code;

    const vscodeProtocol =
      `@vscodeinit&route=${route}&env=@env&version=@version&fileId=@fileId` +
      `&token=${token}&code=${appCode}&type=${this.codeTip}`;

    const encodedUrl = encodeURIComponent(vscodeProtocol);
    window.open(`/open-vscode?url=${encodedUrl}`, '_blank');
  }
}
