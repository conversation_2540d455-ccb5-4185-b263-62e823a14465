import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { AppService } from 'pages/apps/app.service';
import { ReleaseRecord } from '../release-record-list/release-record-list.component';
import { Subscription, timer } from 'rxjs';
import { exhaustMap, takeWhile, tap } from 'rxjs/operators';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AdUserService } from 'pages/login/service/user.service';
import { IntroService } from 'common/service/intro/intro.service';
import { NzProgressStatusType } from 'ng-zorro-antd/progress';
import { DeployService } from '../../../../pages/deploy/deploy.service';
import { tenantNotAllowedToPublishList } from 'common/config/common.config';
import { AuthService } from 'common/service/auth.service';
import { AuthOperate } from 'common/types/auth.types';

@Component({
  selector: 'app-handle-release',
  templateUrl: './handle-release.component.html',
  styleUrls: ['./handle-release.component.less'],
})
export class HandleReleaseComponent implements OnInit, OnDestroy {
  @Input() visible: boolean = false;
  @Input() disableTenantSelection: boolean = false; // 禁用租户选项、默认选择当前租户
  @Output() cancel: EventEmitter<any> = new EventEmitter();
  compileLoading: boolean = false;
  envList: { env: string; platformAddress: string; [key: string]: any }[];
  tenantList: { name: string; id: string; sid: string }[];
  versionForm: FormGroup; // 版本表单
  executeStatus: NzProgressStatusType; //执行状态；
  executePercent: number = 0;
  showDetail: boolean;
  releaseRecordList: ReleaseRecord[] = [];
  ProcessSub$: Subscription;
  env: string;
  deployNo: string;

  disabled: boolean = false;
  authOperate = AuthOperate;

  constructor(
    private deployService: DeployService,
    private fb: FormBuilder,
    private appService: AppService,
    private messageService: NzMessageService,
    private translateService: TranslateService,
    protected userService: AdUserService,
    private introService: IntroService,
    public authService: AuthService,
  ) {}

  ngOnInit() {
    const userInfo = JSON.parse(sessionStorage.getItem('AdUserInfo'));
    this.disabled = tenantNotAllowedToPublishList.includes(userInfo.tenantId);

    this.executeStatus = null;
    this.executePercent = 0;
    this.env = '';
    this.deployNo = '';
    if (this.disableTenantSelection) {
      const userInfo = JSON.parse(sessionStorage.getItem('AdUserInfo'));
      const tenant = userInfo.currTenantList.find((item) => item.sid === userInfo.tenantSid);
      this.versionForm = this.fb.group({
        env: [null, Validators.required],
        tenantId: [tenant?.tenantId, Validators.required],
        syncEsp: [false],
        description: [''],
      });
      this.tenantList = [
        {
          id: tenant?.tenantId,
          sid: tenant?.tenantSid,
          name: tenant?.tenantName,
        },
      ];
    } else {
      this.versionForm = this.fb.group({
        env: [null, Validators.required],
        tenantId: [null, Validators.required],
        syncEsp: [false],
        description: [''],
      });
    }
    this.loadEnvs();
    this.getLastSelected();
  }

  ngOnDestroy() {
    this.ProcessSub$?.unsubscribe();
  }

  /*
     获取环境
   */
  loadEnvs() {
    this.deployService
      .loadEnvs({
        operate: 'publish',
        application: this.appService?.selectedApp?.code,
      })
      .subscribe((res) => {
        if (res.code === 0) {
          this.envList = res.data;
        }
      });
  }

  /** 获取上一次选择的结果 */
  getLastSelected(): void {
    this.deployService.queryAppLatestInfo(this.appService?.selectedApp?.code).subscribe((res) => {
      const { code, message, data } = res;
      if (code === 0) {
        if (!data) return;
        const { latestEnv = '', latestTenant = '' } = data;
        if (this.disableTenantSelection) {
          this.versionForm.patchValue({
            env: latestEnv,
          });
        } else {
          this.versionForm.patchValue({
            env: latestEnv,
            tenantId: latestTenant,
          });
        }
      } else {
        this.messageService.error(message);
      }
    });
  }

  // 选择环境 --选择租户变更
  handleChangeEnv(val: any): void {
    if (!this.disableTenantSelection) {
      if (!val) {
        this.tenantList = [];
        this.versionForm.patchValue({
          tenantId: '',
        });
        return;
      }
      this.deployService.loadTenantsByEnv(val).subscribe(
        (res) => {
          if (res.code === 0) {
            // 租户列表
            this.tenantList = res.data;
          } else {
            this.tenantList = [];
          }
        },
        () => {
          this.tenantList = [];
        },
      );
      this.versionForm.get('tenantId').reset();
    }
  }

  handleModalOk() {
    for (const i of Object.keys(this.versionForm?.controls)) {
      this.versionForm.controls[i].markAsDirty();
      this.versionForm.controls[i].updateValueAndValidity();
    }
    if (this.versionForm.valid) {
      const data = this.versionForm.getRawValue() || {};
      const tenant = this.tenantList?.find((item) => item.id == data?.tenantId);
      const param = {
        env: data?.env,
        syncEsp: data?.syncEsp,
        tenantUsers: [{ tenantId: data?.tenantId, option: 1, tenantSid: tenant?.sid }],
        applicationDataList: [
          {
            application: this.appService?.selectedApp?.code,
            description: data?.description,
            branch: this.userService.getUser('branch') ?? 'develop',
          },
        ],
      };
      this.env = param?.env;
      this.executeStatus = 'active';
      this.deployService.deployVersionProcess(param).subscribe(
        (res) => {
          if (res.code === 0) {
            this.interProcess(res?.data);
            this.deployNo = res?.data;
          }
        },
        () => {
          this.executeStatus = 'exception';
          // 优化: 后端提示报错 前端无需自己报错版更失败
          // this.messageService.error(this.translateService.instant('dj-版更失败'));
        },
      );
    }
  }

  /**
   * 轮询版更进度
   */
  interProcess(deployNo) {
    const wait_for_sec = 2;
    this.ProcessSub$ = timer(0, wait_for_sec * 1000)
      .pipe(
        exhaustMap(() => this.deployService.queryAppDeployProcess(deployNo, this.appService?.selectedApp?.code)),
        tap((res) => {
          this.executePercent = (res?.data || 0) * 100;
          // 版更失败
          if (res?.data === -1) {
            throw new Error();
          }
        }),
        takeWhile((res) => !(res?.data === 1)),
      )
      .subscribe(
        (data) => {},
        (err) => {
          this.executeStatus = 'exception';
          this.messageService.error(this.translateService.instant('dj-版更失败'));
        },
        () => {
          this.executeStatus = 'success';
          this.messageService.success(this.translateService.instant('dj-已完成版更'));
          // 版更成功之后，更新 appService.selectedApp.published 为 true
          this.appService.selectedApp.published = true;
          this.introService.play('appReleaseCheckStep');
        },
      );
  }

  toAthena() {
    this.deployService.getRedisIdByAppCode(this.appService?.selectedApp?.code).subscribe(
      (res) => {
        if (res.code === 0) {
          const origin = this.envList.find((item) => this.env == item.env)?.platformAddress;
          const appCode = this.appService?.selectedApp?.code;
          const iamToken = this.userService.getUser('iamToken');
          const experience = this.introService?.isActive;
          let url = `${origin}/sso-login?userToken=${iamToken}&routerLink=todo/project&appCode=${appCode}&experience=${experience}`;
          if (res?.data) url = url + `&guideScript=${res?.data}`;
          window.open(url, '_blank');
        }
      },
      (error) => {
        console.log(error);
      },
    );
  }

  handleCancel() {
    this.cancel.emit();
  }

  handleDetail() {
    this.deployService
      .queryDeployDetailByApplication({
        deployNo: this.deployNo,
        application: this.appService?.selectedApp?.code,
      })
      .subscribe(
        (res) => {
          if (res.code === 0) {
            this.showDetail = true;
            this.releaseRecordList = res?.data;
          }
        },
        () => {
          this.showDetail = false;
        },
      );
  }
}
