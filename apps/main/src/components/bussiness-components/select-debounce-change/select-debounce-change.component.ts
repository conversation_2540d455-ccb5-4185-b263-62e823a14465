import {Component, EventEmitter, forwardRef, Input, Output} from '@angular/core';
import {ControlValueAccessor, NG_VALUE_ACCESSOR} from "@angular/forms";
import {debounce} from 'lodash';

@Component({
  selector: 'app-select-debounce-change',
  templateUrl: './select-debounce-change.component.html',
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SelectDebounceChangeComponent),
      multi: true,
    },
  ],
})
export class SelectDebounceChangeComponent implements ControlValueAccessor {
  @Input() label: string;
  @Input() required: boolean = true;
  @Input() style: string = '';
  @Input() value: any;
  @Input() allowInput: boolean = true;
  @Input() optionLists: any[] = [];
  @Input() debounceTime: number = 100;
  @Output() change: EventEmitter<any> = new EventEmitter<any>();

  onChange(value) {}
  private onTouched: () => void = () => {};

  registerOnChange(fn: any): void {
    const debounceFn = debounce((value) => {
      this.change.emit(value);
      return fn(value);
    }, this.debounceTime);
    this.onChange = debounceFn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  writeValue(value: any): void {
    this.value = value;
  }
}
