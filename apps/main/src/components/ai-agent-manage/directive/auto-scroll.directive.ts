import { Directive, ElementRef, AfterViewChecked, Input } from '@angular/core';

@Directive({ selector: '[autoScroll]' })
export class AutoScrollDirective implements AfterViewChecked {
  @Input() autoScrollEnabled = true;

  constructor(private el: ElementRef) {}

  ngAfterViewChecked() {
    if (this.autoScrollEnabled && this._shouldScroll()) {
      this._scrollToBottom();
    }
  }

  private _shouldScroll(): boolean {
    const elem = this.el.nativeElement;
    const threshold = 200;
    return elem.scrollTop + elem.clientHeight >= elem.scrollHeight - threshold;
  }

  private _scrollToBottom(): void {
    this.el.nativeElement.scrollTop = this.el.nativeElement.scrollHeight;
  }
}
