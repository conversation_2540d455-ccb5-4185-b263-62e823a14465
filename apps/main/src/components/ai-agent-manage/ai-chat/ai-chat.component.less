.ai-chat-container {
  display: flex;
  flex-direction: column;
  position: relative;
  width: 400px;
  height: calc(100vh - 48px);
  min-height: 600px;
  overflow-y: auto;
  background-color: #fff;
  box-shadow: -3px 0px 4px 0px rgba(0, 0, 0, 0.02),-2px 0px 3px 0px rgba(0, 0, 0, 0.03),-1px 0px 2px 0px rgba(0, 0, 0, 0.01);

  // 左侧拖拽调整宽度的手柄
  .resize-handle {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 6px;
    cursor: ew-resize;
    background: transparent;
    z-index: 10;
    transition: all 0.2s ease;

    &:hover {
      background: rgba(96, 92, 229, 0.1);
      width: 8px;
      left: -1px;
    }

    &.resizing {
      background: rgba(96, 92, 229, 0.2);
      width: 8px;
      left: -1px;
    }

    // 添加一个更明显的拖拽指示器
    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      width: 2px;
      height: 60px;
      background: rgba(96, 92, 229, 0.4);
      border-radius: 1px;
      opacity: 0;
      transition: all 0.2s ease;
    }

    &:hover::before {
      opacity: 0.6;
      height: 80px;
    }

    &.resizing::before {
      opacity: 1;
      height: 100px;
      background: rgba(96, 92, 229, 0.8);
    }
  }

  .chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;

    .chat-header-col {
      flex: 1;
      display: flex;
      gap: 8px;

      &:nth-of-type(1) {
        
      }

      &:nth-of-type(2) {
        display: inline-flex;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        letter-spacing: normal;
        color: #1D1C33;
        justify-content: center;
        img {
          height: 20px;
          width: 20px;
        }

      }

      &:nth-last-of-type(1){
        flex-direction: row-reverse;
      }

      .header-icon-button {
        height: 24px;
        width: 24px;
        color: #1D1C33;
        text-align: center;
        font-size: 20px;
        line-height: 24px;
        cursor: pointer;
        &.font-size-24 {
          font-size: 24px;
        }
        &.disabled{
          opacity: 0.75;
          ::ng-deep  & > svg{
            cursor: not-allowed !important;
          }
        }
      }
  
      .new-chat {
        border-radius: 4px;
        position: relative;
        background: transparent;
        border: 1px solid transparent;
        height: 24px;
        padding: 0 6px;
        line-height: 22px;
        cursor: pointer;
        
        &::after {
          content: '';
          position: absolute;
          inset: 0;
          border-radius: 4px;
          padding: 1px;
          background: linear-gradient(270deg, #B38FFF 0%, #65B5FF 99%);
          -webkit-mask: 
            linear-gradient(#fff 0 0) content-box, 
            linear-gradient(#fff 0 0);
          -webkit-mask-composite: xor;
          mask-composite: exclude;
          pointer-events: none;
        }
        span {
          font-size: 12px;
          background: linear-gradient(271deg, #B38FFF 0%, #65B5FF 99%);
          background-clip: text;
          color: transparent;
        }
      }
    }
  
  }
  
  .chat-body {
    flex: 1;
    overflow-y: auto;
    padding: 16px;
    .chat-message-container {
      margin-bottom: 16px;
      width: 100%;
      max-width: 800px;
      margin-left: auto;
      margin-right: auto;
    }
  }
  
  .chat-footer {
    width: 100%;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;

    padding: 16px;
    transition: all 0.3s ease-in-out;

  
    .input-container {
      position: relative;
      width: 100%;
      background-color: #fff;
      border: 1px solid #D0D0D9;
      border-radius: 12px;
      padding: 16px;
      padding-bottom: 60px;
      box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.05);

      .textarea-wrap{
        position: relative;
        .textarea-auto-height{
          position: absolute;
          line-height: 20px;
          padding: 0;
          width: 100%;
          height: 100%;
          min-height: 100%;
          resize: none;
          border: none;
        }

        .textarea-mirror{
          padding: 0;
          overflow: hidden;
          font-size: 13px;
          white-space: pre-wrap;
          visibility: hidden;
          min-height: 20px;
          max-height: 100px;
          word-wrap: break-word;
          word-break: break-all;
        }
      }
  
      .button-group {
        position: absolute;
        left: 0;
        bottom: 16px;
        width: 100%;
        height: 28px;
        display: flex;
        flex-direction: row-reverse;
        padding: 0 16px;
        .send-message-button{
          height: 28px;
          width: 28px;
          border-radius: 14px;
          color: #605CE5;
          font-size: 28px;
          line-height: 28px;
          text-align: center;
          &.disabled{
            color: #C2C0FC;
            ::ng-deep  & > svg{
              cursor: not-allowed !important;
            }
          }
        }
      }
    }
  }
}






