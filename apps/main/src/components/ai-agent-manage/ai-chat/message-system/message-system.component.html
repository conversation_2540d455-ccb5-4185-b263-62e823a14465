<div class="message-system" [ngClass]="msg.systemType">
  <div class="message-content" *ngIf="msg.systemType === 'error'">
    {{ msg.content }}
  </div>
  <div class="message-content" *ngIf="msg.systemType === 'welcome'">
    <div class="welcome-message-content">{{msg.aiAgent?.lang?.welcomeMessage?.[currentLanguage] ?? ''}}</div>
    <div class="welcome-desc-content">{{msg.aiAgent?.lang?.agentDesc?.[currentLanguage] ?? ''}}</div>
  </div>
</div>
