import {
  Component,
  OnInit,
  Input,
  ElementRef,
  ViewChild,
  OnChanges,
  SimpleChanges,
  AfterViewInit,
} from '@angular/core';
import { ChatMessageSystem } from '../../config/ai-agent-manage.type';
import Vditor from 'vditor';
import { LocaleService } from '../../../../common/service/locale.service';

@Component({
  selector: 'app-message-system',
  templateUrl: './message-system.component.html',
  styleUrls: ['./message-system.component.less'],
})
export class MessageSystemComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() msg: ChatMessageSystem;
  @Input() loading: boolean = false;
  currentLanguage: string;

  constructor(private localeService: LocaleService) {
    this.currentLanguage = this.localeService?.currentLanguage || 'zh_CN';
  }

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  ngAfterViewInit(): void {}
}
