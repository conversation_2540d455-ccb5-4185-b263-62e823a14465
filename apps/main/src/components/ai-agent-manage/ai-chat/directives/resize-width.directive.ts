import { Directive, ElementRef, Input, Output, EventEmitter, OnDestroy, Renderer2 } from '@angular/core';

@Directive({
  selector: '[appResizeWidth]'
})
export class ResizeWidthDirective implements OnDestroy {
  @Input() minWidth = 300;
  @Input() maxWidth = 800;
  @Input() initialWidth = 400;
  @Input() storageKey = 'resize-width';
  @Output() widthChange = new EventEmitter<number>();

  private currentWidth: number;
  private isResizing = false;
  private startX = 0;
  private startWidth = 0;
  private resizeHandle: HTMLElement;

  // 绑定事件处理函数到实例
  private boundOnMouseMove = this.onMouseMove.bind(this);
  private boundOnMouseUp = this.onMouseUp.bind(this);

  constructor(
    private el: ElementRef,
    private renderer: Renderer2
  ) {
    this.initializeWidth();
    this.createResizeHandle();
  }

  ngOnDestroy(): void {
    this.cleanup();
  }

  private initializeWidth(): void {
    // 从本地存储恢复宽度
    const savedWidth = localStorage.getItem(this.storageKey);
    if (savedWidth) {
      const width = parseInt(savedWidth, 10);
      if (width >= this.minWidth && width <= this.maxWidth) {
        this.currentWidth = width;
      } else {
        this.currentWidth = this.initialWidth;
      }
    } else {
      this.currentWidth = this.initialWidth;
    }

    // 设置初始宽度
    this.setElementWidth(this.currentWidth);
    this.widthChange.emit(this.currentWidth);
  }

  private createResizeHandle(): void {
    // 创建拖拽手柄
    this.resizeHandle = this.renderer.createElement('div');
    this.renderer.addClass(this.resizeHandle, 'resize-handle');
    this.renderer.setAttribute(this.resizeHandle, 'title', '拖拽调整宽度，双击重置');
    
    // 添加样式
    this.renderer.setStyle(this.resizeHandle, 'position', 'absolute');
    this.renderer.setStyle(this.resizeHandle, 'left', '0');
    this.renderer.setStyle(this.resizeHandle, 'top', '0');
    this.renderer.setStyle(this.resizeHandle, 'bottom', '0');
    this.renderer.setStyle(this.resizeHandle, 'width', '6px');
    this.renderer.setStyle(this.resizeHandle, 'cursor', 'ew-resize');
    this.renderer.setStyle(this.resizeHandle, 'background', 'transparent');
    this.renderer.setStyle(this.resizeHandle, 'z-index', '10');
    this.renderer.setStyle(this.resizeHandle, 'transition', 'all 0.2s ease');

    // 添加伪元素样式（通过动态创建style标签）
    this.addResizeHandleStyles();

    // 绑定事件
    this.renderer.listen(this.resizeHandle, 'mousedown', this.startResize.bind(this));
    this.renderer.listen(this.resizeHandle, 'dblclick', this.resetWidth.bind(this));

    // 将手柄添加到元素中
    this.renderer.appendChild(this.el.nativeElement, this.resizeHandle);
  }

  private addResizeHandleStyles(): void {
    const styleId = 'resize-handle-styles';
    if (!document.getElementById(styleId)) {
      const style = this.renderer.createElement('style');
      this.renderer.setAttribute(style, 'id', styleId);
      this.renderer.appendChild(style, this.renderer.createText(`
        .resize-handle:hover {
          background: rgba(96, 92, 229, 0.1) !important;
          width: 8px !important;
          left: -1px !important;
        }
        
        .resize-handle.resizing {
          background: rgba(96, 92, 229, 0.2) !important;
          width: 8px !important;
          left: -1px !important;
        }
        
        .resize-handle::before {
          content: '';
          position: absolute;
          left: 50%;
          top: 50%;
          transform: translate(-50%, -50%);
          width: 2px;
          height: 60px;
          background: rgba(96, 92, 229, 0.4);
          border-radius: 1px;
          opacity: 0;
          transition: all 0.2s ease;
        }
        
        .resize-handle:hover::before {
          opacity: 0.6;
          height: 80px;
        }
        
        .resize-handle.resizing::before {
          opacity: 1;
          height: 100px;
          background: rgba(96, 92, 229, 0.8);
        }
      `));
      this.renderer.appendChild(document.head, style);
    }
  }

  private startResize(event: MouseEvent): void {
    event.preventDefault();
    this.isResizing = true;
    this.startX = event.clientX;
    this.startWidth = this.currentWidth;

    // 添加resizing类
    this.renderer.addClass(this.resizeHandle, 'resizing');

    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', this.boundOnMouseMove);
    document.addEventListener('mouseup', this.boundOnMouseUp);
    
    // 防止文本选择
    this.renderer.setStyle(document.body, 'user-select', 'none');
    this.renderer.setStyle(document.body, 'cursor', 'ew-resize');
  }

  private onMouseMove(event: MouseEvent): void {
    if (!this.isResizing) return;

    const deltaX = this.startX - event.clientX; // 向左拖拽为正值
    const newWidth = this.startWidth + deltaX;

    // 动态计算最大宽度，不超过屏幕宽度的80%
    const dynamicMaxWidth = Math.min(this.maxWidth, window.innerWidth * 0.8);

    // 限制宽度范围
    this.currentWidth = Math.max(this.minWidth, Math.min(dynamicMaxWidth, newWidth));
    
    // 更新元素宽度
    this.setElementWidth(this.currentWidth);
    this.widthChange.emit(this.currentWidth);
  }

  private onMouseUp(): void {
    this.isResizing = false;
    
    // 移除resizing类
    this.renderer.removeClass(this.resizeHandle, 'resizing');
    
    // 移除全局事件监听
    document.removeEventListener('mousemove', this.boundOnMouseMove);
    document.removeEventListener('mouseup', this.boundOnMouseUp);
    
    // 恢复默认样式
    this.renderer.removeStyle(document.body, 'user-select');
    this.renderer.removeStyle(document.body, 'cursor');
    
    // 保存宽度到本地存储
    localStorage.setItem(this.storageKey, this.currentWidth.toString());
  }

  private resetWidth(): void {
    this.currentWidth = this.initialWidth;
    this.setElementWidth(this.currentWidth);
    this.widthChange.emit(this.currentWidth);
    localStorage.setItem(this.storageKey, this.currentWidth.toString());
  }

  private setElementWidth(width: number): void {
    this.renderer.setStyle(this.el.nativeElement, 'width', `${width}px`);
  }

  private cleanup(): void {
    if (this.isResizing) {
      document.removeEventListener('mousemove', this.boundOnMouseMove);
      document.removeEventListener('mouseup', this.boundOnMouseUp);
      this.renderer.removeStyle(document.body, 'user-select');
      this.renderer.removeStyle(document.body, 'cursor');
    }
  }
}
