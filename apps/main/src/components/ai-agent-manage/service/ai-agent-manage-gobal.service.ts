import { Injectable } from '@angular/core';

@Injectable({ providedIn: 'root' })
export class AiAgentGobalManageService {
  // ========================= 应用级共享属性 =========================
  private _isShowChat: boolean = false; // 是否显示聊天窗口

  get isShowChat(): boolean {
    return this._isShowChat;
  }

  // ========================= 模块级属性的操作 =========================

  setIsShowChat(value: boolean): void {
    this._isShowChat = value;
  }
}
