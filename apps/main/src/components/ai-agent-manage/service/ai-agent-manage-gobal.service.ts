import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class AiAgentGobalManageService {
  // ========================= 应用级共享属性 =========================
  private _isShowChat: boolean = false; // 是否显示聊天窗口

  get isShowChat(): boolean {
    return this._isShowChat;
  }

  // 创建模型返回的数据
  private _generateModelChange$: Subject<any> = new Subject<any>();
  get generateModelChange$(): Observable<any> {
    return this._generateModelChange$.asObservable();
  }

  // ========================= 应用级共享属性的操作 =========================

  setIsShowChat(value: boolean): void {
    this._isShowChat = value;
  }

  setGenerateModel(value: any): void {
    this._generateModelChange$.next(value);
  }
}
