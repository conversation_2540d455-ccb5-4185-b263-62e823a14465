import { Component, OnInit, OnChanges, OnDestroy, SimpleChanges, AfterViewInit } from '@angular/core';
import { AiAgentManageService } from './service/ai-agent-manage.service';
import { AiAgentGobalManageService } from './service/ai-agent-manage-gobal.service';
import { AiAgentManageRequestService } from './service/ai-agent-manage-request.service';
import { TranslateService } from '@ngx-translate/core';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AiChatSseService } from './service/ai-chat-sse.service';

@Component({
  selector: 'app-ai-agent-manage',
  templateUrl: './ai-agent-manage.component.html',
  styleUrls: ['./ai-agent-manage.component.less'],
  providers: [AiAgentManageService, AiAgentManageRequestService, AiChatSseService],
})
export class AiAgentManageComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  loading = false;
  isDragging = false;

  constructor(
    public aiAgentManageService: AiAgentManageService,
    private aiAgentManageRequestService: AiAgentManageRequestService,
    private translateService: TranslateService,
    private modal: AdModalService,
    private athMessageService: NzMessageService,
    private aiChatSseService: AiChatSseService,
    public aiAgentGobalManageService: AiAgentGobalManageService,
  ) {}

  ngOnInit(): void {
    // 初始化 AI Agent Manage 组件
  }

  ngAfterViewInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {}

  ngOnDestroy(): void {
    // 确保在组件销毁时关闭 SSE 连接
    this.aiChatSseService.closeConnection();
  }

  // ======= 接口请求 =======
  // 请求资产列表
  // 资产下架（资产中心）
  removedAssetOpenTenantRequest(params: unknown): Promise<unknown> {
    return this.aiAgentManageRequestService.removedAssetOpenTenant(params).toPromise();
  }

  // ======= 接口处理 =======
  // 下架资产
  async handleRemovedAssetOpenTenant(assetId: string): Promise<void> {
    const params = {
      assetId,
    };
    try {
      this.loading = true;
      await this.removedAssetOpenTenantRequest(params);
      this.athMessageService.success(this.translateService.instant('dj-操作成功'));
    } catch (error) {
      console.log('handleConfirm error:', error);
    } finally {
      this.loading = false;
    }
  }

  // ======= 其他业务逻辑 =======
  handleClickAiAgentManage(): void {
    if (!this.isDragging) this.aiAgentGobalManageService.setIsShowChat(true);
    this.isDragging = false;
  }
}
