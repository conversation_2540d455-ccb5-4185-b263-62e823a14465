.ai-agent-manage {
  &.isShowChat {
    .ai-chat-wrapper {
      display: block;
    }
    .ai-agent-logo-wrapper {
      display: none;
    }
  }
  
  .ai-chat-wrapper {
    position: fixed;
    z-index: 999;
    bottom: 0;
    right: 0;
    margin: 0 auto;
    display: none;

    &.maximized{
      left: 0;
      ::ng-deep .ai-chat-container{
        width: 100%;
      }
    }
  }

  .ai-agent-logo-wrapper{
    position: fixed;
    bottom: 24px;
    right: 12px;
    z-index: 999;
    height: 48px;
    width: 48px;
    overflow: hidden;
    .ai-agent-logo{
      cursor: move;
      width: 360px;
      height: 360px;
      transform: scale(calc( 48 / 360 ));
      transform-origin: 0% 0%;
      background: url('/assets/img/ai-agent-nana.png') 0 0 no-repeat;
      background-size: 4680px 2160px;
      animation: sprite-animation 5s infinite;
      animation-timing-function: steps(1);
    }
  }


  
  @keyframes sprite-animation {
    0% { background-position: 0 0; } 
    1% { background-position: -360px 0; } 
    2% { background-position: -720px 0; } 
    3% { background-position: -1080px 0; } 
    4% { background-position: -1440px 0; } 
    5% { background-position: -1800px 0; } 
    6% { background-position: -2160px 0; } 
    7% { background-position: -2520px 0; } 
    8% { background-position: -2880px 0; } 
    9% { background-position: -3240px 0; } 
    10% { background-position: -3600px 0; } 
    11% { background-position: -3960px 0; } 
    12% { background-position: -4320px 0; } 
    13% { background-position: 0 -360px; } 
    14% { background-position: -360px -360px; }
    15% { background-position: -720px -360px; }
    16% { background-position: -1080px -360px; }
    17% { background-position: -1440px -360px; }
    18% { background-position: -1800px -360px; }
    19% { background-position: -2160px -360px; }
    20% { background-position: -2520px -360px; }
    21% { background-position: -2880px -360px; }
    22% { background-position: -3240px -360px; }
    23% { background-position: -3600px -360px; }
    24% { background-position: -3960px -360px; }
    25% { background-position: -4320px -360px; }
    26% { background-position: 0 -720px; }
    27% { background-position: -360px -720px; }
    28% { background-position: -720px -720px; }
    29% { background-position: -1080px -720px; }
    30% { background-position: -1440px -720px; }
    31% { background-position: -1800px -720px; }
    32% { background-position: -2160px -720px; }
    33% { background-position: -2520px -720px; }
    34% { background-position: -2880px -720px; }
    35% { background-position: -3240px -720px; }
    36% { background-position: -3600px -720px; }
    37% { background-position: -3960px -720px; }
    38% { background-position: -4320px -720px; }
    39% { background-position: 0 -1080px; }
    40% { background-position: -360px -1080px; }
    41% { background-position: -720px -1080px; }
    42% { background-position: -1080px -1080px; }
    43% { background-position: -1440px -1080px; }
    44% { background-position: -1800px -1080px; }
    45% { background-position: -2160px -1080px; }
    46% { background-position: -2520px -1080px; }
    47% { background-position: -2880px -1080px; }
    48% { background-position: -3240px -1080px; } 
    49% { background-position: -3600px -1080px; } 
    50% { background-position: -3960px -1080px; } 
    51% { background-position: -4320px -1080px; } 
    52% { background-position: 0 -1440px; }
    53% { background-position: -360px -1440px; }
    54% { background-position: -720px -1440px; }
    55% { background-position: -1080px -1440px; }
    56% { background-position: -1440px -1440px; }
    57% { background-position: -1800px -1440px; }
    58% { background-position: -2160px -1440px; }
    59% { background-position: -2520px -1440px; }
    60% { background-position: -2880px -1440px; }
    61% { background-position: -3240px -1440px; } 
    62% { background-position: -3600px -1440px; } 
    63% { background-position: -3960px -1440px; } 
    64% { background-position: -4320px -1440px; } 
    65% { background-position: 0 -1800px; }
    66% { background-position: -360px -1800px; }
    67% { background-position: -720px -1800px; }
    68% { background-position: -1080px -1800px; }
    69% { background-position: -1440px -1800px; }
    70% { background-position: -1800px -1800px; }
    71% { background-position: -2160px -1800px; }
    100% { background-position: -2160px -1800px; }
  }
}

