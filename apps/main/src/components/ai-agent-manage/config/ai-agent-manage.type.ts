export interface IAgentConfig {
  agentId: string; // 智能体id
  agentType: string; // 智能体类型
  thirdId: string; // 第三方标识
  agentName: string; // 智能体名称（多语言）
  lang: Lang; // 多语言
  [propName: string]: any;
  agentDesc: string; // 智能体描述（多语言）
  agentImageUrl: string; // 智能体图标url
  welcomeMessage: string; // 欢迎语（多语言）
}

// 多语言
interface Lang {
  [propName: string]: LangObject;
}

// 多语言对象
export interface LangObject {
  zh_CN: string;
  zh_TW: string;
  en_US?: string;
}

export interface IAgentBaseInfo {
  logo: string; // 智能体logo
}

// 各种业务组件
export interface ChatComponentBase {
  componetType: string; // 组件类型
  componetContent: any; // 组件内容
}

// 创建应用组件
export interface AppCreateChatComponent extends ChatComponentBase {
  componetType: 'appCreate';
  componetContent: {
    appInfo: {
      // 应用信息
      name: string; // 应用名称
      description: string; // 应用描述
      version?: string; // 应用版本
      category?: string; // 应用分类
      lang: Lang;
    };
    businessInfo: any[]; // 模型数据
  };
}

export type ChatComponent = AppCreateChatComponent;

export interface ChatMessageBase {
  role: 'user' | 'assistant' | 'system' | 'component'; // 用户 ai助理 系统 其他业务组件
  id: string;
  timestamp?: number;
}

export interface ChatMessageUser extends ChatMessageBase {
  role: 'user';
  content: string;
}

export interface ChatMessageAssistant extends ChatMessageBase {
  role: 'assistant';
  content: string;
  thinkState: 'idle' | 'start' | 'end'; // 思考状态 空闲中 开始 思考结束
  thinkContent?: string; // 思考的内容
  nodeId: string; // 节点id，当回复是初始化站位的回复时，该字段为空
}

export interface ChatMessageSystemWelcome extends ChatMessageBase {
  role: 'system';
  systemType: 'welcome';
  aiAgent: IAgentConfig;
}
export interface ChatMessageSystemError extends ChatMessageBase {
  role: 'system';
  systemType: 'error';
  content: string;
}

export interface ChatMessageComponent extends ChatMessageBase {
  role: 'component';
  content: ChatComponent;
}

export type ChatMessageSystem = ChatMessageSystemWelcome | ChatMessageSystemError;
export type ChatMessage = ChatMessageUser | ChatMessageAssistant | ChatMessageSystem | ChatMessageComponent;
export type ChatResponse = ChatResponseData | OtherResponseData;

export function isOtherResponseData(data: any): data is OtherResponseData {
  return data && 'otherResponseType' in data;
}

export function isChatResponseData(data: any): data is ChatResponseData {
  return !isOtherResponseData(data);
}

export function isChatMessageAssistant(data: any): data is ChatMessageAssistant {
  return data && data.role === 'assistant';
}

export interface OtherResponseData {
  otherResponseType: 'onclose' | 'onopen';
}

export interface ChatResponseData {
  /**
   * 消息事件类型
   *  start: 开始
   *  answer: 问答
   *  autoExecute: 自动执行（可忽略此事件的消息）
   *  use_knowledge_base: 知识库检索
   *  use_plugin: 插件调用
   *  references: 联网引用的内容
   *  failed: 失败
   * */
  action:
    | 'start'
    | 'answer'
    | 'autoExecute'
    | 'use_knowledge_base'
    | 'use_plugin'
    | 'error'
    | 'references'
    | 'failed'
    | 'result';
  blockId: string; // 块节点主键
  message: string; // 消息内容
  messageSource: string; // 消息来源
  // chatMessage: ChatMessage; // 消息内容
  nodeId: string; // 节点的主键
  sessionId: string; // 会话主键
  status: 'running' | 'done'; // 状态，running:运行中，done:结束
}

export interface resultActionMessage {
  code: number;
  data: resultData[];
}

export interface resultData {
  type: 'appCreate';
  value: any;
}
