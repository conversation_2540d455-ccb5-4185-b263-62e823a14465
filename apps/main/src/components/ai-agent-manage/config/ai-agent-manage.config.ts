import { IAgentConfig } from './ai-agent-manage.type';

export const aiAgentList: IAgentConfig[] = [
  {
    agentId: '3',
    agentType: 'indepthAI',
    thirdId: 'a827ffb0-22cc-45de-8e81-d33ad3f1c6f9',
    agentName: 'AI架构师',
    lang: {
      agentName: {
        zh_CN: 'AI架构师',
        zh_TW: 'AI架构师',
        en_US: 'AI架构师',
      },
      agentDesc: {
        zh_CN: '我可以基于您的描述，智能生成应用，为了更好的了解您的需求，请参照模板进行需求描述',
        zh_TW: '我可以基于您的描述，智能生成应用，为了更好的了解您的需求，请参照模板进行需求描述',
        en_US: '我可以基于您的描述，智能生成应用，为了更好的了解您的需求，请参照模板进行需求描述',
      },
      welcomeMessage: {
        zh_CN: '👋🏻 Hi，娜娜AI架构师为您服务！',
        zh_TW: '👋🏻 Hi，娜娜AI架构师为您服务！',
        en_US: '👋🏻 Hi，娜娜AI架构师为您服务！',
      },
    },
    agentDesc: '我可以基于您的描述，智能生成应用，为了更好的了解您的需求，请参照模板进行需求描述',
    agentImageUrl: '',
    welcomeMessage: '👋🏻 Hi，娜娜AI架构师为您服务！',
  },
  {
    agentId: '2',
    agentType: 'indepthAI',
    thirdId: '667ee76e-e2c9-43a1-a247-815895067c9f',
    agentName: 'dtd',
    lang: {
      agentName: {
        zh_CN: 'dtd',
        zh_TW: 'dtd',
        en_US: 'dtd',
      },
      agentDesc: {
        zh_CN: 'dtd',
        zh_TW: 'dtd',
        en_US: 'dtd',
      },
      welcomeMessage: {
        zh_CN: 'dtd',
        zh_TW: 'dtd',
        en_US: 'dtd',
      },
    },
    agentDesc: 'dtd',
    agentImageUrl: '',
    welcomeMessage: 'dtd',
  },
];

// 创建应用的mock数据
export const appCreateMockData = {
  appInfo: {
    description:
      '该系统涵盖员工基本信息管理（集中管理企业员工的详细个人信息及紧急联系人信息）、考勤管理（准确记录员工的出勤情况并进行统计分析）、薪资福利管理（管理员工的薪资和福利信息并进行计算）、培训发展管理（制定和管理员工的培训计划并记录参与情况）等领域。',
    lang: {
      description: {
        en_US:
          "This system covers areas such as employee basic information management (centrally managing detailed personal information and emergency contact information of enterprise employees), attendance management (accurately recording and statistically analyzing employees' attendance), salary and benefit management (managing and calculating employees' salary and benefit information), and training and development management (formulating and managing employees' training plans and recording participation).",
        zh_CN:
          '该系统涵盖员工基本信息管理（集中管理企业员工的详细个人信息及紧急联系人信息）、考勤管理（准确记录员工的出勤情况并进行统计分析）、薪资福利管理（管理员工的薪资和福利信息并进行计算）、培训发展管理（制定和管理员工的培训计划并记录参与情况）等领域。',
        zh_TW:
          '本系統涵蓋員工基本資訊管理（集中管理企業員工的詳細個人資訊及緊急聯繫人資訊）、考勤管理（準確記錄員工的出勤情況並進行統計分析）、薪資福利管理（管理員工的薪資和福利資訊並進行計算）、培訓發展管理（制定和管理員工的培訓計劃並記錄參與情況）等領域。',
      },
      name: {
        en_US: 'Employee Comprehensive Management System',
        zh_CN: '员工综合管理系统',
        zh_TW: '員工綜合管理系統',
      },
    },
    name: '员工综合管理系统',
  },
  businessInfo: [
    {
      code: 'employee',
      description: '集中管理企业员工的详细个人信息及紧急联系人信息',
      lang: {
        description: {
          en_US:
            'Centralized management of detailed personal information and emergency contact information of enterprise employees',
          zh_CN: '集中管理企业员工的详细个人信息及紧急联系人信息',
          zh_TW: '集中管理企業員工的詳細個人信息及緊急聯繫人信息',
        },
        name: {
          en_US: 'employee',
          zh_CN: '员工',
          zh_TW: '員工',
        },
      },
      model: {
        children: [
          {
            comment: '存储员工紧急联系人信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识紧急联系人的编号',
                fieldId: 'emergency_contact_id',
                fieldName: '紧急联系人编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '紧急联系人姓名',
                fieldId: 'contact_name',
                fieldName: '联系人姓名',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '紧急联系人与员工的关系',
                fieldId: 'relationship',
                fieldName: '与员工关系',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '紧急联系人联系电话',
                fieldId: 'contact_phone',
                fieldName: '联系电话',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
            ],
            name: 'emergency_contact',
          },
          {
            comment: '存储员工考勤记录信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识考勤记录的编号',
                fieldId: 'attendance_id',
                fieldName: '考勤记录编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '考勤日期',
                fieldId: 'attendance_date',
                fieldName: '考勤日期',
                fieldType: 'DATE',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '上班时间',
                fieldId: 'start_time',
                fieldName: '上班时间',
                fieldType: 'TIME',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '下班时间',
                fieldId: 'end_time',
                fieldName: '下班时间',
                fieldType: 'TIME',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '考勤状态',
                fieldId: 'attendance_status',
                fieldName: '考勤状态',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '出勤天数',
                fieldId: 'attendance_days',
                fieldName: '出勤天数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '迟到次数',
                fieldId: 'late_times',
                fieldName: '迟到次数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '早退次数',
                fieldId: 'early_leave_times',
                fieldName: '早退次数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '旷工天数',
                fieldId: 'absenteeism_days',
                fieldName: '旷工天数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
            ],
            name: 'attendance',
          },
          {
            comment: '存储员工薪资福利信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识薪资福利的编号',
                fieldId: 'salary_benefit_id',
                fieldName: '薪资福利编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工基本工资',
                fieldId: 'basic_salary',
                fieldName: '基本工资',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工绩效工资',
                fieldId: 'performance_salary',
                fieldName: '绩效工资',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工奖金',
                fieldId: 'bonus',
                fieldName: '奖金',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工补贴',
                fieldId: 'subsidy',
                fieldName: '补贴',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工社保缴纳情况',
                fieldId: 'social_security_status',
                fieldName: '社保缴纳情况',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工公积金缴纳情况',
                fieldId: 'provident_fund_status',
                fieldName: '公积金缴纳情况',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工带薪年假天数',
                fieldId: 'annual_leave_days',
                fieldName: '带薪年假天数',
                fieldType: 'INT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工应发工资',
                fieldId: 'should_pay_salary',
                fieldName: '应发工资',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工实发工资',
                fieldId: 'actual_pay_salary',
                fieldName: '实发工资',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
            ],
            name: 'salary_benefit',
          },
          {
            comment: '存储员工培训参与情况信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识培训参与情况的编号',
                fieldId: 'training_participation_id',
                fieldName: '培训参与编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与培训计划表中的培训计划编号关联的字段',
                fieldId: 'training_plan_id',
                fieldName: '培训计划编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工是否参加培训',
                fieldId: 'is_participated',
                fieldName: '是否参加培训',
                fieldType: 'BOOLEAN',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工培训成绩',
                fieldId: 'training_score',
                fieldName: '培训成绩',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
            ],
            name: 'training_participation',
          },
        ],
        comment: '集中管理企业员工的详细个人信息及紧急联系人信息',
        fields: [
          {
            fieldDescription: '唯一标识员工的编号',
            fieldId: 'employee_id',
            fieldName: '员工编号',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工姓名',
            fieldId: 'name',
            fieldName: '姓名',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工性别',
            fieldId: 'gender',
            fieldName: '性别',
            fieldType: 'VARCHAR',
            size: '10',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工出生日期',
            fieldId: 'birth_date',
            fieldName: '出生日期',
            fieldType: 'DATE',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工身份证号码',
            fieldId: 'id_card_number',
            fieldName: '身份证号',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工联系方式',
            fieldId: 'contact',
            fieldName: '联系方式',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工入职日期',
            fieldId: 'join_date',
            fieldName: '入职日期',
            fieldType: 'DATE',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工所在部门',
            fieldId: 'department',
            fieldName: '部门',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工职位',
            fieldId: 'position',
            fieldName: '职位',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工学历',
            fieldId: 'education',
            fieldName: '学历',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工毕业院校',
            fieldId: 'graduation_school',
            fieldName: '毕业院校',
            fieldType: 'VARCHAR',
            size: '50',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '员工在职状态',
            fieldId: 'status',
            fieldName: '在职状态',
            fieldType: 'VARCHAR',
            size: '10',
            type: 'SIMPLE',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'emergency_contact',
            },
            fieldId: 'emergency_contact',
            fieldName: '存储员工紧急联系人信息的子表',
            type: 'COLLECTION',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'attendance',
            },
            fieldId: 'attendance',
            fieldName: '存储员工考勤记录信息的子表',
            type: 'COLLECTION',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'salary_benefit',
            },
            fieldId: 'salary_benefit',
            fieldName: '存储员工薪资福利信息的子表',
            type: 'COLLECTION',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'training_participation',
            },
            fieldId: 'training_participation',
            fieldName: '存储员工培训参与情况信息的子表',
            type: 'COLLECTION',
          },
        ],
        name: 'employee',
      },
      name: 'employee',
    },
    {
      code: 'training_plan',
      description: '制定和管理员工的培训计划并记录参与情况',
      lang: {
        description: {
          en_US: 'Develop and manage employee training plans and record participation',
          zh_CN: '制定和管理员工的培训计划并记录参与情况',
          zh_TW: '制定和管理員工的培訓計劃並記錄參與情況',
        },
        name: {
          en_US: 'training_plan',
          zh_CN: '培训计划',
          zh_TW: '培訓計劃',
        },
      },
      model: {
        children: [
          {
            comment: '存储员工培训参与情况信息的子表',
            fields: [
              {
                fieldDescription: '唯一标识培训参与情况的编号',
                fieldId: 'training_participation_id',
                fieldName: '培训参与编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与主表中的培训计划编号关联的字段',
                fieldId: 'training_plan_id',
                fieldName: '培训计划编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '与员工表中的员工编号关联的字段',
                fieldId: 'employee_id',
                fieldName: '员工编号',
                fieldType: 'VARCHAR',
                size: '20',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工是否参加培训',
                fieldId: 'is_participated',
                fieldName: '是否参加培训',
                fieldType: 'BOOLEAN',
                type: 'SIMPLE',
              },
              {
                fieldDescription: '员工培训成绩',
                fieldId: 'training_score',
                fieldName: '培训成绩',
                fieldType: 'FLOAT',
                type: 'SIMPLE',
              },
            ],
            name: 'training_participation',
          },
        ],
        comment: '制定和管理员工的培训计划并记录参与情况',
        fields: [
          {
            fieldDescription: '唯一标识培训计划的编号',
            fieldId: 'training_plan_id',
            fieldName: '培训计划编号',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '培训主题',
            fieldId: 'training_theme',
            fieldName: '培训主题',
            fieldType: 'VARCHAR',
            size: '50',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '培训时间',
            fieldId: 'training_time',
            fieldName: '培训时间',
            fieldType: 'DATETIME',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '培训地点',
            fieldId: 'training_location',
            fieldName: '培训地点',
            fieldType: 'VARCHAR',
            size: '50',
            type: 'SIMPLE',
          },
          {
            fieldDescription: '培训讲师',
            fieldId: 'trainer',
            fieldName: '培训讲师',
            fieldType: 'VARCHAR',
            size: '20',
            type: 'SIMPLE',
          },
          {
            associatedInfo: {
              associatedFields: [],
              tableName: 'training_participation',
            },
            fieldId: 'training_participation',
            fieldName: '存储员工培训参与情况信息的子表',
            type: 'COLLECTION',
          },
        ],
        name: 'training_plan',
      },
      name: 'training_plan',
    },
  ],
};
