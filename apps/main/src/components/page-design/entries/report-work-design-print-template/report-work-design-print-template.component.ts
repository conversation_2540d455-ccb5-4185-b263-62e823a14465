import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnInit,
  Output,
  SimpleChanges,
  TemplateRef,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { TranslateService } from '@ngx-translate/core';
import { cloneDeep } from 'lodash';
import { AppService } from 'pages/apps/app.service';
import { ReportWorkDesignPrintTemplateService } from './report-work-design-print-template.service';
import { AdUserService } from 'pages/login/service/user.service';
import { AuthService } from 'common/service/auth.service';
import { AuthOperate } from 'common/types/auth.types';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';

@Component({
  selector: 'app-report-work-design-print-template',
  templateUrl: './report-work-design-print-template.component.html',
  styleUrls: ['./report-work-design-print-template.component.less'],
  providers: [ReportWorkDesignPrintTemplateService],
})
export class ReportWorkDesignPrintTemplateComponent implements OnInit, OnChanges {
  // 左上角的发布按钮的句柄（之前的逻辑）
  @ViewChild('publishButton', { static: false }) publishButtonRef: any;

  @Input() reportData: any;
  @Input() reportVisible: boolean;
  @Output() closeReport = new EventEmitter();

  @ViewChild('iframeContainer', { read: ViewContainerRef }) iframeContainer: ViewContainerRef;
  @ViewChild('iframeTpl', { read: TemplateRef }) iframeTpl: TemplateRef<any>;

  reportUrl: any;
  reportLoading: boolean;
  abiData: any = {};
  reportDesignData: any;

  adpCallback: string = '';

  constructor(
    private domSanitizer: DomSanitizer,
    private modal: AdModalService,
    private translateService: TranslateService,
    public appService: AppService,
    private languageService: LocaleService,
    public service: ReportWorkDesignPrintTemplateService,
    protected configService: SystemConfigService,
    private userService: AdUserService,
    private newAuthService: AuthService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      const branch = this.userService.getUser('branch') || 'develop';
      this.adpCallback = `${url}?branch=${branch}`;
    });
  }
  ngOnChanges(changes: SimpleChanges): void {
  }

  ngOnInit(): void {
    this.handleInit();
  }

  handleInit(): void {
    this.handleABIDesign();
  }

  handleABIDesign(): void {
    const language = this.languageService?.currentLanguage || 'zh_CN';
    const param = `application=${this.appService?.selectedApp?.code}&code=${this.reportData.code}&locale=${language}`;
    this.service.getReportResid(param).subscribe((res) => {
      if (res?.code === 0) {
        this.abiData = { resid: res.data?.resid, isvcode: res.data?.isvCode, lang: language };
        this.handleDesign();
      }
    });
  }

  // 串接报表：abi（億信）、tbb
  handleDesign(): void {
    const currentReport = cloneDeep(this.reportData);
    const reportDataTemp = this.reportData ? this.reportData : currentReport;

    const { code, lang, pageFlag } = currentReport;
    const appCode = this.appService?.selectedApp?.code;
    const title = lang?.name;
    const token = this.userService.getUser('iamToken');

    const baseUrl = `${
      this.service.abiReportUrl
    }/abi/ebipro/editrpttpl.do?action=edit&@token=${token}&@program_code=${code}&@app_id=${appCode}&@routerKey=${this.userService.getUser(
      'tenantId',
    )}&pageFlag=${pageFlag}&datasourceRefreshFlag=${this.reportData?.needFlag || false}`;
    this.reportDesignData = {
      token,
      baseUrl,
      title,
      code,
      appCode,
      actionId: this.reportData.actionId,
      data: {
        ...reportDataTemp,
        resid: this.abiData?.resid,
        isvcode: this.abiData?.isvcode,
      },
    };
    this.handleLoadDesign();
  }

  handleLoadDesign() {
    this.reportLoading = true;
    let actionId = this.reportDesignData?.actionId;
    let url = this.reportDesignData?.baseUrl;
    this.reportLoading = true;
    this.service.getAbiInnerToken().subscribe(
      (res) => {
        this.reportLoading = false;
        if (res.code === 0) {
          const innerToken = res.data;
          url += `&resid=${this.abiData?.resid}&isvcode=${this.abiData?.isvcode}&lang=${this.abiData?.lang}&data_source_key=${actionId}&data_source_type=ESP&@ReportKey=${this.reportData.appCode}&adpCallback=${this.adpCallback}&innerToken=${innerToken}`;
          this.reportUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(url);
        }
      },
      () => {
        this.reportLoading = false;
      },
    );
  }

  refreshDataSourceAndDesign() {
    const newUrl = this.reportUrl.changingThisBreaksApplicationSecurity.replace(
      'datasourceRefreshFlag=false',
      'datasourceRefreshFlag=true',
    );
    this.reportUrl = this.domSanitizer.bypassSecurityTrustResourceUrl(newUrl);
  }

  // 关闭
  handleReportClose(): void {
    // 没有权限
    const allow = this.newAuthService.getAuth(AuthOperate.UPDATE);
    if (!allow) {
      this.closeReport.emit();
      return;
    }
    const mod = this.modal.confirm({
      nzTitle: this.translateService.instant('dj-数据录入未保存确认'),
      nzOkText: this.translateService.instant('dj-数据录入确定'),
      nzCancelText: this.translateService.instant('dj-数据录入取消'),
      nzWidth: 400,
      nzOnOk: () => {
        this.closeReport.emit();
        mod.destroy();
      },
      nzOnCancel: () => {
        mod.destroy();
      },
    });
  }

  handleSwitchLanguage(lang): void {
    const param = `application=${this.reportData.appCode}&code=${this.reportData.code}&locale=${lang}`;
    this.service.getReportResid(param).subscribe((res) => {
      if (res.code === 0) {
        this.abiData = { resid: res.data?.resid, isvcode: res.data?.isvCode, lang };
        this.handleLoadDesign();
      }
    });
  }

  handlePublish() {
    this.closeReport.emit();
  }
}
