import { ConditionFormulaCode } from '../components/data-view-container/components/data-view-right/components/data-view-filter/data-view-filter-content/advanced-query.type';

// 排序定义
export interface OrderItem {
  schema: string; // 字段标识
  table_path: string; // 对应path
  orderType: 'asc' | 'desc'; // 升序/降序
  order: number; //  顺序
}

// 筛选器定义
// export interface QueryConditions {
//   order: number;
//   search_table_path?: string;
//   search_field: string;
//   search_operator: string;
//   search_value: string[];
//   bracket?: string;
//   logic?: string;
// }

// 多语言
interface Lang {
  [propName: string]: LangObject;
}

// 多语言对象
export interface LangObject {
  zh_CN: string;
  zh_TW: string;
  en_US?: string;
}

// FieldDsl对象
export interface FieldDsl {
  type: ComponentType;
  schema: string;
  width: number;
  path: string;
  // headerName: string;
  // lang: Lang;
  columns?: FieldDsl[];
  setting?: {
    tyep: string;
    setting: {};
  };
  tagType?: string;
  [props: string]: unknown;
}

// 基础Field对象
export interface BaseField {
  data_name: string;
  data_type: string;
  description: string;
  alias?: string;
  lang: Lang;
  required: string | boolean;
  is_collection?: boolean | null;
  is_datakey: boolean;
  current_table?: string | null;
  isQuoteSearch?: boolean | null;
  quoteGroup?: string | null;
  sourceFieldId?: string | null;
  enumKey?: string | null;
  options?: any;
  fullPath: string;
  dapFullPath: string;
  search_table_path: string;
  showSignOff: boolean; // 列编辑器是否展示签核历程
  isJoinField?: boolean | null; // 关联表的该字段是否被关联
  model_data_type?: string;
  [propName: string]: any;
}

export interface TableField extends BaseField {
  field_dsl: FieldDsl;
}

export interface SourceField extends BaseField {
  relateSubField?: SourceField[];
}

export interface IMainTableInfo {
  data_code: string;
  data_name: string;
  lang: Lang;
}

// 左侧列表的tab类型 主表 关联表
export enum LeftTabType {
  main = 'main',
  association = 'association',
  operation = 'operation',
}

// 右侧编辑的类型
export enum RightEditType {
  global = 'global',
  table = 'table',
  field = 'field',
  field_operation = 'field_operation',
}

// 可选择组件
export enum ComponentType {
  DATEPICKER = 'DATEPICKER',
  TIMEPICKER = 'TIMEPICKER',
  PERCENT_INPUT = 'PERCENT_INPUT',
  INPUT = 'INPUT',
  AMOUNT_INPUT = 'AMOUNT_INPUT',
  MEASURE = 'MEASURE',
  SELECT = 'SELECT',
  EOC_SELECT = 'EOC_SELECT',
  EOC_SELECT_NEW = 'EOC_SELECT_NEW',
  NAME_CODE_COMPONENT = 'NAME_CODE_COMPONENT',
  NEW_OLD_COMPONENT = 'NEW_OLD_COMPONENT',
  WORKFLOW_PROGRESS = 'WORKFLOW_PROGRESS',
  ATH_TAG = 'ATH_TAG',
  FILE_UPLOAD = 'FILE_UPLOAD',

  // 以下为mobile需要的组件类型
  LABEL = 'LABEL',
  DW_MOBILE_TEL = 'DW_MOBILE_TEL',
  ENUM = 'ENUM',
  DW_PICTURE = 'DW_PICTURE',
  NUMERICAL = 'NUMERICAL',
  MAINTITLE = 'MAINTITLE',
  SUBTITLE = 'SUBTITLE',
  TAG = 'TAG',
}
// 可选择组件
// const componentCardList = [
//   { name: '电话号码', type: 'DW_MOBILE_TEL', dataType: ['string']},
//   { name: '文本', type: 'LABEL', dataType: ['string']},
//   { name: '枚举', type: 'ENUM', dataType: ['boolean']},
//   { name: '图片拍照', type: 'DW_PICTURE', dataType: ['date']},
//   { name: '附件上传', type: 'FILE_UPLOAD', dataType: ['object']},
//   { name: '数值', type: 'NUMERICAL', dataType: ['numeric']},
//   { name: '标签', type: 'TAG', dataType: ['string']},
// ]
export enum ComponentMobileType {
  DATEPICKER = 'DATEPICKER',
  TIMEPICKER = 'TIMEPICKER',
  PERCENT_INPUT = 'PERCENT_INPUT',
  INPUT = 'INPUT',
  AMOUNT_INPUT = 'AMOUNT_INPUT',
  MEASURE = 'MEASURE',
  SELECT = 'SELECT',
  EOC_SELECT = 'EOC_SELECT',
  EOC_SELECT_NEW = 'EOC_SELECT_NEW',
  NAME_CODE_COMPONENT = 'NAME_CODE_COMPONENT',
  NEW_OLD_COMPONENT = 'NEW_OLD_COMPONENT',
  WORKFLOW_PROGRESS = 'WORKFLOW_PROGRESS',
}

// 视图表格排序
export interface OrderListItem {
  schema: string;
  table_path: string;
  orderType: string;
  order: number;
}

// 视图表格筛选器
export interface QueryConditionsItem {
  order: number;
  search_table_path: string;
  search_field: string;
  search_operator: string;
  search_value: string[];
}

// 视图排序对象
export interface DataViewSortItem {
  code: string;
  lang: Lang;
  sort: number;
}
/** 视图类型 */
export type TViewType = 'browse' | 'searchView' | 'queryPlan'; // 与后端沟通，后端暂时不会刷数据，所以旧类型需要保留

// 数据视图对象基础信息（排除orderList，queryConditions，viewShowFields,conditionList）
export interface DataViewBase {
  code: string;
  viewType: TViewType;
  name: string;
  lang: Lang;
  modelId: string;
  productCode: string;
  application: string;
  sort: number;
  [propName: string]: any;
  // orderList: orderItem[];
  // queryConditions: QueryConditionsItem[];
  // viewShowFields: BaseField[];
}

// 用于保存或者向外暴露的相关数据，最新的需求调整移除了视图排序相关字段
export interface AllSaveDataView {
  dataView: any; // 最新的dataView数据
  isInit: boolean; // 是否是初始化数据
}

export interface VariableCondition {
  data_type: string;
  data_name: string;
  value: string;
  label: string;
  operator: ConditionFormulaCode;
  lang: Lang;
}
