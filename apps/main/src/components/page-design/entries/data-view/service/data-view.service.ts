import { Injectable } from '@angular/core';
import {
  <PERSON><PERSON>ield,
  SourceField,
  ComponentType,
  RightEditType,
  DataViewBase,
  LeftTabType,
  OrderItem,
  AllSaveDataView,
  // QueryConditions,
  LangObject,
  VariableCondition,
  IMainTableInfo,
} from '../config/data-view.type';
import { baseAdvancedGroup, hideFields } from '../config/data-view.config';
import {
  QueryData,
  ConditionGroup,
} from '../components/data-view-container/components/data-view-right/components/data-view-filter/data-view-filter-content/advanced-query.type';
import { TranslateService } from '@ngx-translate/core';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { Observable, Subject } from 'rxjs';
import {
  sourceFiledToTreeNodeList,
  getAllFields,
  getSourceTreeIsAllSelect,
  ColumnsWidth,
  operationReferenceFiledToTreeNodeList,
} from '../utils/tools';
import { cloneDeep, isEmpty } from 'lodash';

@Injectable()
export class DataViewService {
  private _model: any = {};
  get model(): any {
    return this._model;
  }
  set model(value: any) {
    this._model = value;
  }
  // ========================= 左侧面板相关属性 =========================
  // 数据视图左侧面板是否打开
  private _leftIsShow: boolean = true;
  get leftIsShow(): boolean {
    return this._leftIsShow;
  }

  // 左侧列表的tab类型
  private _leftTabType: LeftTabType = LeftTabType.main;
  get leftTabType(): LeftTabType {
    return this._leftTabType;
  }

  // ========================= 中心相关属性 =========================
  // 中心画布展示的操作按钮
  private _operationsLabelList: string[] = [];
  get operationsLabelList(): string[] {
    return this._operationsLabelList;
  }

  // ========================= 右侧面板相关属性 =========================
  // 数据视图右侧面板是否打开
  private _rightIsShow: boolean = true;
  get rightIsShow(): boolean {
    return this._rightIsShow;
  }

  // 右侧当前编辑的类型
  private _rightEditType: RightEditType = RightEditType.table; // 需求调整，暂时不主动触发全局面板
  get rightEditType(): RightEditType {
    return this._rightEditType;
  }

  // 订阅-右侧面板编辑类型
  private _rightEditType$: Subject<RightEditType> = new Subject<RightEditType>();
  get rightEditType$(): Observable<RightEditType> {
    return this._rightEditType$.asObservable();
  }

  // 数据视图右侧筛选管理字段变量模式的条件list
  private _variableConditions: VariableCondition[] = [];
  get variableConditions(): VariableCondition[] {
    return this._variableConditions;
  }

  private _canAddData: boolean;
  get canAddData(): boolean {
    return this._canAddData;
  }

  // ========================= 全局相关属性 =========================
  // 数据视图的code
  private _dataViewCode: string = '';
  get dataViewCode(): string {
    return this._dataViewCode;
  }

  // 订阅-数据视图完整数据变化
  private _dataViewChange$: Subject<AllSaveDataView> = new Subject<AllSaveDataView>();
  get dataViewChange$(): Observable<AllSaveDataView> {
    return this._dataViewChange$.asObservable();
  }

  // 订阅-字段列表删除通知
  private _viewShowFieldsDeleteNotify$: Subject<TableField[]> = new Subject<TableField[]>();
  get viewShowFieldsDeleteNotify$(): Observable<TableField[]> {
    return this._viewShowFieldsDeleteNotify$.asObservable();
  }

  // 数据视图初始化的数据加载状态
  private _isInitLoading: boolean = false;
  get isInitLoading(): boolean {
    return this._isInitLoading;
  }

  // 保存时的加载状态
  private _isSaveLoading: boolean = false;
  get isSaveLoading(): boolean {
    return this._isSaveLoading;
  }

  // 发布时的加载状态
  private _isPublishLoading: boolean = false;
  get isPublishLoading(): boolean {
    return this._isPublishLoading;
  }

  // 订阅-保存和发布的加载状态变化
  private _saveAndPublishLoadingChange$: Subject<boolean> = new Subject<boolean>();
  get saveAndPublishLoadingChange$(): Observable<boolean> {
    return this._saveAndPublishLoadingChange$.asObservable();
  }

  // 视图基本数据
  private _dataViewBase: DataViewBase = null;
  get dataViewBase(): DataViewBase {
    return this._dataViewBase;
  }

  // 视图原始全量数据（init之后，save之后更新）
  private _dataViewOrigin: any = null;
  get dataViewOrigin(): any {
    return this._dataViewOrigin;
  }

  // 订阅-视图原始全量数据数据变化
  private _dataViewOriginChange$: Subject<any> = new Subject<any>();
  get dataViewOriginChange$(): Observable<AllSaveDataView> {
    return this._dataViewOriginChange$.asObservable();
  }

  // 中心表格渲染数据, 后来后端结构有所调整
  private _viewShowFields: TableField[] = [];
  get viewShowFields(): TableField[] {
    return this._viewShowFields;
  }
  // 备份非当前操作界面节点的数据
  private _viewShowFieldsBackup: any = {};
  get viewShowFieldsBackup() {
    return this._viewShowFieldsBackup;
  }

  // 当前中心画布选中的列
  private _currentSelectField: TableField = null;
  get currentSelectField(): TableField {
    return this._currentSelectField;
  }

  // 数据源-主表
  private _mainField: SourceField[] = [];
  get mainField(): SourceField[] {
    return this._mainField;
  }

  // 数据源-主表信息
  private _mainTableInfo: IMainTableInfo = null;
  get mainTableInfo(): IMainTableInfo {
    return this._mainTableInfo;
  }

  // 数据源-关联表
  private _relateSubField: SourceField[] = [];
  get relateSubField(): SourceField[] {
    return this._relateSubField;
  }
  get mainTableAndRelateSubField(): SourceField[] {
    return this._allFields.filter((item) => !item?.isOperate);
  }
  // operation-运算表
  private _operationField: SourceField[] = [];
  get operationField(): SourceField[] {
    return this._operationField;
  }
  // 设置 数据源-运算表
  setOperationField(operationField: SourceField[]): void {
    this._operationField = operationField;
    this._allFields = getAllFields(this._mainField, this._relateSubField, this._operationField);
  }

  // 数据源-全部
  private _allFields: SourceField[] = [];
  get allFields(): SourceField[] {
    return this._allFields;
  }

  // 渲染的 数据源树
  private _sourceTree: NzTreeNodeOptions[] = [];
  get sourceTree(): NzTreeNodeOptions[] {
    return this._sourceTree;
  }

  // 当前选中的树节点key list
  private _sourceTreeCheckedKeys: string[] = [];
  get sourceTreeCheckedKeys(): string[] {
    return this._sourceTreeCheckedKeys;
  }

  // 树节点是否全选中
  private _sourceTreeIsAllSelect: boolean = false;
  get sourceTreeIsAllSelect(): boolean {
    return this._sourceTreeIsAllSelect;
  }

  // 是否处于拖拽状态
  private _isOnDrag: boolean = false;
  get isOnDrag(): boolean {
    return this._isOnDrag;
  }

  // 全量查询条件List-前端维护
  private _conditionList: QueryData[] = cloneDeep(baseAdvancedGroup);
  get conditionList(): QueryData[] {
    return this._conditionList;
  }

  // 平台需要的查询条件List
  private _queryConditions: ConditionGroup[] = [];
  get queryConditions(): ConditionGroup[] {
    return this._queryConditions;
  }

  // 排序依据数据
  private _orderList: OrderItem[] = [];
  get orderList(): OrderItem[] {
    return this._orderList;
  }

  // 当前的语言环境
  get currentLang(): string {
    return this.translateService.currentLang;
  }

  // 是否显示 头部
  private _isShowHeader: boolean = true;
  get isShowHeader(): boolean {
    return this._isShowHeader;
  }

  // 是否显示 头部（右侧）
  private _isShowHeaderRight: boolean = true;
  get isShowHeaderRight(): boolean {
    return this._isShowHeaderRight;
  }

  // 是否是移动
  private _isMobile: boolean = false;
  get isMobile() {
    return this._isMobile;
  }

  // 是否选中操作列
  private _isSelectedColumnOperation: boolean = false;
  get isSelectedColumnOperation() {
    return this._isSelectedColumnOperation;
  }

  // 获取需要默认设置选中的字段, 目前和禁用字段集合公用
  getDefaultCheckedFields() {
    return [
      'tenantsid',
      'tenant_id',
      // 顺序有要求
      'manage_status',
    ].map((fieldName) => `${this.dataViewBase?.originalModelId}.${fieldName}`);
  }

  constructor(private translateService: TranslateService) { }
  // ========================= 左侧面板相关属性操作 =========================
  // 设置数据视图左侧面板是否打开
  setLeftIsShow(isShow: boolean): void {
    this._leftIsShow = isShow;
  }

  // 设置左侧列表的tab类型
  setLeftTabType(type: LeftTabType): void {
    this._leftTabType = type;
  }

  // 设置 渲染的 数据源树
  setSourceTree(sourceTree: NzTreeNodeOptions[]): void {
    this._sourceTree = sourceTree;
    this.updateSourceTreeCheckedInfoByViewShowFields();
  }

  // 通过 viewShowFields 更新 当前选中的树节点key list和是否全选中 的信息 (通过get的方式触发太过频繁，所以仅在树数据以及表格数据变化时update)
  updateSourceTreeCheckedInfoByViewShowFields(): void {
    this._sourceTreeCheckedKeys = this._viewShowFields.map((field) => {
      return field.fullPath;
    });
    this._sourceTreeIsAllSelect = getSourceTreeIsAllSelect(this._sourceTree, this._sourceTreeCheckedKeys);
  }

  // 通过左侧tab类型更新 渲染的 数据源树
  updateSourceTreeByLeftTabType(leftTabType?: LeftTabType): void {
    const leftTabTypeIn = leftTabType ?? this._leftTabType;
    let sourceField;
    switch (leftTabTypeIn) {
      case LeftTabType.main:
        sourceField = this.mainField;
        break;
      case LeftTabType.association:
        sourceField = this.relateSubField;
        break;
      case LeftTabType.operation:
        sourceField = this.operationField;
        break;
    }
    // 2024-08-15 查询方案二期优化，放开租户ID和租户sid的显示
    // const sourceTree = sourceFiledToTreeNodeList(sourceField, [manageStatusFullPath], hideFields);
    const sourceTree = sourceFiledToTreeNodeList(sourceField, this.getDefaultCheckedFields());
    this.setSourceTree(sourceTree);
  }

  /**
   * 获取引用的字段
   */
  getOperationReferenceField() {
    const mainName = this.mainTableInfo?.lang?.data_name || {};
    const mainFieldTem = [
      {
        lang: {
          description: mainName,
        },
        data_name: this.mainTableInfo?.data_code,
        relateSubField: [...this.mainField],
      },
    ];
    // @ts-ignore
    const sourceTree = operationReferenceFiledToTreeNodeList(mainFieldTem.concat(this.relateSubField));
    return sourceTree;
  }
  getOperationReferenceFunc(data) {
    const sourceTree = operationReferenceFiledToTreeNodeList(data, 'functionsField');
    return sourceTree;
  }
  // ========================= 中心相关属性操作 =========================
  // 中心画布展示的操作按钮
  setOperationsLabelList(operationsLabelList: string[]): void {
    this._operationsLabelList = operationsLabelList;
  }

  // ========================= 右侧相关属性操作 =========================
  // 设置数据视图右侧面板是否打开
  setRightIsShow(isShow: boolean): void {
    this._rightIsShow = isShow;
  }

  // 设置右侧编辑面板类型
  setRightEditType(type: RightEditType): void {
    this._rightEditType = type;
    this._rightEditType$.next(type);
  }

  // 设置数据视图右侧筛选管理字段变量模式的条件list
  setVariableCondition(variableConditions: VariableCondition[]): void {
    this._variableConditions = variableConditions;
  }

  // ========================= 全局相关属性操作 =========================
  // 设置数据视图的code
  setDataViewCode(dataViewCode: string): void {
    this._dataViewCode = dataViewCode;
  }

  // 设置初始化数据加载状态
  setIsInitLoading(isInitLoading: boolean): void {
    this._isInitLoading = isInitLoading;
  }

  // 设置保存时的加载状态
  setIsSaveLoading(isSaveLoading: boolean): void {
    this._isSaveLoading = isSaveLoading;
    this._saveAndPublishLoadingChange$.next(this._isPublishLoading || this._isSaveLoading);
  }

  // 设置发布时的加载状态
  setIsPublishLoading(isPublishLoading: boolean): void {
    this._isPublishLoading = isPublishLoading;
    this._saveAndPublishLoadingChange$.next(this._isPublishLoading || this._isSaveLoading);
  }

  // 设置视图基本数据
  setDataViewBase(value: DataViewBase): void {
    this._dataViewBase = value;
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  // 设置视图基本数据的name
  setDataViewBaseName(name: string, nameLang: LangObject, isDataViewChange = true): void {
    this._dataViewBase.name = name;
    this._dataViewBase.lang.name = nameLang;
    if (isDataViewChange) {
      this._dataViewChange$.next(this.getAllSaveDataView());
    }
  }

  // 通过属性key设置视图基本数据
  setDataViewBaseByKey(key: string, value: any): void {
    this._dataViewBase[key] = value;
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  updateDataViewOrigin(): void {
    this._dataViewOrigin = cloneDeep(this.getSaveDataView());
    this._dataViewOriginChange$.next(this._dataViewOrigin);
  }

  // 初始化添加默认需要选中的字段
  private getDefaultViewShowFields(value: TableField[] = []) {
    const resultFields = [];
    const needCheckedFields = this.getDefaultCheckedFields();
    needCheckedFields.map((fieldFullPath) => {
      const index = value.findIndex((info) => info.fullPath === fieldFullPath);
      if (index < 0) {
        const field = this.mainField.find((field) => field.fullPath === fieldFullPath);
        if (field) {
          const cloneField = cloneDeep(field);
          const field_dsl = this.generateDefDslData(cloneField);
          resultFields.push({ ...cloneField, field_dsl });
        }
      }
    });
    return resultFields.concat(value);
  }

  // 在getDefaultViewShowFields方法之后调用，这时候manage_status一定有了
  private dealManageStatusFieldIndex(value: TableField[] = []) {
    const manageStatusFullPath = `${this.dataViewBase?.originalModelId}.manage_status`;
    const statusIndex = value.findIndex((info) => info.fullPath === manageStatusFullPath);
    if (statusIndex >= 0 && statusIndex !== value.length - 1) {
      value.splice(statusIndex, 1, value[statusIndex]);
    }
  }

  // 设置中心表格渲染数据
  // 2024-04-25 视图需求，后端不准备刷数据，所以为了处理历史数据，这边针对历史数据做个处理，将manage_status强制排到最后一列
  setViewShowFields(value: TableField[] = [], isInit = false) {
    let recLists = [];
    // 展示的field列表最后一列如果不是manage_status，就处理一下，将manage_status强制放到最后一列
    // 这边的前提是manage_status必须选择且不允许取消选中
    if (isInit) {
      const initCheckedFields = this.getDefaultViewShowFields(value);
      this.dealManageStatusFieldIndex(initCheckedFields);
      recLists = initCheckedFields;
    } else {
      recLists = [...value];
    }
    console.warn(recLists, '_viewShowFields');
    this._viewShowFields = recLists;
    this.updateSourceTreeCheckedInfoByViewShowFields();
    this.updateRightEditTypeByViewShowFieldsChange();
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  setViewShowFieldsBackup(value) {
    this._viewShowFieldsBackup = value;
  }

  setMobileInfo(val: boolean) {
    this._isMobile = val;
  }

  // 中心表格渲染数据变化时更新右侧编辑面板类型
  updateRightEditTypeByViewShowFieldsChange(): void {
    if (this._viewShowFields.length === 0) {
      this.setRightEditType(RightEditType.table); // 需求调整，暂时隐藏全局面板
    }
  }

  // 通过index删除中心表格的渲染数据
  viewShowFieldsDelete(deleteIndex: number, lengeth: number = 1): void {
    if (
      this._rightEditType === RightEditType.field &&
      this._currentSelectField &&
      this._viewShowFields[deleteIndex].fullPath === this._currentSelectField.fullPath
    ) {
      this.setRightEditType(RightEditType.table); // 需求调整，暂时隐藏全局面板
    }
    const deletedViewShowFields = this._viewShowFields.splice(deleteIndex, lengeth);
    this.updateRightEditTypeByViewShowFieldsChange();
    this.updateSourceTreeCheckedInfoByViewShowFields();
    this._dataViewChange$.next(this.getAllSaveDataView());
    this._viewShowFieldsDeleteNotify$.next(deletedViewShowFields);
  }

  // 批量删除FormItem，没有id，fullPath唯一
  deleteFormItemByFullPath(fullPathList: string[]): void {
    if (
      this._rightEditType === RightEditType.field &&
      this._currentSelectField &&
      fullPathList.includes(this._currentSelectField.fullPath)
    ) {
      this.setRightEditType(RightEditType.table); // 需求调整，暂时隐藏全局面板
    }
    this._viewShowFields = this._viewShowFields.filter((item) => !fullPathList.includes(item.fullPath));
    this.updateRightEditTypeByViewShowFieldsChange();
    this.updateSourceTreeCheckedInfoByViewShowFields();
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  moveToLast(arr, index) {
    if (index < 0 || index >= arr.length) {
      // 索引超出范围，不做任何操作
      return arr;
    }
    // 移除指定位置的元素
    const element = arr.splice(index, 1)[0];
    // 将移除的元素放到数组的最后一位
    arr.push(element);
    return arr;
  }

  // 使用解构赋值交换两个位置的元素
  replaceItems(arr, index1, index2) {
    [arr[index1], arr[index2]] = [arr[index2], arr[index1]];
    return arr;
  }

  getDefDslType(newDragField, isMobile) {
    if (isMobile) {
      return {
        type: newDragField?.mobileType || ComponentType.LABEL,
        hidden: false,
        setting: {},
      };
    } else {
      return {
        type: newDragField.enumKey ? ComponentType.SELECT : ComponentType.INPUT,
        width: ColumnsWidth[newDragField?.model_data_type] ?? 160,
      };
    }
  }

  /**
   * 生成默认的dsl数据
   * @param newDragField
   * @param isReverse  是否需要返回非当前端的type
   * @returns
   */
  generateDefDslData(newDragField, isReverse?) {
    const fullPathList = newDragField.fullPath.split('.');
    const schema = fullPathList.splice(-1)[0];
    const path = fullPathList.join('.');
    const isMobile = isReverse ? !this.isMobile : this.isMobile;
    const dslData = {
      schema: schema,
      path: path,
      ...this.getDefDslType(newDragField, isMobile),
    };
    return dslData;
  }

  // 中心表格插入数据
  viewShowFieldsInsert(dragField: SourceField, index = 0): void {
    const newDragField = cloneDeep(dragField);
    if (!newDragField.field_dsl) {
      newDragField.field_dsl = this.generateDefDslData(newDragField);
    }
    this._viewShowFields.splice(index, 0, newDragField as TableField);
    this.viewShowFieldsUpdate();
  }

  // 中心表格数据改变位置
  viewShowFieldsChange(from: number, to: number): void {
    if (from === to || from > this._viewShowFields.length - 1 || to > this._viewShowFields.length) return;
    const field = this._viewShowFields.splice(from, 1)[0];
    this._viewShowFields.splice(from < to ? to - 1 : to, 0, field);
    this.viewShowFieldsUpdate();
  }

  // 移动特殊处理 只改变字段type
  viewShowFieldsChangeType(dragField: SourceField, index = 0) {
    const newDragField = cloneDeep(dragField);
    (newDragField.field_dsl['type'] = newDragField?.mobileType || ComponentType.LABEL),
      (this._viewShowFields[index] = newDragField as TableField);
    this.viewShowFieldsUpdate();
  }

  viewShowFieldsUpdate() {
    this.updateRightEditTypeByViewShowFieldsChange();
    this.updateSourceTreeCheckedInfoByViewShowFields();
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  // 设置当前中心画布选中的列
  setCurrentSelectField(value: TableField): void {
    this._currentSelectField = value;
    this.setIsSelectedColumnOperation(false);
    // this._dataViewChange$.next(this.getAllSaveDataView());
  }

  // 设置当前是否选中操作列
  setIsSelectedColumnOperation(mark: boolean) {
    this._isSelectedColumnOperation = mark;
  }

  // 通过关键字设置当前中心画布选中列的部分属性
  setCurrentSelectFieldByKey(key: string, value: any): void {
    this._currentSelectField[key] = value;
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  // 设置当前中心画布选中列的部分属性（通过属性关键字路径list）
  setCurrentSelectFieldByKeyPath(keyPath: string[], value: any): void {
    const targetKey = keyPath.splice(-1);
    const obj = keyPath.reduce((pre, cur) => {
      return pre[cur];
    }, this._currentSelectField);

    obj[targetKey[0]] = value;
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  // 设置 数据源-主表
  setMainField(mainField: SourceField[]): void {
    this._mainField = mainField;
    this._allFields = getAllFields(this._mainField, this._relateSubField, this._operationField);
  }

  // 设置 数据源-主表信息
  setMainTableInfo(mainTableInfo: IMainTableInfo): void {
    this._mainTableInfo = mainTableInfo;
  }

  // 设置 数据源-关联表
  setRelateSubField(relateSubField: SourceField[]): void {
    this._relateSubField = relateSubField;
    this._allFields = getAllFields(this._mainField, this._relateSubField, this._operationField);
  }

  // 设置是否处于拖拽状态
  setIsOnDrag(isOnDrag: boolean): void {
    this._isOnDrag = isOnDrag;
  }

  // 设置全量查询条件List
  setConditionList(value: any[]): void {
    this._conditionList = value;
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  // 设置平台需要的查询条件List
  setQueryConditions(value: ConditionGroup[]): void {
    this.setOperationConditions(value, 'filter');
    this._queryConditions = value;
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  /**
   * 操作字段添加特殊标识
   * @param value
   * @param type
   */
  setOperationConditions(value, type: string) {
    this.operationField?.map((field) => {
      value.map((item: any) => {
        if (type === 'order') {
          if (item?.schema === field.data_name && !item.table_path) {
            item.sortType = 'OPERATE';
            item.sortExpression = field.expression;
          }
        } else {
          if (item?.search_field === field.data_name && !item.search_table_path) {
            item.searchType = 'OPERATE';
            item.searchExpression = field.expression;
          }
        }
      });
    });
  }

  // 设置 排序依据数据
  setOrderList(value: OrderItem[]): void {
    this.setOperationConditions(value, 'order');
    this._orderList = value;
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  // 设置 是否显示 头部
  setIsShowHeader(value: boolean): void {
    this._isShowHeader = value;
  }

  // 设置 是否显示 右侧
  setIsShowHeaderRight(value: boolean): void {
    this._isShowHeaderRight = value;
  }

  // 获取保存所需的数据视图数据
  getSaveDataView(): any {
    return {
      ...this.dataViewBase,
      conditionList: this.conditionList,
      queryConditions: this.queryConditions,
      orderList: this.orderList,
      ...this.getViewShowFields(),
    };
  }
  getViewShowFields() {
    const commonData = {
      data_code: this.dataViewBase?.code,
      data_name: this.dataViewBase?.name,
      service_code: this.dataViewBase?.productCode,
      lang: {
        data_name: this.dataViewBase?.lang?.name,
      },
    };
    const viewShowFields = cloneDeep(this.viewShowFields);
    // 当前操作的Fields
    const newViewShowFields = {
      ...commonData,
      field: viewShowFields,
    };
    // 根据当前操作的Fields同步非当前端的Fields
    const syncViewShowFields = {
      ...commonData,
      field: this.syncFieldData(viewShowFields, this.viewShowFieldsBackup?.field),
    };
    const viewShowFieldsPc = this.isMobile ? syncViewShowFields : newViewShowFields;
    const viewShowFieldsMobile = this.isMobile ? newViewShowFields : syncViewShowFields;
    return {
      viewShowFields: viewShowFieldsPc,
      viewShowFieldsMobile: {
        ...viewShowFieldsMobile,
        canAddData: this._canAddData,
      },
      operationFields: {
        field: this.operationField,
        can_filter: true,
        can_sort: true,
      },
    };
  }
  /**
   * 同步数据 pc或者mobile数据field变化 需使两端数据一致
   * @param referenceItems
   * @param targetItems
   */
  syncFieldData(referenceItems, targetItems = []) {
    if (!(referenceItems?.length && targetItems?.length)) {
      return targetItems;
    }
    const newItems = [];
    for (let i = 0; i < referenceItems.length; i++) {
      const referenceItem = cloneDeep(referenceItems[i]) || {};
      const targetItem = targetItems.find((item) => item.fullPath === referenceItem?.fullPath);
      if (targetItem) {
        newItems.push(targetItem);
      } else {
        const field_dsl = this.generateDefDslData(referenceItem, true);
        newItems.push(Object.assign({}, referenceItem, { field_dsl }));
      }
    }
    return newItems;
  }

  // 获取保存所需的所有数据（需求调整，视图排序结构被移除）
  getAllSaveDataView(): AllSaveDataView {
    return {
      dataView: this.getSaveDataView(),
      isInit: this._isInitLoading,
    };
  }
  setMobileCanAddData(data: boolean) {
    this._canAddData = data;
  }
  handleChangeCanAddData(data: boolean) {
    this._canAddData = data;
    this._dataViewChange$.next(this.getAllSaveDataView());
  }

  // 重置所有dataView数据
  resetAll(): void {
    this._leftIsShow = true;
    this._leftTabType = LeftTabType.main;
    this._isOnDrag = false;
    this._operationsLabelList = [];
    this._rightIsShow = true;
    this._rightEditType = RightEditType.table; // 需求调整，暂时不主动触发全局面板
    this._dataViewCode = '';
    this._isInitLoading = false;
    this._isSaveLoading = false;
    this._isPublishLoading = false;
    this._dataViewBase = null;
    this._viewShowFields = [];
    this._currentSelectField = null;
    this._mainField = [];
    this._mainTableInfo = null;
    this._relateSubField = [];
    this._allFields = [];
    this._sourceTree = [];
    this._sourceTreeCheckedKeys = [];
    this._conditionList = cloneDeep(baseAdvancedGroup);
    this._orderList = [];
    this._queryConditions = [];
    this._dataViewOrigin = null;
  }
}
