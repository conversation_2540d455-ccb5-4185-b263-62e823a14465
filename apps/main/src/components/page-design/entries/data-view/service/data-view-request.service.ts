import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import { Observable } from 'rxjs';
import { HttpClient } from '@angular/common/http';

@Injectable()
export class DataViewRequestService {
  serviceUrl: string;

  constructor(protected configService: SystemConfigService, protected http: HttpClient) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.serviceUrl = url;
    });
  }

  // 获取数据视图详情
  getDataViewDetail(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/dataView/getDataViewDetail`;
    return this.http.get(url, {
      params,
    });
  }

  // 获取该模型的视图列表,视图排序用
  queryDataViewByModel(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/dataView/queryDataViewByModel`;
    return this.http.post(url, params);
  }

  // 更新视图列表的顺序接口,视图排序用
  updateDataViewSort(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/dataView/updateDataViewSort`;
    return this.http.post(url, params);
  }

  // 获取数据视图数据源
  queryDataViewFields(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/dataView/queryDataViewFields`;
    return this.http.post(url, params);
  }

  // 保存数据视图
  saveDataView(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/dataView/saveDataView`;
    return this.http.post(url, params);
  }

  // 检查是否被绑定(视图，模型)
  checkBindRelation(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/tbb/checkBindRelation`;
    return this.http.post(url, params);
  }
  // 获取引用函数列表
  // 获取变量模式规则数据
  getPresetDataObject(key: string = 'filter-param-rule'): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/presetData/getPresetDataObject?key=${key}`;
    return this.http.get(url);
  }

  // 获取预览查询配置
  getPreviewQueryConfig(viewCode: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/dataView/getPreviewQueryConfig?viewCode=${viewCode}`;
    return this.http.get(url);
  }

  // 获取预览结果
  getPreviewResult(params: any): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/dataView/getPreviewResult`;
    return this.http.post(url, params);
  }

  fetchModelDrivenData(params: { code: string; serviceCode: string }): Observable<any> {
    const url = `${this.serviceUrl}/athena-designer/modelDriver/queryModelByCode`;
    return this.http.get(url, { params });
  }
}
