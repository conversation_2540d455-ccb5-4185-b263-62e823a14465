import { AfterViewInit, ChangeDetectorRef, Component, ElementRef, Input, OnInit, ViewChild } from '@angular/core';
import { EditorComponent as MonacoEditorComponent } from 'ngx-monaco-editor';
import { DataViewRequestService } from '../../service/data-view-request.service';
import { LocaleService } from 'common/service/locale.service';
import { FormGroup, FormBuilder, FormControl } from '@angular/forms';
import { isEmpty, set } from 'lodash';
import { addIndexToArray, groupArrayElements, objectToObjectArray } from '../../utils/tools';
import dayjs from 'dayjs';
// @ts-ignore
import { format } from 'sql-formatter';

@Component({
  selector: 'app-preview-debug',
  templateUrl: './preview-debug.component.html',
  styleUrls: ['./preview-debug.component.less'],
  providers: [DataViewRequestService],
})
export class PreviewDebugComponent implements OnInit, AfterViewInit {
  @Input() viewCode: string;
  @ViewChild('wrapperElement') wrapperElement: ElementRef;
  @ViewChild('containerElement') containerElement: ElementRef;
  @ViewChild('monaco', { static: false }) monacoComponent!: MonacoEditorComponent;

  editor;
  editorOptions = {
    readOnly: true,
    language: 'sql',
  };
  containerHeight: string = '0';

  isSpinning = false;

  isExpanded = false;

  get expandable() {
    return this.params.length > 2;
  }

  get hasParams() {
    return this.params.length > 0;
  }

  get displayParams() {
    return this.isExpanded ? this.params : this.params.slice(0, 2);
  }

  //# 数据
  mode = 'A';
  queryConditionVariables = [];
  // 二维数组
  params: any[][] = [];

  searchTableHeader = [];
  searchTableData = [];
  runTableData = [];
  code = '';

  currentLang: string;

  validateForm: FormGroup;

  DATA_TYPE_DICT;

  constructor(
    private dataViewRequestService: DataViewRequestService,
    private cdk: ChangeDetectorRef,
    private languageService: LocaleService,
    private fb: FormBuilder,
  ) {
    this.currentLang = this.languageService.currentLanguage || 'zh_CN';
    this.validateForm = this.fb.group({});
    this.initDict();
  }

  async ngOnInit(): Promise<void> {
    try {
      const configRes = await this.dataViewRequestService.getPreviewQueryConfig(this.viewCode).toPromise();
      const { queryConditionVariables, returnFields } = configRes.data;
      this.queryConditionVariables = queryConditionVariables || [];
      // 1. 初始化表单
      this.initForm();
      this.params = groupArrayElements(queryConditionVariables);
      // 2. 初始化表格
      this.searchTableHeader = addIndexToArray(returnFields);
      this.isSpinning = true;
      const resultRes = await this.dataViewRequestService
        .getPreviewResult({
          queryConditionValues: isEmpty(this.validateForm.value) ? [] : objectToObjectArray(this.validateForm.value),
          viewCode: this.viewCode,
        })
        .toPromise();
      const { dataList, record, returnSql } = resultRes.data;
      // 3. 初始化表格数据
      this.searchTableData = addIndexToArray(dataList || []);
      this.runTableData = [...addIndexToArray([record])];
      this.code = returnSql;
    } catch (error) {
      console.log(error);
    } finally {
      this.isSpinning = false;
    }
  }

  ngAfterViewInit(): void {}

  defaultParamsValue(variables: any[]) {
    return variables.reduce((acc, curr) => {
      acc[curr.searchValue] = this.DATA_TYPE_DICT[curr.dataType]?.defaultValue;
      return acc;
    }, {});
  }

  initForm() {
    const params = this.defaultParamsValue(this.queryConditionVariables);
    this.addControls(params);
  }

  addControls(params: any) {
    if (isEmpty(params)) {
      return;
    }
    for (const [key, value] of Object.entries(params)) {
      this.validateForm.addControl(key, new FormControl(value));
    }
  }

  initDict() {
    this.DATA_TYPE_DICT = {
      date: {
        defaultValue: dayjs().format('YYYY-MM-DD'),
      },
      datetime: {
        defaultValue: dayjs().format('YYYY-MM-DD HH:mm:ss'),
      },
      number: {
        defaultValue: 1.0,
      },
      boolean: {
        defaultValue: 0,
      },
    };
  }

  handleClear() {
    this.validateForm.reset();
  }

  handleReset() {
    this.initDict();
    const params = this.defaultParamsValue(this.queryConditionVariables);
    this.validateForm.patchValue(params);
  }

  async handleSearch() {
    this.isSpinning = true;
    const result = objectToObjectArray(this.validateForm.value);
    const map = this.queryConditionVariables?.reduce((pre, curr) => {
      pre[curr.searchValue] = curr.dataType;
      return pre;
    }, {});
    result.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const type = map[key];
        if ('date' === type) {
          item[key] = dayjs(item[key]).format('YYYY-MM-DD');
        } else if ('datetime' === type) {
          item[key] = dayjs(item[key]).format('YYYY-MM-DD HH:mm:ss');
        }
      });
    });
    const resultRes = await this.dataViewRequestService
      .getPreviewResult({
        queryConditionValues: isEmpty(result) ? [] : result,
        viewCode: this.viewCode,
      })
      .toPromise();
    this.isSpinning = false;
    const { dataList, record, returnSql } = resultRes.data;
    // 3. 初始化表格数据
    this.searchTableData = addIndexToArray(dataList || []);
    this.runTableData = [...addIndexToArray([record, ...this.runTableData])];

    this.code = format(returnSql);
    this.runTableData = [...this.runTableData];

    console.log('njnjjn: ', this.searchTableData);
  }

  handleToggleExpand() {
    this.isExpanded = !this.isExpanded;
  }

  handleInitMonaco(editor) {
    this.editor = editor;
    this.editor.setValue(format(this.editor.getValue()));
  }
}
