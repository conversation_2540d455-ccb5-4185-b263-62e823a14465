import {
  Component,
  OnInit,
  Input,
  TemplateRef,
  Output,
  EventEmitter,
  OnChanges,
  OnDestroy,
  SimpleChanges,
} from '@angular/core';
import { DataViewService } from './service/data-view.service';
import { DataViewRequestService } from './service/data-view-request.service';
import { Subject, Subscription } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';
import { AllSaveDataView, RightEditType } from './config/data-view.type';
import { baseAdvancedGroup } from './config/data-view.config';
import { cloneDeep, isEqual } from 'lodash';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { PreviewDebugComponent } from './components/preview-debug/preview-debug.component';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-data-view',
  templateUrl: './data-view.component.html',
  styleUrls: ['./data-view.component.less'],
  providers: [DataViewService, DataViewRequestService],
})
export class DataViewComponent implements OnInit, OnChanges, OnDestroy {
  @Input() pageFrom: string;
  @Input() dataViewCode = ''; // （必须）数据视图 code
  @Input() operationsLabelList: string[] = []; // （可选）中心表格 渲染操作按钮
  @Input() isShowHeader: boolean = true; // 是否显示头部
  @Input() isShowHeaderRight: boolean = true; // 是否显示头部(右侧)
  @Input() headerCustomTemplate: TemplateRef<any> | null = null; // （可选）定制头部（头部的左侧部分）
  @Input() editCustomTemplate: TemplateRef<any> | null = null; // （可选）定制右侧编辑面板（部分，插入）
  @Input() editCustomWholeTemplate: TemplateRef<any> | null = null; // （可选）定制右侧编辑面板（全部）
  @Input() leftCustomWholeTemplate: TemplateRef<any> | null = null; // （可选）定制左侧面板（全部）
  @Input() centerCustomTemplate: TemplateRef<any> | null = null; // （可选）中心区域（部分，插入）
  @Input() isMobile: boolean = false; // 是否是移动
  @Input() dataSourceFields = []; // 字段列表
  @Output() rightEditTypeChange: EventEmitter<RightEditType> = new EventEmitter(); // 当前编辑面板类型
  @Output() dataViewChange: EventEmitter<AllSaveDataView> = new EventEmitter(); // 数据视图变化
  @Output() saveAndPublishLoadingChange: EventEmitter<boolean> = new EventEmitter(); // 当保存和发布状态变化时触发的事件，配合优化需求，保存和发布时模型驱动解决方案左侧菜单不可点击
  @Output() dataViewOriginChange: EventEmitter<boolean> = new EventEmitter(); // 数据视图原始数据变化

  destroy$ = new Subject();
  rightEditType$: Subscription; // 右侧 编辑面板的 状态 变化
  dataViewChange$: Subscription; // 数据视图 数据 变化
  saveAndPublishLoadingChange$: Subscription; // 保存和发布loading状态变化
  dataViewOriginChange$: Subscription; // 数据视图原始数据变化

  constructor(
    public dataViewService: DataViewService,
    private dataViewRequestService: DataViewRequestService,
    private drawerService: NzDrawerService,
    private translateService: TranslateService,
  ) { }

  ngOnInit(): void {
    // this.dataViewService.setDataViewCode(this.dataViewCode);
    this.rightEditType$ = this.dataViewService.rightEditType$
      .pipe(takeUntil(this.destroy$))
      .subscribe((type: RightEditType) => {
        this.rightEditTypeChange.emit(type);
      });
    this.dataViewChange$ = this.dataViewService.dataViewChange$
      .pipe(takeUntil(this.destroy$), debounceTime(100))
      .subscribe((allSaveDataView: AllSaveDataView) => {
        this.dataViewChange.emit(allSaveDataView);
      });
    this.saveAndPublishLoadingChange$ = this.dataViewService.saveAndPublishLoadingChange$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isLoading: boolean) => {
        this.saveAndPublishLoadingChange.emit(isLoading);
      });

    this.dataViewService.setMobileInfo(this.isMobile);
    this.dataViewOriginChange$ = this.dataViewService.dataViewOriginChange$
      .pipe(takeUntil(this.destroy$))
      .subscribe((dataViewOrigin: any) => {
        this.dataViewOriginChange.emit(dataViewOrigin);
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('dataViewCode')) {
      if (
        changes.dataViewCode.currentValue &&
        changes.dataViewCode.currentValue !== changes.dataViewCode.previousValue
      ) {
        this.initDataView();
      }
    }
    if (Object.keys(changes).includes('operationsLabelList')) {
      this.dataViewService.setOperationsLabelList(changes.operationsLabelList.currentValue);
    }
    if (Object.keys(changes).includes('isShowHeader')) {
      this.dataViewService.setIsShowHeader(changes.isShowHeader.currentValue);
    }
    if (Object.keys(changes).includes('isShowHeaderRight')) {
      this.dataViewService.setIsShowHeaderRight(changes.isShowHeaderRight.currentValue);
    }
  }

  getField(viewShowFields, viewShowFieldsMobile) {
    if (this.isMobile) {
      // 是移动 且移动有viewShowFieldsMobile节点
      if (viewShowFieldsMobile && viewShowFieldsMobile?.field?.length) {
        return viewShowFieldsMobile?.field;
      } else {
        return viewShowFields?.field?.map((item) => {
          item.field_dsl = {};
          item.field_dsl['type'] = 'LABEL';
          item.field_dsl['setting'] = {};
          item.field_dsl['hidden'] = false;
          return item;
        });
      }
    } else {
      return viewShowFields?.field;
    }
  }

  private async initDataView(): Promise<void> {
    this.dataViewService.resetAll();
    this.dataViewService.setDataViewCode(this.dataViewCode);
    if (!this.dataViewService.dataViewCode) return;
    this.dataViewService.setIsInitLoading(true);
    const param = {
      viewCode: this.dataViewService.dataViewCode,
    };
    this.dataViewRequestService.getDataViewDetail(param).subscribe(
      async (res) => {
        try {
          const {
            orderList = [],
            queryConditions = [],
            viewShowFields = {},
            operationFields = {},
            viewShowFieldsMobile,
            conditionList = cloneDeep(baseAdvancedGroup),
            ...dataViewBase
          } = res.data;
          // 备份非当前操作的节点
          const backUpData = this.isMobile ? viewShowFields : viewShowFieldsMobile;
          this.dataViewService.setViewShowFieldsBackup(cloneDeep(backUpData || {}));

          this.dataViewService.setDataViewBase(dataViewBase);
          this.dataViewService.setConditionList(conditionList);
          this.dataViewService.setOrderList(orderList);
          this.dataViewService.setQueryConditions(queryConditions);

          const [getDataViewFieldsRes, getPresetDataObjectRes] = await Promise.all([
            this.getDataViewFields(dataViewBase.modelId, dataViewBase.productCode),
            this.getPresetDataObject(),
          ]);

          const modelResult = await this.fetchModelDrivenData(getDataViewFieldsRes.data.data_code, getDataViewFieldsRes.data.service_code);

          this.dataViewService.model = modelResult.data.model;

          const { mainField = [], relateSubField = [] } = getDataViewFieldsRes.data;
          this.dataViewService.setMainTableInfo({
            data_code: getDataViewFieldsRes.data.data_code,
            data_name: getDataViewFieldsRes.data.data_name,
            lang: getDataViewFieldsRes.data.lang,
          });
          this.dataViewService.setMainField(mainField);
          this.dataViewService.setRelateSubField(relateSubField);
          // 获取运算字段列表
          this.dataViewService.setOperationField(operationFields?.field || []);
          // 2024-04-28移动一下位置，默认添加的manage_status需要从mainField里面拿manage_status信息
          const field = this.getField(viewShowFields, viewShowFieldsMobile);
          this.dataViewService.setViewShowFields(field ?? [], true); // 后端结构调整，现在需要取field才是之前的viewShowFields
          // TODOdh 移动临时处理增加canAddData 后续优化
          const canAddData = viewShowFieldsMobile?.canAddData || false;
          this.dataViewService.setMobileCanAddData(canAddData);

          this.dataViewService.updateSourceTreeByLeftTabType();

          this.dataViewService.setVariableCondition(getPresetDataObjectRes?.data?.obj ?? []);
        } catch (error) {
          // console.error(error);
        }

        this.dataViewService.updateDataViewOrigin();
        this.dataViewService.setIsInitLoading(false);
      },
      () => {
        this.dataViewService.setIsInitLoading(false);
      },
    );
  }

  // 刷新数据视图数据
  public reLoadDataView(): void {
    this.initDataView();
  }

  private fetchModelDrivenData(code: string, serviceCode: string): Promise<any> {
    return this.dataViewRequestService.fetchModelDrivenData({ code, serviceCode }).toPromise();
  }

  private getDataViewFields(modelId: any, productCode: any): Promise<any> {
    const param = {
      modelId,
      productCode,
    };
    return this.dataViewRequestService.queryDataViewFields(param).toPromise();
  }

  // 获取过滤器规则模式数据
  private getPresetDataObject(): Promise<any> {
    return this.dataViewRequestService.getPresetDataObject().toPromise();
  }

  // 单纯更新当前数据视图的name信息
  public updateDataViewName(): void {
    this.dataViewService.setIsInitLoading(true);
    const param = {
      viewCode: this.dataViewService.dataViewCode,
    };
    this.dataViewRequestService.getDataViewDetail(param).subscribe(
      (res) => {
        const name = res.data?.name;
        const nameLang = res.data?.lang?.name;
        this.dataViewService.setDataViewBaseName(name, nameLang, false);

        this.dataViewService.setIsInitLoading(false);
      },
      () => {
        this.dataViewService.setIsInitLoading(false);
      },
    );
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.rightEditType$?.unsubscribe();
    this.dataViewChange$?.unsubscribe();
    this.saveAndPublishLoadingChange$.unsubscribe();
    this.dataViewOriginChange$?.unsubscribe();
  }

  public checkContentChangeWithoutSave() {
    if (this.dataViewService.isInitLoading) return false;
    return !isEqual(this.dataViewService.getSaveDataView(), this.dataViewService.dataViewOrigin);
  }

  handleChangeDataViewName(data) {
    this.dataViewService.setDataViewBaseName(data?.name, data?.lang?.name, false);
  }

  handleView() {
    this.drawerService.create<PreviewDebugComponent, { value: string }, string>({
      nzTitle: this.translateService.instant('dj-预览结果'),
      nzContent: PreviewDebugComponent,
      nzPlacement: 'bottom',
      nzHeight: '75%',
      nzBodyStyle: {
        paddingBottom: '0px',
      },
      nzContentParams: {
        viewCode: this.dataViewService.dataViewCode,
      },
    });
  }
}
