import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DataViewComponent } from './data-view.component';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { DataViewHeaderComponent } from './components/data-view-header/data-view-header.component';
import { DataViewLeftComponent } from './components/data-view-container/components/data-view-left/data-view-left.component';
import { DataViewCenterComponent } from './components/data-view-container/components/data-view-center/data-view-center.component';
import { DataViewCenterMobileComponent } from './components/data-view-container/components/data-view-center-mobile/data-view-center.component';
import { DataViewRightComponent } from './components/data-view-container/components/data-view-right/data-view-right.component';
import { DataViewRightMobileComponent } from './components/data-view-container/components/data-view-right-mobile/data-view-right.component';
import { DataViewContainerComponent } from './components/data-view-container/data-view-container.component';
import { TranslateModule } from '@ngx-translate/core';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { DataViewFormComponent } from './components/data-view-container/components/data-view-center/components/data-view-form/data-view-form.component';
import { DataViewFormMobileComponent } from './components/data-view-container/components/data-view-center-mobile/components/data-view-form/data-view-form.component';
import { DataViewDragDirective } from './directive/data-view-drag.directive';
import { DataViewDropDirective } from './directive/data-view-drop.directive';

import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzTreeModule } from 'ng-zorro-antd/tree';
// import { DataViewService } from './service/data-view.service';
// import { DataViewRequestService } from './service/data-view-request.service';
import { AdEmptyModule } from 'components/ad-ui-components/ad-empty/ad-empty.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { DataViewSortComponent } from './components/data-view-container/components/data-view-right/components/data-view-sort/data-view-sort.component';
import { DataViewSortContentComponent } from './components/data-view-container/components/data-view-right/components/data-view-sort/data-view-sort-content/data-view-sort-content.component';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { DataViewTableSortComponent } from './components/data-view-container/components/data-view-right/components/data-view-table-sort/data-view-table-sort.component';
import { DataViewTableSortContentComponent } from './components/data-view-container/components/data-view-right/components/data-view-table-sort/data-view-table-sort-content/data-view-table-sort-content.component';
import { DataViewFilterComponent } from './components/data-view-container/components/data-view-right/components/data-view-filter/data-view-filter.component';
import { DataViewFilterContentComponent } from './components/data-view-container/components/data-view-right/components/data-view-filter/data-view-filter-content/data-view-filter-content.component';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { AdDatePickerModule } from 'components/ad-ui-components/ad-date-picker/ad-date-picker.module';

import { AdInputNumberModule } from 'components/ad-ui-components/ad-input-number/ad-input-number.module';
import { DataViewFieldNameComponent } from './components/data-view-container/components/data-view-right/components/data-view-field-name/data-view-field-name.component';
import { DataViewFieldAliasComponent } from './components/data-view-container/components/data-view-right/components/data-view-field-alias/data-view-field-alias.component';
import { DataViewFieldWidthComponent } from './components/data-view-container/components/data-view-right/components/data-view-field-width/data-view-field-width.component';
import { DataViewFieldColumnEditComponent } from './components/data-view-container/components/data-view-right/components/data-view-field-column-edit/data-view-field-column-edit.component';
import { DataViewFieldCheckComponent } from './components/data-view-container/components/data-view-right-mobile/components/data-view-field-switch/data-view-field-switch.component';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { PublishButtonModule } from 'components/bussiness-components/module-publish-button/module-publish-button.module';
import { InputModule } from '../../../form-components/input/input.module';
import { DwFormEditorComponentsModule } from '@webdpt/form-editor-components';

// 2024-04-23 视图需求新增组件
import { DataViewTableColumnSettingComponent } from './components/data-view-container/components/data-view-right/components/data-view-table-column-setting/data-view-table-column-setting.component';
import { DataViewTableHeightComponent } from './components/data-view-container/components/data-view-right/components/data-view-table-height/data-view-table-height.component';
import { DataViewTableLinkerComponent } from './components/data-view-container/components/data-view-right/components/data-view-table-linker/data-view-table-linker.component';
import { DataViewTableSectionFilterModule } from './components/data-view-container/components/data-view-right/components/data-view-table-second-filter/data-view-table-second-filter.module';
import { DataViewTableSecondSortComponent } from './components/data-view-container/components/data-view-right/components/data-view-table-second-sort/data-view-table-second-sort.component';
import { DataViewTableTotalComponent } from './components/data-view-container/components/data-view-right/components/data-view-table-total/data-view-table-total.component';
import { DataViewCollapseComponent } from './components/data-view-container/components/data-view-right/components/data-view-collapse/data-view-collapse.component';
import { DataViewRowSelectorModule } from './components/data-view-container/components/data-view-right/components/data-view-row-selector/data-view-row-selector.module';
import { DataViewRowOperationComponent } from './components/data-view-container/components/data-view-right/components/data-view-row-operation/data-view-row-operation.component';
import { JsonEditorModule } from 'components/bussiness-components/json-editor/json-editor.module';
import { NzCollapseModule } from 'ng-zorro-antd/collapse';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { DirectiveModule } from 'common/directive/directive.module';

import { NzRadioModule } from 'ng-zorro-antd/radio';
import { ScriptEditorModule } from 'components/bussiness-components/script-editor/script-editor.module';
import { AdCascaderModule } from '../../../ad-ui-components/ad-cascader/ad-cascader.module';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { AdRangePickerModule } from 'components/ad-ui-components/ad-range-picker/ad-range-picker.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { AddOperateFieldModalModule } from './components/data-view-container/components/data-view-left/add-operate-field-modal/add-operate-field-modal.module';
import { PreviewDebugComponent } from './components/preview-debug/preview-debug.component';
import { NzAlertModule } from 'ng-zorro-antd/alert';
import { NzTableModule } from 'ng-zorro-antd/table';
import { AdInputModule } from 'components/ad-ui-components/ad-input/ad-input.module';
import { MonacoEditorModule } from 'ngx-monaco-editor';
import { NzIconModule } from 'ng-zorro-antd/icon';

@NgModule({
  declarations: [
    DataViewComponent,
    DataViewHeaderComponent,
    DataViewLeftComponent,
    DataViewCenterComponent,
    DataViewCenterMobileComponent,
    DataViewRightComponent,
    DataViewRightMobileComponent,
    DataViewContainerComponent,
    DataViewFormComponent,
    DataViewFormMobileComponent,
    DataViewDragDirective,
    DataViewDropDirective,
    DataViewSortComponent,
    DataViewSortContentComponent,
    DataViewTableSortComponent,
    DataViewTableSortContentComponent,
    DataViewFilterComponent,
    DataViewFilterContentComponent,
    DataViewFieldNameComponent,
    DataViewFieldAliasComponent,
    DataViewFieldWidthComponent,
    DataViewFieldColumnEditComponent,
    DataViewFieldCheckComponent,
    DataViewTableColumnSettingComponent,
    DataViewTableHeightComponent,
    DataViewTableLinkerComponent,
    DataViewTableSecondSortComponent,
    DataViewTableTotalComponent,
    DataViewCollapseComponent,
    DataViewRowOperationComponent,
    PreviewDebugComponent,
  ],
  imports: [
    CommonModule,
    NzDrawerModule,
    TranslateModule,
    AdButtonModule,
    AdIconModule,

    FormsModule,
    NzTreeModule,
    NzCheckboxModule,
    AdEmptyModule,
    AdModalModule,
    DragDropModule,
    AdSelectModule,
    NzDividerModule,
    AdDatePickerModule,

    AdInputNumberModule,
    NzFormModule,
    ReactiveFormsModule,
    NzInputModule,
    NzSelectModule,
    NzSpinModule,
    NzSwitchModule,
    PublishButtonModule,
    InputModule,
    DwFormEditorComponentsModule,
    NzCollapseModule,
    NzCheckboxModule,
    NzTreeSelectModule,
    NzPopoverModule,
    NzDropDownModule,
    JsonEditorModule,
    DirectiveModule,
    DataViewRowSelectorModule,
    DataViewTableSectionFilterModule,
    NzRadioModule,
    ScriptEditorModule,
    AdCascaderModule,
    AdRangePickerModule,
    NzToolTipModule,
    AddOperateFieldModalModule,
    NzAlertModule,
    NzTableModule,
    AdInputModule,
    AdDatePickerModule,
    AdSelectModule,
    MonacoEditorModule,
    NzIconModule,
  ],
  // providers: [DataViewService, DataViewRequestService],
  exports: [DataViewSortComponent, DataViewComponent, DataViewFilterComponent, DataViewFilterContentComponent],
})
export class DataViewModule {}
