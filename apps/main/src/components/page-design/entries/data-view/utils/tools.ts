import { SourceField } from '../config/data-view.type';
import { NzTreeNodeOptions } from 'ng-zorro-antd/tree';

export function sourceFiledToTreeNodeList(
  sourceField: SourceField[],
  disabledKeys = [],
  hideFields: string[] = [],
): NzTreeNodeOptions[] {
  const treeNodeList: NzTreeNodeOptions[] = [];
  sourceField.forEach((fieldItem) => {
    const { relateSubField = [], ...field } = fieldItem;
    const treeNode = {
      title: field.description,
      key: field.fullPath,
      checked: false,
      field,
      children: [],
      disabled: disabledKeys?.includes(field.fullPath),
      disableCheckbox: disabledKeys?.includes(field.fullPath),
    };
    if (relateSubField.length > 0) {
      treeNode.children = sourceFiledToTreeNodeList(relateSubField, hideFields);
    }

    if (!hideFields.includes(field.data_name)) treeNodeList.push(treeNode);
  });

  return treeNodeList;
}

/**
 * 引用字段和函数转成tree
 * @param sourceField
 * @param disabledKeys
 * @param hideFields
 */
export function operationReferenceFiledToTreeNodeList(
  sourceField: any,
  childrenNodeType: string = 'relateSubField',
): NzTreeNodeOptions[] {
  const treeNodeList: NzTreeNodeOptions[] = [];
  sourceField.forEach((fieldItem) => {
    const { relateSubField = [], functions = [], ...field } = fieldItem;
    let relateSubFieldTem = [];
    if (childrenNodeType === 'relateSubField') {
      relateSubFieldTem = relateSubField;
    } else {
      relateSubFieldTem = functions;
    }
    const treeNode = {
      title: childrenNodeType === 'relateSubField' ? field.data_name : field.name,
      description: field?.lang?.description,
      dataType: field?.data_type,
      key: field.fullPath || field.key,
      checked: false,
      field,
      children: [],
      isLeaf: false,
      expanded: true,
    };
    if (relateSubFieldTem.length > 0) {
      treeNode['disabled'] = true;
      treeNode.children = operationReferenceFiledToTreeNodeList(relateSubFieldTem, childrenNodeType);
    } else {
      treeNode.isLeaf = true;
    }
    if (!['json', 'object', 'array'].includes(field?.data_type)) {
      treeNodeList.push(treeNode);
    }
  });

  return treeNodeList;
}
// 只取叶子结点
function getAllRelateSubField(relateSubField: SourceField[]): any[] {
  let returnField = [];
  relateSubField.forEach((relateField) => {
    const { relateSubField = [], ...field } = relateField;
    if (relateSubField.length > 0) {
      returnField = [...returnField, ...getAllRelateSubField(relateSubField)];
    } else {
      returnField.push(field);
    }
  });
  return returnField;
}

// 获取所有数据源字段
export function getAllFields(
  mainField: SourceField[],
  relateSubField: SourceField[],
  operationField: SourceField[],
): any[] {
  return [...mainField, ...getAllRelateSubField(relateSubField), ...operationField];
}

// 根据选中key list 判断 树 是否被全选
export function getSourceTreeIsAllSelect(treeNodeList: NzTreeNodeOptions[], keyList: string[]): any {
  const isAllSelect = treeNodeList.every((node: NzTreeNodeOptions) => {
    if (node?.children.length > 0) {
      return getSourceTreeIsAllSelect(node.children, keyList);
    } else {
      return keyList.includes(node.key);
    }
  });

  return isAllSelect;
}

/** 不同数据类型的列宽 */
export enum ColumnsWidth {
  CHAR = 160,
  VARCHAR = 160,
  TEXT = 160,
  JSON = 160,
  BIT = 80,
  TINYINT = 80,
  SMALLINT = 80,
  INT = 80,
  BIGINT = 80,
  FLOAT = 160,
  DOUBLE = 160,
  DECIMAL = 160,
  DATE = 160,
  TIMESTAMP = 200,
  TIME = 150,
  DATETIME = 200,
}

export function addIndexToArray<T extends object>(
  array: T[],
  startIndex: number = 1,
  indexField: string = 'index'
): (T & Record<string, number>)[] {
  return array.map((item, i) => ({
    ...item,
    [indexField]: i + startIndex
  }));
}

/**
 * 将一维数组按照指定数量分组为二维数组
 * @param array 需要分组的一维数组
 * @param groupSize 每组的元素数量，默认为4
 * @returns 分组后的二维数组
 */
export function groupArrayElements<T>(array: T[], groupSize: number = 4): T[][] {
  if (!array || !array.length) {
    return [];
  }

  const result: T[][] = [];

  for (let i = 0; i < array.length; i += groupSize) {
    result.push(array.slice(i, i + groupSize));
  }

  return result;
}

/**
 * 将对象转换为包含单键值对对象的数组，并可以通过处理函数自定义输出
 * @param obj 需要转换的对象
 * @param handler 处理每个键值对的函数，接收key和value参数，返回处理后的对象
 * @returns 处理后的对象数组
 * 
 * 例如: 
 * {a: 1, b: 2} => [{key: 'a', value: 1}, {key: 'b', value: 2}]
 */
export function objectToObjectArray<T = any, R = any>(
  obj: Record<string, T>,
  handler?: (key: string, value: T) => R
): Array<R | Record<string, T>> {
  if (!obj || typeof obj !== 'object') {
    return [];
  }

  return Object.entries(obj).map(([key, value]) => {
    if (handler && typeof handler === 'function') {
      return handler(key, value);
    }
    return { [key]: value };
  });
}