import { Injectable } from '@angular/core';
import { PageCode, SubPageInfo, WorkData, WorkDesignInfo } from '../config/data-entry-work-design.type';
import {
  MenuCode,
  PageUIElementSearchInfo,
  PageUIElement,
  PageInfo,
  PageInfoType,
  SubPage,
  DataQueryComponent,
  DataQueryComponentGroup,
  OutletComponent,
} from '../config/data-entry-work-design.type';
import { WorkDesignConfig } from 'components/page-design/components/dsl-work-design/shared/interface/dsl-work-design.interface';
import { Observable, Subject } from 'rxjs';
import { IsvPackageDataAllInfo } from '@webdpt/form-editor-components';
import { getWorkDesignPageType, getPageCode } from '../utils/tools';
import { TranslateService } from '@ngx-translate/core';
import { UUID } from 'angular2-uuid';
import {
  transformDataSourceList,
  getTypeAlias,
} from 'components/page-design/components/data-source/data-source.component.util';
@Injectable()
export class DataEntryWorkDesignService {
  private _config: WorkDesignConfig = null; // 配置项
  get config() {
    return this._config;
  }

  private _workData: WorkData = null; // 完整的数据录入界面设计基础数据
  get workData() {
    return this._workData;
  }

  private _workDesignInfo: WorkDesignInfo = null; // 完整的数据录入界面设计完整数据
  get workDesignInfo() {
    return this._workDesignInfo;
  }

  get dataSources(): any {
    return this.page?.dataSources || {};
  }

  get dataSourceNames(): any {
    /**
     * 之前聊的是数据源以dataSourceNames为准，不允许为空
     * 但是实际情况，这玩意几千条数据都是空的
     * 所以没办法，加上兼容dataSourceNames的取值逻辑
     */
    return this.page?.dataSourceNames || this.getDefaultDataSourceNames();
  }

  get isCustom(): any {
    // 当前只有界面设计，和编辑界面可以转定制
    return this.page?.isCustomize;
  }

  get operateCustomTemplateRelates(): any[] {
    return this.page?.operateCustomTemplateRelates || [];
  }

  // 当前页面的内容数据(界面设计器同时只能操作一个页面，页面可以是主页面或子页面)
  get page(): any {
    if (this._activePageInfo?.type === PageInfoType.SUB_PAGE) {
      return this._subPageList?.find((sub) => sub.code === this._activePageInfo.code) ?? {};
    }
    return this._workDesignInfo?.[getWorkDesignPageType(this._activePageInfo?.code)];
  }

  private _pageInfoList: PageInfo[] = []; // 页面信息列表

  get pageInfoList(): PageInfo[] {
    return this._pageInfoList;
  }

  get menuPageInfoList(): PageInfo[] {
    // 菜单页面列表
    return this._pageInfoList.filter((item) => item.type === PageInfoType.MENU_PAGE);
  }

  get subPageInfoList(): PageInfo[] {
    // 子页面列表
    return this._pageInfoList
      .filter((item) => item.type === PageInfoType.SUB_PAGE)
      .map((pageInfo: SubPageInfo) => {
        const subPageData = this._subPageList.find((sub) => sub.code === pageInfo.code);
        if (!!subPageData) {
          pageInfo.name = subPageData?.lang?.name?.[this.translateService?.currentLang] ?? subPageData.name;
          pageInfo.data = subPageData;
        }
        return pageInfo;
      });
  }

  // 获取当前页面环境所有相关的PageUIElement
  // 子页面和作业自身的页面其pageUIElement存放位置不同
  // 子页面的PageUIElement是单独存放在各自子页面的，但作业自身页面（浏览，编辑这种）的所有PageUIElement会放在一处
  // 平台定义的pageUIElement 其实是 list，这点需要注意，会产生干扰
  get currentPageUIElements(): PageUIElement[] {
    if (this._activePageInfo.type === PageInfoType.SUB_PAGE) {
      return this.page?.pageUIElement || [];
    }
    return this._workDesignInfo?.pageUIElement || [];
  }

  // 定制作业名称
  get customTip(): string {
    return `${this.workData.code}-${this.config.pageCode}`;
  }

  private _initInfoLoading: boolean = false; // 初始化数据信息加载的loading
  get initInfoLoading(): boolean {
    return this._initInfoLoading;
  }

  private _saveLoading: boolean = false; // 保存时的loading
  get saveLoading(): boolean {
    return this._saveLoading;
  }

  // 订阅-保存时的loading状态变化
  private _saveLoadingChange$: Subject<boolean> = new Subject<boolean>();
  get saveLoadingChange$(): Observable<boolean> {
    return this._saveLoadingChange$.asObservable();
  }

  private _activePageInfo: PageInfo = null; // 当前激活的页面
  get activePageInfo() {
    return this._activePageInfo;
  }

  // isv定制组件信息
  private _isvPackageDataList: IsvPackageDataAllInfo[] = [];
  get isvPackageDataList() {
    return this._isvPackageDataList;
  }

  // 子页面的结构数据
  private _subPageList: SubPage[] = []; // 子页面完全信息list
  get subPageList(): SubPage[] {
    return this._subPageList;
  }

  constructor(private translateService: TranslateService) {}

  setWorkData(workData: WorkData): void {
    this._workData = workData;
  }

  setWorkDesignInfo(workDesignInfo: WorkDesignInfo): void {
    this._workDesignInfo = workDesignInfo;
  }

  // 通过关键字设置数据录入界面设计完整数据的部分属性
  setWorkDesignInfoByKey(key: string, value: any): void {
    this._workDesignInfo[key] = value;
  }

  // 设置数据录入界面设计完整数据的部分属性（通过属性关键字路径list）
  setWorkDesignInfoByKeyPath(keyPath: string[], value: any): void {
    const targetKey = keyPath.splice(-1);
    const obj = keyPath.reduce((pre, cur) => {
      return pre[cur];
    }, this._workDesignInfo);

    obj[targetKey[0]] = value;
  }

  // 设置当前页面的部分属性（通过属性关键字路径list）
  setCurrentPageInfoByKeyPath(keyPath: string[], value: any): void {
    if (!this.page) return;

    const targetKey = keyPath.splice(-1);
    const obj = keyPath.reduce((pre, cur) => {
      return pre[cur];
    }, this.page);

    obj[targetKey[0]] = value;
  }

  setInitInfoLoading(initInfoLoading: boolean): void {
    this._initInfoLoading = initInfoLoading;
  }

  setSaveLoading(saveLoading: boolean): void {
    this._saveLoading = saveLoading;
    this._saveLoadingChange$.next(saveLoading);
  }

  setActivePageInfo(activePageInfo: PageInfo): void {
    this._activePageInfo = activePageInfo;
  }

  setPageInfoList(pageInfoList: PageInfo[], pageInfoType?: PageInfoType): void {
    if (pageInfoType) {
      this._pageInfoList = this._pageInfoList.filter((item) => item.type !== pageInfoType).concat(pageInfoList);
      return;
    }
    this._pageInfoList = pageInfoList;
  }

  setConfig(config: WorkDesignConfig): void {
    this._config = config;
  }

  // 设置 isv定制组件信息
  setIsvPackageDataList(isvPackageDataList: IsvPackageDataAllInfo[]) {
    this._isvPackageDataList = isvPackageDataList;
  }

  // 获取当前数据源
  getCurrentDataSource() {
    return this.dataSources[this.dataSourceNames[0]];
  }

  // 通过code，activityId，pageCode即PageUIElementSearchInfo
  // 获取当前页面环境对应pageUIElement(平台定义的pageUIElement其实是pageUIElement list)中对应的一条pageUIElement数据
  // 此外，子页面和非子页面的pageUIElement位置不同，所以currentPageUIElements中有对当前页面的判断
  getCurrentPageUIElementData(pageUIElementSearchInfo: PageUIElementSearchInfo): PageUIElement {
    return this.currentPageUIElements.find(
      (item) =>
        item.code === pageUIElementSearchInfo.code &&
        item.activityId === pageUIElementSearchInfo.activityId &&
        item.pageCode === pageUIElementSearchInfo.pageCode,
    );
  }

  // 获取当前作业页面环境对应pageUIElement(平台定义的pageUIElement其实是pageUIElement list)
  // 中满足部分条件的所有pageUIElement数据
  // 此外，子页面和非子页面的pageUIElement位置不同，所以currentPageUIElements中有对当前页面的判断
  getCurrentPageUIElementDatas(pageUIElementSearchInfo: Partial<PageUIElementSearchInfo>): PageUIElement[] {
    return this.currentPageUIElements.filter(
      (item) =>
        (!pageUIElementSearchInfo?.code || item.code === pageUIElementSearchInfo.code) &&
        (!pageUIElementSearchInfo?.activityId || item.activityId === pageUIElementSearchInfo.activityId) &&
        (!pageUIElementSearchInfo?.pageCode || item.pageCode === pageUIElementSearchInfo.pageCode),
    );
  }

  // 通过code，activityId，pageCode即PageUIElementSearchInfo
  // 设置当前作业页面环境对应pageUIElement(平台定义的pageUIElement其实是pageUIElement list)中的数据
  // 如果没有找到则插入一条数据
  // 此外，子页面和非子页面的pageUIElement位置不同，所以currentPageUIElements中有对当前页面的判断
  setCurrentPageUIElement(
    pageUIElementSearchInfo: PageUIElementSearchInfo,
    pageUIElement: PageUIElement,
    index?: number,
  ): void {
    const findIndex = [undefined, null].includes(index)
      ? this.currentPageUIElements.findIndex(
          (item) =>
            item.code === pageUIElementSearchInfo.code &&
            item.activityId === pageUIElementSearchInfo.activityId &&
            item.pageCode === pageUIElementSearchInfo.pageCode,
        )
      : index;

    this.currentPageUIElements.splice(findIndex, findIndex >= 0 ? 1 : 0, pageUIElement);
  }

  // 设置当前页面的完整的pageUIElement（替换）（此外平台定义的pageUIElement其实是pageUIElement list）
  setCurrentPageUIElements(pageUIElements: PageUIElement[]): void {
    // 结构不同处理逻辑 不同
    if (this._activePageInfo.type === PageInfoType.MENU_PAGE) {
      const pageCode = getPageCode(this._activePageInfo);
      const pageUIElementResult = this.currentPageUIElements
        .filter((item) => item.pageCode !== pageCode)
        .concat(pageUIElements);
      this._workDesignInfo.pageUIElement = pageUIElementResult; // TODO
    }

    if (this.activePageInfo.type === PageInfoType.SUB_PAGE) {
      this.page.pageUIElement = pageUIElements;
    }
  }

  // 通过 dataSource 获取 用来查找 pageUIElementData 的 PageUIElementSearchInfo 数据
  getCurrentDataSourcePageUIElementSearchInfoByDataSource(dataSource: any): PageUIElementSearchInfo {
    return {
      code: dataSource?.dataViewQuery?.code ?? '',
      activityId: this.getCurrentActivityId(this._activePageInfo),
      pageCode: getPageCode(this._activePageInfo),
    };
  }

  // 获取当前数据源下的PageUIElement
  getCurrentDataSourcePageUIElementData(): PageUIElement {
    return this.getCurrentPageUIElementData(
      this.getCurrentDataSourcePageUIElementSearchInfoByDataSource(this.getCurrentDataSource()),
    );
  }

  // 设置当前数据源下的PageUIElement
  setCurrentDataSourcePageUIElementData(pageUIElementData: PageUIElement) {
    this.setCurrentPageUIElement(
      this.getCurrentDataSourcePageUIElementSearchInfoByDataSource(this.getCurrentDataSource()),
      pageUIElementData,
    );
  }

  // 在subPageList中设置一条subPage，如果没有找到，就插入一条，找到就覆盖
  setSubPage(subPage: SubPage): void {
    const findIndex = this._subPageList.findIndex((item) => item.code === subPage.code);
    this._subPageList.splice(findIndex, findIndex >= 0 ? 1 : 0, subPage);
  }

  resetSubPageList(): void {
    this._subPageList = [];
  }

  /**
   * 如果是新版数据，一定会有dataSourceNames
   * 这边处理的是没有dataSourceNames的情况
   * 所以就应该用老逻辑
   */
  getDefaultDataSourceNames(): string[] {
    if (!this.dataSources) return [];
    const keys = Object.keys(this.dataSources);
    if (keys.length === 0) return [];
    const isQueryPlan = getTypeAlias(keys[0]) === 'queryPlan';
    if (isQueryPlan) {
      const defaultKey = keys.find((key) => this.dataSources[key]?.dataViewQuery?.isDefault);
      if (defaultKey) {
        return this.dataSources[defaultKey].name ?? defaultKey;
      }
    }
    const dataSourceLists = transformDataSourceList(this.dataSources);
    if (dataSourceLists.length === 0) return [];
    const defaultDataSourceName = dataSourceLists[0].json.name;
    if (!defaultDataSourceName) return [];
    return [defaultDataSourceName];
  }

  // 获取MenuCode指定页面的索引界面pageUIElement（子页面没有索引界面），没有的话会创建
  // 只有当前的数据源是查询方案数据源才会生成索引界面
  // getIndexPageUIElementByMenuCode(menuCode: MenuCode): any {
  //   const menuPage = this._workDesignInfo?.[getWorkDesignPageType(menuCode)];
  //   const dataSources = menuPage?.dataSources;
  //   // 查询方案模式才需要，查询方案数据源和其他类型数据源互斥
  //   if (!dataSources || Object.values<any>(dataSources)?.[0]?.type !== 'QUERYPLAN') {
  //     return null;
  //   }

  //   const menuPagePageUIElements: PageUIElement[] = this._workDesignInfo?.pageUIElement?.filter(
  //     (item) => item.pageCode === menuCode,
  //   );
  //   // 获取索引页，没有就创建
  //   const indexPageUIElement: PageUIElement = menuPage?.indexPageUIElement ?? {
  //     activityId: this?.workData?.code,
  //     code: 'index',
  //     pageCode: menuCode,
  //     application: this._workDesignInfo?.application,
  //     elements: {
  //       gridSettings: [],
  //       hooks: [],
  //       layout: [],
  //       operations: [],
  //       submitActions: [],
  //     },
  //   };

  //   // 出口组件
  //   const outletComponent: OutletComponent = indexPageUIElement?.elements?.layout?.find(
  //     (item: any) => item.type === 'OUTLET',
  //   ) ?? {
  //     id: UUID.UUID(),
  //     type: 'OUTLET',
  //   };

  //   // 数据容器组件
  //   const dataQueryComponent: DataQueryComponent = indexPageUIElement?.elements?.layout?.find(
  //     (item: any) => item.type === 'DATA_QUERY',
  //   ) ?? {
  //     id: UUID.UUID(),
  //     type: 'DATA_QUERY',
  //     viewType: 'tab',
  //     showAccount: false,
  //     bgColor: '#FFFFFF',
  //     direction: 'horizontal',
  //     group: [],
  //   };

  //   // 产生固定 schema
  //   dataQueryComponent['schema'] = `${this.workDesignInfo.code}_index`;

  //   const group: DataQueryComponentGroup[] = Object.values<any>(dataSources)
  //     .sort((a, b) => a.sequence - b.sequence)
  //     .map((dataSource) => {
  //       const originGroupItem: DataQueryComponentGroup = dataQueryComponent.group.find(
  //         (item) => item.viewCode === dataSource.dataViewQuery.code,
  //       );
  //       const dataSourcePageUIElement: PageUIElement = menuPagePageUIElements.find(
  //         (item) => item.code === dataSource.dataViewQuery.code,
  //       );
  //       // const { schema = '', path = '' } =
  //       //   dataSourcePageUIElement?.elements?.layout?.find((item) => item.type === 'ATHENA_TABLE') ?? {};

  //       return {
  //         id: originGroupItem?.id ?? UUID.UUID(),
  //         schema: dataSource?.dataViewQuery?.code,
  //         path: '',
  //         viewCode: dataSource?.dataViewQuery?.code,
  //         viewName: dataSource?.title,
  //         lang: {
  //           viewName: dataSource?.lang?.title,
  //         },
  //         isDefault: dataSource.dataViewQuery?.isDefault,
  //         isShow: true,
  //         outletConfig: {
  //           targetId: outletComponent.id,
  //           url: 'dataView/show',
  //           pageCode: menuCode,
  //           activityId: this?.workData?.code,
  //         },
  //         image: originGroupItem?.image ?? {
  //           src: '',
  //           title: dataSource.title,
  //         },
  //       };
  //     });

  //   dataQueryComponent.group = group;
  //   indexPageUIElement.elements.layout = [dataQueryComponent, outletComponent];
  //   return indexPageUIElement;
  // }

  // 更新MenuCode指定页面的索引界面pageUIElement，没有的话会创建
  // updateIndexPageUIElementByMenuCode(menuCode: MenuCode, indexPageUIElement?: any) {
  //   const indexPageUIElementData = indexPageUIElement ?? this.getIndexPageUIElementByMenuCode(menuCode);
  //   this.setWorkDesignInfoByKeyPath([getWorkDesignPageType(menuCode), 'indexPageUIElement'], indexPageUIElementData);
  //   // 和后端约定的flag,后端会在编译时根据该字段处理数据源
  //   this.setWorkDesignInfoByKeyPath(
  //     [getWorkDesignPageType(menuCode), 'totalCountFlag'],
  //     !!indexPageUIElementData?.elements?.layout?.find((item: any) => item.type === 'DATA_QUERY')?.showAccount,
  //   );
  // }

  // 设置自定义打印操作，此属性在外层
  setOperateCustomTemplateRelates(customs: any[]): void {
    if (this.page) {
      this.page.operateCustomTemplateRelates = customs || [];
    }
  }

  // 根据pageInfo 获取 activityId
  getCurrentActivityId(pageInfo: PageInfo): string {
    if (pageInfo.type === PageInfoType.SUB_PAGE) {
      return pageInfo.code;
    }
    return this?.workData?.code;
  }
}
