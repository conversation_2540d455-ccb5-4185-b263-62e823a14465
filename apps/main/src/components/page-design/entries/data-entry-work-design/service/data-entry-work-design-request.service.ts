import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SystemConfigService } from 'common/service/system-config.service';
import { AppService } from 'pages/apps/app.service';
import { handleOperateTemplateRelates } from 'common/utils/print-search';

@Injectable()
export class DataEntryWorkDesignRequestService {
  apiUrl = '';
  constructor(
    private httpClient: HttpClient,
    private systemConfig: SystemConfigService,
    private appService: AppService,
  ) {
    this.systemConfig.get('adesignerUrl').subscribe((url) => {
      this.apiUrl = url;
    });
  }

  // 加载驱动模型的界面数据
  loadModelPageView(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/pageDesign/queryByCode`;
    return this.httpClient.get(url, {
      params,
    });
  }

  // 保存
  pageDesignUpdate(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/pageDesign/update`;
    return this.httpClient.post(url, handleOperateTemplateRelates(params));
  }

  getSimpleModelCodeDatas(): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/modelDriver/modelAssign/queryList`;
    return this.httpClient.get(url, {
      params: { application: this.appService.selectedApp.code, queryType: 'association' },
    });
  }

  // 根据数据源生成默认的element
  generateDefaultDslByCode(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/pageDesign/generateDefaultDslByCode`;
    return this.httpClient.post(url, params);
  }

  // 新增子页面
  subPageAdd(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/subpage/subpageAdd`;
    return this.httpClient.post(url, params);
  }

  // 更新子页面基础信息
  subPageUpdateBaseInfo(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/subpage/updateBaseInfo`;
    return this.httpClient.post(url, params);
  }

  // 更新子页面
  subpageUpdate(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/subpage/subpageUpdate`;
    return this.httpClient.post(url, params);
  }

  // 更新子页面-批量
  subPageBatchUpdate(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/subpage/subpageBatchUpdate`;
    return this.httpClient.post(url, params);
  }

  // 删除子页面
  subPageDelete(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/subpage/subpageDelete`;
    return this.httpClient.post(url, params);
  }

  // 查询子页面详情
  subPageQueryDetail(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/subpage/subpageQueryDetail`;
    return this.httpClient.post(url, params);
  }

  // 查询子页面列表
  subPageQueryList(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/subpage/subpageQueryList`;
    return this.httpClient.post(url, params);
  }

  /**
   * 上传文件地址(url)
   */
  getUploadUrl(): string {
    return `${this.apiUrl}/athena-designer/dmc/shareUploadFile`;
  }

  // 同步页面
  subpageBatchSync(params: any): Observable<any> {
    const url = `${this.apiUrl}/athena-designer/subpage/subpageBatchSync`;
    return this.httpClient.post(url, params);
  }
}
