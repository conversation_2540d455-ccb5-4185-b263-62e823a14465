import {
  Category,
  MenuCode,
  PageCode,
  PageInfo,
  PageInfoType,
  PageUIElementSearchInfo,
} from '../config/data-entry-work-design.type';
import { signDocumentConfigCategoryList } from '../config/data-entry-work-design.config';

// 获取pageType（历史逻辑,作业中的主页面才有可能涉及）
export const getWorkDesignPageType = (code: MenuCode) => {
  return code === MenuCode.EDIT_PAGE ? 'detailDsl' : 'pageDsl';
};

// 根据pageInfo 获取 pageCode(历史逻辑)
export const getPageCode = (pageInfo: PageInfo): PageCode => {
  if (pageInfo.type === PageInfoType.SUB_PAGE) {
    return 'sub-page';
  }
  return pageInfo.code;
};
