// 多语言
export interface Lang {
  [propName: string]: LangObject;
}

// 多语言对象
export interface LangObject {
  zh_CN: string;
  zh_TW: string;
  en_US?: string;
}

// 菜单code 改用 平台的 pageCode
export enum MenuCode {
  BASIC_DATA = 'basic-data', // 单档多栏的 界面设计
  BROWSE_PAGE = 'browse-page', // 多档的 浏览界面
  EDIT_PAGE = 'edit-page', // 多档的编辑界面
}

export type PageCode = MenuCode | 'sub-page';

export interface WorkData {
  code: string;
  [propName: string]: any;
}

// 界面设计完整数据
export interface WorkDesignInfo {
  code: string;
  category: Category;
  application: string;
  iamCondition?: any[];
  name?: string;
  businessCode?: string;
  [propName: string]: any;
}

// 代码数据
export interface CodeData {
  pageDsl?: any;
  detailDsl?: any;
  [propName: string]: any;
}

export enum PageInfoType {
  MENU_PAGE = 'MENU_PAGE',
  SUB_PAGE = 'SUB_PAGE',
}

export interface MenuPageInfo {
  name: string;
  code: MenuCode;
  type: PageInfoType.MENU_PAGE;
}

export interface SubPageInfo {
  name: string;
  code: string;
  type: PageInfoType.SUB_PAGE;
  data: any; //TODO 子页面数据，当刚进入作业时，只会加载子页面list，此时该数据为基础数据，点击子页面后，会调子面面详情接口，之后data就是全量数据
}

export type PageInfo = MenuPageInfo | SubPageInfo;

// 数据录入category类型
export enum Category {
  'SIGN-DOCUMENT' = 'SIGN-DOCUMENT', // 模型驱动单档多栏
  'TREEDATA-SINGLE-DOCUMENT' = 'TREEDATA-SINGLE-DOCUMENT', // 模型驱动单档多栏树
  'DOUBLE-DOCUMENT-FORM' = 'DOUBLE-DOCUMENT-FORM', // 模型驱动单档
  'DOUBLE-DOCUMENT' = 'DOUBLE-DOCUMENT', // 模型驱动双档
  'DOUBLE-DOCUMENT-MULTI' = 'DOUBLE-DOCUMENT-MULTI', // 模型驱动多档
  'TREEDATA-DOUBLE-DOCUMENT-FORM' = 'TREEDATA-DOUBLE-DOCUMENT-FORM', // 模型驱动单档树
  'TREEDATA-DOUBLE-DOCUMENT' = 'TREEDATA-DOUBLE-DOCUMENT', // 模型驱动双档树
  'TREEDATA-DOUBLE-DOCUMENT-MULTI' = 'TREEDATA-DOUBLE-DOCUMENT-MULTI', // 模型驱动多档树
}

// pageUIElement 的查询信息
export interface PageUIElementSearchInfo {
  code: string;
  activityId: string;
  pageCode: string;
}

// 后台用来产生PageUIElement的原始信息
export interface PageUIElementGenerateOrigin extends PageUIElementSearchInfo {
  actionId?: string;
  type: string;
}

// pageUIElement
export interface PageUIElement extends PageUIElementSearchInfo {
  elements: any;
  operateCustomTemplateRelates?: any[];
}

export interface OperateTemplateRelates {
  pattern: 'BUSINESS_PERFORM' | 'BUSINESS_CHARGE' | 'DATA_ENTRY_PERFORM';
  targetId: string;
  client: 'PC' | 'APP';
  templates: {
    templateId: string;
    actionParams: any[];
  }[];
}

// 子页面的结构
// 后端定义的结构
// 为何与之前的结构不一致？后端的回复是 之前没有和 运行时 保持一致，现在需要保持一致
export interface SubPage {
  code: string;
  application: string;
  parentPageCode: string;
  comeFrom: string;
  masterFromDataSourceName: boolean;
  dataSources: any;
  dataSourceNames: string[];
  pageUIElement: PageUIElement[];
  [propName: string]: any;
}

// 用于 组装的 数据容器 组件dsl
export interface DataQueryComponent {
  id: string;
  bgColor: '#F7F8FA' | '#FFFFFF';
  direction: 'horizontal' | 'vertical';
  showAccount: boolean;
  viewType: 'tab' | 'card';
  viewName: string;
  group: DataQueryComponentGroup[];
  type: 'DATA_QUERY';
  [props: string]: any;
}

export interface DataQueryComponentGroup {
  viewCode: string;
  image: {
    src: string;
    title: string;
  };
  [props: string]: any;
}

export interface OutletComponent {
  id: string;
  type: 'OUTLET';
  [props: string]: any;
}
