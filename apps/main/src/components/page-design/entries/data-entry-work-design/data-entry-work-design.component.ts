import {
  Component,
  OnInit,
  Input,
  Output,
  EventEmitter,
  OnChanges,
  OnDestroy,
  SimpleChanges,
  ViewChild,
  TemplateRef,
  ChangeDetectorRef,
} from '@angular/core';
import { DataEntryWorkDesignService } from './service/data-entry-work-design.service';
import { DataEntryWorkDesignRequestService } from './service/data-entry-work-design-request.service';
import { TranslateService } from '@ngx-translate/core';
import {
  MenuCode,
  PageUIElement,
  PageInfoType,
  WorkData,
  CodeData,
  PageInfo,
  MenuPageInfo,
  SubPageInfo,
  SubPage,
} from './config/data-entry-work-design.type';
import { signDocumentConfigCategoryList, historyModalProps } from './config/data-entry-work-design.config';

import { isEqual, cloneDeep } from 'lodash';

import {
  SIGN_DOCUMENT_CONFIG,
  DOUBLE_DOCUMENT_CONFIG,
} from 'components/page-design/components/dsl-work-design/shared/config/dsl-work-design.config';

import { SubModuleEnum } from 'components/page-design/components/dsl-work-design/shared/config/dsl-work-sidebar.config';

import { NzMessageService } from 'ng-zorro-antd/message';
import { StateManagementComponent } from './components/state-management/state-management.component';
import { Subscription, Subject } from 'rxjs';
import { takeUntil, debounceTime } from 'rxjs/operators';

import { AppService } from '../../../../pages/apps/app.service';
import { EActivitiyType } from 'components/bussiness-components/preview-modal';

import { IsvCustomPackageService } from 'common/service/isv-custom-package/isv-custom-package.service';
import { AppTypes } from 'pages/app/typings';

import { DataSourceModeEnum } from 'components/page-design/components/dsl-work-design/shared/config/dsl-work-sidebar.config';

import { BusinessShareInfo } from '../../../bussiness-components/business-share-consumer/type';
import { BusinessShareConsumerComponent } from '../../../bussiness-components/business-share-consumer/business-share-consumer.component';

// import {
//   DynamicWorkDesignInfo,
//   DynamicWorkDesignRenderData,
// } from '@athena-designer-core/src/components/DynamicWorkDesign/config/type';
import { DesignerTypes } from 'app/types';
import { AdUserService } from 'pages/login/service/user.service';

import { PageEditInfo } from './components/page-info-modal/page-info-modal.component';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { getPageCode } from './utils/tools';
import { getOperationList } from './components/operate-permission-management/operate-permission-management.tool';
import {
  UpdateSubpageModalPageInfo,
  UpdatePageInfo,
  UpdateSubpageModalSubmitData,
} from './components/update-subpage-modal/update-subpage-modal.component';
import { IsPrivatizationService } from 'common/service/is-privatization.service';

@Component({
  selector: 'app-data-entry-work-design',
  templateUrl: './data-entry-work-design.component.html',
  styleUrls: ['./data-entry-work-design.component.less'],
  providers: [DataEntryWorkDesignService, DataEntryWorkDesignRequestService],
})
export class DataEntryWorkDesignComponent implements OnInit, OnChanges, OnDestroy {
  @Input() modelPageType: 'design' | 'browse' | 'edit' | 'notModelDriven' = 'notModelDriven'; // 模型驱动解决方案页面类型，默认是notModelDriven，走非模型驱动逻辑
  @Input() workData: WorkData; // 数据录入基础信息

  @Input() isHideHeaderRight: boolean = false; // 是否隐藏头部右侧操作
  @Input() headerCustomTemplate: TemplateRef<any> | null = null; // 模型驱动解决方案自定义头部，之前处理这块的逻辑的开发者将headerCustomTemplate放在workData里面，参与了workData的更新，性能堪忧，现在将其优化拆分出去
  @Output() close: EventEmitter<any> = new EventEmitter();
  @Output() saveAndPublishLoadingChange: EventEmitter<boolean> = new EventEmitter(); // 当保存和发布状态变化时触发的事件，配合优化需求，保存和发布时模型驱动解决方案左侧菜单不可点击
  @Output() contentChangeWithoutSaveChange: EventEmitter<boolean> = new EventEmitter(); // 内容变更未保存状态的变化

  // 是否是子页面
  get isSubPage() {
    return this.dataEntryWorkDesignService.activePageInfo?.type === PageInfoType.SUB_PAGE;
  }

  // 是否是编辑界面
  get isEditPage() {
    return (
      this.dataEntryWorkDesignService.activePageInfo?.type === PageInfoType.MENU_PAGE &&
      this.dataEntryWorkDesignService.activePageInfo?.code === MenuCode.EDIT_PAGE
    );
  }

  // 是否是界面设计界面
  get isBasicDataPage() {
    return (
      this.dataEntryWorkDesignService.activePageInfo?.type === PageInfoType.MENU_PAGE &&
      this.dataEntryWorkDesignService.activePageInfo?.code === MenuCode.BASIC_DATA
    );
  }

  // 是否展示预览按钮
  get showPreviewButton() {
    return (
      this.dataEntryWorkDesignService.activePageInfo?.type === PageInfoType.MENU_PAGE &&
      this.dataEntryWorkDesignService.activePageInfo?.code !== MenuCode.BROWSE_PAGE
    );
  }

  get isBrowsePage() {
    return (
      this.dataEntryWorkDesignService.activePageInfo?.type === PageInfoType.MENU_PAGE &&
      this.dataEntryWorkDesignService.activePageInfo?.code === MenuCode.BROWSE_PAGE
    );
  }

  // 是否可以转定制
  get canChangeCustom() {
    return (
      this.dataEntryWorkDesignService?.activePageInfo?.type === PageInfoType.MENU_PAGE &&
      [MenuCode.BASIC_DATA, MenuCode.EDIT_PAGE].includes(this.dataEntryWorkDesignService?.activePageInfo?.code)
    );
  }

  // 是否展示查询方案编辑按钮（查询方案转数据容器）
  // 模型驱动应用2.0的浏览界面，且数据源是查询方案，才会展示
  // get isShowQueryPlanEditButton() {
  //   return (
  //     this.modelPageType === 'browse' &&
  //     this.dataEntryWorkDesignService?.dataSources &&
  //     Object.values<any>(this.dataEntryWorkDesignService?.dataSources)?.[0]?.type === 'QUERYPLAN' &&
  //     this.dataEntryWorkDesignService?.activePageInfo?.type !== PageInfoType.SUB_PAGE // 这里有点特殊，理论上来说，模型驱动2.0的应用，子页面的入口应该是在资源树中，但这期没做左侧资源树，所以需要该判断
  //   );
  // }

  // 是否展示状态管理按钮
  get showStateModalButton() {
    return (
      this.modelPageType === 'notModelDriven' &&
      this.dataEntryWorkDesignService?.activePageInfo?.type === PageInfoType.MENU_PAGE &&
      [MenuCode.BASIC_DATA, MenuCode.BROWSE_PAGE].includes(this.dataEntryWorkDesignService.activePageInfo?.code)
    );
  }

  historyModalProps: HistoryModalProps = cloneDeep(historyModalProps); // 操作记录弹窗信息
  showCodeModal: boolean; // 代码界面是否打开
  codeData: CodeData = {}; // 代码界面的代码数据

  showStateModal: boolean; // 是否打开状态管理弹窗

  // 预览相关
  previewVisible = false;
  previewParam = null;

  workDataHasChanged: boolean = false; // 关闭时提醒

  destroy$ = new Subject();
  saveAndPublishLoadingChange$: Subscription; // 保存和发布loading状态变化

  initPageData: {
    pageDsl: any;
    detailDsl: any;
    pageUIElement: any[];
    subPageList: any[];
  } = null; // 每个页面formio第一次完整渲染之后的数据对象（配合检测数据变更使用）

  dynamicWorkDesignBusinessShareInfo: BusinessShareInfo = null; // 使用子应用的共享业务组件

  designerType = `${DesignerTypes.WORK}@${this.userService.getUser('branch')}`;
  gobalErrorMessage: string = ''; // 数据录入错误信息，当不为空时，代表数据录入页面存在问题

  isPageSelectVisible: boolean = false; // 是否显示页面选择弹窗
  isPageInfoModalVisible: boolean = false; // 是否显新增子页面
  pageEditInfo: PageEditInfo = null; // 新增/编辑页面信息

  isUpdateSubpageModalVisible: boolean = false; // 是否展示同步子页面弹窗
  updateSubpageModalPageInfo: UpdateSubpageModalPageInfo; // 同步子页面弹窗中选中的页面信息
  updateSubpageModalPageInfoList: UpdateSubpageModalPageInfo[] = []; // 同步子页面弹窗中页面信息列表

  // isQueryPlanModalVisible: boolean = false; // 是否显示查询方案信息弹窗
  // queryPlanEditInfo: QueryPlanEditInfo = null; // 查询方案编辑信息

  // openwindowVisible: boolean;
  // openwindowData: any;

  // 发布按钮
  @ViewChild('publishButton', { static: false }) publishButtonRef: any;
  @ViewChild('stateManagement') stateManagement: StateManagementComponent;
  @ViewChild('dynamicWorkDesign') dynamicWorkDesign: BusinessShareConsumerComponent;

  constructor(
    public dataEntryWorkDesignService: DataEntryWorkDesignService,
    private dataEntryWorkDesignRequestService: DataEntryWorkDesignRequestService,
    private athMessageService: NzMessageService,
    private translateService: TranslateService,
    private appService: AppService,
    private isvCustomPackageService: IsvCustomPackageService,
    private userService: AdUserService,
    private modal: AdModalService,
    private cd: ChangeDetectorRef,
    private isPrivatizationService: IsPrivatizationService,
  ) {}

  ngOnInit(): void {
    this.dataEntryWorkDesignService.setSaveLoading(true);
    this.saveAndPublishLoadingChange$ = this.dataEntryWorkDesignService.saveLoadingChange$
      .pipe(takeUntil(this.destroy$))
      .subscribe((isLoading: boolean) => {
        this.saveAndPublishLoadingChange.emit(isLoading);
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('workData')) {
      this.handleInit();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // 初始化
  private async handleInit(): Promise<void> {
    this.dataEntryWorkDesignService.setInitInfoLoading(true);
    try {
      this.dataEntryWorkDesignService.setWorkData(this.workData);
      // 必须先请求自定义组件信息，之后再请求界面数据 进行渲染
      // appType 为 4 是随心控解决方案，随心控解决方案 不需要加载 自定义组件
      if (this.appService.selectedApp.appType !== AppTypes.SCENARIO_KIT) {
        const isvPackageDataList = await this.isvCustomPackageService.getIsvPackageDataList(
          this.appService.selectedApp,
        );
        this.dataEntryWorkDesignService.setIsvPackageDataList(isvPackageDataList);
      }

      const initWorkDesignInfoRes = await this.initWorkDesignInfo();
      this.dataEntryWorkDesignService.setWorkDesignInfo(initWorkDesignInfoRes.data);
      await this.updateSubPageInfoList();
      this.initMenus();
      this.updateRenderInfo();
    } catch (error) {
      this.gobalErrorMessage = error?.error?.errorMessage ?? error?.message ?? 'init error';
      console.log('handleInit error:', error);
    } finally {
      this.dataEntryWorkDesignService.setInitInfoLoading(false);
    }
  }

  // 初始化界面设计数据信息
  initWorkDesignInfo(): Promise<any> {
    const params = {
      code: this.dataEntryWorkDesignService.workData.code,
      type: 'dsl',
    };
    return this.dataEntryWorkDesignRequestService.loadModelPageView(params).toPromise();
  }

  // 更新子页面列表
  async updateSubPageInfoList(needloading = false): Promise<any> {
    needloading && this.dataEntryWorkDesignService.setSaveLoading(true);
    try {
      const getSubPageListRes = await this.getSubPageList();
      if (getSubPageListRes.code === 0)
        this.dataEntryWorkDesignService.setPageInfoList(
          getSubPageListRes?.data?.map((subPage: any) => {
            return {
              name: subPage?.lang?.name?.[this.translateService?.currentLang] ?? subPage.name,
              code: subPage?.code,
              type: PageInfoType.SUB_PAGE,
              data: subPage,
            };
          }),
          PageInfoType.SUB_PAGE,
        );
    } catch (error) {
      console.log('updateSubPageList error:', error);
    } finally {
      needloading && this.dataEntryWorkDesignService.setSaveLoading(false);
    }
  }

  // 初始化菜单
  initMenus(): void {
    const pages = [];
    if (signDocumentConfigCategoryList.includes(this.dataEntryWorkDesignService.workDesignInfo.category)) {
      pages.push({
        name: this.translateService.instant('dj-界面设计'),
        code: MenuCode.BASIC_DATA,
        type: PageInfoType.MENU_PAGE,
      } as MenuPageInfo);
    } else {
      if (this.modelPageType !== 'edit') {
        pages.push({
          name: this.translateService.instant('dj-浏览界面'),
          code: MenuCode.BROWSE_PAGE,
          type: PageInfoType.MENU_PAGE,
        } as MenuPageInfo);
      }

      if (this.modelPageType !== 'browse') {
        pages.push({
          name: this.translateService.instant('dj-编辑界面'),
          code: MenuCode.EDIT_PAGE,
          type: PageInfoType.MENU_PAGE,
        } as MenuPageInfo);
      }
    }
    this.dataEntryWorkDesignService.setPageInfoList(pages, PageInfoType.MENU_PAGE);
    this.dataEntryWorkDesignService.setActivePageInfo(pages[0]);
  }

  // 更新渲染数据
  updateRenderInfo(): void {
    // 获取基础config
    const config = cloneDeep(
      signDocumentConfigCategoryList.includes(this.dataEntryWorkDesignService.workDesignInfo.category)
        ? SIGN_DOCUMENT_CONFIG
        : DOUBLE_DOCUMENT_CONFIG,
    );

    // TODO
    config.formCenterConfig.dataEntryDefaultOptionsCode = this.dataEntryWorkDesignService.activePageInfo?.code as
      | 'design'
      | 'listSetting'
      | 'editPage';

    config.formSetConfig.uploadCategory = this.appService?.selectedApp?.code;
    config.formSetConfig.productName = this.dataEntryWorkDesignService.workDesignInfo?.serviceCode;

    config.formCenterConfig.applyToField = ['design', 'listSetting'].includes(
      this.dataEntryWorkDesignService.activePageInfo?.code,
    )
      ? 'BUTTON_GROUP'
      : 'UIBOT_BUTTON_GROUP';

    // 浏览界面 不展示 规则列表
    if (this.dataEntryWorkDesignService.activePageInfo?.code === MenuCode.BROWSE_PAGE) {
      config.sidebarConfig.ModuleCodeList = config.sidebarConfig.ModuleCodeList.filter(
        (code) => code !== SubModuleEnum.Rule,
      );
    }

    // 模型驱动解决方案展示数据查询视图
    if (this.modelPageType === 'edit') {
      config.formCenterConfig.serachViewDisplay = true;
    }

    // 模型驱动解决方案的浏览界面 允许切换查询方案
    if (this.modelPageType === 'browse') {
      config.sidebarConfig.dataSourceOptions.isShowQueryPlan = true; // 开启查询方案数据源
      config.sidebarConfig.queryPlanDataSourceMode = DataSourceModeEnum.Multiple;
      // this.dataEntryWorkDesignService.updateIndexPageUIElementByMenuCode(MenuCode.BROWSE_PAGE);
    }

    this.dataEntryWorkDesignService.setConfig(config);

    // 初始化 DynamicWorkDesign BussinessInfo
    this.handleInitDynamicWorkDesignBussinessInfo();
  }

  // 获取子页面详情
  getSubPageQueryDetail(code): Promise<any> {
    const { application } = this.dataEntryWorkDesignService.workDesignInfo ?? {};
    const params = {
      application,
      code,
    };
    return this.dataEntryWorkDesignRequestService.subPageQueryDetail(params).toPromise();
  }

  // 查询子页面列表
  getSubPageList(): Promise<any> {
    const { code, application } = this.dataEntryWorkDesignService.workDesignInfo ?? {};
    const params = {
      application: application,
      parentPageCode: code,
    };
    return this.dataEntryWorkDesignRequestService.subPageQueryList(params).toPromise();
  }

  // 切换页面
  async handleTogglePage(pageInfo: PageInfo, isForceRefresh = false): Promise<void> {
    if (this.dataEntryWorkDesignService.activePageInfo === pageInfo && !isForceRefresh) return;

    // TODO
    const dynamicWorkDesignStatus = this.dynamicWorkDesign.handleBussinessShareRefMethod('getStatus');
    if (dynamicWorkDesignStatus !== 'Ready') return;

    if (
      pageInfo.type === PageInfoType.SUB_PAGE &&
      !this.dataEntryWorkDesignService.subPageList.find((item) => item.code === pageInfo.code)
    ) {
      try {
        this.dataEntryWorkDesignService.setSaveLoading(true);
        const subPageRes = await this.getSubPageQueryDetail(pageInfo.code);
        if (subPageRes.code === 0) {
          this.dataEntryWorkDesignService.setSubPage(subPageRes.data);
        }
      } catch (error) {
        console.log('getSubPageQueryDetail error:', error);
      } finally {
        this.dataEntryWorkDesignService.setSaveLoading(false);
      }
    }

    this.dataEntryWorkDesignService.setActivePageInfo(pageInfo);
    this.updateRenderInfo();
  }

  handleChangeCustom(data: any): void {
    this.dataEntryWorkDesignService.setCurrentPageInfoByKeyPath(['isCustomize'], data);
    const currentPageUIElement = this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData();
    this.dataEntryWorkDesignService.currentPageUIElements.forEach((pageUIElement, index) => {
      if (pageUIElement.pageCode === 'browse-page') {
        const { elements, ...otherInfo } = pageUIElement;
        elements?.operations?.forEach((operation) => {
          if (operation.openWindowDefine) operation.openWindowDefine.isCustomize = data;
        });
        this.dataEntryWorkDesignService.setCurrentPageUIElement(otherInfo, { ...otherInfo, elements }, index);
      }
      if (
        !data &&
        pageUIElement.code === currentPageUIElement.code &&
        pageUIElement.activityId === currentPageUIElement.activityId &&
        pageUIElement.pageCode === currentPageUIElement.pageCode
      ) {
        this.dataEntryWorkDesignService.setCurrentPageUIElement(
          currentPageUIElement,
          {
            ...currentPageUIElement,
            elements: {
              ...(currentPageUIElement?.elements || {}),
              submitActions: [], // 定制转标准时，清空SubmitActions
            },
          },
          index,
        );
      }
    });
    this.updateRenderInfo();
  }

  // 保存
  async handleSave(isAfterSaveNeedPublish = false): Promise<void> {
    // TODO 一些校验逻辑需要补充

    // if (
    //   this.dataEntryWorkDesignService.formInstance?.editForm &&
    //   this.dataEntryWorkDesignService.formInstance?.editForm?.initialized &&
    //   !this.dataEntryWorkDesignService.formInstance?.editForm.checkValidity()
    // ) {
    //   this.athMessageService.error(this.translateService.instant('dj-字段配置中存在必填项未填写'));
    //   return;
    // }

    // if (
    //   ['design', 'listSetting', 'editPage'].includes(this.dataEntryWorkDesignService.activeMenu) &&
    //   !this.appDslWorkDesign?.handleValidateSubmit()
    // ) {
    //   this.athMessageService.error(this.translateService.instant('dj-字段配置中存在必填项未填写'));
    //   return;
    // }

    if (this.stateManagement && !this.stateManagement.saveStateManagement()) return;

    this.dataEntryWorkDesignService.setSaveLoading(true);
    await this.pageDesignUpdate();
    await this.subPageBatchUpdate(this.dataEntryWorkDesignService.subPageList);

    if (isAfterSaveNeedPublish) {
      this.publishButtonRef.startPublish();
    } else {
      this.athMessageService.success(this.translateService.instant('dj-保存成功'));
    }

    this.initPageData = this.getInitPageData();
    this.judgeDataChange();

    this.dataEntryWorkDesignService.setSaveLoading(false);
  }

  preCheckIamCondition() {
    const client = 'PC';
    const iamCondition = this.dataEntryWorkDesignService?.workDesignInfo?.iamCondition ?? [];
    const isCustom = this.dataEntryWorkDesignService.isCustom;
    const pageUIElement = this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData();
    const pageCode = pageUIElement?.pageCode;
    if (isCustom) {
      return iamCondition;
    } else {
      const layout = pageUIElement?.elements?.layout;
      const operations = pageUIElement?.elements?.operations;
      const operateLists = getOperationList(layout, operations, false) ?? [];
      const correctionIamCondition = iamCondition.map((condition) => {
        const authKey = condition.key;
        const refComponents = condition.refComponents ?? [];
        const restComponents = refComponents.filter(
          (comp) => !(comp.pageCode === pageCode && comp.platform === client),
        );
        const relateOperation = operateLists
          .filter((operate) => operate.authKey === authKey)
          .map((operation) => {
            return {
              componentId: operation.componentId,
              componentName: operation.componentName,
              lang: operation.lang?.componentName
                ? { componentName: operation.lang?.componentName }
                : {
                    componentName: {
                      zh_CN: operation.componentName,
                      en_US: operation.componentName,
                      zh_TW: operation.componentName,
                    },
                  },
              pageCode,
              platform: client,
              templates: operation.templates ?? [],
              authKey: operation.authKey,
              isCustomize: isCustom,
            };
          });
        return {
          ...condition,
          refComponents: [...restComponents, ...relateOperation],
        };
      });
      return correctionIamCondition;
    }
  }

  async pageDesignUpdate(): Promise<any> {
    // const correctionIamCondition = this.preCheckIamCondition();
    // this.dataEntryWorkDesignService.setWorkDesignInfoByKey('iamCondition', correctionIamCondition);
    const workDesignInfo = cloneDeep(this.dataEntryWorkDesignService.workDesignInfo);
    // 固定false 给后台区分模型设计|界面设计（false）
    workDesignInfo.generateDslAction = true;
    return this.dataEntryWorkDesignRequestService.pageDesignUpdate(workDesignInfo).toPromise();
  }

  // 批量更新子页面
  async subPageBatchUpdate(subPageList: SubPage[]): Promise<any> {
    const params = {
      subPageList,
    };
    return this.dataEntryWorkDesignRequestService.subPageBatchUpdate(params).toPromise();
  }

  // 获取删除子页面
  async subPageDelete(code: string): Promise<any> {
    const { application } = this.dataEntryWorkDesignService.workDesignInfo ?? {};
    const params = {
      application,
      codes: [code],
    };
    return this.dataEntryWorkDesignRequestService.subPageDelete(params).toPromise();
  }

  // 打开代码开窗
  openCodeModal(): void {
    this.codeData = cloneDeep(this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData()?.elements);
    if (!this.codeData) {
      this.athMessageService.error(this.translateService.instant('dj-没有设定数据源'));
      return;
    }
    this.showCodeModal = true;
  }

  handleCodeModal(data: any): void {
    this.showCodeModal = false;
    this.dataEntryWorkDesignService.setCurrentDataSourcePageUIElementData({
      ...this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData(),
      elements: data,
    });
    this.updateRenderInfo();
  }

  openModifyHistoryModal(): void {
    this.historyModalProps.code = this.dataEntryWorkDesignService.workData.code;
    this.historyModalProps.transferModal = true;
  }

  // close时判断页面数据是否变化
  judgeDataChange() {
    if (!!this.initPageData) {
      const {
        pageDsl: sourcePageDsl,
        detailDsl: sourceDetailDsl,
        pageUIElement: sourcePageUIElement,
        subPageList: sourceSubPageList,
      } = this.initPageData;
      const {
        pageDsl: targetPageDsl,
        detailDsl: targetDetailDsl,
        pageUIElement: targetPageUIElement,
        subPageList: targetSubPageList,
      } = this.getInitPageData();

      const isPageDslEqual = isEqual(sourcePageDsl, targetPageDsl);
      const isDetailDslEqual = isEqual(sourceDetailDsl, targetDetailDsl);
      const isPageUIElementEqual =
        sourcePageUIElement.length === targetPageUIElement.length &&
        sourcePageUIElement.every((sourceItem) =>
          isEqual(
            sourceItem,
            targetPageUIElement.find(
              (targetItem) =>
                sourceItem.code === targetItem.code &&
                sourceItem.activityId === targetItem.activityId &&
                sourceItem.pageCode === targetItem.pageCode,
            ),
          ),
        );

      // 用一个循环 作出对比，且记录新增的 子页面数据，后续同步
      // 主要原因是 我们进入 作业设计时 并不会加载所有的子页面 数据
      // 只有 点开子页面才会加载，所以在第一次加载时不做对比，仅记录到init数据中，后续做对比
      const { extraSubPageList, isSubPageEqual } = targetSubPageList.reduce(
        (resData: { extraSubPageList: SubPage[]; isSubPageEqual: boolean }, targetItem: SubPage) => {
          const existsSubPage = sourceSubPageList.find((sourceItem) => sourceItem.code === targetItem.code);
          if (existsSubPage) {
            resData.isSubPageEqual = resData.isSubPageEqual && isEqual(existsSubPage, targetItem);
          } else {
            resData.extraSubPageList.push(targetItem);
          }
          return resData;
        },
        { extraSubPageList: [], isSubPageEqual: true },
      );

      sourceSubPageList.push(...extraSubPageList);
      this.workDataHasChanged = !isPageDslEqual || !isDetailDslEqual || !isPageUIElementEqual || !isSubPageEqual;

      console.log('judgeDataChange:', this.workDataHasChanged);
      this.contentChangeWithoutSaveChange.emit(this.workDataHasChanged);
    }
  }

  // 渲染数据发生变化
  handleChangePage(): void {
    if (!this.initPageData) this.initPageData = this.getInitPageData();
    this.judgeDataChange();
  }

  async handlePreview(): Promise<void> {
    // 子页面暂时无法预览
    if (this.dataEntryWorkDesignService.activePageInfo.type === PageInfoType.SUB_PAGE) return;

    // 保存后打开预览
    await this.handleSave();

    this.previewParam = {
      taskCode: this.dataEntryWorkDesignService.workDesignInfo.code,
      pageCode: this.dataEntryWorkDesignService.activePageInfo?.code,
      actionId: this.dataEntryWorkDesignService?.getCurrentDataSource()?.actionId,
      type: 'performer',
      domain: 'DataEntry',
      activityType: EActivitiyType.BASE_DATA,
      category: this.workData.defaultCategory,
    };
    this.previewVisible = true;
  }

  // 处理了用户点击了发布
  handleClickPublicAction(): void {
    this.handleSave(true);
  }

  // 获取用于对比数据变更的初始化作业数据
  getInitPageData() {
    return {
      pageDsl: cloneDeep(this.dataEntryWorkDesignService.workDesignInfo['pageDsl'] ?? {}),
      detailDsl: cloneDeep(this.dataEntryWorkDesignService.workDesignInfo['detailDsl'] ?? {}),
      pageUIElement: cloneDeep(this.dataEntryWorkDesignService.workDesignInfo['pageUIElement'] ?? []),
      subPageList: cloneDeep(this.dataEntryWorkDesignService.subPageList ?? []),
    };
  }

  handleInitDynamicWorkDesignBussinessInfo() {
    console.time('DynamicWorkDesign All start');
    // this.dataEntryWorkDesignService.setSaveLoading(true);
    let renderExtendedFields = {};

    if (
      this.dataEntryWorkDesignService.activePageInfo?.type === PageInfoType.MENU_PAGE &&
      this.dataEntryWorkDesignService.activePageInfo?.code !== MenuCode.EDIT_PAGE
    ) {
      renderExtendedFields = cloneDeep(this.dataEntryWorkDesignService.workDesignInfo.extendedFields ?? {});
    }

    if (this.dataEntryWorkDesignService.activePageInfo?.type === PageInfoType.SUB_PAGE) {
      renderExtendedFields = cloneDeep(this.dataEntryWorkDesignService.page.extendedFields ?? {});
    }

    const pageCode = getPageCode(this.dataEntryWorkDesignService.activePageInfo);

    const pageUIElementContent = cloneDeep(
      this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData()?.elements,
    );

    const {
      code,
      category,
      application,
      iamCondition = [],
      name,
      businessCode,
    } = this.dataEntryWorkDesignService.workDesignInfo ?? {};

    const dataSources = this.dataEntryWorkDesignService?.dataSources;
    const dataSourceNames =
      this.dataEntryWorkDesignService?.page?.dataSourceNames?.filter((item) => dataSources[item]) ?? [];

    this.dynamicWorkDesignBusinessShareInfo = {
      componentType: 'DynamicWorkDesign', // 组件类型
      componentProps: {
        dynamicWorkDesignInfo: {
          code:
            this.dataEntryWorkDesignService.activePageInfo.type === PageInfoType.SUB_PAGE
              ? this.dataEntryWorkDesignService.activePageInfo?.code
              : code,
          name:
            this.dataEntryWorkDesignService.activePageInfo.type === PageInfoType.SUB_PAGE
              ? this.dataEntryWorkDesignService.page?.name
              : name,
          category,
          applicationCode: application,
          pageCode: pageCode,
          parentPageCode: this.dataEntryWorkDesignService?.page?.parentPageCode,
          businessCode:
            this.dataEntryWorkDesignService.activePageInfo.type === PageInfoType.SUB_PAGE ? '' : businessCode,
          appType: this.appService?.selectedApp?.appType,
          config: this.dataEntryWorkDesignService.config,
          dynamicWorkDesignConfig: {
            commonConfig: {
              isCustomize: this.dataEntryWorkDesignService.isCustom,
              customTip: `${this.translateService.instant('dj-当前为定制界面，定制组件的类型为：')} ${
                this.dataEntryWorkDesignService.customTip
              }`,
              customType: this.dataEntryWorkDesignService.customTip,
              customButtonTip: this.translateService.instant('dj-打开源码'),
            },
            businessConfig: {
              masterFromDataSourceName: !!this.dataEntryWorkDesignService?.workDesignInfo?.masterFromDataSourceName,
              ruleTemplateManagerUrl: `${location.origin}/template-center/rule-template-manage`,
              productName: this.dataEntryWorkDesignService.workDesignInfo?.serviceCode || '',
              isOpenWindowShowQueryPlan: this.modelPageType !== 'notModelDriven',
              modelPageType: this.modelPageType,
              isPrivatization: this.isPrivatizationService.isPrivatization,
            },
          },
          isvPackageDataList: this.dataEntryWorkDesignService.isvPackageDataList,
        },
        dynamicWorkDesignRenderData: {
          dataSources,
          dataSourceNames,
          extendedFields: renderExtendedFields,
          pageUIElementContent,
          iamCondition,
        },
        changeDynamicWorkDesignRenderData: (data) => {
          console.timeEnd('DynamicWorkDesign All start');
          console.log('changeDynamicWorkDesignRenderData:', data);
          const { pageUIElementContent, dataSourceNames, dataSources, iamCondition, groupSchemaList = [] } = data;

          this.dataEntryWorkDesignService.setCurrentPageInfoByKeyPath(['dataSourceNames'], dataSourceNames);
          this.dataEntryWorkDesignService.setCurrentPageInfoByKeyPath(['dataSources'], dataSources);
          this.dataEntryWorkDesignService.setWorkDesignInfoByKey('iamCondition', iamCondition);
          this.dataEntryWorkDesignService.setCurrentPageInfoByKeyPath(['groupSchemaList'], groupSchemaList);

          // TODO 继续完成
          const currentDataSource = this.dataEntryWorkDesignService.getCurrentDataSource();
          // 因为现在不存在 多 tab的 逻辑了，所以currentPageUIElements仅需维护当前最新的这条数据即可
          // 置空当前页面的pageUIElements
          this.dataEntryWorkDesignService.setCurrentPageUIElements([]);
          if (currentDataSource && pageUIElementContent) {
            const pageUIElementSearchInfo =
              this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementSearchInfoByDataSource(
                currentDataSource,
              );
            const currentPageUIElement = {
              ...pageUIElementSearchInfo,
              elements: pageUIElementContent,
            };
            this.dataEntryWorkDesignService.setCurrentPageUIElements([currentPageUIElement]);
          }

          // this.dataEntryWorkDesignService.setCurrentDataSourcePageUIElementData({
          //   ...this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData(),
          //   elements: pageUIElementContent,
          // });
          // this.dataEntryWorkDesignService.setSaveLoading(false);
          // this.cd.detectChanges();

          // 如果是模型驱动2.0 的 浏览界面，才会更新索引页面，当然如果当前的数据源不是查询方案数据源，索引页面也是空的
          // if (this.modelPageType === 'browse') {
          //   this.dataEntryWorkDesignService.updateIndexPageUIElementByMenuCode(MenuCode.BROWSE_PAGE);
          // }

          this.handleChangePage();
        },
        changeDynamicWorkDesignStatus: (data) => {
          console.log('changeDynamicWorkDesignStatus:', data);
          this.dataEntryWorkDesignService.setSaveLoading(data !== 'Ready');
          this.cd.detectChanges();
        },
        // editOpenWindow: (data) => {
        //   console.log('editOpenWindow:', data);
        //   this.handleOpenWindow(data);
        // },
      },
    };
  }

  handleAddSubPage() {
    console.log('添加子页面');
    this.isPageInfoModalVisible = true;
    this.pageEditInfo = {
      type: 'add',
      data: null,
    };
  }

  handleDeleteSubPage(code: string) {
    this.modal.confirm({
      nzTitle: this.translateService.instant('dj-是否确认删除？'),
      nzOkText: this.translateService.instant('dj-确定'),
      nzOnOk: async () => {
        console.log('删除子页面', code);
        this.dataEntryWorkDesignService.setSaveLoading(true);
        await this.subPageDelete(code);
        await this.updateSubPageInfoList();
        this.handleTogglePage(this.dataEntryWorkDesignService?.menuPageInfoList[0]);
        this.dataEntryWorkDesignService.setSaveLoading(false);
      },
      nzCancelText: this.translateService.instant('dj-取消'),
      nzOnCancel: () => {},
    });
  }

  handleEditCurrentPage() {
    if (this.isEditPage || this.isBasicDataPage) {
      this.pageEditInfo = {
        type: 'editIsCustom',
        data: {
          isCustom: this.dataEntryWorkDesignService?.isCustom,
        },
      };
    }

    if (this.isSubPage) {
      const { code, name, lang } = this.dataEntryWorkDesignService?.page;
      this.pageEditInfo = {
        type: 'editSubPage',
        data: {
          code,
          name,
          lang,
        },
      };
    }
    if (this.isBrowsePage) {
      this.pageEditInfo = {
        type: 'editPermissionOnly',
        data: {},
      };
    }

    this.isPageInfoModalVisible = true;
  }

  addSubPage(pageData): Promise<any> {
    const { code, application } = this.dataEntryWorkDesignService.workDesignInfo ?? {};
    const params = {
      application: application,
      parentPageCode: code,
      comeFrom: 'DATA_ENTRY', // 固定
      ...pageData,
      name: pageData?.lang?.['name']?.[this.translateService?.currentLang] ?? '',
    };
    return this.dataEntryWorkDesignRequestService.subPageAdd(params).toPromise();
  }

  editSubPage(pageData): Promise<any> {
    const params = {
      ...pageData,
    };
    return this.dataEntryWorkDesignRequestService.subPageUpdateBaseInfo(params).toPromise();
  }

  async handlePageInfoModalSubmit(pageEditInfo: PageEditInfo) {
    try {
      this.dataEntryWorkDesignService.setSaveLoading(true);
      const { type, data } = pageEditInfo;
      switch (type) {
        case 'add':
          await this.addSubPage(data);
          this.athMessageService.success(this.translateService.instant('dj-添加成功'));
          await this.updateSubPageInfoList();
          this.handleTogglePage(
            this.dataEntryWorkDesignService?.subPageInfoList[
              this.dataEntryWorkDesignService?.subPageInfoList?.length - 1
            ],
          );
          break;
        case 'editSubPage':
          const { name, lang } = data;
          this.dataEntryWorkDesignService.setCurrentPageInfoByKeyPath(['name'], name);
          this.dataEntryWorkDesignService.setCurrentPageInfoByKeyPath(['lang'], lang);
          this.athMessageService.success(this.translateService.instant('dj-操作成功'));
          break;
        case 'editIsCustom':
          const { isCustom } = data;
          this.handleChangeCustom(isCustom);
          this.athMessageService.success(this.translateService.instant('dj-操作成功'));
          break;
        case 'editPermissionOnly':
          this.updateRenderInfo();
          this.athMessageService.success(this.translateService.instant('dj-操作成功'));
          break;
        default:
          break;
      }
    } catch (error) {
      console.log('handleConfirm error:', error);
    } finally {
      this.handlePageInfoModalClose();
      this.dataEntryWorkDesignService.setSaveLoading(false);
    }
  }

  handlePageInfoModalClose() {
    this.isPageInfoModalVisible = false;
    this.pageEditInfo = null;
  }

  // 模型驱动2.0的浏览界面，且数据源是查询方案，可以配置查询方案
  // handleCurrentPageQueryPlan() {
  //   const indexPageUIElement = this.dataEntryWorkDesignService.getIndexPageUIElementByMenuCode(MenuCode.BROWSE_PAGE);
  //   const queryPlanComponent = indexPageUIElement?.elements?.layout?.find((item) => item.type === 'DATA_QUERY');
  //   if (!queryPlanComponent) return;
  //   this.queryPlanEditInfo = {
  //     ...queryPlanComponent,
  //   };
  //   this.isQueryPlanModalVisible = true;
  // }

  // async handleQueryPlanModalSubmit(queryPlanEditInfo: QueryPlanEditInfo) {
  //   const indexPageUIElement = this.dataEntryWorkDesignService.getIndexPageUIElementByMenuCode(MenuCode.BROWSE_PAGE);
  //   if (!indexPageUIElement) return;
  //   indexPageUIElement.elements.layout = [
  //     queryPlanEditInfo,
  //     ...indexPageUIElement?.elements?.layout?.filter((item) => item.type !== 'DATA_QUERY'),
  //   ];
  //   this.dataEntryWorkDesignService.updateIndexPageUIElementByMenuCode(MenuCode.BROWSE_PAGE, indexPageUIElement);
  //   this.updateRenderInfo();
  //   this.handleQueryPlanModalClose();
  // }

  // handleQueryPlanModalClose() {
  //   this.isQueryPlanModalVisible = false;
  //   this.queryPlanEditInfo = null;
  // }

  handlePageSelectVisibleChange(value) {
    this.isPageSelectVisible = value;
  }

  // handleOpenWindow(data) {
  //   this.openwindowVisible = true;
  //   this.openwindowData = data;
  //   this.cd.detectChanges();
  // }

  // handleCloseOpenwindow() {
  //   this.openwindowVisible = false;
  //   this.openwindowData = undefined;
  // }

  // handleSaveOpenwindow(data) {
  //   // 保证roleAttention被删除，也就是升级到 自定义开窗成功
  //   delete data?.openWindowDefine?.roleAttention;

  //   const currentPageUIElement = this.dataEntryWorkDesignService.getCurrentDataSourcePageUIElementData();
  //   const operations = currentPageUIElement?.elements?.operations;
  //   const findIndex = operations.findIndex((item) => item.id === data.id);
  //   operations.splice(findIndex, findIndex >= 0 ? 1 : 0, data);
  //   this.dataEntryWorkDesignService.setCurrentDataSourcePageUIElementData({
  //     ...currentPageUIElement,
  //   });
  //   this.updateRenderInfo();

  //   this.handleCloseOpenwindow();
  // }

  // 打开状态管理开窗
  openStateModal(): void {
    this.showStateModal = true;
  }

  handleStateModalOk(data: any): void {
    this.showStateModal = false;
    this.dataEntryWorkDesignService.setWorkDesignInfoByKeyPath(['extendedFields'], data);
    this.updateRenderInfo();
    // console.log('handleStateModal:', data);
  }

  getUpdateSubpageModalPageInfo(subPageInfo: SubPageInfo): UpdateSubpageModalPageInfo {
    const { code: targetCode, name: targetName, lang: targetLang } = subPageInfo.data;
    return {
      code: targetCode,
      name: targetName,
      lang: targetLang,
    };
  }

  // 同步子页面
  handleUpdateSubPage(pageCode): void {
    const targetSubPageInfo = this.dataEntryWorkDesignService.subPageInfoList.find(
      (item) => item.code === pageCode,
    ) as SubPageInfo;

    this.updateSubpageModalPageInfo = this.getUpdateSubpageModalPageInfo(targetSubPageInfo);

    this.updateSubpageModalPageInfoList = (this.dataEntryWorkDesignService.subPageInfoList as SubPageInfo[])
      .filter((item: SubPageInfo) => item.code !== pageCode)
      .map((item: SubPageInfo) => this.getUpdateSubpageModalPageInfo(item));

    this.isUpdateSubpageModalVisible = true;
  }

  // 同步子页面
  handleSubpageBatchSync(sourcePage: UpdatePageInfo, targetPages: UpdatePageInfo[]): Promise<any> {
    const { application } = this.dataEntryWorkDesignService.workDesignInfo ?? {};
    const params = {
      application: application,
      sourcePage,
      targetPages,
    };
    return this.dataEntryWorkDesignRequestService.subpageBatchSync(params).toPromise();
  }

  async handleUpdateSubpageModalSubmit(updateSubpageModalSubmitData: UpdateSubpageModalSubmitData) {
    this.isUpdateSubpageModalVisible = false;
    const { sourcePage, targetPages } = updateSubpageModalSubmitData;

    this.dataEntryWorkDesignService.setSaveLoading(true);
    try {
      await this.handleSave();
      const handleSubpageBatchSyncRes = await this.handleSubpageBatchSync(sourcePage, targetPages);
      if (handleSubpageBatchSyncRes.code === 0) {
        this.athMessageService.success(this.translateService.instant('dj-操作成功'));
      }
    } catch (error) {
      console.log('subpageBatchSync error:', error);
    } finally {
      this.dataEntryWorkDesignService.setSaveLoading(false);
    }

    this.dataEntryWorkDesignService.resetSubPageList();
    this.handleTogglePage(this.dataEntryWorkDesignService.activePageInfo, true);
  }

  getIsMainPage(page: PageInfo) {
    return (
      page.type === PageInfoType.MENU_PAGE && (page.code === MenuCode.BASIC_DATA || page.code === MenuCode.BROWSE_PAGE)
    );
  }
}
