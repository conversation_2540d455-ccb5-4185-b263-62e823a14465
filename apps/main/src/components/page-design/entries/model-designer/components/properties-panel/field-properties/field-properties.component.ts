/**
 * 字段属性
 */
import { Component, Input, OnChanges, OnDestroy, OnInit, SimpleChanges } from '@angular/core';
import { ModelDesignerService } from '../../../service/model-designer.service';
import { StoreService } from '../../../service/store.service';
import { fieldTypeDefaultValue, fieldTypes } from '../../../utils/model-uitil';
import { TranslateService } from '@ngx-translate/core';
import { AbstractControl, FormGroup, ValidationErrors, Validators } from '@angular/forms';
import { debounceTime, distinctUntilChanged, filter } from 'rxjs/operators';
import { FieldTypes } from '../../../types/types';
import { LocaleService } from 'common/service/locale.service';
import { validatorForm } from 'common/utils/core.utils';
import { isEmpty, set } from 'lodash';
import { ModelValidators } from '../../../utils/model-validators';
import { Subject } from 'rxjs';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';

@Component({
  selector: 'model-designer-field-properties',
  templateUrl: './field-properties.component.html',
  styleUrls: ['./field-properties.component.less'],
})
export class FieldPropertiesComponent implements OnInit, OnChanges, OnDestroy {
  @Input() fieldUUID;

  isEmpty = isEmpty;

  form: FormGroup;
  // 表单定义的默认值
  formDefinedData: any;
  formSubscribe$: any;
  model: any;
  currentField: any;
  currentFieldPath: string;

  // 字段数据类型
  fieldTypes = Object.keys(fieldTypes).map((item) => {
    if (item === 'FILE') {
      return { label: this.translate.instant('dj-附件'), value: item };
    } else if (item === 'MULTIPLE') {
      return { label: this.translate.instant('dj-多选项'), value: item };
    }
    return { label: item, value: item };
  });
  fieldIdAutoTips: Record<string, Record<string, string>> = {
    default: {
      fieldIdValidate: this.translate.instant('dj-格式错误'),
      fieldIdRepeat: this.translate.instant('dj-字段名重复'),
      required: this.translate.instant('dj-必填'),
      maxSize: this.translate.instant('dj-长度超出范围'),
      maxScale: this.translate.instant('dj-精度超出范围'),
      isMysqlKeywords: this.translate.instant('dj-isMysqlKeywords'),
      positiveNumber: this.translate.instant('dj-格式错误'),
    },
  };
  associationModalVisible: boolean = false;

  currentLang: string;
  // 1：null 2：空字符串 3：自定义
  defaultValueState: number;
  fieldType$: any;
  flag: boolean;

  dropDownVocabularyVisble: boolean = false;
  dataElementVisble: boolean = false;
  representClassVisble: boolean = false;

  // 选项编辑
  optionIndex: number = -1;
  optionData: any;
  optionVisible: boolean = false;
  options: any[] = [];

  get state() {
    return this.store.state;
  }

  get getAssociatedQueryFields() {
    const { associatedFields, tableName } = this.currentField.associatedInfo || {};

    if (associatedFields?.length) {
      return associatedFields.map((item) => tableName + '.' + item.alias?.fieldId).join(',');
    }
    return tableName;
  }

  get getAssociatedFields() {
    const { joinField, tableName } = this.currentField.associatedInfo || {};

    if (joinField?.fieldId) {
      return tableName + '.' + joinField.fieldId;
    }
    return tableName;
  }

  constructor(
    private service: ModelDesignerService,
    private store: StoreService,
    private translate: TranslateService,
    private languageService: LocaleService,
  ) {
    this.currentLang = this.languageService.currentLanguage || 'zh_CN';
    // 初始化form结构
    this.form = this.service.createFieldPropertiesForm();
    this.formDefinedData = this.service.createFieldPropertiesForm().getRawValue();

    /* 订阅 字段类型、Size、Scale valueChanges */
    this.hanldeChangeFieldTypeAndSizeAndScale();
    this.subscribeFormChange();
    // 监听字段数据类型变化,改变数据类型对应的长度、精度的必填和禁用
    this.fieldType$ = this.form
      .get('fieldType')
      .valueChanges.pipe(filter(() => !this.flag))
      .subscribe((value) => {
        const type = this.form.get('type').value;
        //  根据数据类型设置默认值
        this.setDefaultValue(value);
        // 根据数据类型 对应的长度、精度的必填和禁用
        this.service.controlSizeScaleByFieldType(value, this.form, type);
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.fieldUUID.currentValue) {
      this.formProtection(() => this.form?.reset(this.formDefinedData));
      this.init();
    }
  }

  ngOnInit() {}

  ngOnDestroy(): void {
    this.unsubscribe();
  }

  unsubscribe() {
    this.formSubscribe$?.unsubscribe();
    this.fieldType$?.unsubscribe();
  }

  init() {
    // 初始化initFormContent
    this.initFormContent();
    // 重置表单disable
    this.resetDisabled();
    //  根据数据类型设置默认值
    this.setDefaultValue(this.currentField?.fieldType);
    // form赋初始化值
    this.form.patchValue(this.currentField, { emitEvent: false });
    const { dictionaryContent } = this.currentField;
    this.options = JSON.parse(dictionaryContent || '[]');
    // 默认值初始状态
    this.defaultValueStatus();
    // 根据字段类型动态disable属性
    this.service.disablePropsByFieldType(this.currentField, this.form, this.model?.name);
    // 根据数据类型 对应的长度、精度的必填和禁用
    this.currentField?.fieldType &&
      this.service.controlSizeScaleByFieldType(this.currentField.fieldType, this.form, this.currentField.type);

    // 验证表单
    if (!this.currentField?._broken) {
      this.currentFieldValid();
    }
    if (this.currentField?._broken) {
      // 删除_broken
      const fieldObj =
        this.service.modelList
          .find((m) => m.name === this.model.name)
          .fields.find((f) => f._uuid === this.currentField._uuid) || {};
      Reflect.deleteProperty(fieldObj, '_broken');
      this.service.updateModelReference(this.model.name);
    }
  }

  subscribeFormChange() {
    this.formSubscribe$ = this.form.valueChanges
      .pipe(
        filter(() => !this.flag),
        debounceTime(200),
        distinctUntilChanged(),
      )
      .subscribe((data) => {
        // 获取当前被选中的字段
        const { field, path } =
          this.service.findFieldByModel(this.state.currentTabName, this.currentField?._uuid, {
            returnPath: true,
          }) || {};
        this.currentFieldPath = path;

        const currentModel = this.service.modelList.find((m) => m.name === this.state.currentTabName);
        const currentModelIndex = this.service.modelList.findIndex((m) => m.name === this.state.currentTabName);
        // 更新模型中字段数据
        set(currentModel, this.currentFieldPath, this.form.getRawValue());
        // 更新引用
        this.service.modelList[currentModelIndex] = { ...this.service.modelList[currentModelIndex] };
        this.currentFieldValid();
      });
  }

  initFormContent() {
    const fieldControl = this.form.get('fieldId');
    // 获取当前模型
    this.model = this.service.modelList.find((m) => m.name === this.state.currentTabName);
    // 获取当前被选中的字段
    const { field, path } =
      this.service.findFieldByModel(this.state.currentTabName, this.fieldUUID, {
        returnPath: true,
      }) || {};
    this.currentField = field;

    this.currentFieldPath = path;
    // 添加字段重复验证
    fieldControl.clearValidators();
    fieldControl.setValidators([
      ModelValidators.trim,
      Validators.required,
      ModelValidators.fieldId,
      this.fieldIdValidator.bind(this),
    ]);
    fieldControl.updateValueAndValidity({ emitEvent: false });
  }

  /**
   * 重置表单disabled
   */
  resetDisabled() {
    const initDisableKeys = ['scale', 'autoIncrement', 'dictionaryContent'];

    for (const key in this.form.controls) {
      if (initDisableKeys.includes(key)) continue;
      const control = this.form.controls[key];
      control.enable({ emitEvent: false });
    }
  }

  /**
   * 验证字段名重复
   * @param control
   * @returns
   */
  fieldIdValidator(control: AbstractControl): ValidationErrors | null {
    const fields = this.model?.fields?.filter((f) => f._uuid !== this.currentField._uuid) || [];
    if (fields.find((f) => f.fieldId === control.value)) {
      return { fieldIdRepeat: true };
    }
    return null;
  }

  currentFieldValid() {
    if (!this.currentField || this.currentField.isSystem) return;
    // 为了验证字段是否有重复，验证前先把禁用的fieldId恢复可验证状态，验证完成后再disable
    const isFieldIdDisabled = this.form.get('fieldId').disabled;
    if (isFieldIdDisabled) {
      this.form.get('fieldId').enable({ emitEvent: false });
    }

    this.store.setState((state) => {
      state.fieldFormsValid[this.model.name][this.currentField._uuid] = this.form.valid;
    });
    validatorForm(this.form);
    this.service.updateModelReference(this.model.name);

    // 验证完成后再disable
    if (isFieldIdDisabled) {
      this.form.get('fieldId').disable({ emitEvent: false });
    }

    // bug修改，重复字段场景，当前字段修改后，其他字段状态未更新
    const fields = this.model?.fields?.filter((f) => f._uuid !== this.currentField._uuid) || [];
    fields
      .filter((f) => !f.isSystem)
      .forEach((fieldData) => {
        const form = this.service.createFieldPropertiesForm();
        // 过滤第一个
        // if (i === 0) return;
        const t = setTimeout(() => {
          // 子表组件渲染完成后验证子表字段
          clearTimeout(t);
          if (fieldData.type !== 'GROUP') {
            const valid = this.service.checkFieldValid(fieldData, form);
            this.store.setState((state) => {
              state.fieldFormsValid[this.model.name][fieldData._uuid] = valid;
            });
          }
        }, 0);
      });
  }

  /**
   * 多语言控件回调
   * @param currentFieldGroup
   * @param key
   * @param data
   * @param lang
   */
  handlePatchFieldNameLang(currentFieldGroup, key: any, data: any, lang: any): void {
    currentFieldGroup.patchValue({
      [key]: data?.value,
      lang: {
        ...(lang || {}),
        fieldName: data.lang.value,
      },
    });
  }

  /**
   * 监听字段数据类型变化,改变数据类型对应的长度、精度的必填和禁用
   * @param fieldType
   */
  fieldTypeChange(fieldType) {}

  /**
   * 根据数据类型设置长度、精度默认值
   * @param dataType
   */
  setDefaultValue(dataType) {
    if (fieldTypeDefaultValue[dataType]) {
      Object.keys(fieldTypeDefaultValue[dataType]).forEach((name) =>
        this.form.get(name).setValue(fieldTypeDefaultValue[dataType][name]),
      );
    } else {
      ['size', 'scale'].forEach((name) => this.form.get(name).setValue(null));
    }
  }

  transformFieldName(data) {
    const { descriptionZhCn, descriptionZhTw, descriptionEnUs } = data || {};
    const _currentLanguage = this.languageService.currentLanguage || 'zh_CN';
    let text = descriptionZhCn;
    switch (_currentLanguage) {
      case 'zh_CN':
        text = descriptionZhCn;
        break;
      case 'zh_TW':
        text = descriptionZhTw;
        break;
      case 'en_US':
        text = descriptionEnUs;
        break;
    }
    return text;
  }

  /**
   * 打开关联表开窗
   */
  showAssociatedModal(type): void {
    if (type === FieldTypes.QUOTE_QUERY) {
      this.store.setState({ associationModalVisible: true });
      return;
    }
    if (type === FieldTypes.QUOTE) {
      this.store.setState({ msAssociationModalVisible: true });
      return;
    }
  }

  /**
   * 解决Angular12 bug：在onChanges中改变disable状态，模板不同步
   * @param controlName
   * @returns
   */
  disabled(controlName) {
    return this.form.get(controlName).disabled;
  }

  /**
   * 默认值状态
   * @returns
   */
  defaultValueStatus() {
    const defaultValue = this.form.get('defaultValue')?.value;
    if (defaultValue === null) {
      this.defaultValueState = 1;
      return;
    }
    if (defaultValue === '') {
      this.defaultValueState = 2;
      return;
    }
    this.defaultValueState = 3;
    return;
  }

  /**
   * 默认值change
   * @param value
   */
  handleChangeDefaultType(value) {
    this.defaultValueState = value;
    if (value === 1) {
      this.form.patchValue({ defaultValue: null });
    } else if (value === 2) {
      this.form.patchValue({ defaultValue: '' });
    }
  }

  isRequired(name) {
    return this.form.get(name).hasValidator(Validators.required);
  }

  /**
   * form保护，用于form状态改变时不触发valueChanges订阅。
   * 例如：form.reset() 会重置表单，包括验证效果，但会触发valueChanges订阅，使用此方法可避免订阅触发
   * 注意：flag=false通过 setTimeout，如果同步调用formProtection可能会出现不可预期的问题
   * @param callback
   */
  formProtection(callback) {
    this.flag = true;
    try {
      callback && callback();
    } finally {
      const t = setTimeout(() => {
        clearTimeout(t);
        this.flag = false;
      }, 0);
    }
  }

  handleClearDictionaryKey() {
    this.form.patchValue({
      dictionaryId: '',
      enumValue: '',
      dictionaryContent: '',
    });
    // this.options = [];
  }

  handleDropDownVocabulary(params) {
    console.log(params);
    this.dropDownVocabularyVisble = false;
    this.form.patchValue({
      dictionaryId: params.selectedRows[0].id,
      enumValue: params.selectedRows[0].key,
      dictionaryContent: JSON.stringify(params.selectedRows[0]?.values) || '',
    });
    this.options = params.selectedRows[0]?.values;
  }

  handleDataElement(params) {
    this.dataElementVisble = false;
    const fieldData = this.vocabularyDictionaryToField(params.selectedRows[0]);
    this.form.patchValue({
      ...fieldData,
    });
    this.options = this.options = JSON.parse(fieldData.dictionaryContent || '[]');
  }

  handleClearDataElement(): void {
    this.form.patchValue({
      fieldId: '',
    });
  }

  /**
   * 辞汇字典（字段）数据转字段数据
   * @param data
   */
  vocabularyDictionaryToField(data) {
    const { descriptionZhCn, descriptionZhTw, descriptionEnUs } = data || {};
    const description = {
      zh_CN: descriptionZhCn,
      zh_TW: descriptionZhTw,
      en_US: descriptionEnUs,
    };
    const currentLanguage = this.languageService.currentLanguage || 'zh_CN';
    if (data) {
      return {
        fieldId: data?.dataName,
        fieldName: description[currentLanguage],
        businessCode: data?.bizCode,
        fieldType: data?.dataType,
        lang: {
          fieldName: description,
        },
        size: data?.size,
        scale: data?.fieldPrecision,
        enumValue: data?.dictionaryKey,
        dictionaryContent: data?.dictionaryValues,
        wordDictionaryId: data?.id, // 数据元id
        businessTypeId: data?.relateBusinessTypeId, // 表示类id
        dictionaryId: data?.relateDictionaryId, // 字典id
      };
    }
  }

  handleClearRepresentClass() {
    this.form.patchValue({
      businessCode: null,
      fieldType: null,
      size: null,
      scale: null,
      businessTypeId: null,
    });
  }

  handleRepresentClass(params) {
    this.representClassVisble = false;
    const fieldData = this.service.transformBusinessTypeData(params.selectedRows[0]);
    this.formProtection(() => {
      this.form.patchValue({
        ...fieldData,
      });
    });
  }

  /**
   * 拖拽
   * @param event
   */
  drop(event: CdkDragDrop<string[]>): void {
    moveItemInArray(this.options, event.previousIndex, event.currentIndex);
    this.form.patchValue({
      dictionaryId: null,
      enumValue: '',
      dictionaryContent: JSON.stringify(this.options) || '',
    });
  }

  /**
   * 编辑选项
   * @param index
   * @param option
   */
  handleEdit(index: number, option: any) {
    this.optionIndex = index;
    this.optionData = option;
    this.optionVisible = true;
  }

  /**
   * 删除选项
   * @param index
   * @param option
   */
  handleDelete(index: number, option: any) {
    this.options.splice(index, 1);
    this.form.patchValue({
      dictionaryId: null,
      enumValue: '',
      dictionaryContent: JSON.stringify(this.options) || '',
    });
  }

  /**
   * 添加选项
   */
  handleAdd(): void {
    this.optionIndex = -1;
    this.optionData = {};
    this.optionVisible = true;
  }

  /**
   * 修改显示值
   * @param key
   * @param data
   */
  handlePatchOptionLang(key: any, data: any): void {
    if (data?.needLang) {
      this.optionData.lang = {
        ...(this.optionData.lang || {}),
        [key]: data.lang?.value,
      };
    }
    this.optionData['value'] = data?.value;
  }

  /**
   * 选项值变更
   * @param data
   */
  handleChangeOptionValue(data: any): void {
    this.optionData.code = data?.value;
  }

  /**
   * 获取选项类型
   * @param value
   * @returns
   */
  handleGetOptionValueType(value: any): string {
    if (['string', 'number', 'boolean'].includes(typeof value)) {
      return typeof value;
    }
    return 'string';
  }

  /**
   * 取消编辑选项
   */
  handleOptionCancel(): void {
    this.optionIndex = -1;
    this.optionData = {};
    this.optionVisible = false;
  }

  /**
   * 确认编辑选择
   */
  handleOptionOk(): void {
    const structOptionData = {
      code: this.optionData.code,
      value: this.optionData.value,
      lang: {
        value: this.optionData.lang?.value,
      },
      sort: this.optionData.sort ?? 0,
    };
    if (this.optionIndex > -1) {
      this.options[this.optionIndex] = structOptionData;
    } else {
      this.options.push(structOptionData);
    }
    this.handleOptionCancel();
    this.form.patchValue({
      dictionaryId: null,
      enumValue: '',
      dictionaryContent: JSON.stringify(this.options) || '',
    });
  }

  /**
   * 监听字段类型、长度、精度变化
   */
  private hanldeChangeFieldTypeAndSizeAndScale() {
    const keys = ['fieldType', 'size', 'scale'];
    keys.forEach((key) => {
      this.form
        .get(key)
        .valueChanges.pipe(filter(() => !this.flag))
        .subscribe(() => {
          this.formProtection(() => {
            if (this.form.get('businessCode').value) {
              this.form.get('businessCode').setValue(undefined);
            }
            if (this.form.get('businessTypeId').value) {
              this.form.get('businessTypeId').setValue(undefined);
            }
          });
        });
    });
  }
}
