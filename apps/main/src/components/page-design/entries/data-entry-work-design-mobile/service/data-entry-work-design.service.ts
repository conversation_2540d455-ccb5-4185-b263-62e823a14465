import { Injectable } from '@angular/core';
import { WorkData, WorkDesignInfo } from '../config/data-entry-work-design.type';
import { MenuCode } from '../config/data-entry-work-design.type';
import { WorkDesignConfig } from 'components/page-design/components/dsl-work-design/shared/interface/dsl-work-design.interface';
import { Observable, Subject } from 'rxjs';
import {
  PageUIElement,
  PageUIElementSearchInfo,
} from '../../data-entry-work-design/config/data-entry-work-design.type';
import { getDefaultDataSourceName } from '../utils/tools';
import { signDocumentConfigCategoryList } from '../../data-entry-work-design/config/data-entry-work-design.config';
import { DataSourceModeEnum } from '../../../components/dsl-work-design/shared/config/dsl-work-sidebar.config';
import { getMasterFromDataSourceName } from 'components/page-design/components/dsl-work-design-mobile/shared/utils/dsl-work-design.component.util';
import { cloneDeep } from 'lodash';

@Injectable()
export class DataEntryWorkDesignService {
  get operateCustomTemplateRelates(): any[] {
    return this.page?.operateCustomTemplateRelates || [];
    // return this.getCurrentPageUIElement()?.operateCustomTemplateRelates || [];
  }
  private _config: WorkDesignConfig = null; // 配置项
  get config() {
    return this._config;
  }

  private _workData: WorkData = null; // 完整的数据录入界面设计基础数据
  get workData() {
    return this._workData;
  }

  private _workDesignInfo: WorkDesignInfo = null; // 完整的数据录入界面设计完整数据
  get workDesignInfo() {
    return this._workDesignInfo;
  }
  masterFromDataSourceName: boolean;
  get pageType() {
    return this._activeMenu === MenuCode.editPage ? 'detailDslMobile' : 'pageDslMobile';
  }
  get pcPageType() {
    return this._activeMenu === MenuCode.editPage ? 'detailDsl' : 'pageDsl';
  }
  private _dataSourceName = '';
  // 当前的数据源名称
  get dataSourceName(): string {
    return this._dataSourceName;
  } // 数据源

  // 现在的 dataSourceNames 和 pageUIElementData 是对应的
  // 而dataSourceName，代表当前选择的数据源，但仅仅是前端维护
  // dataSourceName的初始化，如果是查询方案的数据源会取isDefault，否则会取dataSourceNames的第一个
  get dataSourceNames(): any {
    return (
      this._workDesignInfo?.[this.pageType]?.dataSourceNames ||
      this._workDesignInfo?.[this.pcPageType]?.dataSourceNames ||
      []
    );
  }
  get dataSources(): any {
    return (
      this._workDesignInfo?.[this.pageType]?.dataSources || this._workDesignInfo?.[this.pcPageType]?.dataSources || {}
    );
  }
  // get dataSourceNames(): any {
  //   return this._workDesignInfo?.[this.pageType]?.dataSourceNames || this.handleInitPCDataSourceNames();
  // }
  // get dataSources(): any {
  //   return this._workDesignInfo?.[this.pageType]?.dataSources || this.handleInitPCDataSources();
  // }
  // handleInitPCDataSourceNames() {
  //   const dataSourceNames = this._workDesignInfo?.[this.pcPageType]?.dataSourceNames;
  //   let newDataSourceNames = [];
  //   if (dataSourceNames?.length) {
  //     newDataSourceNames = dataSourceNames.map((value) => {
  //       return value + '_mobile';
  //     });
  //   }
  //   return newDataSourceNames;
  // }
  // handleInitPCDataSources() {
  //   const dataSources = this._workDesignInfo?.[this.pcPageType]?.dataSources;
  //   const newDataSources = {};
  //   if (dataSources) {
  //     for (const key in dataSources) {
  //       if (dataSources.hasOwnProperty(key)) {
  //         newDataSources[`${key}_mobile`] = dataSources[key];
  //       }
  //     }
  //   }
  //   return newDataSources;
  // }

  get isCustom(): any {
    return this.page?.isCustomize;
  }

  get page(): any {
    return this._workDesignInfo?.[this.pageType];
  }

  get pageUIElement(): PageUIElement[] {
    return this._workDesignInfo?.pageUIElement || [];
  }

  get descriptionLang(): any {
    const data = this.workData;
    return data?.extendFields?.descriptionLang;
  }

  // 定制作业名称
  get customTip(): string {
    return `${this.workData.code}-${this.config.pageCode}`;
  }

  private _initInfoLoading: boolean = false; // 初始化数据信息加载的loading
  get initInfoLoading(): boolean {
    return this._initInfoLoading;
  }

  private _saveLoading: boolean = false; // 保存时的loading
  get saveLoading(): boolean {
    return this._saveLoading;
  }

  // 订阅-保存时的loading状态变化
  private _saveLoadingChange$: Subject<boolean> = new Subject<boolean>();
  get saveLoadingChange$(): Observable<boolean> {
    return this._saveLoadingChange$.asObservable();
  }
  private _activeMenu: MenuCode = null; // 当前激活的菜单
  get activeMenu() {
    return this._activeMenu;
  }
  private _submitActionsBase: any[] = []; // 基本按钮配置
  get submitActionsBase(): any[] {
    return this._submitActionsBase;
  }

  private _formInstance: any = null; // formio实例
  get formInstance(): any {
    return this._formInstance;
  }

  private _ruleList: any[] = []; // 规则列表
  get ruleList(): any[] {
    return this._ruleList;
  }
  constructor() {}

  setWorkData(workData: WorkData): void {
    this._workData = workData;
  }

  setWorkDesignInfo(workDesignInfo: WorkDesignInfo): void {
    this._workDesignInfo = workDesignInfo;
    this.masterFromDataSourceName = getMasterFromDataSourceName(workDesignInfo);
  }

  // 通过关键字设置数据录入界面设计完整数据的部分属性
  setWorkDesignInfoByKey(key: string, value: any): void {
    this._workDesignInfo[key] = value;
  }

  // 设置数据录入界面设计完整数据的部分属性（通过属性关键字路径list）
  setWorkDesignInfoByKeyPath(keyPath: string[], value: any): void {
    const targetKey = keyPath.splice(-1);
    const obj = keyPath.reduce((pre, cur) => {
      return pre[cur];
    }, this._workDesignInfo);

    obj[targetKey[0]] = value;
  }

  setInitInfoLoading(initInfoLoading: boolean): void {
    this._initInfoLoading = initInfoLoading;
  }

  setSaveLoading(saveLoading: boolean): void {
    this._saveLoading = saveLoading;
    this._saveLoadingChange$.next(saveLoading);
  }

  setActiveMenu(activeMenu: MenuCode): void {
    const isChangeMenu = this._activeMenu !== activeMenu;
    this._activeMenu = activeMenu;

    if (isChangeMenu) {
      this.resetDataSourceName();
    }
  }

  resetDataSourceName(dataViewQueryCode = '') {
    this.setDataSourceName(getDefaultDataSourceName(this.dataSources, this.dataSourceNames, dataViewQueryCode));
  }

  setDataSourceName(dataSourceName: string): void {
    this._dataSourceName = dataSourceName;
  }
  // setLastDataSourceName(lastDataSourceName: string) {
  //   this._lastDataSourceName = lastDataSourceName;
  // }

  setConfig(config: WorkDesignConfig): void {
    this._config = config;
  }

  setSubmitActionsBase(submitActionsBase: any[]): void {
    this._submitActionsBase = submitActionsBase;
  }

  setFormInstance(formInstance: any[]): void {
    this._formInstance = formInstance;
  }

  setRuleList(ruleList: any[]): void {
    this._ruleList = ruleList;
  }
  // 根据pageType 获取 pageCode
  getPageCode(pageType: string) {
    if (signDocumentConfigCategoryList.includes(this._workDesignInfo?.category)) {
      return 'basic-data-mobile';
    }
    return pageType === 'pageDslMobile' ? 'browse-page-mobile' : 'edit-page-mobile';
  }

  // 获取当前数据源
  getCurrentDataSource() {
    return this.dataSources[this._dataSourceName];
  }

  // 获取当前数据源的模式是否是多数据源模式
  getCurrentDataSourceIsMultiple(): boolean {
    return (
      this.config?.sidebarConfig?.[
        this.getCurrentDataSource()?.type === 'QUERYPLAN' ? 'queryPlanDataSourceMode' : 'basicDataSourceMode'
      ] === DataSourceModeEnum.Multiple
    );
  }

  // 通过code，activityId，pageCode即PageUIElementSearchInfo获取pageUIElement中对应的数据
  getPageUIElementData(pageUIElementSearchInfo: PageUIElementSearchInfo): PageUIElement {
    return this.pageUIElement.find(
      (item) =>
        item.code === pageUIElementSearchInfo.code &&
        item.activityId === pageUIElementSearchInfo.activityId &&
        item.pageCode === pageUIElementSearchInfo.pageCode,
    );
  }
  // 通过code，activityId，pageCode即PageUIElementSearchInfo设置pageUIElement中对应的数据
  // 如果没有则插入一条数据
  setPageUIElementData(pageUIElementSearchInfo: PageUIElementSearchInfo, pageUIElement: PageUIElement): void {
    const findIndex = this.pageUIElement.findIndex(
      (item) =>
        item.code === pageUIElementSearchInfo.code &&
        item.activityId === pageUIElementSearchInfo.activityId &&
        item.pageCode === pageUIElementSearchInfo.pageCode,
    );

    this._workDesignInfo.pageUIElement.splice(findIndex, findIndex >= 0 ? 1 : 0, pageUIElement);
  }

  // 通过 code 获取 用来查找 pageUIElementData 的 PageUIElementSearchInfo 数据
  getDataSourcePageUIElementSearchInfoByCode(code: string): PageUIElementSearchInfo {
    return {
      code,
      activityId: this?.workData?.code,
      pageCode: this.getPageCode(this.pageType),
    };
  }

  // 通过 dataSource 获取 用来查找 pageUIElementData 的 PageUIElementSearchInfo 数据
  getDataSourcePageUIElementSearchInfoByDataSource(dataSource: any): PageUIElementSearchInfo {
    return this.masterFromDataSourceName
      ? {
          code: dataSource?.dataViewQuery?.code ?? '',
          activityId: this?.workData?.code,
          pageCode: this.getPageCode(this.pageType),
        }
      : {
          code: dataSource?.dataViewQuery?.code ?? '',
          activityId: this?.workData?.code,
          pageCode: this.getPageCode(this.pageType),
        };
  }

  // 获取当前数据源下的PageUIElement
  getCurrentDataSourcePageUIElementData(): PageUIElement {
    return this.getPageUIElementData(
      this.getDataSourcePageUIElementSearchInfoByDataSource(this.getCurrentDataSource()),
    );
  }

  /**
   * 没有移动节点自动生成PageUIElementData
   */
  generateNewElementData(): PageUIElement {
    const temObj = this.getDataSourcePageUIElementSearchInfoByDataSource(this.getCurrentDataSource());
    temObj.pageCode = temObj?.pageCode?.split('-mobile')[0];
    const PageUIElementDataTem = cloneDeep(this.getPageUIElementData(temObj));
    PageUIElementDataTem.elements = {};
    PageUIElementDataTem.pageCode = PageUIElementDataTem.pageCode + '-mobile';
    return PageUIElementDataTem;
  }

  // 设置当前数据源下的PageUIElement
  setCurrentDataSourcePageUIElementData(pageUIElementData: PageUIElement) {
    this.setPageUIElementData(
      this.getDataSourcePageUIElementSearchInfoByDataSource(this.getCurrentDataSource()),
      pageUIElementData,
    );
  }

  // 根据 DataViewQueryCode 获取 dataSource
  getDataSourceByDataViewQueryCode(dataViewQueryCode: string): any {
    return Object.values(this.dataSources).find((item: any) => item.dataViewQuery?.code === dataViewQueryCode);
  }

  // 根据 DataViewQueryCode 获取 dataSourceName
  getDataSourceNameByDataViewQueryCode(dataViewQueryCode: string): any {
    return Object.keys(this.dataSources).find(
      (key: string) => this.dataSources[key]?.dataViewQuery?.code === dataViewQueryCode,
    );
  }

  // 设置自定义打印操作，此属性在外层
  setOperateCustomTemplateRelates(customs: any[]): void {
    if (this.page) {
      this.page.operateCustomTemplateRelates = customs || [];
    }
    // const pageUIElement = this.getCurrentPageUIElement();
    // if (pageUIElement) pageUIElement.operateCustomTemplateRelates = customs || [];
  }

  getCurrentPageUIElement(): PageUIElement | undefined {
    if (this.getCurrentDataSource()) {
      return this.getCurrentDataSourcePageUIElementData();
    } else {
      return this.pageUIElement?.find(
        (e) => e.activityId === this.workData?.code && e.pageCode === this.getPageCode(this.pageType),
      );
    }
  }
}
