<section #tableContainer class="table-container">
  <nz-table
    #table
    class="model-content-table"
    [nzFrontPagination]="false"
    [nzShowPagination]="false"
    nzSize="small"
    [nzBordered]="true"
    [nzData]="tableDataSource"
    [nzScroll]="{ y: state.contentHeight + 'px', x: '600px' }"
  >
    <thead>
      <tr>
        <th
          nzWidth="50px"
          [nzChecked]="calcFieldAllChecked()"
          [nzIndeterminate]="calcFieldIndeterminate()"
          (nzCheckedChange)="onAllChecked($event)"
        ></th>
        <th nzWidth="30px"></th>
        <th nzEllipsis nzWidth="200px">
          {{ 'dj-字段名称' | translate }}
          <i
            adIcon
            style="margin-left: 5px"
            type="question-circle"
            nzTheme="outline"
            nz-tooltip
            [nzTooltipTitle]="'dj-字段说明' | translate: { len: 60 }"
          ></i>
        </th>
        <th nzEllipsis nzWidth="200px">{{ 'dj-说明' | translate }}</th>
        <th nzEllipsis nzWidth="150px">{{ 'dj-数据类型' | translate }}</th>
        <th nzWidth="100px">{{ 'dj-必填' | translate }}</th>
        <th nzWidth="100px">{{ 'dj-唯一' | translate }}</th>
        <th nzWidth="100px" nzEllipsis>{{ 'dj-长度' | translate }}</th>
        <th nzWidth="100px" nzEllipsis>{{ 'dj-精度' | translate }}</th>
        <th nzWidth="100px" nzRight class="text-right">{{ 'dj-操作' | translate }}</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let fieldData of table.data; let i = index">
        <tr
          class="model-table-form drag-item"
          data-dragitem="true"
          data-type="parent"
          [attr.data-is-group]="fieldData.type === 'GROUP'"
          [attr.data-index]="i"
          [attr.data-uuid]="fieldData._uuid"
          [attr.data-is-system]="!!fieldData.isSystem"
          [ngClass]="{
            'row-disable': fieldData.type === 'GROUP' || fieldData.isSystem,
            'selected-row': selectedFieldUUID === fieldData._uuid,
            'field-error': fieldValid?.[fieldData._uuid] === false,
            'system-field': fieldData.isSystem && !fieldData.isPk && !['manage_status', 'tenant_id'].includes(fieldData.fieldId)
          }"
          [draggable]="fieldData.isSystem ? false : canMove"
          (dragstart)="handleDragStart($event, 'parent', i)"
          (drop)="handleDrop($event)"
          (dragend)="handleDragEnd($event, i)"
          (mouseenter)="handleMouseEnter($event, fieldData)"
          (mouseleave)="handleMouseLeave($event, fieldData)"
          (click)="selectRow(fieldData)"
        >
          <td
            *ngIf="fieldData.type !== 'GROUP'"
            [nzChecked]="!!fieldData.checked"
            (nzCheckedChange)="fieldData.checked = $event"
            [nzDisabled]="fieldData._customConfig?.checkboxDisabled"
            style="text-align: center"
          ></td>
          <td *ngIf="fieldData.type === 'GROUP'" style="text-align: center"></td>

          <ng-container *ngIf="fieldData.type !== 'GROUP'; else groupTemplate" context:{fieldData}>
            <ng-container *ngTemplateOutlet="tdTemplate; context: { fieldData, isSub: false, i }"></ng-container>
          </ng-container>
          <td nzRight>
            <div *ngIf="!fieldData.isSystem" class="options">
              <!-- 字段属性 -->
              <i
                class="option-item"
                style="transform: scale(1.3)"
                adIcon
                iconfont="iconshezhihao-xian"
                v-if="fieldData.type !== 'GROUP' && !fieldData.isSystem"
                (click)="handleOpenProperty($event, fieldData)"
              ></i>
              <!-- 编辑分组 -->
              <i
                *ngIf="fieldData.type === 'GROUP'"
                class="option-item"
                adIcon
                iconfont="iconjizhikabianji1"
                (click)="handleEditGroup($event, fieldData)"
              ></i>
              <!-- 删除行 -->
              <i
                class="option-item"
                adIcon
                iconfont="iconjizhikashanchu1"
                [ngClass]="{
                  'bk-options':
                    modelBKs.includes(fieldData._uuid) ||
                    isFK(fieldData) ||
                    service?.originModelData?.modelType === 'api'
                }"
                (click)="handleDeleteField($event, fieldData)"
              ></i>
              <!-- 可拖拽 -->
              <i
                class="option-item move-button"
                adIcon
                iconfont="icontuozhuaiIC"
                (mousedown)="canMove = true"
                (mouseup)="canMove = false"
                (click)="handleRowClick($event)"
              ></i>
            </div>

            <div
              *ngIf="fieldData.isSystem && ['manage_status', 'tenant_id'].includes(fieldData.fieldId)"
              class="options"
            >
              <!-- 删除行 ['manage_status','tenant_id'] 专用-->
              <i
                class="option-item"
                adIcon
                iconfont="iconjizhikashanchu1"
                *ngIf="service?.originModelData?.modelType !== 'api' || 'tenant_id' !== fieldData.fieldId"
                (click)="handleDeleteField($event, fieldData)"
              ></i>
            </div>
          </td>

          <ng-template #tdTemplate let-fieldData="fieldData" let-isSub="isSub" let-i="i" let-subIndex="subIndex">
            <ng-container *ngIf="isSub">
              <td style="text-align: center"></td>
              <td></td>
            </ng-container>
            <td nzAlign="left">
              <!-- 分组内字段 -->
              <ng-container *ngIf="isSub">
                <ng-container [ngTemplateOutlet]="fieldIconTemplate"></ng-container>
                <input
                  nz-input
                  style="padding: 0 8px; width: calc(100% - 20px)"
                  [disabled]="fieldData._customConfig.fieldIdDisable"
                  [(ngModel)]="fieldData.fieldId"
                  (ngModelChange)="dispatchChangeNotify()"
                />
              </ng-container>
            </td>
            <!-- 字段 -->
            <td *ngIf="!isSub">
              <ng-container *ngIf="selectedFieldUUID !== fieldData._uuid; else editField">
                <span class="edit-field" *ngIf="!fieldData.fieldId; else normalFieldId" style="color: #ff4d4f">
                  <ng-container [ngTemplateOutlet]="fieldIconTemplate"></ng-container>
                  {{ 'dj-待配置' | translate }}
                  <i adIcon iconfont="iconbianji-tongyong" class="icon-edit" *ngIf="hoverUuid === fieldData._uuid"></i>
                </span>
                <ng-template #normalFieldId>
                  <span class="edit-field">
                    <ng-container [ngTemplateOutlet]="fieldIconTemplate"></ng-container>
                    <span class="field-id" nz-tooltip [nzTooltipTitle]="fieldData.fieldId">{{
                      fieldData.fieldId
                    }}</span>
                    <span class="repeat" *ngIf="fieldIdRepeat(fieldData.fieldId)">{{
                      'dj-字段重复2' | translate
                    }}</span>
                    <i
                      adIcon
                      iconfont="iconbianji-tongyong"
                      class="icon-edit"
                      *ngIf="hoverUuid === fieldData._uuid"
                    ></i>
                  </span>
                </ng-template>
              </ng-container>
              <ng-template #editField>
                <div class="edit-field">
                  <ng-container [ngTemplateOutlet]="fieldIconTemplate"></ng-container>
                  <!-- <input
                    nz-input
                    id="edit-field-input"
                    style="padding: 0 8px; width: calc(100% - 20px)"
                    [disabled]="fieldData._customConfig.fieldIdDisable"
                    [(ngModel)]="fieldData.fieldId"
                    (ngModelChange)="dispatchChangeNotify()"
                  /> -->
                  <nz-input-group [nzSuffix]="suffixIcon">
                    <input
                      nz-input
                      id="edit-field-input"
                      style="padding: 0 8px; width: calc(100% - 20px)"
                      [disabled]="fieldData._customConfig.fieldIdDisable"
                      [(ngModel)]="fieldData.fieldId"
                      (ngModelChange)="dispatchChangeNotify()"
                    />
                  </nz-input-group>
                  <ng-template #suffixIcon>
                    <span class="input-clear" *ngIf="!isEmpty(fieldData.fieldId)">
                      <i
                        class="iconfont"
                        adIcon
                        iconfont="iconcloseIcon"
                        aria-hidden="true"
                        (click)="handleClearDataElement()"
                      ></i>
                    </span>
                    <i
                      adIcon
                      iconfont="iconkaichuang"
                      aria-hidden="true"
                      class="window-icon iconfont"
                      (click)="handleOpenDataElement(fieldData, i)"
                    >
                    </i>
                  </ng-template>
                  <span class="repeat" *ngIf="fieldIdRepeat(fieldData.fieldId)">{{ 'dj-字段重复2' | translate }}</span>
                </div>
              </ng-template>
            </td>
            <!-- 说明 -->
            <td nzEllipsis>
              <ng-container *ngIf="selectedFieldUUID !== fieldData._uuid; else editFieldName">
                <span
                  *ngIf="!(fieldData.lang?.fieldName?.[currentLang] || fieldData.fieldName); else normalFieldName"
                  style="color: #ff4d4f"
                  class="edit-field"
                >
                  {{ 'dj-待配置' | translate }}
                  <i adIcon iconfont="iconbianji-tongyong" class="icon-edit" *ngIf="hoverUuid === fieldData._uuid"></i>
                </span>
                <ng-template #normalFieldName>
                  <span
                    nz-tooltip
                    class="edit-field"
                    [nzTooltipTitle]="fieldData.lang?.fieldName?.[currentLang] || fieldData.fieldName"
                  >
                    {{ fieldData.lang?.fieldName?.[currentLang] || fieldData.fieldName }}
                    <i
                      adIcon
                      iconfont="iconbianji-tongyong"
                      class="icon-edit"
                      *ngIf="hoverUuid === fieldData._uuid"
                    ></i>
                  </span>
                </ng-template>
              </ng-container>
              <ng-template #editFieldName>
                <app-component-input
                  [attr]="{
                    needLang: true,
                    lang: { value: fieldData.lang?.fieldName },
                    innerLabel: false,
                    required: true,
                    maxlength: 50,
                    readOnly: fieldData._customConfig.fieldNameDisable,
                    placeholder: 'dj-请输入' | translate
                  }"
                  [value]="fieldData.lang?.fieldName?.[currentLang] || fieldData.fieldName"
                  (callBack)="handlePatchFieldNameLang(fieldData, $event)"
                >
                </app-component-input>
              </ng-template>
            </td>
            <!-- 数据类型 -->
            <td nzEllipsis>
              <ng-container *ngIf="selectedFieldUUID !== fieldData._uuid; else editFieldType">
                <span
                  *ngIf="!fieldData.fieldType && fieldData.type === 'SIMPLE'; else normalFieldType"
                  style="color: #ff4d4f"
                  class="edit-field"
                >
                  {{ 'dj-待配置' | translate }}
                  <i adIcon iconfont="iconbianji-tongyong" class="icon-edit" *ngIf="hoverUuid === fieldData._uuid"></i>
                </span>
                <ng-template #normalFieldType>
                  <span nz-tooltip [nzTooltipTitle]="fieldData.fieldTypee" class="edit-field">
                    {{ dataTypeMap[fieldData.fieldType] }}
                    <i
                      adIcon
                      iconfont="iconbianji-tongyong"
                      class="icon-edit"
                      *ngIf="hoverUuid === fieldData._uuid"
                    ></i>
                  </span>
                </ng-template>
              </ng-container>
              <ng-template #editFieldType>
                <ad-select
                  *ngIf="!fieldData._customConfig.fieldTypeDisable"
                  [nzShowSearch]="true"
                  nzDropdownClassName="variable-modal-custom-select"
                  [(ngModel)]="fieldData.fieldType"
                  (ngModelChange)="handleTypeChange(fieldData, $event)"
                  style="width: 100%"
                >
                  <ng-container *ngFor="let data of dataTypes">
                    <ad-option nzCustomContent [nzValue]="data.value" [nzHide]="data.hide" [nzLabel]="data.label">
                      <div class="custom-option-content">
                        <div class="option-label">
                          {{ data.label }}
                          <span class="description" nz-tooltip [nzTooltipTitle]="data.description">{{
                            data.description
                          }}</span>
                        </div>
                      </div>
                    </ad-option>
                  </ng-container>
                </ad-select>
                <span
                  nz-tooltip
                  [nzTooltipTitle]="fieldData.fieldTypee"
                  *ngIf="fieldData._customConfig.fieldTypeDisable"
                >
                  {{
                    fieldData.fieldType === 'FILE'
                      ? ('dj-附件' | translate)
                      : fieldData.fieldType === 'MULTIPLE'
                      ? ('dj-多选项' | translate)
                      : fieldData.fieldType
                  }}
                </span>
              </ng-template>
            </td>
            <!-- 必填 -->
            <td nzAlign="left">
              <ng-container *ngIf="selectedFieldUUID !== fieldData._uuid; else editNotNull">
                <span class="edit-field">
                  <i *ngIf="fieldData.notNull" adIcon type="check" style="color: #6a4cff"></i>
                  <i adIcon iconfont="iconbianji-tongyong" class="icon-edit" *ngIf="hoverUuid === fieldData._uuid"></i>
                </span>
              </ng-container>
              <ng-template #editNotNull>
                <label
                  [nzDisabled]="fieldData._customConfig.notNullDiable"
                  nz-checkbox
                  [(ngModel)]="fieldData.notNull"
                  (ngModelChange)="checkboxChanged(fieldData)"
                >
                </label>
              </ng-template>
            </td>
            <!-- 唯一 -->
            <td nzAlign="left">
              <ng-container *ngIf="selectedFieldUUID !== fieldData._uuid; else editUnique">
                <span class="edit-field">
                  <i *ngIf="fieldData.unique" adIcon type="check" style="color: #6a4cff"></i>
                  <i adIcon iconfont="iconbianji-tongyong" class="icon-edit" *ngIf="hoverUuid === fieldData._uuid"></i>
                </span>
              </ng-container>
              <ng-template #editUnique>
                <label
                  [nzDisabled]="fieldData._customConfig.uniqueDisable"
                  nz-checkbox
                  [(ngModel)]="fieldData.unique"
                  (ngModelChange)="checkboxChanged(fieldData)"
                >
                </label>
              </ng-template>
            </td>
            <!-- 长度 -->
            <td nzEllipsis>
              <ng-container *ngIf="selectedFieldUUID !== fieldData._uuid; else editSize">
                <span
                  class="edit-field"
                  *ngIf="!fieldData.size && kFieldTypes[fieldData.fieldType]?.size?.required; else normalFieldSize"
                  style="color: #ff4d4f"
                >
                  {{ 'dj-待配置' | translate }}
                  <i adIcon iconfont="iconbianji-tongyong" class="icon-edit" *ngIf="hoverUuid === fieldData._uuid"></i>
                </span>
                <ng-template #normalFieldSize>
                  <span class="edit-field">
                    {{ fieldData.size }}
                    <i
                      adIcon
                      iconfont="iconbianji-tongyong"
                      class="icon-edit"
                      *ngIf="hoverUuid === fieldData._uuid"
                    ></i>
                  </span>
                </ng-template>
              </ng-container>
              <ng-template #editSize>
                <input
                  nz-input
                  style="padding: 0 8px"
                  [disabled]="fieldData._customConfig.sizeDisable"
                  [maxLength]="48"
                  [(ngModel)]="fieldData.size"
                  (ngModelChange)="dispatchChangeNotify()"
                />
              </ng-template>
            </td>
            <!-- 精度 -->
            <td nzEllipsis>
              <ng-container *ngIf="selectedFieldUUID !== fieldData._uuid; else editScale">
                <span
                  class="edit-field"
                  *ngIf="!fieldData.scale && kFieldTypes[fieldData.fieldType]?.scale?.required; else normalFieldScale"
                  style="color: #ff4d4f"
                >
                  {{ 'dj-待配置' | translate }}
                  <i adIcon iconfont="iconbianji-tongyong" class="icon-edit" *ngIf="hoverUuid === fieldData._uuid"></i>
                </span>
                <ng-template #normalFieldScale>
                  <span class="edit-field">
                    {{ fieldData.scale }}
                    <i
                      adIcon
                      iconfont="iconbianji-tongyong"
                      class="icon-edit"
                      *ngIf="hoverUuid === fieldData._uuid"
                    ></i>
                  </span>
                </ng-template>
              </ng-container>
              <ng-template #editScale>
                <input
                  nz-input
                  style="padding: 0 8px"
                  [disabled]="fieldData._customConfig.scaleDisable"
                  [maxLength]="48"
                  [(ngModel)]="fieldData.scale"
                  (ngModelChange)="dispatchChangeNotify()"
                />
              </ng-template>
            </td>

            <ng-template #fieldIconTemplate>
              <i
                *ngIf="modelBKs.includes(fieldData._uuid); else noBKTemplate"
                class="field-icon bk"
                adIcon
                nz-tooltip
                [nzTooltipTitle]="'dj-业务主键' | translate"
                iconfont="iconzhujian"
                style="margin-right: 4px; color: #ff7f00"
              ></i>
              <ng-template #noBKTemplate>
                <i
                  class="field-icon"
                  [ngClass]="{
                    'quote-query': fieldData.type === 'QUOTE_QUERY',
                    quote: fieldData.type === 'QUOTE',
                    collection: fieldData.type === 'COLLECTION'
                  }"
                  adIcon
                  nz-tooltip
                  style="margin-right: 4px"
                  [ngStyle]="
                    fieldData.isPk
                      ? {
                          color: '#ff7f00'
                        }
                      : undefiend
                  "
                  [nzTooltipTitle]="fieldData | fieldTipsPipe | translate"
                  [iconfont]="fieldData | fieldIconPipe"
                ></i>
              </ng-template>
            </ng-template>
          </ng-template>

          <ng-template #groupTemplate>
            <td
              [nzExpand]="expandSet.has(fieldData._uuid)"
              (nzExpandChange)="onExpandChange(fieldData._uuid, $event)"
            ></td>
            <!-- 类型 -->
            <td colspan="7" nzEllipsis>
              <i adIcon iconfont="iconopened_folder"></i>
              {{ fieldData.fieldId }}
            </td>
          </ng-template>
        </tr>

        <!-- 分组内tr -->
        <ng-container *ngIf="fieldData.children?.length > 0">
          <ng-container *ngFor="let subFieldData of fieldData.children; let index = index">
            <tr
              *ngIf="expandSet.has(fieldData._uuid)"
              class="model-table-form drag-item"
              data-dragitem="true"
              data-type="sub"
              data-is-group="false"
              [ngClass]="{ 'selected-row': selectedFieldUUID === subFieldData._uuid,'field-error': fieldValid?.[subFieldData._uuid] === false}"
              [attr.data-parent-index]="i"
              [attr.data-index]="index"
              [attr.data-is-system]="!!subFieldData.isSystem"
              [draggable]="canMove"
              (dragstart)="handleDragStart($event, 'sub', i, index)"
              (drop)="handleDrop($event)"
              (dragend)="handleDragEnd($event, i, index)"
              (click)="selectRow(subFieldData)"
            >
              <ng-container
                *ngTemplateOutlet="tdTemplate; context: { fieldData: subFieldData, isSub: true, i, subIndex: index }"
              ></ng-container>
              <td nzRight>
                <div *ngIf="!fieldData.isSystem" class="options">
                  <i
                    class="option-item"
                    adIcon
                    iconfont="iconjizhikashanchu1"
                    [ngClass]="{ 'bk-options': modelBKs.includes(subFieldData._uuid) || isFK(subFieldData) }"
                    (click)="handleDeleteField($event, subFieldData)"
                  ></i>
                  <i
                    class="option-item move-button"
                    adIcon
                    iconfont="icontuozhuaiIC"
                    (mousedown)="canMove = true"
                    (mouseup)="canMove = false"
                    (click)="handleRowClick($event)"
                  ></i>
                </div>
              </td>
            </tr>
          </ng-container>
        </ng-container>
      </ng-container>
    </tbody>
  </nz-table>
</section>

<app-data-element-modal
  *ngIf="dataElementVisble"
  [visible]="dataElementVisble"
  (cancel)="dataElementVisble = false"
  (confirm)="handleDataElement($event)"
></app-data-element-modal>
