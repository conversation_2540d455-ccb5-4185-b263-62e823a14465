import { Component, Input, NgZone, OnChanges, OnInit, SimpleChanges, ViewChild } from '@angular/core';
import { ModelDesignerService } from '../../../service/model-designer.service';
import { StoreService } from '../../../service/store.service';
import { Subject, fromEvent } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { delay, isEmpty, isNumber, remove } from 'lodash';
import { moveItemInArray } from '@angular/cdk/drag-drop';
import { LocaleService } from 'common/service/locale.service';
import { FieldTypes } from '../../../types/types';
import { to } from 'common/utils/core.utils';
import { defaultValue, fieldTypeDefaultValue, fieldTypes, mysqlKeywords } from '../../../utils/model-uitil';
import { NzMessageService } from 'ng-zorro-antd/message';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'model-designer-table',
  templateUrl: './model-table.component.html',
  styleUrls: ['./model-table.component.less'],
})
export class ModelTableComponent implements OnInit, OnChanges {
  @Input() model: any;
  @ViewChild('tableContainer', { static: true }) table;

  selectedField: any = null;
  // 当前鼠标hover的uuid
  public hoverUuid: string | undefined;
  public readonly kFieldTypes = fieldTypes;

  tableData: any[] = [];
  canMove: boolean = false; // 是否可以移动
  draggedIndex; // 当前被拖拽的index
  targetIndex; // 移入的坐标
  dragDirection: 'up' | 'down' | 'group'; // 移入目标的方向,上、下、组内
  draggedLevel: 'parent' | 'sub'; // 当前被拖拽的是父级or子级
  targetLevel: 'parent' | 'sub'; // 移入的目标是父级or子级
  draggedParentIndex: number = null; // 当移动子元素时，记录当前父级index
  targetParentIndex: number = null; // 拖入到分组内时，记录目标元素的父级(分组)index
  draggedIsGroup: string;
  targetIsGroup: string;
  expandSet: Set<any> = new Set();
  timeingFlag: any;
  currentTime: number;
  currentEditGroup: any;
  targetIsSystem: boolean;
  currentLang: string;

  dataElementVisble: boolean = false;

  private destroy$ = new Subject<void>();

  public readonly dataTypes = [
    ...['VARCHAR', 'CHAR', 'TINYTEXT', 'TEXT', 'MEDIUMTEXT', 'LONGTEXT'].map((value, i) => ({
      label: this.translate.instant('dj-字符文本'),
      description: this.translate.instant('dj-表示汉语、英语、数字等文本内容'),
      value,
      hide: i !== 0,
    })),
    ...['INT', 'TINYINT', 'SMALLINT', 'BIGINT'].map((value, i) => ({
      label: this.translate.instant('dj-整数'),
      description: this.translate.instant('dj-包含正负整数'),
      value,
      hide: i !== 0,
    })),
    ...['DOUBLE', 'FLOAT', 'DECIMAL'].map((value, i) => ({
      label: this.translate.instant('dj-小数'),
      description: this.translate.instant('dj-表示有效数字内的小数'),
      value,
      hide: i !== 0,
    })),
    ...['BIT'].map((value, i) => ({
      label: this.translate.instant('dj-布尔值'),
      description: this.translate.instant('dj-包含真(true)、假(false)两个值'),
      value,
      hide: i !== 0,
    })),
    ...['DATE'].map((value, i) => ({
      label: this.translate.instant('dj-日期'),
      description: this.translate.instant('dj-用于仅日期存储'),
      value,
      hide: i !== 0,
    })),
    ...['DATETIME', 'TIMESTAMP', 'TIME'].map((value, i) => ({
      label: this.translate.instant('dj-日期时间'),
      description: this.translate.instant('dj-用于日期、时间的存储'),
      value,
      hide: i !== 0,
    })),
    ...['FILE'].map((value, i) => ({
      label: this.translate.instant('dj-附件'),
      description: this.translate.instant('dj-用于附件文件的存储'),
      value,
      hide: i !== 0,
    })),
    ...['MULTIPLE'].map((value, i) => ({
      label: this.translate.instant('dj-多选项'),
      description: this.translate.instant('dj-用于多选枚举值的存储'),
      value,
      hide: i !== 0,
    })),
  ];

  public readonly dataTypeMap = this.dataTypes.reduce((pre, curr) => {
    pre[curr.value] = curr.label;
    return pre;
  }, {});

  get state() {
    return this.store.state;
  }

  get currentPanelSetting() {
    const { propPanpelSetting, currentTabName } = this.state;
    return propPanpelSetting?.[currentTabName];
  }

  get selectedFieldUUID() {
    return this.currentPanelSetting?.fieldUUID;
  }

  get fieldValid() {
    return this.state.fieldFormsValid?.[this.model.name] || {};
  }

  get modelBKs() {
    const bks = Object.values(this.state.modelIndexes[this.model.name] || {});
    return bks;
  }

  get tableDataSource() {
    if (this.service.keywords.fields) {
      return this.tableData.filter((data) => {
        if (data.fieldId.includes(this.service.keywords.fields)) return true;
        const fieldName = data.lang?.fieldName?.[this.translate.instant('dj-LANG')] || data.fieldName;
        return fieldName.includes(this.service.keywords.fields);
      });
    }
    return this.tableData;
  }

  isEmpty = isEmpty;

  constructor(
    private service: ModelDesignerService,
    private store: StoreService,
    private ngZone: NgZone,
    private message: NzMessageService,
    private languageService: LocaleService,
    private translate: TranslateService,
  ) {
    this.currentLang = this.languageService.currentLanguage || 'zh_CN';
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.model.currentValue) {
      this.tableData = [...this.model.fields];
      this.configTableData(this.tableData);
    }
  }

  ngOnInit() {
    // 当前model业务主键快照，添加唯一标识
    this.service.upadteModelIndexes(this.model.name);
    // 初始化模型字段验证
    this.store.setState((state) => {
      state.fieldFormsValid[this.model.name] = {};
    });
  }

  ngAfterViewInit(): void {
    this.ngZone.runOutsideAngular(() => {
      fromEvent(this.table.nativeElement, 'dragover')
        .pipe(takeUntil(this.destroy$))
        .subscribe((event: DragEvent) => {
          this.handleDragEnter(event);
        });

      fromEvent(this.table.nativeElement, 'dragleave')
        .pipe(takeUntil(this.destroy$))
        .subscribe((event: DragEvent) => {
          this.handleDragLeave(event);
        });
    });

    const expands = document.querySelectorAll('.ant-table-row-expand-icon');
    expands.forEach((dom) => dom.addEventListener('click', (e) => e.stopPropagation()));
  }

  isFK(field): boolean {
    const parentModel = this.service.modelList.find((item) => item.name === this.model._pid);
    if (!parentModel) return false;
    return !!parentModel.fields.find((item) => item.fieldId === field.fieldId && item.isSystem);
  }

  fieldIdRepeat(fieldId: string): boolean {
    if (!fieldId) return false;
    const len = this.model.fields?.filter((field) => field.fieldId === fieldId)?.length || 0;
    return len > 1;
  }

  //#region 表头的checkbox
  calcFieldAllChecked(): boolean {
    const availableData = this.tableData.filter((data) => !data._customConfig?.checkboxDisabled);
    return availableData.every((item) => item.checked);
  }

  calcFieldIndeterminate(): boolean {
    const availableData = this.tableData.filter((data) => !data._customConfig?.checkboxDisabled);
    const checked = this.calcFieldAllChecked();
    return !checked && availableData.some((item) => item.checked);
  }

  onAllChecked(checked: boolean) {
    const availableData = this.tableData.filter((data) => !data._customConfig?.checkboxDisabled);
    availableData.forEach((item) => (item.checked = checked));
  }
  //#endregion

  checkboxChanged(fieldData: any) {
    this.selectRow(fieldData);
    this.dispatchChangeNotify();
  }

  handleTypeChange(fieldData: any, next: string) {
    if (fieldTypeDefaultValue[next]) {
      Object.keys(fieldTypeDefaultValue[next]).forEach((name) => {
        fieldData[name] = fieldTypeDefaultValue[next][name];
      });
    } else {
      ['size', 'scale'].forEach((name) => {
        fieldData[name] = null;
      });
    }
    this.dispatchChangeNotify();
  }

  dispatchChangeNotify() {
    // 当前行有数据被修改
    this.currentFieldValid();
    if (this.store.state.currentTabName !== this.model.name) return;
    this.service.refreshPropertyFormPatch$.next(true);
  }

  handlePatchFieldNameLang(fieldData: any, data: any) {
    fieldData.fieldName = data.value;
    fieldData.lang.fieldName = data.lang.value;
    this.dispatchChangeNotify();
  }

  onExpandChange(id: number, checked: boolean): void {
    if (checked) {
      this.expandSet.add(id);
    } else {
      this.expandSet.delete(id);
    }
  }

  /**
   * 删除字段
   * @param e
   * @param fieldData 字段数据
   */
  async handleDeleteField(e, fieldData) {
    e.stopPropagation();

    if (
      this.modelBKs.includes(fieldData._uuid) ||
      this.isFK(fieldData) ||
      this.service?.originModelData?.modelType === 'api'
    )
      return;

    if (this.service.isIndexField(fieldData, this.model)) {
      this.message.error(this.translate.instant('dj-该字段是索引，请先删除索引'));
      return;
    }

    if (fieldData.type === FieldTypes.COLLECTION) {
      const [err, res] = await to(this.service.removeCollectionField(fieldData));
      if (res) {
        this.updateAfterRemoveField(fieldData);
        // 更新被删表的主表children
        remove(
          this.service.modelList.find((m) => m.name === this.model.name).children,
          (child: any) => child.name === fieldData.fieldId,
        );
        // 如果被删除的字段在fieldFormsValid里，则同步删除
        this.service.deleteFieldFromFieldFormsValid(fieldData._uuid);
        this.service.refreshApiConfig$.next(true);
      }
      return;
    }

    // 删除组
    if (fieldData.type === 'GROUP') {
      const [err, res] = await to(this.service.removeGroup(fieldData));
      if (res) {
        this.updateAfterRemoveField(fieldData);
        this.service.refreshApiConfig$.next(true);
      }
      return;
    }

    const [err, res] = await to(this.service.removeField(fieldData));
    if (res) {
      this.updateAfterRemoveField(fieldData);
      // 如果被删除的字段在fieldFormsValid里，则同步删除
      this.service.deleteFieldFromFieldFormsValid(fieldData._uuid);
      this.service.refreshApiConfig$.next(true);
    }
  }

  /**
   * 删除字段后更新表格、属性面板、
   * @param fieldData
   */
  updateAfterRemoveField(fieldData) {
    // 更新列表
    this.tableData = [...this.service.modelList.find((m) => m.name === this.model.name).fields];
    this.configTableData(this.tableData);
    // 如果被删除了被选中的字段，需要关闭属性面板
    if (fieldData._uuid === this.selectedFieldUUID) {
      this.store.setState((state) => {
        const tabSet = state.propPanpelSetting[state.currentTabName];
        const newSet = { fieldUUID: '', visible: false, type: '' };
        state.propPanpelSetting[state.currentTabName] = { ...tabSet, ...newSet };
      });
    }
  }

  handleRowClick(e) {
    e.stopPropagation();
  }

  /**
   * 编辑分组
   * @param fieldData
   */
  handleEditGroup(e, fieldData): void {
    e.stopPropagation();
    this.store.setState({
      currentGroupUuid: fieldData._uuid,
      groupModalVisible: true,
    });
  }

  /**
   * 选中行
   * @param fieldData 字段
   * @param propOpen 是否展开属性面板
   */
  selectRow(fieldData, propOpen: boolean = false) {
    if (fieldData.type === 'GROUP' || fieldData.isSystem) return;
    this.selectedField = fieldData;
    this.store.setState((state) => {
      state.propPanpelSetting[state.currentTabName].fieldUUID = fieldData._uuid;
      state.propPanpelSetting[state.currentTabName].visible = true;
      if (propOpen) {
        state.propPanpelSetting[state.currentTabName].propOpen = propOpen;
      }
      state.propPanpelSetting[state.currentTabName].type = 'field';
    });
  }

  /**
   * 开始拖拽
   * @param e
   * @param index
   */
  handleDragStart(e, type, parentIndex, subIndex) {
    const tr = e.target.closest('tr');

    this.draggedLevel = type;
    this.draggedIsGroup = JSON.parse(tr.dataset.isGroup);
    this.targetIsSystem = false;

    if (JSON.parse(tr.dataset.isSystem)) {
      return;
    }

    if (type === 'sub') {
      this.draggedParentIndex = parentIndex ?? null;
      this.draggedIndex = subIndex;
      return;
    }

    this.draggedIndex = parentIndex;
  }

  handleMouseEnter(e: MouseEvent, fieldData: any) {
    e.stopPropagation();
    if (this.hoverUuid !== fieldData._uuid) {
      if (fieldData.type === 'GROUP' || fieldData.isSystem) return;
      this.hoverUuid = fieldData._uuid;
    }
  }

  handleMouseLeave(e: MouseEvent, fieldData: any) {
    e.stopPropagation();
    if (this.hoverUuid !== undefined) {
      this.hoverUuid = undefined;
    }
  }

  /**
   * 拖拽到目标
   * @param e
   * @param index
   */
  handleDragEnter(e) {
    e.preventDefault();
    const tr = e.target.closest('tr');
    const dataset = tr?.dataset;

    if (!tr) return;

    // @ts-ignore
    const { type, index, parentIndex, isGroup, isSystem, uuid } = {
      ...dataset,
      index: parseInt(dataset.index),
      parentIndex: parseInt(dataset.parentIndex),
      isGroup: JSON.parse(dataset.isGroup),
      isSystem: JSON.parse(dataset.isSystem),
    };

    this.targetIndex = index;
    this.targetLevel = type;
    this.targetParentIndex = parentIndex ?? null;
    this.targetIsGroup = isGroup;

    if (isSystem) {
      this.targetIsSystem = true;
      return;
    }
    if (index === this.draggedIndex && this.draggedParentIndex === this.targetParentIndex) return;
    if (this.draggedIsGroup && this.targetLevel === 'sub') return;

    const rect = tr.getBoundingClientRect();
    const center = rect.top + rect.height / 2;
    const top = rect.top + rect.height / 3;
    const bottom = rect.top + (rect.height * 2) / 3;
    const y = e.clientY;

    if (isGroup && !this.draggedIsGroup) {
      if (y > top && y < bottom) {
        this.dragDirection = 'group';
        // 长停留自动打开分组
        this.longGroupEnter(() => {
          this.ngZone.run(() => {
            !this.expandSet.has(uuid) && this.expandSet.add(uuid);
          });
        });
      } else if (y < top) {
        this.dragDirection = 'up';
      } else {
        this.dragDirection = 'down';
      }
    } else {
      if (y < center) {
        this.dragDirection = 'up';
      } else {
        this.dragDirection = 'down';
      }
    }

    this.renderTrDragState(tr, this.dragDirection);
  }

  renderTrDragState(targetTr, status: 'up' | 'down' | 'group') {
    const classNames = ['drag-enter-up', 'drag-enter-down', 'drag-enter-group'];
    const classMap = { up: 'drag-enter-up', down: 'drag-enter-down', group: 'drag-enter-group' };

    classNames.forEach((name) => {
      targetTr.classList.remove(name);
    });
    targetTr.classList.add(classMap[status]);
  }

  /**
   * 在分组上长时间停留后自动打开分组
   * @param fn
   */
  longGroupEnter(fn) {
    let t = null;

    if (!this.timeingFlag) {
      this.currentTime = new Date().getTime();
    }
    this.timeingFlag = true;

    clearTimeout(t);
    t = setTimeout(() => {
      clearTimeout(t);
      if (new Date().getTime() - this.currentTime >= 900) {
        fn && fn();
      }
    }, 100);
  }

  handleDragLeave(e) {
    e.preventDefault();
    const tr = e.target.closest('tr');

    if (!tr) return;

    const classNames = ['drag-enter-up', 'drag-enter-down', 'drag-enter-group'];

    classNames.forEach((name) => {
      tr.classList.remove(name);
    });
    this.timeingFlag = false;
  }

  handleDrop(e) {
    e.preventDefault();
    this.handleDragLeave(e);
  }

  /**
   * 父级拖拽结束
   * @param e
   * 分情况：
   *  1.平级排序
   *  2.子级拖到父级
   */
  handleDragEnd(e) {
    if (this.targetIsSystem) return;
    if (!this.targetLevel || !isNumber(this.targetIndex)) return;
    if (this.draggedIsGroup && this.targetLevel === 'sub') return;
    if (this.targetIndex === this.draggedIndex && this.draggedParentIndex === this.targetParentIndex) return;

    if (this.draggedLevel === this.targetLevel) {
      // 拖拽到自己原位直接return;
      if (this.dragDirection === 'up' && this.targetIndex - 1 === this.draggedIndex) {
        return;
      }
      // down: 1.如果 targetIndex+1 === draggedIndex 则直接return；
      if (this.dragDirection === 'down' && this.targetIndex + 1 === this.draggedIndex) {
        return;
      }
    }

    this.draggedLevel === 'parent' ? this.handleParentDragEnd() : this.handleSubDragEnd();
    // 重建引用让table渲染
    this.tableData = [...this.model.fields];
    this.configTableData(this.tableData);
    // 重置拖拽相关的变量
    this.resetDragConst();
    this.handleDragLeave(e);
  }

  /**
   * 1.平级拖拽
   * 2.父级拖到分组上
   * 3.父级拖到子级
   */
  handleParentDragEnd() {
    let targetIndex = this.targetIndex;
    const { fields } = this.model;
    const draggedData = fields[this.draggedIndex];

    // 父级平级之间拖拽
    if (this.targetLevel === 'parent') {
      // 如果是拖到组内
      if (this.dragDirection === 'group') {
        // 拖动到组内，则把拖动元素放到组内
        const group = this.model.fields[this.targetIndex];
        const field = this.model.fields[this.draggedIndex];
        group.children.push(field);
        this.model.fields.splice(this.draggedIndex, 1);
        return;
      }

      if (this.dragDirection === 'down' && this.draggedIndex > this.targetIndex) {
        targetIndex += 1;
      }
      moveItemInArray(this.model.fields, this.draggedIndex, targetIndex);
      return;
    }

    // 父拖到子
    if (this.dragDirection === 'down') {
      targetIndex += 1;
    }
    // 在分组内拖拽位置处添加
    fields[this.targetParentIndex].children.splice(targetIndex, 0, draggedData);
    // 删除父级
    fields.splice(this.draggedIndex, 1);
  }

  handleOpenProperty(e: MouseEvent, fieldData: any): void {
    e.stopPropagation();
    this.selectRow(fieldData, true);
  }

  /**
   * 1.同组子级拖拽
   * 2.子级拖到父级
   * 3.不同组子级之间拖拽
   */
  handleSubDragEnd(): void {
    const fields = this.model.fields;
    const draggedData = fields[this.draggedParentIndex].children[this.draggedIndex];
    let targetIndex = this.targetIndex;

    // 子元素拖到子元素
    if (this.targetLevel === 'sub') {
      // 同组内子拖子
      if (this.targetParentIndex === this.draggedParentIndex) {
        if (this.dragDirection === 'down' && this.draggedIndex > this.targetIndex) {
          targetIndex += 1;
        }
        moveItemInArray(fields[this.targetParentIndex].children, this.draggedIndex, targetIndex);
        return;
      }

      /* 不同组内子拖子 */
      if (this.dragDirection === 'down') {
        targetIndex += 1;
      }
      // 添加拖拽的数据
      fields[this.targetParentIndex].children.splice(targetIndex, 0, draggedData);
      // 删除被拖拽的数据
      fields[this.draggedParentIndex].children.splice(this.draggedIndex, 1);
      return;
    }

    /* 子拖到组外 */
    if (this.dragDirection === 'down') {
      targetIndex += 1;
    }

    fields[this.draggedParentIndex].children.splice(this.draggedIndex, 1);
    fields.splice(targetIndex, 0, draggedData);
  }

  /**
   * 重置拖拽相关的变量
   */
  resetDragConst() {
    this.canMove = false; // 是否可以移动
    this.draggedIndex = null; // 当前被拖拽的index
    this.targetIndex = null; // 移入的坐标
    this.dragDirection = null; // 移入目标的方向,上、下、组内
    this.draggedLevel = null; // 当前被拖拽的是父级or子级
    this.targetLevel = null; // 移入的目标是父级or子级
    this.draggedParentIndex = null; // 当移动子元素时，记录当前父级index
    this.targetParentIndex = null; // 拖入到分组内时，记录目标元素的父级(分组)index
  }

  /**
   * 校验当前行的数据
   */
  public currentFieldValid() {
    const currentField = this.tableDataSource.find((item) => item._uuid === this.selectedFieldUUID);
    if (!currentField || currentField.isSystem) return;
    const valid = this.checkFieldValidate(currentField);
    this.store.setState((state) => {
      state.fieldFormsValid[this.model.name][currentField._uuid] = valid;
    });
  }

  //#region 私有方法
  /**
   * 校验字段
   * @param fieldData
   * @returns
   */
  private checkFieldValidate(fieldData: any): boolean {
    if (!fieldData.fieldId) {
      return false;
    }
    if (!/^(?!\d+$)[a-z0-9_]*$/.test(fieldData.fieldId)) {
      return false;
    }
    if (mysqlKeywords.includes(fieldData.fieldId?.toLocaleUpperCase())) {
      return false;
    }
    if (fieldData.fieldId?.length > 60) {
      return false;
    }
    if (!fieldData.fieldName) {
      return false;
    }
    if (!fieldData.fieldType) {
      return false;
    }
    if (!(/^(0|[1-9]\d*)(\.\d+)?$/.test(fieldData.size) || ['', null, undefined].includes(fieldData.size))) {
      return false;
    }
    if (!(/^(0|[1-9]\d*)(\.\d+)?$/.test(fieldData.scale) || ['', null, undefined].includes(fieldData.scale))) {
      return false;
    }
    return true;
  }

  private configTableData(list: any) {
    list.forEach((data) => {
      data._customConfig = {
        checkboxDisabled: this.fieldCheckDisable(data),
        ...this.fieldDataDisable(data),
        ...this.controlSizeScaleByFieldType(data),
      };
      if (data.children?.length) {
        this.configTableData(data.children);
      }
    });
  }

  /**
   * checkbox是否禁用
   * @param fieldData
   * @returns
   */
  private fieldCheckDisable(fieldData: any): boolean {
    if (fieldData.isSystem && ['manage_status', 'tenant_id'].includes(fieldData.fieldId)) {
      if (this.service?.originModelData?.modelType !== 'api' || 'tenant_id' !== fieldData.fieldId) {
        return false;
      }
      return true;
    }
    if (fieldData.isSystem) return true;
    if (
      this.modelBKs.includes(fieldData._uuid) ||
      this.isFK(fieldData) ||
      this.service?.originModelData?.modelType === 'api'
    )
      return true;
    return false;
  }

  /**
   * fieldId是否禁用
   * @param fieldData
   */
  private fieldDataDisable(fieldData: any): { [k: string]: boolean } {
    const fieldType = fieldData?.type;
    const isBK = this.state.modelIndexes[this.model.name]?.[fieldData?.fieldId] === fieldData?._uuid;
    if (fieldData.isSystem)
      return {
        fieldIdDisable: true,
        fieldNameDisable: true,
        fieldTypeDisable: true,
        notNullDiable: true,
        uniqueDisable: true,
        sizeDisable: true,
        scaleDisable: true,
      };
    if (fieldType === FieldTypes.QUOTE) {
      return {
        fieldIdDisable: true,
        fieldNameDisable: false,
        fieldTypeDisable: true,
        notNullDiable: false,
        uniqueDisable: false,
        sizeDisable: true,
        scaleDisable: true,
      };
    }
    if (fieldType === FieldTypes.QUOTE_QUERY) {
      return {
        fieldIdDisable: false,
        fieldNameDisable: false,
        fieldTypeDisable: true,
        notNullDiable: false,
        uniqueDisable: false,
        sizeDisable: true,
        scaleDisable: true,
      };
    }
    if (fieldType === FieldTypes.COLLECTION) {
      return {
        fieldIdDisable: true,
        fieldNameDisable: false,
        fieldTypeDisable: true,
        notNullDiable: true,
        uniqueDisable: true,
        sizeDisable: true,
        scaleDisable: true,
      };
    }
    if (isBK) {
      return {
        fieldIdDisable: true,
        fieldNameDisable: false,
        fieldTypeDisable: true,
        notNullDiable: false,
        uniqueDisable: true,
        sizeDisable: true,
        scaleDisable: true,
      };
    }
    if (this.service.originModelData?.modelType === 'api' && fieldData.type !== FieldTypes.QUOTE_QUERY) {
      return {
        fieldIdDisable: true,
        fieldNameDisable: false,
        fieldTypeDisable: false,
        notNullDiable: false,
        uniqueDisable: false,
        sizeDisable: false,
        scaleDisable: false,
      };
    }
    return {
      fieldIdDisable: false,
      fieldNameDisable: false,
      fieldTypeDisable: false,
      notNullDiable: false,
      uniqueDisable: false,
      sizeDisable: false,
      scaleDisable: false,
    };
  }

  private controlSizeScaleByFieldType(fieldData: any) {
    const fieldType = fieldData?.fieldType;
    const type = fieldData?.type;
    if (!fieldType) return {};
    let keys = ['size', 'scale', 'unique', 'defaultValue'];
    const oFieldType = fieldTypes[fieldType];
    let isLogic = false;
    // 逻辑字段处理
    if (type === FieldTypes['LOGIC']) {
      keys = ['size', 'scale', 'unique', 'defaultValue', 'notNull', 'enumValue', 'businessCode'];
      isLogic = true;
    }
    const result = {};
    keys.forEach((key) => {
      const ignoreKey = ['size', 'scale'].includes(key);
      const isDisabled = isLogic && !ignoreKey ? true : oFieldType[key]?.disabled;
      if (isDisabled) {
        result[`${key}Disable`] = true;
      }
    });
    return result;
  }

  handleOpenDataElement(fieldData, index) {
    this.dataElementVisble = true;
  }

  handleDataElement(params) {
    this.dataElementVisble = false;
    const selectedIndex = this.tableData.findIndex((item) => item._uuid === this.selectedFieldUUID);
    const fieldData = this.vocabularyDictionaryToField(params.selectedRows[0]);
    Object.assign(this.tableData[selectedIndex], fieldData);
    this.dispatchChangeNotify();
  }

  handleClearDataElement(): void {
    const selectedIndex = this.tableData.findIndex((item) => item._uuid === this.selectedFieldUUID);
    Object.assign(this.tableData[selectedIndex], { fieldId: '' });
    this.dispatchChangeNotify();
  }

  /**
   * 辞汇字典（字段）数据转字段数据
   * @param data
   */
  vocabularyDictionaryToField(data) {
    const { descriptionZhCn, descriptionZhTw, descriptionEnUs } = data || {};
    const description = {
      zh_CN: descriptionZhCn,
      zh_TW: descriptionZhTw,
      en_US: descriptionEnUs,
    };
    const currentLanguage = this.languageService.currentLanguage || 'zh_CN';
    if (data) {
      return {
        fieldId: data?.dataName,
        fieldName: description[currentLanguage],
        businessCode: data?.bizCode,
        fieldType: data?.dataType,
        lang: {
          fieldName: description,
        },
        size: data?.size,
        scale: data?.fieldPrecision,
        enumValue: data?.dictionaryKey,
        dictionaryContent: data?.dictionaryValues,
        wordDictionaryId: data?.id, // 辞汇字典id
        businessTypeId: data?.relateBusinessTypeId, // 业务类型id
        dictionaryId: data?.relateDictionaryId, // 下拉辞汇id
      };
    }
  }

  //#endregion
}
