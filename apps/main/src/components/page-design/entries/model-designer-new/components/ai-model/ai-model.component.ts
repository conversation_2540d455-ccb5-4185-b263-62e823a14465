import { Component, ElementRef, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { NzUploadChangeParam, NzUploadFile } from 'ng-zorro-antd/upload';
import { TranslateService } from '@ngx-translate/core';
import { NzMessageService } from 'ng-zorro-antd/message';
import { SystemConfigService } from 'common/service/system-config.service';
import { AiModelService } from './ai-model.service';
import { IMessageModel } from './ai-model.type';
import { v4 as uuid } from 'uuid';
import { delay } from 'lodash';

@Component({
  selector: 'app-ai-model',
  templateUrl: './ai-model.component.html',
  styleUrls: ['./ai-model.component.less'],
})
export class AiModelComponent implements OnInit {
  // 使用场景： businesss：创建模型对象 field：模型的field
  @Input() scene: 'business' | 'field' = 'business';
  // 模型类型：业务档-transaction，参数档-param，基础档-basic，原始类型-default
  @Input() modelType: string = 'default';

  @Output() close: EventEmitter<void> = new EventEmitter();
  @Output() callback: EventEmitter<any> = new EventEmitter();

  @ViewChild('end') end: ElementRef<HTMLDivElement>;

  public textValue: string | undefined = undefined;

  // 对话内容
  public messages: IMessageModel[] = [
    {
      id: 'xx',
      type: 'receive',
      msgType: 'default',
      loading: false,
    },
  ];

  public uploadFile: any = null;
  public fileUrl: any | undefined = undefined;

  // 当前选中的tab index
  public index: number = 0;
  // 今日剩余次数
  public limit: number = 0;
  public timeLoading: boolean = false;
  // 查看示例
  public exampleVisible: boolean = false;
  public readonly exampleSrc: string = '/assets/img/ai-generate-example.png?times=1';
  public loading: boolean = false;
  public uploadUrl: string;
  // 是否全屏
  public fullscreen: boolean = false;

  // 是否正在与服务端通信
  private sending: boolean = false;

  @ViewChild('container') container: ElementRef<HTMLDivElement>;

  get btnDisabled() {
    if (this.limit <= 0 || this.sending) return true;
    if (this.index === 0) {
      return !this.textValue;
    } else {
      return !this.uploadFile || !this.fileUrl || this.loading;
    }
  }

  constructor(
    public element: ElementRef,
    private message: NzMessageService,
    private translateService: TranslateService,
    private sanitizer: DomSanitizer,
    protected configService: SystemConfigService,
    private service: AiModelService,
  ) {
    this.configService.get('adesignerUrl').subscribe((url) => {
      this.uploadUrl = `${url}/athena-designer/dmc/shareUploadFile`;
    });
  }

  ngOnInit() {
    this.getGptLimit();
  }

  public getGptLimit() {
    this.handleAiGPTGenerateTimes();
  }

  /**
   * 重置数据
   */
  public reset() {
    this.textValue = undefined;

    this.messages = [
      {
        id: 'xx',
        type: 'receive',
        msgType: 'default',
        loading: false,
      },
    ];

    this.uploadFile = null;
    this.fileUrl = undefined;
    this.index = 0;
    this.limit = -1;
    this.timeLoading = false;
    this.exampleVisible = false;
    this.loading = false;
    this.uploadUrl = '';
    this.fullscreen = false;
    this.sending = false;
  }

  public updateMessageStatus(msgId: string, status: 'generating' | 'success' | 'retry') {
    const message = this.messages.find((msg) => msg.id === msgId);
    if (!message || message.loading) return;
    message.status = status;
  }

  /**
   * 查询还剩多少次
   */
  private async handleAiGPTGenerateTimes(): Promise<void> {
    this.timeLoading = true;
    try {
      const result = await this.service.getAiGPTGenerateCount({ generationType: 'field' }).toPromise();
      this.limit = result.data || 0;
    } finally {
      this.timeLoading = false;
    }
  }

  handleCloseDrawer(): void {
    this.close.emit();
  }

  handleViewExample(e: MouseEvent): void {
    e.stopPropagation();
    this.exampleVisible = true;
  }

  handleRemove(): void {
    this.uploadFile = undefined;
    this.fileUrl = undefined;
  }

  toggleFullScreen(e: MouseEvent): void {
    e.stopPropagation();
    if (this.fullscreen) {
      this.container.nativeElement.style.width = '400px';
    } else {
      this.container.nativeElement.style.width = '100%';
    }
    this.fullscreen = !this.fullscreen;
  }

  mouseDown(event: any): void {
    event.stopPropagation();
    window.onmousemove = (e) => {
      const div = this.container.nativeElement;
      if (!div) return;
      div.style.width = document.body.clientWidth - e.clientX + 'px';
    };
    window.onmouseup = () => {
      window.onmousemove = null;
    };
  }

  handleBeforeUpload = (file: File): boolean => {
    this.loading = true;
    const { name, size } = file;
    const nameArr = name.split('.');
    let nameSuffix = nameArr[nameArr.length - 1];
    nameSuffix = nameSuffix.toLowerCase();
    if (!['pdf', 'png', 'jpg', 'bmp', 'jpeg', 'tif', 'heif', 'heic'].includes(nameSuffix)) {
      this.message.error(
        this.translateService.instant('dj-仅支持（*.pdf,*.png,*.jpg,*.bmp,*.jpeg,*.tif,*.heif,*.heic）文件'),
      );
      this.loading = false;
      return false;
    }
    this.uploadFile = file;
    this.fileUrl = this.sanitizer.bypassSecurityTrustUrl(URL.createObjectURL(file));
    return true;
  };

  handleChange({ file }: NzUploadChangeParam): void {
    const status = file.status;
    if (status === 'done') {
      this.fileUrl = file.response.data;
      this.loading = false;
    } else if (status === 'error') {
      this.fileUrl = undefined;
      this.loading = false;
    }
  }

  handleDrop(event: DragEvent) {
    const files = event.dataTransfer.files;
    if (this.uploadFile || !files?.length) {
      return;
    }
    if (files.length > 1) {
      this.message.error(this.translateService.instant('dj-只支持一个文件上传！'));
      return;
    }
    this.handleBeforeUpload(files[0]);
    event.preventDefault();
  }

  onDragOver(event: DragEvent) {
    event.preventDefault();
  }

  onDragLeave(event: DragEvent) {
    event.preventDefault();
  }

  async handleSend(): Promise<void> {
    this.sending = true;
    const uuidString = uuid();
    let result: any;
    const isTextMode = this.index === 0;
    const historyPrompts = this.messages
      .filter((msg) => msg.origin)
      .map((msg) => {
        return {
          content: msg.origin,
          role: msg.type === 'send' ? 'user' : 'assistant',
        };
      });
    if (isTextMode) {
      this.messages.push({
        id: uuid(),
        type: 'send',
        origin: this.textValue,
        msgType: 'text',
        content: this.textValue,
      });
    } else {
      this.messages.push({
        id: uuid(),
        type: 'send',
        msgType: 'image',
        content: this.fileUrl,
      });
    }
    this.messages.push({
      id: uuidString,
      type: 'receive',
      msgType: 'table',
      loading: true,
    });
    this.scrollToBottom();
    // 文本
    try {
      if (isTextMode) {
        const param = {
          content: this.textValue,
          modelType: this.modelType,
          historyPrompts,
        };
        this.textValue = '';
        result = await this.service.getAiGPTGenerateFieldName(param).toPromise();
      } else {
        const param = { fileURL: this.fileUrl };
        this.fileUrl = undefined;
        this.uploadFile = undefined;
        result = await this.service.getGptGenerateFieldsByOCR(param).toPromise();
      }
    } catch (e) {
      result = {
        message: e.message,
        code: -1,
      };
    } finally {
      this.sending = false;
    }
    // 更新数据
    const message = this.messages.find((e) => e.id === uuidString);
    if (result.code === 0) {
      if (isTextMode) {
        message.origin = JSON.stringify(result.data);
      }
      this.limit = result.data.leftCount;
      if (result.data.tableName) {
        message.content = result.data;
        message.loading = false;
      } else {
        message.content = this.translateService.instant('dj-无法解析n', {
          n: isTextMode ? this.translateService.instant('dj-文本') : this.translateService.instant('dj-图片'),
        });
        message.msgType = 'text';
        message.loading = false;
      }
    } else {
      message.content = result.message;
      message.msgType = 'text';
      message.loading = false;
    }
    this.scrollToBottom();
  }

  /**
   * 滚到底部
   */
  private scrollToBottom(): void {
    delay(() => {
      this.end?.nativeElement?.scrollIntoView({ behavior: 'smooth' });
    }, 150);
  }
}
