import {
  Component,
  OnInit,
  EventEmitter,
  ViewChild,
  Injector,
  ViewEncapsulation,
  Input,
  SimpleChanges,
  Output,
  NgZone,
  OnDestroy,
  TemplateRef,
  OnChanges,
  ChangeDetectorRef,
} from '@angular/core';
import {
  DwDynamicFormBuilderComponent,
  FormEditorFramework,
  InterfaceConversionService,
  JsonUndoRedoService,
} from '@webdpt/form-editor';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { PageSize, PageSizeService, CRUDDslConvertService, EDesignMode } from '@webdpt/form-editor-components';

import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { TranslateService } from '@ngx-translate/core';
import { defaultBuilderOptions, submitActions } from './dsl-form-render.config';
import { SendSubscriberMessageData, DslBuilderOptions } from './dsl-form-render.type';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { cloneDeep, debounce } from 'lodash';
import { DslFormRenderMobileService } from './service/dsl-form-render-mobile.service';
import { DslFormRenderShareMobileService } from './service/dsl-form-render-share-mobile.service';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { isEmpty } from 'common/utils/core.utils';
import { flattenData } from './utils/tools';
import { AdUserService } from 'pages/login/service/user.service';
import { SESSIONSTORAGE_KEY_CONSTANT } from 'common/config/SessionStorageKeyConstant';
import { AppService } from 'pages/apps/app.service';
import { SubModuleEnum } from '../dsl-work-design/shared/config/dsl-work-sidebar.config';
// import { ComponentCustomHooks } from '../../../bussiness-components/hooks-script-editor/share/config';
import { DslFormRenderRequestService } from './service/dsl-form-render-request.service';
import { DslWorkDesignMobileService } from '../dsl-work-design-mobile/service/dsl-work-design-mobile.service';
import { WorkDesignService } from '../../entries/work-design/work-design.service';
import {
  Group,
  getElementInfo,
  getFieldLevelType,
  generateRootComponent,
  judgeCanDrop,
  generateComponent,
} from '../dsl-work-design-mobile/utils/create.mobileComponent';
import { setMobileComponentList } from '../dsl-work-design-mobile/utils/components';
import { WorkSidebarMobileService } from '../dsl-work-design-mobile/dsl-work-sidebar-mobile/service/work-sidebar-mobile.service';
import { UUID } from 'angular2-uuid';

@Component({
  selector: 'app-dsl-form-render-mobile',
  templateUrl: './dsl-form-render-mobile.component.html',
  styleUrls: ['./dsl-form-render-mobile.component.less', '../../../../assets/css/lcdpSty/style.less'],
  providers: [DslFormRenderMobileService, DslFormRenderRequestService],
  encapsulation: ViewEncapsulation.None,
})
export class DslFormRenderMobileComponent implements OnInit, OnDestroy, OnChanges {
  defaultBuilderOptions: DslBuilderOptions; // formio 配置项
  buttonParent: 'BUTTON_GROUP' | 'CARD' | '' = ''; // BUTTON_GROUP底部按钮 CARD卡片按钮
  buttonGroup$;
  moduleData: any; // 字段或模块属性
  tempCId = ''; // 解决按钮组件死循环
  tempCardCId = ''; //
  form: any = {
    components: [],
  }; // 画布渲染的formio数据

  formTemp = null; // 在form初始化完成之前临时用于接收form数据，在formReady之后进行消费，主要由于使用空components进行form的初始化速度会很快

  private _formInitLoading = true; // form初始化loading
  private _isRenderDsl = false; // 是否渲染dsl数据
  private _isNotSaveHistoryAfterFormReady = true; // form ready 后是否记录历史
  appliedComponents: any[];
  hooks: any;
  isPropertiesOpen = true;
  get buttonGroupModal() {
    return this.service.buttonGroupModal;
  }
  get formInitLoading() {
    return this._formInitLoading;
  }
  set formInitLoading(data: boolean) {
    this._formInitLoading = data;
    this.isFormInitReadyChange.emit(data && this._isRenderDsl);
  }

  showCustomTemplatePanel: boolean = false;

  destroy$ = new Subject();
  formJson$; // formio数据的订阅
  resetFormDragEvent$; // 重置画布的拖拽注册
  builderOptions: any; // 画布的build参数
  pageSize: PageSize = undefined;
  private _formInstance = null; // formio渲染实例
  dslRuleOutChange$: Subject<any>; // 规则变化向外输出的订阅

  selectComponentCallback: (component: any) => any; // 选中formio组件后的回调

  // action开窗组件 历史逻辑
  actionModal = false;
  actionData = {
    actionId: '',
    actionName: '',
    useApp: 'true',
  };
  source;
  componentList = [];
  componentType = 'INPUT';
  // 卡片类型
  cardTypes = ['DW_CARD', 'DW_ZTB_VIEW_LIST', 'DW_GROUP_CARD'];
  // 需要特殊处理的组件类型 卡片 操作菜单
  specialComponentTypes = [...this.cardTypes, 'DW_AUTO_FILL'];

  // 当前选择的组件
  selectedComponent: any = null;

  get historyFlag(): string {
    return `${this.dslConfig.pageCode}-${this.dataState}`;
  }

  // 迁移逻辑
  get lang(): string {
    return this.languageService?.currentLanguage || 'zh_CN';
  }

  get advancedPanelTop(): number {
    const nav = document.querySelector('.breadcrumb-nav');
    const top = nav?.getBoundingClientRect()?.height || 0;
    return top;
  }
  // 数据录入特殊处理
  get showEditPageOptions(): boolean {
    return sessionStorage.getItem('PAGETYPE') === 'listSetting' && this.dslConfig?.needJudgePageType;
  }
  // 数据录入特殊处理
  get showBrowsePageOptions(): boolean {
    return sessionStorage.getItem('PAGETYPE') === 'editPage' && this.dslConfig?.needJudgePageType;
  }

  @ViewChild('dropTypeSelectModalTitle')
  dropTypeSelectModalTitleTpl: TemplateRef<any>;
  @ViewChild('dropTypeSelectModalContent')
  dropTypeSelectModalContentTpl: TemplateRef<any>;
  @ViewChild('formBuilder') formBuilder: DwDynamicFormBuilderComponent;
  @Input() isCustom: Boolean = false;
  @Input() pageData: any = {};
  @Output() handleSwitchCustomBack: EventEmitter<any> = new EventEmitter();
  @Output() handleChangeOperateCustomTemplateCallBack: EventEmitter<any> = new EventEmitter();

  @Input() showOperateCustomTemplate: boolean = true;
  @Input() fieldData: any = {};
  @Input() masterFromDataSourceName;
  @Input() fieldTree = [];
  @Input() renderDslData; // 渲染用dsl数据
  @Input() rules = []; // 规则列表
  @Input() dslConfig; // config
  @Input() dataState = 'default';
  @Input() dataSources = []; // 数据源列表
  @Input() dataSourceName = ''; // 数据源名称
  @Input() originalDataSources = {}; // 数据源（未经处理的与dataSources数据结构不同）
  @Input() selectFiledItems = []; // 左侧选择的字段
  @Input() submitActionsPcBase = []; // pc的按钮
  @Input() submitActionsBase = []; // 基础按钮
  @Input() extendHeader: any;

  @Output() readonly dslDataChange: EventEmitter<any> = new EventEmitter(); // dsl 数据更新回调
  @Output() readonly propertiesOpenChange: EventEmitter<boolean> = new EventEmitter(); // 属性设置面板是否打开状态改变的回调
  @Output() readonly selectComponentChange: EventEmitter<any> = new EventEmitter(); // 选中组件的回调
  @Output() readonly isFormInitReadyChange: EventEmitter<any> = new EventEmitter(); // form是否初始化完成状态改变的回调
  @Output() readonly formInstanceChange: EventEmitter<any> = new EventEmitter(); // 暴露formInstance，主要用于获取比如表单验证状态等
  @Output() readonly formIsReady: EventEmitter<boolean> = new EventEmitter(); // formio是否渲染完

  get appCode(): string {
    return this.appService.selectedApp?.code;
  }

  constructor(
    private workDesignService: WorkDesignService,
    private cd: ChangeDetectorRef,
    public service: DslWorkDesignMobileService,
    private injector: Injector,
    private pageSizeService: PageSizeService,
    private languageService: LocaleService,
    private translate: TranslateService,
    private interfaceConversionService: InterfaceConversionService,
    private zone: NgZone,
    private message: NzMessageService,
    private modalService: NzModalService,
    private formHistory: JsonUndoRedoService,
    private dslFormRenderService: DslFormRenderMobileService,
    private dslFormRenderShareService: DslFormRenderShareMobileService,
    private modal: AdModalService,
    protected crudConvert: CRUDDslConvertService,

    private userService: AdUserService,
    protected configService: SystemConfigService,
    public appService: AppService,
    public dslFormRenderRequestService: DslFormRenderRequestService,
    public workSidebarMobileService: WorkSidebarMobileService,
  ) {
    this.defaultBuilderOptions = cloneDeep(defaultBuilderOptions);
    this.selectComponentCallback = (component: any) => {
      this.handleSelectComponent.bind(this)(component);
      this.moduleData = {};
      this.tempCardCId = '';
      switch (component.type) {
        case 'DW_BUTTON':
        case 'DW_BUTTON_JUMP':
          this.buttonParent = 'BUTTON_GROUP';
          this.tempCId = component.customOptions.cId;
          const componentTypekey = component.type;
          const submitAction = cloneDeep(component.customOptions.submitAction) || {};
          this.handleSelectButton('submitAction', { ...submitAction, componentTypekey });
          break;
        case 'DW_CARD':
        case 'DW_ZTB_VIEW_LIST':
        case 'DW_GROUP_CARD':
          this.tempCardCId = component.customOptions.cId;
      }
    };
  }

  ngOnInit(): void {
    this.init();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('masterFromDataSourceName')) {
      this.defaultBuilderOptions['dslDataMobile']['masterFromDataSourceName'] = this.masterFromDataSourceName;
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
      }
    }
    if (Object.keys(changes).includes('fieldTree')) {
      this.defaultBuilderOptions['dslData']['fieldTreeData'] = this.fieldTree;
      if (this.fieldTree?.length) {
        this.defaultBuilderOptions['dslDataMobile']['allField'] = this.convertFieldTreeData(this.fieldTree);
      }
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
      }
    }
    if (Object.keys(changes).includes('dataSources')) {
      this.defaultBuilderOptions['dslDataMobile']['dataSources'] = this.dataSources;
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
      }
    }

    // 给formio增加数据源dataSourceList字段和当前数据源
    if (Object.keys(changes).includes('originalDataSources')) {
      this.defaultBuilderOptions['dslDataMobile']['dataSourceList'] = this.originalDataSources;
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
      }
    }
    // 数据源名称
    if (Object.keys(changes).includes('dataSourceName')) {
      this.defaultBuilderOptions['dslDataMobile']['currentDataSource'] = this.originalDataSources[this.dataSourceName];
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
      }
    }

    if (Object.keys(changes).includes('rules')) {
      this.updateRuleToFormIo();
    }

    if (Object.keys(changes).includes('pageData')) {
      this.defaultBuilderOptions['dslDataMobile']['showSingleHeadFrom'] =
        changes.pageData.currentValue?.showSingleHeadFrom;
      this.dslFormRenderService.setShowSingleHeadFrom(changes.pageData.currentValue?.showSingleHeadFrom);
    }
    // dsl数据
    if (Object.keys(changes).includes('renderDslData')) {
      this.hooks = changes.renderDslData.currentValue?.hooks || [];
      // this.dslFormRenderService.uibotDslChange(this.renderDslData);
      const originForm = this.dslFormRenderService.converToFormioJson(this.renderDslData, {
        handleOperationType: this.dslConfig.formCenterConfig.applyToField,
        mergeCellEnable: this.dslConfig.formCenterConfig.mergeCellEnable,
        code: this.service?.workData?.code,
      });
      this.defaultBuilderOptions.dslDataMobile.formJson = originForm;
      // if (this.renderDslData.renderStateManagementData) {
      //   originForm = this.dslFormRenderService.dealManageStatus(
      //     originForm,
      //     this.renderDslData.renderStateManagementData,
      //   );
      // }
      //
      // this.crudConvert.mountRulesToComponent(originForm.components, this.rules);

      if (this.formInitLoading) {
        this.formTemp = originForm;
      } else {
        this._formInstance?.resetPropertiesContent();
        this.form = originForm;
      }
    }
    // 外层业务入口config数据
    if (Object.keys(changes).includes('dslConfig')) {
      this.setDslConfigToFormio();
    }

    if (Object.keys(changes).includes('fieldData')) {
      if (!isEmpty(this.fieldData)) {
        this.defaultBuilderOptions['dslDataMobile']['dataSources_flatten'] = flattenData(this.fieldData);
      }
    }
    // 选择字段到画布中
    if (Object.keys(changes).includes('selectFiledItems') && changes.selectFiledItems.currentValue?.length) {
      const allFields = this.fieldData?.field;
      const formioJson = this.dslFormRenderService.toConvertMobileJson(
        changes.selectFiledItems.currentValue,
        allFields,
      );
      // mobile将此处处理：获取当前是第几页 然后把组件塞到第几页
      this.selectFieldToForm(formioJson);
    }
    this.setSubmitActionsInfoToFormio(changes);
  }
  setDslConfigToFormio() {
    const dslConfig = this.dslConfig || {};
    const { formCenterConfig } = dslConfig;
    this.interfaceConversionService.isButtonVisible = formCenterConfig.isShowCustomButton;
    this.defaultBuilderOptions['dslDataMobile']['pageCode'] = dslConfig.pageCode;
    this.defaultBuilderOptions['dslDataMobile']['isNoDataState'] = formCenterConfig.isNoDataState;
    if (formCenterConfig.formAreaPaddingTop) {
      this.defaultBuilderOptions['dslDataMobile']['formAreaPaddingTop'] = formCenterConfig.formAreaPaddingTop;
    }
    if (formCenterConfig.formAreaMarginTop) {
      this.defaultBuilderOptions['dslDataMobile']['formAreaMarginTop'] = formCenterConfig.formAreaMarginTop;
    }
    if (this.dslConfig.advancePanel) {
      this.defaultBuilderOptions['dslData']['advancePanel'] = dslConfig.advancePanel;
    }
    this.defaultBuilderOptions['dslDataMobile']['dataEntryDefaultOptionsCode'] =
      formCenterConfig.dataEntryDefaultOptionsCode;
    this.defaultBuilderOptions['dslData']['isHideRule'] = !this.dslConfig.sidebarConfig?.ModuleCodeList.includes(
      SubModuleEnum.Rule,
    );
    this.defaultBuilderOptions['dslDataMobile']['mergeCellEnable'] = formCenterConfig.mergeCellEnable;
    this.defaultBuilderOptions['dslDataMobile']['needIsFocusDisplay'] = !!formCenterConfig.needIsFocusDisplay;
    if (Reflect.has(formCenterConfig ?? {}, 'serachViewDisplay')) {
      this.defaultBuilderOptions['dslData']['serachViewDisplay'] = formCenterConfig.serachViewDisplay;
    }
    // if (Reflect.has(this.dslConfig.formCenterConfig ?? {}, 'serachViewDisplay')) {
    //   this.defaultBuilderOptions['dslData']['serachViewDisplay'] = this.dslConfig.formCenterConfig.serachViewDisplay;
    // }
  }
  /**
   * 设置按钮信息
   */
  setSubmitActionsInfoToFormio(changes) {
    // 基础按钮数据
    if (Object.keys(changes).includes('submitActionsBase')) {
      this.defaultBuilderOptions['dslDataMobile']['submitActionsNewBase'] = this.submitActionsBase;
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
        // 数据录入特殊处理 按钮动态改变 需重新更新画布
        if (this.defaultBuilderOptions?.dslDataMobile?.designMode === EDesignMode.MDENTER) {
          this.formRebuild();
        }
      }
    }
    // pc按钮
    if (Object.keys(changes).includes('submitActionsPcBase')) {
      this.defaultBuilderOptions['dslDataMobile']['submitActionsBase'] = this.submitActionsPcBase;
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
      }
    }
  }

  /**
   * 平铺FieldTree数据
   * @param arr
   */
  convertFieldTreeData(arr) {
    const arrTem = [];
    convertData(arr);
    function convertData(arr) {
      arr.forEach((item) => {
        arrTem.push(item.data_name);
        if (item.field && item.field.length) {
          convertData(item.field);
        }
      });
    }
    return arrTem;
  }

  ngOnDestroy(): void {
    this.buttonGroup$.unsubscribe();
    this.destroy$.next();
    this.destroy$.complete();
    this.dslFormRenderService.resetCacheData();
    this.formHistory.clear();
    this.pageSizeService.setSize(null);
  }

  /**
   * 避免画布多次rebuild
   */
  formRebuild = debounce(() => {
    this.formBuilder?.rebuild();
  }, 100);

  /**
   * 按钮增加和设置
   */
  buttonEventHandler() {
    this.buttonGroup$ = this.service.buttonGroup$.subscribe((res) => {
      const currentTab = this.dslFormRenderService.getCurrentIndex();
      const page = this.form?.components[0]?.components[currentTab];
      const component = this.dslFormRenderService.findDeepButtonGroup('DW_BUTTON_GROUP', page);
      if (res) {
        //   增加按钮
        const title = res?.lang?.title?.zh_CN;
        let type = 'DW_BUTTON';
        if (title === '保存') {
          type = 'DW_BUTTON_SAVE';
        }
        if (title === '终止') {
          type = 'DW_BUTTON_STOP';
        }
        if (!res.isPcBtn) {
          if (!res?.submitType?.schema) {
            // @ts-ignore
            res?.submitType?.schema = this.dataSourceName;
          }
          // @ts-ignore
          res?.isMobileCheckButton = true;
        }
        const buttonComponent = {
          type: 'DW_BUTTON',
          customOptions: {
            buttonType: type,
            title: {
              label: res?.lang?.title[this.currentLan],
              ...res?.lang?.title,
            },
            name: {
              label: res?.lang?.title[this.currentLan],
              ...res?.lang?.title,
            },
            submitAction: res,
            componentId: UUID.UUID(),
          },
          key: `button${new Date().getTime().toString(32)}`,
        };
        if (!component?.components?.length) {
          component['components'] = [];
        }
        component?.components?.push(buttonComponent);
        this.formBuilder.rebuild();
      } else {
        // 点击设置
        const submitActions = component?.components.map((item) => {
          // hook需要的组件信息同时删除hook不需要的数据
          const _customOptions = cloneDeep(item.customOptions) || {};
          if (_customOptions.hasOwnProperty('submitAction')) {
            delete _customOptions.submitAction;
          }
          if (_customOptions.hasOwnProperty('rules')) {
            delete _customOptions.rules;
          }
          if (_customOptions.hasOwnProperty('action')) {
            delete _customOptions.action;
          }
          const buttonCustomOptions = {
            type: item.type,
            customOptions: {
              ..._customOptions,
            },
          };
          return {
            ...item.customOptions?.submitAction,
            buttonCustomOptions,
          };
        });
        this.handleSelectButton('submitActions', cloneDeep(submitActions));
      }
    });
  }
  /**
   * 设置规则数据
   */
  ruleEventHandler() {
    this.workDesignService.ruleListData$.subscribe((data: any) => {
      this.defaultBuilderOptions['dslDataMobile']['ruleList'] = data;
      this.buildOptions();
      // @ts-ignore
      if (window.mobileInstance) {
        // @ts-ignore
        mobileInstance?.emit('mobile-formio-rule-refresh', data);
      }
    });
  }
  /**
   * 数据状态处理
   */
  dataStatesEventHandler() {
    this.service.dataStates$.subscribe((data: any) => {
      this.syncFormJsonSetUpFrom();
      if (data?.type === 'SelectedDataState') {
        this.defaultBuilderOptions['dslDataMobile']['selectedDataState'] = data.data;
        this.moduleData = {};
      } else {
        this.defaultBuilderOptions['dslDataMobile']['dataStates'] = data.data.dataStates;
        const selectedDataState = this.service._selectedDataState$.getValue();
        const selectDataStateCode = data.data.dataStates?.find((item) => {
          return item.code == selectedDataState;
        });
        this.defaultBuilderOptions['dslDataMobile']['selectedDataState'] = !!selectDataStateCode
          ? selectedDataState
          : data.data.dataStates[0]?.code;
        this.defaultBuilderOptions['dslDataMobile']['showSingleHeadFrom'] = data?.showSingleHeadFrom;
        this.handleSingleHeadFrom(data?.showSingleHeadFrom);
        this.formRebuild();
      }
      this.buildOptions();
    });
  }
  /**
   * 给formio增加共享数据
   */
  addFormIoOptions() {
    this.buttonEventHandler();
    this.ruleEventHandler();
    this.dataStatesEventHandler();
    // 不同设计器入口区分
    this.defaultBuilderOptions['dslDataMobile']['designMode'] = this.dslConfig?.designMode;
    this.defaultBuilderOptions['dslDataMobile']['fieldMode'] = this.dslConfig?.sidebarConfig?.fieldMode;
    // 是否全部隐藏状态栏
    this.defaultBuilderOptions['dslDataMobile']['hiddeAllHeaderInfo'] =
      this.dslConfig?.formCenterConfig?.hiddeAllHeaderInfo;
    // 状态栏配置信息
    this.defaultBuilderOptions['dslDataMobile']['headerStatusOpts'] =
      this.dslConfig?.formCenterConfig?.headerStatusOpts;
    // workData
    this.defaultBuilderOptions['dslDataMobile']['workData'] = this.service.workData;
    this.defaultBuilderOptions['dslDataMobile']['pageCode'] = this.service.pageCode;
    this.defaultBuilderOptions['dslDataMobile']['isCustom'] = this.isCustom;
    this.defaultBuilderOptions['dslDataMobile']['showOperateCustomTemplate'] = this.showOperateCustomTemplate;

    // 数据状态
    this.defaultBuilderOptions['dslDataMobile']['dataStates'] = this.pageData?.dataStates;
    this.defaultBuilderOptions['dslDataMobile']['selectedDataState'] = this.service._selectedDataState$.getValue();
    // 是否开启单双档模式
    this.defaultBuilderOptions['dslDataMobile']['showSingleHeadFrom'] = this.pageData?.showSingleHeadFrom;
    // 随心控用
    if (this.service.config.sidebarConfig?.offline) {
      this.defaultBuilderOptions['dslDataMobile']['fieldData'] = this.fieldData;
    }
    this.buildOptions();
  }
  // 单双档模式同步setUpFrom的FormJson数据
  syncFormJsonSetUpFrom() {
    let setUpForm = null;
    if (this.pageData?.showSingleHeadFrom) {
      setUpForm = this.form?.components[0]?.components?.find((item) => item.type === 'set-up-form');
    }
    this.dslFormRenderService.setNewSetUpForm(setUpForm);
  }
  // 单双档模式开关处理
  handleSingleHeadFrom(visible) {
    this.form?.components[0]?.components.forEach((com) => {
      if (com.type === 'set-up-form') {
        // @ts-ignore
        com?.customOptions?.visible = visible;
        // @ts-ignore
        com?.customOptions?.visibleStorage = visible;
        // com.hidden = !visible;
      }
    });
  }
  buildOptions() {
    if (this.builderOptions) {
      this.builderOptions = this.defaultBuilderOptions;
    }
  }
  get currentLan() {
    return this.languageService.currentLanguage;
  }
  init(): void {
    this.handleTokenInfo();
    // this.interfaceConversionService.isButtonVisible = true; // 开启自定义界面的设置按钮
    this.formInitLoading = true;
    // 随心控特殊处理
    if (!this.service.config.sidebarConfig?.offline) {
      this.refreshRule();
    }
    // 指定默認的版面寬度
    // if (!this.pageSizeService.currentPageSize) {
    this.pageSizeService.setSize(PageSize.MOBILE);
    // 移动端独有分类
    // this.defaultBuilderOptions.builder = MobileBuilderGroup;
    this.addFormIoOptions();
    // this.taskWorkDesignMobileService.dataSourceListSubject$.next();
    this.pageSizeService.sizeChanged$.pipe(takeUntil(this.destroy$)).subscribe(this.changePageSize());
    // 切換語系時
    this.languageService.language$.pipe(takeUntil(this.destroy$)).subscribe((res) => {
      const rebuild = !!this.builderOptions;

      const i18n = this.translate.translations;
      const cimodeI18n = {};
      for (const lang in i18n) {
        cimodeI18n[lang.replace('_', '-')] = i18n[lang];
      }
      this.defaultBuilderOptions.language = res.next.replace('_', '-');
      this.defaultBuilderOptions.i18n = cimodeI18n;

      this.builderOptions = this.defaultBuilderOptions;

      if (rebuild) {
        this.formBuilder?.rebuild();
      }
    });

    this.formHistory.undoEvent$.pipe(takeUntil(this.destroy$)).subscribe(() => this.undo());
    this.formHistory.redoEvent$.pipe(takeUntil(this.destroy$)).subscribe(() => this.redo());
  }
  refreshRuleData() {
    if (this.service.activeModule === 'rule') {
      this.workDesignService.refreshRulesSubject$.next();
    } else {
      this.refreshRule();
    }
  }
  /**
   * 初始化规则
   */
  refreshRule() {
    // @ts-ignore
    const { code, category: type, application } = this.service.workData || {};
    // @ts-ignore
    const applicationCode = application || this.service.workData?.data?.application;
    const param = {
      applicationCode,
      code,
      type,
      extendHeader: this.extendHeader,
    };
    this.workSidebarMobileService.loadWorkDesign(param).subscribe((res) => {
      if (res.code === 0) {
        res.data?.rule.forEach((item) => {
          if (!Object.keys(item.content || {}).length) {
            item.content = item.contentMobile;
          }
        });
        this.workDesignService.ruleListData = res.data?.rule;
      }
    });
  }
  /**
   * 获取解决方案/用户权限信息，塞到缓存，为配合模型驱动统一代码
   * todo 后期优化
   */
  handleTokenInfo() {
    const {
      userInfo: { iamToken },
    } = this.userService as any;
    sessionStorage.setItem(SESSIONSTORAGE_KEY_CONSTANT.lcdpUserToken, iamToken);
    this.configService.get('appToken').subscribe((appToken) => {
      sessionStorage.setItem(SESSIONSTORAGE_KEY_CONSTANT.lcdpAppToken, appToken);
    });
    sessionStorage.setItem(SESSIONSTORAGE_KEY_CONSTANT.application, this.appService?.selectedApp?.code);
  }
  changePageSize(): any {
    return (size) => {
      FormEditorFramework.bootstrap('common', this.injector, true);
      if (size === PageSize.MOBILE) {
        FormEditorFramework.bootstrap('mobile', this.injector, false);
      } else {
        FormEditorFramework.bootstrap('athena-ui', this.injector, false);
      }
      this.formBuilder?.rebuild();
      this.pageSize = size;
    };
  }
  sidebarWidth = '266px';
  syncSidebarWidth(width) {
    if (width) {
      const widthTemp = parseInt(width.slice(0, -2), 10) - 4 + 'px';
      this.zone.run(() => {
        this.sidebarWidth = widthTemp;
      });
    }
  }
  formReady(instance: any): void {
    this.formInitLoading = false;
    this.handleCustomFormEvent(instance);
    instance.ready.then(() => {
      this.updateUibotDslAndSchemaList(instance.form);
      this.updateRuleToFormIo();
      this.formIsReady.emit(true);
      // this.updateDealManageStatus();

      if (!this._isNotSaveHistoryAfterFormReady) {
        this.formHistory.push(instance.form, this.historyFlag);
      } else {
        this._isNotSaveHistoryAfterFormReady = false;
      }

      // this.formIsReady.emit(instance.isLoading);
    });

    this._formInstance = instance;

    this.formInstanceChange.emit(this._formInstance);

    if (this.formTemp) {
      this.form = this.formTemp;
      this.formTemp = null;
      this._isRenderDsl = true;
    }
    instance.on('leftSildebarWidthChange', (width) => {
      this.syncSidebarWidth(width);
    });
    instance.off('selectComponent', this.selectComponentCallback);
    instance.on('selectComponent', this.selectComponentCallback);

    // instance.off('customDrop', this.customDropCallback);
    // instance.on('customDrop', this.customDropCallback);

    /**
     * 若拖拽的來源元素沒有data-type屬性，則會觸發customDrop事件。
     * 處理完element之後，直接調用Formio.onDrop()
     */
    instance.off('customDrop');
    instance.on('customDrop', (source: { element: Element; target: Element; source: Element; sibling: Element }) => {
      this.zone.run(() => {
        this.selectDragTargetType(source.element, source.target, source.source, source.sibling)
          .then((result: any) => {
            this.formBuilder.instance.onDrop(
              result.element,
              result.target,
              result.source,
              result.sibling,
              result.componentInfo,
            );
          })
          .catch(({ message }) => {
            if (message === 'diff') {
              this.message.error(this.translate.instant('dj-拖拽字段非目标容器字段的直接子集！'));
            }
            source.element?.remove();
          });
      });
    });

    instance.off('schemaChange');
    instance.on('schemaChange', () => {
      // this.updateEditFormRules();
    });

    // 点击高级tab
    instance.off('tabClick');
    instance.on('tabClick', (e) => {
      const advancedPanel: any = document.querySelector('[ref="page-design-advanced-panel"]');
      const { panelLevel } = e.target.dataset;

      advancedPanel.setAttribute('data-level', panelLevel);
      advancedPanel.style.display = panelLevel === '0' ? 'none' : 'block';
    });

    this.formJson$?.unsubscribe();
    this.formJson$ = this.dslFormRenderService.formJson$.pipe(takeUntil(this.destroy$)).subscribe((formJson) => {
      this.defaultBuilderOptions.dslDataMobile.formJson = formJson;
      this.form = formJson;
      // @ts-ignore
      if (window.mobileInstance && formJson) {
        // @ts-ignore
        mobileInstance?.emit('mobile-formio-change', formJson);
      }
    });

    // 重新绑定拖拽元素
    instance.emit('initDnd');
    this.resetFormDragEvent$?.unsubscribe();
    this.resetFormDragEvent$ = this.dslFormRenderShareService.resetFormDragEvent$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        instance.emit('initDnd');
      });

    // 初始化时置空被选中的控件
    // this.selectedComponent = null;
    // 隐藏高级面板
    const advancedPanel: any = document.querySelector('[ref="page-design-advanced-panel"]');
    advancedPanel.style.display = 'none';

    instance.off('sendSubscriberMessageOut');
    instance.on('sendSubscriberMessageOut', (data: SendSubscriberMessageData) => {
      const { subscriberKey, subscriberValue } = data;
      if (subscriberKey === 'ruleOutChange$') {
        this.dslFormRenderShareService.rulesChangeEvent(subscriberValue);
      }
    });
  }
  /**
   * 自定义formIO事件
   * @param instance
   */
  handleCustomFormEvent(instance) {
    // @ts-ignore
    window.mobileInstance = instance;
    instance.off('mobile-button-group-setting');
    instance.on('mobile-button-group-setting', () => {
      //  按钮组设置
      this.service.openButtonGroupModal();
    });
    instance.off('mobile-button-group-add');
    instance.on('mobile-button-group-add', (data) => {
      //  按钮组添加
      this.service.addButtonToGroup(data);
    });
    // 头部信息添加
    instance.off('mobile-tip-setting');
    instance.on('mobile-tip-setting', (data) => {
      this.updateTipToMobileForm(data);
    });
    // 刷新左侧列表
    instance.off('mobile-rule-list-refresh');
    instance.on('mobile-rule-list-refresh', () => {
      this.refreshRuleData();
    });
    // 左侧规则改变调取
    instance.off('mobile-rule-change');
    instance.on('mobile-rule-change', (data) => {
      this.dslFormRenderShareService.updateRightComponentRule(data);
    });
    // 数据状态
    instance.off('mobile-status-open');
    instance.on('mobile-status-open', () => {
      this.service.openStateModal();
    });
    instance.off('mobile-status-select');
    instance.on('mobile-status-select', (data) => {
      this.service.changeSelectedDataState(data);
    });
    // 定制
    instance.off('mobile-custom-change');
    instance.on('mobile-custom-change', (data) => {
      if (data) {
        this.modal.confirm({
          nzTitle: this.translate.instant('dj-确认切换为定制界面？'),
          nzContent: this.translate.instant('dj-切换为定制界面将重置当前标准界面的界面代码'),
          nzWrapClassName: 'vertical-center-modal',
          nzOkText: this.translate.instant('dj-确定'),
          nzOnOk: () => {
            this.handleSwitchCustomBack.emit(data);
            // 定制
            this.defaultBuilderOptions['dslDataMobile']['isCustom'] = data;
            this.buildOptions();
            this.formBuilder.rebuild();
          },
          nzOnCancel: () => {},
        });
      } else {
        this.handleSwitchCustomBack.emit(data);
        // 定制
        this.defaultBuilderOptions['dslDataMobile']['isCustom'] = data;
        this.buildOptions();
        this.formBuilder.rebuild();
      }
    });
    //   按钮状态监听
    instance.off('mobile-buttonList-status-change');
    instance.on('mobile-buttonList-status-change', (res) => {
      // this.cardOpenBtn$.next(res);
      this.buttonParent = 'CARD';
      if (res.show) {
        this.service.buttonGroupModal = true;
        this.handleSelectButton('submitActions', res.buttonList, 0, res.buttonLayoutType);
      } else {
        this.service.buttonGroupModal = false;
        this.moduleData = {};
      }
    });
    instance.off('mobile-custom-template-change');
    instance.on('mobile-custom-template-change', (show) => {
      this.showCustomTemplatePanel = show;
    });
  }
  /**
   * 更新提示信息到formIo
   */
  updateTipToMobileForm(data) {
    // @ts-ignore
    this.form?.components[0].customOptions?.tipSetting = data;
    this.updateUibotDslAndSchemaList(this.form);
  }
  // 设置界面 选择左侧按钮
  handleSelectSettingBtn(btnComponent) {
    this.selectedComponent = btnComponent;
  }
  handleSelectButton(type, data: any, index = 0, buttonLayoutType?: number): void {
    this.moduleData = {
      type: type,
      data: data,
      formioJson: cloneDeep(this.form),
      buttonLayoutType: buttonLayoutType + '',
      needHookTab: this.buttonParent !== 'CARD' && type === 'submitActions',
      componentType: this.buttonParent,
    };
    if (type === 'submitActions') {
      this.moduleData.index = index;
    } else {
      this.service.buttonGroupModal = true;
    }
    this.cd.detectChanges();
  }
  initHistory(): void {
    this.formHistory.addTab(this.historyFlag);
    if (!this.formHistory?.cache[this.historyFlag]?.document) {
      this.formHistory.init(
        {
          components: [],
        },
        this.historyFlag,
      );
    }
    this.formHistory.updateStatus(this.historyFlag);
  }

  handleSelectComponent(component): void {
    // 根据被选组件有无path显示或隐藏tabs和高级面板
    const tabs: any = document.querySelector('[ref="propTabs"]');
    const advancedPanel: any = document.querySelector('[ref="page-design-advanced-panel"]');

    if (component.customOptions) {
      // TODOdh 移动不需要展示高级面板tab的组件有哪些？
      // if (!!tabs && !Object.keys(ComponentCustomHooks).includes(component.customOptions?.type)) {
      //   tabs.style.display = 'none';
      // }
      advancedPanel.setAttribute('data-level', 0);
      advancedPanel.style.display = 'none'; // 赋值当前选中的组件
      this.selectedComponent = component;
    }
    this.selectComponentChange.emit(component);
  }

  componentChanged(event: any): void {
    // formJson数据
    const form = cloneDeep(event?.builder?.form);
    if (['addComponent', 'saveComponent', 'deleteComponent'].indexOf(event.type) > -1) {
      if (['addComponent', 'saveComponent'].includes(event.type)) {
        const componentDom: any = document.getElementById(event.component.id);
        if (componentDom) {
          componentDom.click();
        }
      }

      if (event.type === 'deleteComponent') {
        this.handleDelHooks(event);
        if (event.component.type === 'set-up-page' || event.component.type === 'set-up-drawer') {
          form.components[0].currentTab = 1;
          form.components[0].currentPageRecord = 1;
          form.components[0].components.forEach((com, index) => {
            if (index === 1) {
              com.hidden = false;
            } else {
              com.hidden = true;
            }
          });
          this.formBuilder.rebuild();
        }
      }
      this.updateUibotDslAndSchemaList(form);
    }
  }
  /**
   * 获取被选中组件及其包含组件的id
   */
  getComponentIds(component: any): any[] {
    const componentIds = [];
    let recursive = (component) => {
      if (component.customOptions?.componentId) {
        componentIds.push(component.customOptions.componentId);
      }
      if (component.components?.length) {
        component.components.forEach((item) => {
          recursive(item);
        });
      }
    };

    recursive(component);
    recursive = null;
    return componentIds;
  }
  /**
   * 删除组件对应的hooks
   */
  handleDelHooks(event) {
    const componentIds = this.getComponentIds(event.component);
    const hooks = this.hooks.filter((hook) => !componentIds.includes(hook.eventSource));
    this.handleHooksChange(hooks);
    // 隐藏tabs和高级面板
    const advancedPanel: any = document.querySelector('[ref="page-design-advanced-panel"]');
    advancedPanel.style.display = 'none';
  }
  /**
   * formJson转成DSL
   * @param form
   */
  updateUibotDslAndSchemaList(form: any): void {
    this.appliedComponents = this.getSchemaList(form.components);
    this.dslFormRenderService.formSchemaListChange(this.appliedComponents);
    const cloneForm = cloneDeep(form);
    const uibotJson = this.dslFormRenderService.converToUibotJson(cloneForm);
    uibotJson.hooks = this.hooks;
    this.dslFormRenderService.uibotDslChange(uibotJson);
    this.dslFormRenderService.formChange(form);
    this.dslDataChange.emit(uibotJson);
  }

  handleSelectAction(e: any): void {
    const { actionId, actionType } = e;
    this.dslFormRenderRequestService.loadActionField(actionId).subscribe(
      (res) => {
        if (res.code === 0) {
          const language = this.languageService?.currentLanguage || 'zh_CN';
          let dataSources = {};
          if (res.data?.data_name) {
            dataSources = {
              [res.data.data_name]: {
                type: actionType === 'EspAction' ? 'ESP' : 'SD',
                title: res.data.description[language],
                actionId,
                serviceName: actionId.includes('esp_') ? actionId.replaceAll('esp_', '') : actionId,
                masterData: res.data.data_name,
                dataKeys: [],
                actionParams: [],
                metadataFields: [],
                notArray: false,
              },
            };
          }
          this.actionModal = false;
          const result = this.source;
          result.element.setAttribute('data-dataSources', JSON.stringify(dataSources));
          result.element.setAttribute('data-submitActions', JSON.stringify(submitActions));
          this.formBuilder.instance.onDrop(result.element, result.target, result.source, result.sibling);
        } else {
          this.source.element?.remove();
        }
      },
      () => {
        this.source.element?.remove();
      },
    );
  }

  closeSelectAction(): void {
    this.source.element?.remove();
    this.actionModal = false;
  }

  async selectDragTargetType(element: any, target: any, source: any, sibling: any): Promise<{}> {
    if (!element) {
      throw new Error('diff');
    }
    // 获取拖拽组件跟目标容器的属性
    const {
      parentId,
      _category,
      targetType,
      dataType,
      defaultFormioDataType,
      parentCategory,
      title,
      path,
      schema,
      disabled,
      targetCode,
    } = getElementInfo(element, target, this.languageService.currentLanguage) || {};

    // 可以选择的移动组件列表
    this.componentList = setMobileComponentList({ data_type: dataType });
    this.componentType = this.componentList[0].type;

    // 根结点类型
    const rootCategoryList = ['array', 'object'];

    // 是否可拖拽
    const canDrop = judgeCanDrop({ targetType, targetCode, parentId, path });
    if (!canDrop) {
      throw new Error('diff');
    }

    const dropData = { element, target, source, sibling };

    const info = {
      group: Group.dw,
      type: this.componentType,
      dataType: defaultFormioDataType,
      schema,
      title,
      disabled,
      path,
      parentCategory,
    };
    let _info;
    if (rootCategoryList.includes(_category)) {
      // 表单或者卡片
      // const tableData = this.fieldData[].get(schema);
      const tableData = this.getTabData() || {};
      const fileds = tableData[schema];
      _info = generateRootComponent({ ...info, _category }, fileds, this.languageService.currentLanguage);
      // return { ...dropData, componentInfo: _info };
    } else {
      const fieldLevelType = getFieldLevelType(path, rootCategoryList, _category);
      if (disabled) {
        // 系统字段 不需要选择组件
        _info = generateComponent({ ...info, parentId, targetType }, fieldLevelType);
        // return { ...dropData, componentInfo: _info };
      } else {
        // 选择组件
        return await this.showTypeSelectModal({ ...info, parentId, targetType }, fieldLevelType, dropData);
      }
    }
    return { ...dropData, componentInfo: _info };
  }
  buildDataMap(data, dataMap) {
    if (!data) {
      return;
    }
    const { is_array, field, data_name, data_type } = data;
    if (is_array || data_type === 'object') {
      dataMap[data_name] = field;
    }
    if (field?.length) {
      field.forEach((item) => {
        this.buildDataMap(item, dataMap);
      });
    }
  }
  getTabData() {
    const dataMap = {};
    this.buildDataMap(this.fieldData, dataMap);
    return dataMap;
  }
  async showTypeSelectModal(info: any, fieldLevelType: any, dropData: any): Promise<{}> {
    return new Promise((resolve, reject) => {
      this.modalService.create({
        nzTitle: this.dropTypeSelectModalTitleTpl,
        nzContent: this.dropTypeSelectModalContentTpl,
        nzOnOk: () => {
          const _info = generateComponent({ ...info, type: this.componentType }, fieldLevelType);
          resolve({ ...dropData, componentInfo: _info });
        },
        nzOnCancel: () => {
          reject(new Error('cancel'));
        },
      });
    });
  }

  // 撤销
  undo(): void {
    this.dslFormRenderService.formChange(this.formHistory.undo(this._formInstance?.form, this.historyFlag));
    this._isNotSaveHistoryAfterFormReady = true;
    this._formInstance?.emit('cancelComponent');
  }

  // 恢复
  redo(): void {
    this.dslFormRenderService.formChange(this.formHistory.redo(this._formInstance?.form, this.historyFlag));
    this._isNotSaveHistoryAfterFormReady = true;
    this._formInstance?.emit('cancelComponent');
  }

  getSchemaList(components: any): any[] {
    let componentList = [];
    components?.forEach((item) => {
      // eslint-disable-next-line max-len
      if (item.customOptions?.id && item.customOptions?.type !== 'curd-block') {
        // eslint-disable-next-line max-len
        componentList.push({
          label: item.customOptions.title,
          value: item.customOptions?.id || item.customOptions?.schema,
          path: item.customOptions?.path,
          fullPath: item.customOptions?.mobilePath,
          type: item.type,
          editable: item.customOptions?.editable,
          id: item.customOptions?.componentId,
        });
      }
      if (item.components) {
        componentList = [...componentList, ...this.getSchemaList(item.components)];
      }
    });
    return componentList;
  }

  isPropertiesOpenChange(isOpen: any): void {
    this.isPropertiesOpen = isOpen;
    this.propertiesOpenChange.emit(isOpen);
  }

  /**
   * hooks变化后的回调
   * @param hooks
   */
  handleHooksChange(hooks: any): void {
    this.hooks = hooks;
    this.updateUibotDslAndSchemaList(this._formInstance.form);
  }

  updateRuleToFormIo(): void {
    const rules = !this.dslConfig?.sidebarConfig?.ModuleCodeList.includes(SubModuleEnum.Rule) ? [] : this.rules;

    this._formInstance?.emit('sendSubscriberMessageIn', {
      subscriberKey: 'ruleInChange$',
      subscriberValue: rules ?? [],
    } as SendSubscriberMessageData);
  }

  handleChangeSubmit(callback) {
    const selectedDataState = this.service._selectedDataState$.getValue();
    if (this.buttonParent === 'CARD') {
      this.dslFormRenderService.changeNewCardSubmit(
        selectedDataState,
        callback.data,
        this.tempCardCId,
        callback.buttonLayoutType,
      );
      (window as any).mobileInstance.emit('mobile-buttonList-change', callback);
    } else {
      this.dslFormRenderService.changeSubmitAction(selectedDataState, callback.data, this.tempCId);
    }
    this.updateUibotDslAndSchemaList(this.form);
    (window as any).mobileInstance.emit('mobile-buttons-change', this.form);
    // @ts-ignore
    // window.mobileInstance?.emit('mobile-formio-change', this.form);
  }
  /**
   * 选择字段后初始化formio画布
   * @param form
   */
  selectFieldToForm(form: any) {
    this.dslFormRenderService.transSelectFieldToComponent(form.components[0]);
    // 当前位置
    const currentTab = this.dslFormRenderService.getCurrentIndex();
    // 当前容器组件
    const pageComponents = this.form.components[0].components;
    if (pageComponents[currentTab]?.type === 'set-up-drawer') {
      pageComponents[currentTab].components[0].components.length = 0;
      pageComponents[currentTab].components[1].components.length = 0;
      pageComponents[currentTab].components[0]?.components.push(form.components[0]);
    } else {
      pageComponents[currentTab].components[0].components.length = 0;
      pageComponents[currentTab].components[0]?.components.push(form.components[0]);
    }
    this.updateUibotDslAndSchemaList(this.form);
    this.formRebuild();
  }
  //   随心控解决方案特殊处理
  // 查询字段
  // handleLoadField(): void {
  //   const res = this.fieldSetService.fieldData;
  //   const fields = res.data?.field || [];
  //   const data = {
  //     ...(res.data || {}),
  //     field: fields,
  //   };
  //   this.fieldData = this.handleFormatField(data, 'field');
  // }
  // 格式化数据
  // handleFormatField(data: any, childMark: string): any[] {
  //   const jsonNotArray = ['true', true].includes(this.pageSelectedSource?.json?.notArray);
  //   const appData = cloneDeep((childMark === 'field' ? [data] : data) || []);
  //   const format = (origin, parent, root, target) => {
  //     return (origin || []).map((d) => {
  //       const currentTarget = target ? `${target}.${parent}` : parent;
  //       // 这里还是要根据后台的data_type =>formio组件默认的dataType (defaultFormioDataType)
  //       const returnVal = {
  //         ...d,
  //         fieldId: d.data_name,
  //         category: d.is_array ? 'array' : d.data_type,
  //         key: d.data_name,
  //         title: d.data_name,
  //         parentId: parent,
  //         target: currentTarget,
  //         root,
  //         editable: !d.isSystem,
  //       };
  //       if (d[childMark]?.length > 0) {
  //         returnVal['children'] = format(d[childMark], d.data_name, root, currentTarget);
  //         returnVal['expanded'] = true;
  //       } else {
  //         returnVal['isLeaf'] = true;
  //         // 是叶子节点的给添加一个defaultFormioDataType属性
  //         returnVal['defaultFormioDataType'] = getDefaultFormioDataTypeByFormioCompType(
  //           getComponentType(returnVal),
  //           returnVal,
  //         );
  //       }
  //       return returnVal;
  //     });
  //   };
  //   appData.forEach((d) => {
  //     d.key = d.data_name;
  //     d.title = d.data_name;
  //     d.fieldId = d.data_name;
  //     d.category = d.is_array && !jsonNotArray ? 'array' : d.data_type;
  //     d.target = '';
  //     d.expanded = true;
  //     d.children = format(d[childMark], d['data_name'], d['data_name'], '');
  //   });
  //   return appData;
  // }

  handleChangeOperateCustomTemplate(event: any): void {
    this.handleChangeOperateCustomTemplateCallBack.emit(event);
  }
}
