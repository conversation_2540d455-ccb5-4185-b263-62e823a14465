import {
  Component,
  OnInit,
  EventEmitter,
  ViewChild,
  Injector,
  ViewEncapsulation,
  Input,
  SimpleChanges,
  Output,
  NgZone,
  OnDestroy,
  TemplateRef,
  OnChanges,
  ElementRef,
} from '@angular/core';
import {
  DwDynamicFormBuilderComponent,
  FormEditorFramework,
  InterfaceConversionService,
  JsonUndoRedoService,
  // IS_BUTTON_VISIBLE,
} from '@webdpt/form-editor';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import {
  ATHENA_TYPES,
  PageSize,
  PageSizeService,
  CRUDDslConvertService,
  AthenaDataType,
  IsvCustomService,
  IsvPackageDataAllInfo,
} from '@webdpt/form-editor-components';

import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { TranslateService } from '@ngx-translate/core';
import { defaultBuilderOptions, submitActions } from './dsl-form-render.config';
import { SendSubscriberMessageData, DslBuilderOptions } from './dsl-form-render.type';

import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { cloneDeep, isEqual, last, lowerCase, debounce } from 'lodash';
import { DslFormRenderService } from './service/dsl-form-render.service';
import { DslFormRenderShareService } from './service/dsl-form-render-share.service';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';

import { isEmpty, isNone } from 'common/utils/core.utils';
import { flattenData, getRulesBySchemaPath } from './utils/tools';

import { AdUserService } from 'pages/login/service/user.service';
import { SESSIONSTORAGE_KEY_CONSTANT } from 'common/config/SessionStorageKeyConstant';
import { AppService } from 'pages/apps/app.service';
import { SubModuleEnum } from '../dsl-work-design/shared/config/dsl-work-sidebar.config';

import {
  Group,
  IComponentInfo,
  IInfo,
  IAthenaTableConfig,
  createComponentByDrag,
  handleComponentTitle,
  handleComponentInfo,
  handleComponentsInfo,
  handleButtonGroupDefault,
  getComponentList,
  getDeafultByFieldType,
} from './utils/component-tools';
import { ComponentCustomHooks } from '../../../bussiness-components/hooks-script-editor/share/config';
import { DslFormRenderRequestService } from './service/dsl-form-render-request.service';
import { DslWorkDesignService } from '../dsl-work-design/service/dsl-work-design.service';
import { UUID } from 'angular2-uuid';

@Component({
  selector: 'app-dsl-form-render',
  templateUrl: './dsl-form-render.component.html',
  styleUrls: ['./dsl-form-render.component.less', '../../../../assets/css/lcdpSty/style.less'],
  // providers: [DslFormRenderService, { provide: IS_BUTTON_VISIBLE, useValue: true }, InterfaceConversionService],
  providers: [DslFormRenderService, DslFormRenderRequestService, IsvCustomService],
  encapsulation: ViewEncapsulation.None,
})
export class DslFormRenderComponent implements OnInit, OnDestroy, OnChanges {
  defaultBuilderOptions: DslBuilderOptions; // formio 配置项

  form: any = {
    components: [],
  }; // 画布渲染的formio数据

  formTemp = null; // 在form初始化完成之前临时用于接收form数据，在formReady之后进行消费，主要由于使用空components进行form的初始化速度会很快

  private _formInitLoading = true; // form初始化loading
  private _isRenderDsl = false; // 是否渲染dsl数据
  private _isNotSaveHistoryAfterFormReady = true; // form ready 后是否记录历史
  appliedComponents: any[];
  hooks: any;

  get formInitLoading() {
    return this._formInitLoading;
  }
  set formInitLoading(data: boolean) {
    this._formInitLoading = data;
    this.isFormInitReadyChange.emit(data && this._isRenderDsl);
  }

  destroy$ = new Subject();
  formJson$; // formio数据的订阅
  resetFormDragEvent$; // 重置画布的拖拽注册
  builderOptions: any; // 画布的build参数
  pageSize: PageSize = undefined;
  private _formInstance = null; // formio渲染实例
  dslRuleOutChange$: Subject<any>; // 规则变化向外输出的订阅

  customDropCallback: (source: any) => any; // 自定义拖拽后的回调
  selectComponentCallback: (component: any) => any; // 选中formio组件后的回调

  // action开窗组件 历史逻辑
  actionModal = false;
  actionData = {
    actionId: '',
    actionName: '',
    useApp: 'true',
  };
  source;
  componentList = [];
  componentType = 'INPUT';

  isInterfaceTypeInit = false; // 定制界面的订阅是否初始化

  // 当前选择的组件
  selectedComponent: any = null;

  get historyFlag(): string {
    return `${this.dslConfig.pageCode}-${this.dataState}`;
  }

  // 迁移逻辑
  get lang(): string {
    return this.languageService?.currentLanguage || 'zh_CN';
  }

  get advancedPanelTop(): number {
    const nav = document.querySelector('.breadcrumb-nav');
    const top = nav?.getBoundingClientRect()?.height || 0;
    return top;
  }
  customHooks: any[];

  // sourceTarget: Element; // 拖拽容器
  get sourceTarget(): Element {
    return this.el?.nativeElement?.querySelector?.('div.drag-container');
  }

  checkEditFormValidDebounce: Function; // 触发EditForm表单验证

  lineOperations: any[] = []; // 临时增加的逻辑，为了防止line类型的operation丢失，在此处暂存，不参与formio渲染

  @ViewChild('dropTypeSelectModalTitle')
  dropTypeSelectModalTitleTpl: TemplateRef<any>;
  @ViewChild('dropTypeSelectModalContent')
  dropTypeSelectModalContentTpl: TemplateRef<any>;
  @ViewChild('formBuilder') formBuilder: DwDynamicFormBuilderComponent;
  @Input() fieldData = {};
  @Input() fieldTree = [];
  @Input() renderDslData; // 渲染用dsl数据
  @Input() rules = []; // 规则列表
  @Input() dslConfig; // config
  @Input() dataState = 'default';
  @Input() dataSources = []; // 数据源列表
  @Input() canCustom: boolean;
  @Input() workServiceData;
  @Input() isvPackageDataList: IsvPackageDataAllInfo[] = []; // isv定制组件信息
  @Input() favouriteCode: string;
  @Input() applicationCodeProxy: string;

  @Output() readonly dslDataChange: EventEmitter<any> = new EventEmitter(); // dsl 数据更新回调
  @Output() readonly changeToCustom: EventEmitter<any> = new EventEmitter(); // 转定制
  @Output() readonly propertiesOpenChange: EventEmitter<boolean> = new EventEmitter(); // 属性设置面板是否打开状态改变的回调
  @Output() readonly selectComponentChange: EventEmitter<any> = new EventEmitter(); // 选中组件的回调
  @Output() readonly isFormInitReadyChange: EventEmitter<any> = new EventEmitter(); // form是否初始化完成状态改变的回调
  @Output() readonly formInstanceChange: EventEmitter<any> = new EventEmitter(); // 暴露formInstance，主要用于获取比如表单验证状态等
  @Output() readonly formIsReady: EventEmitter<any> = new EventEmitter(); // formio是否渲染完

  constructor(
    private injector: Injector,
    private pageSizeService: PageSizeService,
    private languageService: LocaleService,
    private translate: TranslateService,
    private interfaceConversionService: InterfaceConversionService,
    private zone: NgZone,
    private message: NzMessageService,
    private modalService: NzModalService,
    private formHistory: JsonUndoRedoService,
    private dslFormRenderService: DslFormRenderService,
    private dslFormRenderShareService: DslFormRenderShareService,
    private modal: AdModalService,
    protected crudConvert: CRUDDslConvertService,
    private userService: AdUserService,
    protected configService: SystemConfigService,
    public appService: AppService,
    public dslFormRenderRequestService: DslFormRenderRequestService,
    private el: ElementRef,
    private isvCustomService: IsvCustomService,
    private dslWorkDesignService: DslWorkDesignService,
  ) {
    this.defaultBuilderOptions = cloneDeep(defaultBuilderOptions);
    this.customDropCallback = (source: any) => {
      this.handleCustomDrop.bind(this)(source);
    };
    this.selectComponentCallback = (component: any) => {
      this.handleSelectComponent.bind(this)(component);
    };

    this.checkEditFormValidDebounce = debounce(this.checkEditFormValid, 500);
  }

  ngOnInit(): void {
    this.init();
    this.dslWorkDesignService.hooksSubject$.subscribe((data) => {
      this.hooks = data;
    });
  }

  ngAfterViewInit(): void {
    // div.builder-components.drag-container.formio-builder-form.drag-over
    // this.sourceTarget = this.el.nativeElement.querySelector('div.drag-container');
  }

  ngOnChanges(changes: SimpleChanges): void {
    /**
     * 模版打印使用appCode
     */
    this.defaultBuilderOptions['dslData']['appCode'] = this.appService.selectedApp?.code;
    if (Object.keys(changes).includes('dataState')) {
      this.defaultBuilderOptions['dslData']['dataState'] = this.dataState;
      this.initHistory();
    }

    if (Object.keys(changes).includes('fieldTree')) {
      this.defaultBuilderOptions['dslData']['fieldTreeData'] = this.fieldTree;
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
        // this.formBuilder?.rebuild();
      }
    }
    if (Object.keys(changes).includes('dataSources')) {
      this.defaultBuilderOptions['dslData']['dataSources'] = this.dataSources;
      if (this.builderOptions) {
        this.builderOptions = this.defaultBuilderOptions;
        // this.formBuilder?.rebuild();
      }
    }

    if (Object.keys(changes).includes('rules')) {
      this.updateRuleToFormIo();
    }

    if (Object.keys(changes).includes('renderDslData')) {
      this.handleRenderDslData(this.formInitLoading);
    }

    if (Object.keys(changes).includes('dslConfig')) {
      this.interfaceConversionService.isButtonVisible = this.dslConfig.formCenterConfig.isShowCustomButton;
      this.defaultBuilderOptions['dslData']['pageCode'] = this.dslConfig.pageCode;
      this.defaultBuilderOptions['dslData']['productName'] = this.dslConfig.formSetConfig?.productName || '';
      this.defaultBuilderOptions['dslData']['isNoDataState'] = this.dslConfig.formCenterConfig.isNoDataState;
      this.defaultBuilderOptions['dslData']['useGroupSetting'] = this.dslConfig.formCenterConfig.useGroupSetting;
      this.defaultBuilderOptions['dslData']['uploadCategory'] = this.dslConfig.formSetConfig?.uploadCategory || '';
      if (this.dslConfig.formCenterConfig.formAreaPaddingTop) {
        this.defaultBuilderOptions['dslData']['formAreaPaddingTop'] =
          this.dslConfig.formCenterConfig.formAreaPaddingTop;
      }
      if (this.dslConfig.formCenterConfig.formAreaMarginTop) {
        this.defaultBuilderOptions['dslData']['formAreaMarginTop'] = this.dslConfig.formCenterConfig.formAreaMarginTop;
      }
      if (this.dslConfig.advancePanel) {
        this.defaultBuilderOptions['dslData']['advancePanel'] = this.dslConfig.advancePanel;
      }
      if (Reflect.has(this.dslConfig, 'canCustom')) {
        this.defaultBuilderOptions['dslData']['canCustom'] = this.dslConfig.canCustom;
        // 正常我们修改了 配置 想要立即获得formio的渲染效果，只需要 rebuild 即可
        // 但当前 我们 为了 性能 并不希望 这个 canCustom 按钮 触发 整个form的 rebuild
        // 所以 经过沟通， 当前 选择 直接操作dom，后续考虑 canCustom 按钮不经由  formio 渲染，彻底解决该问题
        const conversionDom = document.querySelector('interface-conversion-el') as HTMLElement;
        if (conversionDom) conversionDom.style.visibility = this.dslConfig.canCustom ? 'visible' : 'hidden';
      }
      if (!isNone(this.dslConfig.formSetConfig.checkboxDisabled)) {
        this.defaultBuilderOptions['dslData']['checkboxDisabled'] = this.dslConfig.formSetConfig.checkboxDisabled;
      }
      if (!isNone(this.dslConfig.formSetConfig.checkboxDefaultValue)) {
        this.defaultBuilderOptions['dslData']['checkboxDefaultValue'] =
          this.dslConfig.formSetConfig.checkboxDefaultValue;
      }
      this.defaultBuilderOptions['dslData']['dataEntryDefaultOptionsCode'] =
        this.dslConfig.formCenterConfig.dataEntryDefaultOptionsCode;
      this.defaultBuilderOptions['dslData']['isHideRule'] = !this.dslConfig.sidebarConfig?.ModuleCodeList.includes(
        SubModuleEnum.Rule,
      );
      this.defaultBuilderOptions['dslData']['mergeCellEnable'] = this.dslConfig.formCenterConfig.mergeCellEnable;
      this.defaultBuilderOptions['dslData']['groupSummaryEnable'] = this.dslConfig.formCenterConfig?.groupSummaryEnable;
      this.defaultBuilderOptions['dslData']['needIsFocusDisplay'] =
        !!this.dslConfig.formCenterConfig.needIsFocusDisplay;
      if (Reflect.has(this.dslConfig.formCenterConfig ?? {}, 'serachViewDisplay')) {
        this.defaultBuilderOptions['dslData']['serachViewDisplay'] = this.dslConfig.formCenterConfig.serachViewDisplay;
      }

      if (this.dslConfig.formSetConfig.isToolbarShowAdd) {
        this.defaultBuilderOptions['dslData']['isToolbarShowAdd'] = this.dslConfig.formSetConfig.isToolbarShowAdd;
      }

      this.defaultBuilderOptions['dslData']['showAssociatedTaskCard'] =
        !!this.dslConfig.formSetConfig?.showAssociatedTaskCard;

      if (this.dslConfig.isTenantProcessId) {
        this.defaultBuilderOptions['dslData']['isTenantProcessId'] = this.dslConfig.isTenantProcessId;
      }
      if (this.dslConfig.isManuallyInitiated) {
        this.defaultBuilderOptions['dslData']['isManuallyInitiated'] = this.dslConfig.isManuallyInitiated;
      }
      if (this.dslConfig.isAbiReprty) {
        this.defaultBuilderOptions['dslData']['isAbiReprty'] = this.dslConfig.isAbiReprty;
      }

      // if (this.builderOptions) {
      //   this.builderOptions = this.defaultBuilderOptions;
      //   this.formBuilder?.rebuild();
      // }
    }
    if (Object.keys(changes).includes('fieldData')) {
      if (!isEmpty(this.fieldData)) {
        this.defaultBuilderOptions['dslData']['dataSources_flatten'] = flattenData(this.fieldData);
      }
    }

    if (Object.keys(changes).includes('workServiceData')) {
      this.defaultBuilderOptions['dslData']['workServiceData'] = this.workServiceData;
    }

    if (Object.keys(changes).includes('favouriteCode')) {
      this.defaultBuilderOptions['dslData']['favouriteCode'] = this.favouriteCode;
    }

    if (Object.keys(changes).includes('applicationCodeProxy')) {
      this.defaultBuilderOptions['dslData']['applicationCodeProxy'] = this.applicationCodeProxy;
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.dslFormRenderService.resetCacheData();
    this.formHistory.clear();
    this.isvCustomService.clearIsvCustomComponent();
  }

  init(): void {
    this.handleTokenInfo();
    // this.interfaceConversionService.isButtonVisible = true; // 开启自定义界面的设置按钮
    this.formInitLoading = true;
    // 指定默認的版面寬度
    // if (!this.pageSizeService.currentPageSize) {
    this.pageSizeService.setSize(PageSize.DESKTOP);
    // }

    this.pageSizeService.sizeChanged$.pipe(takeUntil(this.destroy$)).subscribe(this.changePageSize());
    // 切換語系時
    this.languageService.language$.pipe(takeUntil(this.destroy$)).subscribe((res) => {
      const rebuild = !!this.builderOptions;

      const i18n = this.translate.translations;
      const cimodeI18n = {};
      for (const lang in i18n) {
        cimodeI18n[lang.replace('_', '-')] = i18n[lang];
      }
      this.defaultBuilderOptions.language = res.next.replace('_', '-');
      this.defaultBuilderOptions.i18n = cimodeI18n;

      this.builderOptions = this.defaultBuilderOptions;

      if (rebuild) {
        this.formBuilder?.rebuild();
      }
    });

    this.interfaceConversionService.interfaceType$.pipe(takeUntil(this.destroy$)).subscribe((interfaceType) => {
      if (!this.isInterfaceTypeInit) return (this.isInterfaceTypeInit = true);

      this.modal.confirm({
        nzTitle: this.translate.instant('dj-确认切换为定制界面？'),
        nzContent: this.translate.instant('dj-切换为定制界面将重置当前标准界面的界面代码'),
        nzWrapClassName: 'vertical-center-modal',
        nzOkText: this.translate.instant('dj-确定'),
        nzOnOk: () => {
          this.changeToCustom.emit();
        },
        nzOnCancel: () => {},
      });
    });

    this.formHistory.undoEvent$.pipe(takeUntil(this.destroy$)).subscribe(() => this.undo());
    this.formHistory.redoEvent$.pipe(takeUntil(this.destroy$)).subscribe(() => this.redo());

    this.dslFormRenderShareService.openwindowInEvent$.pipe(takeUntil(this.destroy$)).subscribe((data) => {
      this.updateOpenWindowToFormIo(data);
    });
  }

  initIsvPackageData() {
    this.isvCustomService.clearIsvCustomComponent();
    this.isvPackageDataList.forEach((packageData) => {
      this.isvCustomService.batchRegister(
        packageData.isvCustomComponentInfoList,
        packageData.packageInfo,
        false,
        false,
      );
    });
    this.isvCustomService.applyIsvComponent();
  }

  // 处理 RenderDslData
  handleRenderDslData(isFormTemp = false) {
    this.hooks = this.renderDslData?.hooks || [];
    const { lineOperations, otherOperations } = this.renderDslData?.operations?.reduce(
      (acc, operation) => {
        acc[operation.mode === 'line' ? 'lineOperations' : 'otherOperations'].push(operation);
        return acc;
      },
      { lineOperations: [], otherOperations: [] },
    );

    this.lineOperations = lineOperations;
    const currentRenderDslData = {
      ...this.renderDslData,
      operations: otherOperations.map((s) => ({ ...s, id: s.id || UUID.UUID() })),
    };

    this.dslFormRenderService.uibotDslChange(currentRenderDslData);
    let originForm = this.dslFormRenderService.converToFormioJson(currentRenderDslData, {
      handleOperationType: this.dslConfig.formCenterConfig.applyToField,
      mergeCellEnable: this.dslConfig.formCenterConfig.mergeCellEnable,
    });

    if (currentRenderDslData.renderStateManagementData) {
      originForm = this.dslFormRenderService.dealManageStatus(
        originForm,
        currentRenderDslData.renderStateManagementData,
      );
    }

    this.crudConvert.mountRulesToComponent(originForm.components, this.rules);
    if (isFormTemp) {
      this.formTemp = originForm;
    } else {
      this._formInstance?.resetPropertiesContent();
      this.form = originForm;
    }
  }

  /**
   * 获取解决方案/用户权限信息，塞到缓存，为配合模型驱动统一代码
   * todo 后期优化
   */
  handleTokenInfo() {
    const {
      userInfo: { iamToken },
    } = this.userService as any;
    sessionStorage.setItem(SESSIONSTORAGE_KEY_CONSTANT.lcdpUserToken, iamToken);
    this.configService.get('appToken').subscribe((appToken) => {
      sessionStorage.setItem(SESSIONSTORAGE_KEY_CONSTANT.lcdpAppToken, appToken);
    });
    sessionStorage.setItem(SESSIONSTORAGE_KEY_CONSTANT.application, this.appService?.selectedApp?.code);
  }

  changePageSize(): any {
    return (size) => {
      FormEditorFramework.bootstrap('common', this.injector, true);
      if (size === PageSize.MOBILE) {
        FormEditorFramework.bootstrap('mobile', this.injector, false);
      } else {
        FormEditorFramework.bootstrap('athena-ui', this.injector, false);
      }
      // changePageSize 是最早期的 formio逻辑，首先这里的 逻辑 存在一定问题
      // 按之前这块逻辑里，组件初始化的时候会触发一次changePageSize
      // 而第一次的 changePageSize 进行了FormEditorFramework 的 bootstrap , 而初始化的 时候 必然会 rebuild一次
      // 这一次的 rebuild 明显是不必要的
      // 那么我们 就可以根据 之前的变量 pageSize 控制 formio 组件是否 渲染，在bootstrap完成之前 是不应该 渲染 formio组件的
      // 同时 不存在pageSize 的时候 触发 changePageSize 也是 不需要 rebuild的
      // 做完了 这一层 优化 之后，我们的 初始化 自定义 组件的 逻辑 就可以 写在这里
      // 两个 逻辑 结合 就是，初始化changePageSize的时候 ， 初始化 自定义 组件，非初始化changePageSize时 触发rebuild
      // 而只有 完成 初始化 之后，formio的 builder 组件 才应该被渲染
      if (this.pageSize) {
        this.formBuilder?.rebuild();
      } else {
        this.initIsvPackageData();
        this.handleRenderDslData(true);
      }

      this.pageSize = size;
    };
  }

  formReady(instance: any): void {
    this.formInitLoading = false;

    instance.ready.then(() => {
      this.updateUibotDslAndSchemaList(instance.form);
      this.updateRuleToFormIo();
      // this.updateDealManageStatus();

      if (!this._isNotSaveHistoryAfterFormReady) {
        this.formIsReady.emit({
          status: true,
          isFinalReady: true,
        });
        this.formHistory.push(instance.form, this.historyFlag);
      } else {
        this._isNotSaveHistoryAfterFormReady = false;
        this.formIsReady.emit({
          status: true,
          isFinalReady: false,
        });
      }
      // this.formIsReady.emit(instance.isLoading);
    });

    this._formInstance = instance;

    this.formInstanceChange.emit(this._formInstance);

    if (this.formTemp) {
      this.form = this.formTemp;
      this.formTemp = null;
      this._isRenderDsl = true;
    }

    instance.off('selectComponent', this.selectComponentCallback);
    instance.on('selectComponent', this.selectComponentCallback);

    instance.off('customDrop', this.customDropCallback);
    instance.on('customDrop', this.customDropCallback);

    instance.off('schemaChange');
    instance.on('schemaChange', () => {
      // this.updateEditFormRules();
    });

    // 点击高级tab
    instance.off('tabClick');
    instance.on('tabClick', (e) => {
      const advancedPanel: any = document.querySelector('[ref="page-design-advanced-panel"]');
      const { panelLevel } = e.target.dataset;

      advancedPanel.setAttribute('data-level', panelLevel);
      advancedPanel.style.display = panelLevel === '0' ? 'none' : 'block';
    });

    this.formJson$?.unsubscribe();
    this.formJson$ = this.dslFormRenderService.formJson$.pipe(takeUntil(this.destroy$)).subscribe((formJson) => {
      this.form = formJson;
    });

    // 重新绑定拖拽元素
    instance.emit('initDnd');
    this.resetFormDragEvent$?.unsubscribe();
    this.resetFormDragEvent$ = this.dslFormRenderShareService.resetFormDragEvent$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        instance.emit('initDnd');
      });

    // 初始化时置空被选中的控件
    this.selectedComponent = null;
    // 隐藏高级面板
    const advancedPanel: any = document.querySelector('[ref="page-design-advanced-panel"]');
    advancedPanel.style.display = 'none';

    instance.off('sendSubscriberMessageOut');
    instance.on('sendSubscriberMessageOut', (data: SendSubscriberMessageData) => {
      const { subscriberKey, subscriberValue } = data;
      if (subscriberKey === 'ruleOutChange$') {
        this.dslFormRenderShareService.rulesChangeEvent(subscriberValue);
      } else if (subscriberKey === 'openwindowOutChange$') {
        this.dslFormRenderShareService.openwindowChangeEvent(subscriberValue);
      } else if (subscriberKey === 'operationHooksChange$') {
        this.dslFormRenderShareService.operationHooksChangeEvent(subscriberValue);
      }
    });
  }

  initHistory(): void {
    this.formHistory.addTab(this.historyFlag);
    if (!this.formHistory?.cache[this.historyFlag]?.document) {
      this.formHistory.init(
        {
          components: [],
        },
        this.historyFlag,
      );
    }
    this.formHistory.updateStatus(this.historyFlag);
  }

  // 迁移之前interface-configuration的用户自定义拖拽逻辑
  handleCustomDrop(source: {
    element: Element;
    target: Element;
    source: Element;
    sibling: Element;
    type?: string;
  }): void {
    this.zone.run(() => {
      if (source.type && source.type === 'dsl-custom-drop') {
        this.handleTreeSelect(source);
        return;
      }
      this.handleSelectDragTargetTypeOnDrop(source.element, source.target, source.source, source.sibling);
    });
  }

  handleSelectDragTargetTypeOnDrop(element: Element, target: Element, source: Element, sibling: Element): void {
    this.selectDragTargetType(element, target, source, sibling)
      .then((result: any) => {
        const { element, target, source, sibling, componentInfo, children } = result;
        if (componentInfo.type === 'form-list') {
          componentInfo.title = '';
        }
        this.formBuilder.instance.onDrop(element, target, source, sibling, componentInfo, children);
      })
      .catch(({ message }) => {
        if (!!message && message !== 'cancel') {
          this.message.error(this.translate.instant(message));
        }
        element?.remove();
      });
  }

  handleSelectComponent(component): void {
    // 根据被选组件有无path显示或隐藏tabs和高级面板
    const tabs: any = document.querySelector('[ref="propTabs"]');
    const advancedPanel: any = document.querySelector('[ref="page-design-advanced-panel"]');

    if (component.customOptions) {
      const { hooks = [], isHookActive } =
        this.isvPackageDataList
          // @ts-ignore
          ?.flatMap((s) => s.isvCustomComponentInfoList || [])
          ?.find((s) => s.type === component?.customOptions?.type) || {};
      this.customHooks = hooks;
      if (!!tabs && !Object.keys(ComponentCustomHooks).includes(component.customOptions?.type) && !isHookActive) {
        tabs.style.display = 'none';
      }
      // todo km-hooks
      advancedPanel.setAttribute('data-level', 0);
      advancedPanel.style.display = 'none'; // 赋值当前选中的组件
      this.selectedComponent = component;
    }
    this.selectComponentChange.emit(component);
  }

  // getComponentsAllInfo(components: any[]) {
  //   return components.map((com) => {
  //     const componentClass = Components.components[com.type];
  //     if (componentClass) {
  //       com.customOptions = merge(componentClass.schema()?.customOptions, com.customOptions);
  //     }
  //     if (com.components?.length > 0) {
  //       com.components = this.getComponentsAllInfo(com.components);
  //     }

  //     if (this.dslConfig.formCenterConfig.needIsFocusDisplay && !this.containerTypeList.includes(com.type)) {
  //       com.customOptions.isFocusDisplay = true;
  //     }

  //     return com;
  //   });
  // }

  componentChanged(event: any): void {
    let form = cloneDeep(event?.builder?.form);
    if (['addComponent', 'saveComponent', 'deleteComponent'].indexOf(event.type) > -1) {
      if (['addComponent', 'saveComponent'].includes(event.type)) {
        form = this.dslFormRenderService.handleFormOpenWindowConfig(form);
        const componentDom: any = document.getElementById(event.component.id);
        if (componentDom) {
          componentDom.click();
        }
      }

      if (event.type === 'deleteComponent') {
        // 隐藏tabs和高级面板
        const advancedPanel: any = document.querySelector('[ref="page-design-advanced-panel"]');
        advancedPanel.style.display = 'none';
      }

      this.formHistory.push(form, this.historyFlag);
      this.updateUibotDslAndSchemaList(form);
      this.updateDealManageStatus();
    } else if (['updateComponent'].indexOf(event.type) > -1) {
      // 内含外显组件切换内部组件类型属于更新组件updateComponent，需要保存
      this.formHistory.push(form, this.historyFlag);
      this.updateUibotDslAndSchemaList(form);
      this.updateDealManageStatus();

      this.checkEditFormValidDebounce(event?.builder?.editForm);
    }
  }

  // 处理状态列
  updateDealManageStatus(): void {
    if (this.renderDslData.renderStateManagementData && this._formInstance?.form) {
      const formTemp = this.dslFormRenderService.dealManageStatus(
        cloneDeep(this._formInstance.form),
        this.renderDslData.renderStateManagementData,
      );

      let isNeedRebuild = false;
      for (let i = 0; i < this._formInstance.form.components.length; i++) {
        if (this._formInstance.form.components[i].components?.length === 0) continue;
        if (!isEqual(this._formInstance.form.components[i], formTemp.components[i])) {
          this._formInstance.form.components[i] = formTemp.components[i];
          isNeedRebuild = true;
          // 因为只有第一层会处理ManageStatus，所以仅需 画布rebuild 即可
          // const instanceCom = this._formInstance.webform.components.find((com) => {
          //   return com.id === this._formInstance.form.components[i].id;
          // });
          // instanceCom?.parent?.rebuild();
        }
      }
      if (isNeedRebuild) {
        this._formInstance?.rebuild();
        // this.formBuilder?.rebuild();
      }
    }
  }

  updateUibotDslAndSchemaList(form: any): void {
    this.appliedComponents = this.getSchemaList(form.components);
    // const componentIdDataMap = new Map(
    //   this.appliedComponents.map((appliedComponent) => {
    //     return [appliedComponent.id, { schema: appliedComponent.value, type: appliedComponent.type, path: appliedComponent.path }];
    //   }),
    // );

    const componentIdDataMap = this.appliedComponents.map((appliedComponent) => {
      return {
        id: appliedComponent.id,
        schema: appliedComponent.value,
        type: appliedComponent.type,
        path: appliedComponent.path,
        title: appliedComponent.title,
        lang: appliedComponent.lang,
      };
    });
    this.dslFormRenderService.formSchemaListChange(this.appliedComponents);

    const cloneForm = cloneDeep(form);

    const uibotJson = this.dslFormRenderService.converToUibotJson(cloneForm);

    uibotJson.hooks = this.hooks;
    uibotJson.componentIdDataMap = componentIdDataMap;

    uibotJson.operations = [
      ...(uibotJson.operations || [])
        .reduce((map, item) => {
          if (!map.has(item.id) || map.get(item.id).timeStamp < item.timeStamp) {
            map.set(item.id, item);
          }
          return map;
        }, new Map())
        .values(),
      ...this.lineOperations,
    ];

    this.dslFormRenderService.uibotDslChange(uibotJson);
    this.dslDataChange.emit(uibotJson);
  }

  handleTreeSelect(source: any): void {
    this.source = source;
    this.actionModal = true;
    this.actionData = {
      actionId: '',
      actionName: '',
      useApp: 'true',
    };
  }

  handleSelectAction(e: any): void {
    const { actionId, actionName, actionType } = e;
    this.dslFormRenderRequestService.loadActionField(actionId).subscribe(
      (res) => {
        if (res.code === 0) {
          const language = this.languageService?.currentLanguage || 'zh_CN';
          let dataSources = {};
          if (res.data?.data_name) {
            dataSources = {
              [res.data.data_name]: {
                type: actionType === 'EspAction' ? 'ESP' : 'SD',
                title: res.data.description[language],
                actionId,
                serviceName: actionId.includes('esp_') ? actionId.replaceAll('esp_', '') : actionId,
                masterData: res.data.data_name,
                dataKeys: [],
                actionParams: [],
                metadataFields: [],
                notArray: false,
              },
            };
          }
          this.actionModal = false;
          const result = this.source;
          result.element.setAttribute('data-dataSources', JSON.stringify(dataSources));
          result.element.setAttribute('data-submitActions', JSON.stringify(submitActions));
          this.formBuilder.instance.onDrop(result.element, result.target, result.source, result.sibling);
        } else {
          this.source.element?.remove();
        }
      },
      () => {
        this.source.element?.remove();
      },
    );
  }

  closeSelectAction(): void {
    this.source.element?.remove();
    this.actionModal = false;
  }

  // 迁移之前interface-configuraton拖拽字段逻辑
  selectDragTargetType(element: any, target: any, source: any, sibling: any): Promise<{}> {
    const typeString = element.dataset.title;
    const typeArr = typeString.split('&&');
    // const targetId = target.formioComponent?.component?.customOptions?.schema ?? '';
    const _category = typeArr[8];
    const targetType = target.formioComponent?.component?.type ?? 'form'; // LAYOUT, TABS,中的子容器没有type
    // 这个dataType 是后台数据库存测元数据的数据类型
    const dataType = typeArr[2];
    // 模型设计的原始类型，这里取出来为了根据类型给width
    const dbFieldType = typeArr[11];
    const defaultWidth = getDeafultByFieldType({ dbFieldType });
    // let defaultFormioDataType = typeArr[6];
    let title = typeArr[1];
    // 解析出来是要混入label 取当前系统语言(迁移的逻辑)
    title = JSON.parse(title);
    title = handleComponentTitle(title, this.languageService?.currentLanguage);

    const schema = typeArr[0];
    const path: string = typeArr[10];
    const isSystem = typeArr[5];
    const disabled = dataType !== 'object' && isSystem === 'true';
    const parentCategory = typeArr[9];
    this.componentList = getComponentList({ data_type: dataType, targetType });
    this.componentType = this.componentList[0].type;
    const rootCategoryList = ['array', 'object']; // 根结点类型

    // 表格不可以拖入行容器中
    if (targetType === ATHENA_TYPES.FLEXIBLE_BOX && _category === 'array') {
      return new Promise((resolve, reject) => {
        reject(new Error(`dj-行布局容器不支持拖入表格`));
      });
    }
    // 行容器最多支持6个子元素
    if (targetType === ATHENA_TYPES.FLEXIBLE_BOX && target.formioContainer?.length === 6) {
      return new Promise((resolve, reject) => {
        reject(new Error(`dj-行布局容器最多可拖入6个控件数量`));
      });
    }

    const dropData = { element, target, source, sibling };

    // 如果是表格或者表单 则不需要根据弹框去选择
    if (rootCategoryList.includes(_category)) {
      return new Promise((resolve) => {
        const _type = _category === 'array' ? 'athena-table' : 'form-list';
        // this.handleElementSet(element, _type, defaultFormioDataType, schema, path, title, placeholder, disabled);
        const tableData = cloneDeep(this.defaultBuilderOptions['dslData']['dataSources_flatten'].get(schema));
        // 过滤掉置灰的字段
        const usedField = (this.appliedComponents ?? [])
          .filter((item) => schema === last(item?.path?.split('.')))
          .map((item) => item.value);
        tableData.field = tableData.field.filter((field) => !usedField.includes(field.data_name));
        const info: IInfo = {
          group: Group.dw,
          type: _type,
          dataType: dataType,
          schema,
          title,
          disabled,
          path: path,
          parentCategory,
        };
        // 生成表格信息
        let compInfo: IAthenaTableConfig = handleComponentInfo(info, path);

        const { dataEntryDefaultOptionsCode } = this.dslConfig.formCenterConfig;
        if (['listSetting', 'design', 'editPage'].includes(dataEntryDefaultOptionsCode)) {
          // 是否是浏览页面
          const isBrowsePage: boolean = dataEntryDefaultOptionsCode === 'listSetting';
          // 是否是单挡
          const isSingle: boolean = dataEntryDefaultOptionsCode === 'design';
          // 表格editform配置
          const editForm = {
            editable: true,
            checkbox: isBrowsePage || isSingle,
            saveColumnsWidth: true,
            advancedSearch: true,
            suppressAutoAddRow: !isSingle,
            operations: [],
          };
          // 表格的operations
          if (!isSingle) {
            editForm.operations.push(handleButtonGroupDefault(isBrowsePage ? 'add' : 'add-row', schema));
          }
          if (!path && !!schema && (isSingle || isBrowsePage)) {
            // 只有主表才需要生成数据导出，数据导入，下载模板
            const listGetAction = this.dataSources[0]?.json?.actionId ?? '';
            editForm.operations.push(handleButtonGroupDefault('data_export', schema, { actionId: listGetAction }));
            const createAction = listGetAction.replace('list.get', 'create') ?? '';
            editForm.operations.push(handleButtonGroupDefault('upload_file', schema, { actionId: createAction }));
            editForm.operations.push(handleButtonGroupDefault('download_template', schema, { actionId: createAction }));
          }
          compInfo = { ...compInfo, ...editForm };
          compInfo.components = handleComponentsInfo(tableData.field, path ? `${path}.${schema}` : schema, {
            lang: this.lang,
            // 状态列
            manageStatusCol: {
              show: isBrowsePage || isSingle,
              extendedFields: this.renderDslData.renderStateManagementData?.needStatusColumn
                ? this.renderDslData.renderStateManagementData
                : '',
            },
            // 操作列
            actionCol: {
              show: _type !== 'form-list' && !isSingle,
              operations: [handleButtonGroupDefault(isBrowsePage ? 'edit' : 'delete', schema)],
            },
            ruleFn: (info) => (!isBrowsePage ? getRulesBySchemaPath(this.rules, info.schema, info.path) : []),
          });
        } else {
          const editForm = {
            editable: true,
            checkbox: true,
            saveColumnsWidth: true,
            advancedSearch: true,
            suppressAutoAddRow: true,
            operations: [],
          };
          compInfo = { ...compInfo, ...editForm };
          compInfo.components = handleComponentsInfo(tableData.field, path ? `${path}.${schema}` : schema, {
            lang: this.lang,
            ruleFn: (info) => getRulesBySchemaPath(this.rules, info.schema, info.path),
          });
        }
        const rowSpanTree = {
          label: 'dj-合并单元格',
          checked: false,
          disabled: !this.dslConfig.formCenterConfig.mergeCellEnable,
        };
        compInfo = { ...compInfo, rowSpanTree };
        resolve({ ...dropData, componentInfo: compInfo });
      });
    }

    return new Promise((resolve, reject) => {
      if (disabled) {
        const info: IInfo = {
          group: Group.dw,
          type: this.componentType,
          // dataType: defaultFormioDataType,
          schema,
          title,
          disabled,
          path: path,
          parentCategory,
        };
        resolve({ ...dropData, componentInfo: handleComponentInfo(info, path) });
      } else {
        this.modalService.create({
          nzTitle: this.dropTypeSelectModalTitleTpl,
          nzContent: this.dropTypeSelectModalContentTpl,
          nzOnOk: () => {
            // 对时间类型组件要特殊处理 defaultFormioDataType 受弹窗用户选择组件影响，为了properties中数据类型下拉选项的配置中有，保证默认类型在对应的下拉选项中
            // if (ATHENA_TYPES.DATEPICKER === this.componentType || ATHENA_TYPES.TIMEPICKER === this.componentType) {
            //   if (ATHENA_TYPES.DATEPICKER === this.componentType) {
            //     defaultFormioDataType = AthenaDataType.DATE;
            //   } else {
            //     defaultFormioDataType = AthenaDataType.TIME;
            //   }
            // }
            const info: IInfo = {
              group: Group.dw,
              type: this.componentType,
              // dataType: defaultFormioDataType,
              schema,
              title,
              disabled,
              path: path,
              parentCategory,
              width: defaultWidth,
            };
            const _info: IComponentInfo = handleComponentInfo(info, path);
            // 处理标签控件
            if (['ATH_TAG_FACE', 'ATH_TAG_LINE', 'ATH_TAG_COLOR', 'ATH_TAG_STAMP'].includes(this.componentType)) {
              _info['type'] = 'ATH_TAG';
              _info['dataType'] = undefined;
              _info['tagType'] = lowerCase(this.componentType.split('_')[2]);
            }
            // 设置内含外显
            if (
              this.componentType === ATHENA_TYPES.NAME_CODE_COMPONENT ||
              this.componentType === ATHENA_TYPES.NEW_OLD_COMPONENT
            ) {
              const nameCodeInfo: IInfo = {
                group: Group.dw,
                type: ATHENA_TYPES.INPUT,
                dataType: 'string',
                schema,
                title,
                disabled,
                path: path,
                parentCategory,
              };
              const nameCodeCompInfo: IComponentInfo = handleComponentInfo(nameCodeInfo, path);
              // 内含外显第2个组件给个默认的输入组件
              const defaultInfo: IInfo = {
                group: Group.dw,
                type: ATHENA_TYPES.INPUT,
                dataType: 'string',
                schema: '',
                title: {
                  zh_CN: '',
                  zh_TW: '',
                  en_US: '',
                },
                disabled,
                path: path,
                parentCategory,
              };
              const labelCompInfo = handleComponentInfo(defaultInfo, path);
              _info.components = [nameCodeCompInfo, cloneDeep(labelCompInfo)];
            }
            // 设置eoc schemas
            if (this.componentType === ATHENA_TYPES.EOC_SELECT) {
              const tableData = this.defaultBuilderOptions['dslData']['dataSources_flatten'];
              const _path = path.split('.').pop();
              const schemas = createComponentByDrag(this.componentType, schema, tableData.get(_path));
              // element.setAttribute('data-schemas', JSON.stringify(schemas));
              _info.schemas = JSON.stringify(schemas);
            }
            const _rules = getRulesBySchemaPath(this.rules, schema, path);
            // element.setAttribute('data-rules', JSON.stringify(_rules));
            _info.rules = JSON.stringify(_rules);
            resolve({ ...dropData, componentInfo: _info });
          },
          nzOnCancel: () => {
            reject(new Error('cancel'));
          },
        });
      }
    });
  }

  // 撤销
  undo(): void {
    this.dslFormRenderService.formChange(this.formHistory.undo(this._formInstance?.form, this.historyFlag));
    this._isNotSaveHistoryAfterFormReady = true;
    this._formInstance?.emit('cancelComponent');
  }

  // 恢复
  redo(): void {
    this.dslFormRenderService.formChange(this.formHistory.redo(this._formInstance?.form, this.historyFlag));
    this._isNotSaveHistoryAfterFormReady = true;
    this._formInstance?.emit('cancelComponent');
  }

  getSchemaList(components: any): any[] {
    let componentList = [];
    components?.forEach((item) => {
      // eslint-disable-next-line max-len
      if (item.customOptions?.id && item.customOptions?.type !== 'curd-block') {
        // eslint-disable-next-line max-len
        componentList.push({
          label: item.customOptions.title,
          value: item.customOptions?.schema,
          path: item.customOptions?.path,
          type: item.type,
          editable: item.customOptions?.editable,
          id: item.customOptions?.componentId,
        });
      }
      if (item.components) {
        componentList = [...componentList, ...this.getSchemaList(item.components)];
      }
      if (item.customOptions?.type === 'TOOLBAR') {
        componentList = [
          ...componentList,
          ...(item.customOptions?.options?.items ?? item.customOptions?.options?.optionList ?? []).map((button) => {
            return {
              lang: button.lang,
              title: button.title,
              label: button.title,
              value: null,
              path: item.customOptions?.path + item.customOptions?.schema,
              type: button.type,
              editable: null,
              id: button?.id,
            };
          }),
        ];
      }
    });
    return componentList;
  }

  isPropertiesOpenChange(isOpen: any): void {
    this.propertiesOpenChange.emit(isOpen);
  }

  /**
   * hooks变化后的回调
   * @param hooks
   */
  handleHooksChange(hooks: any): void {
    this.hooks = hooks;
    this.updateUibotDslAndSchemaList(this._formInstance.form);
  }

  /**
   * 获取被选中组件及其包含组件的id
   */
  getComponentIds(component: any): any[] {
    const componentIds = [];
    let recursive = (component) => {
      if (component.customOptions?.componentId) {
        componentIds.push(component.customOptions.componentId);
      }
      if (component.components?.length) {
        component.components.forEach((item) => {
          recursive(item);
        });
      }
    };

    recursive(component);
    recursive = null;
    return componentIds;
  }

  updateRuleToFormIo(): void {
    const rules = !this.dslConfig?.sidebarConfig?.ModuleCodeList.includes(SubModuleEnum.Rule) ? [] : this.rules;

    this._formInstance?.emit('sendSubscriberMessageIn', {
      subscriberKey: 'ruleInChange$',
      subscriberValue: rules ?? [],
    } as SendSubscriberMessageData);
  }

  checkEditFormValid(editForm: any) {
    editForm?.submitForm();
  }

  updateOpenWindowToFormIo(data): void {
    this._formInstance?.emit('sendSubscriberMessageIn', {
      subscriberKey: 'openwindowInChange$',
      subscriberValue: data ?? {},
    } as SendSubscriberMessageData);
  }
}
