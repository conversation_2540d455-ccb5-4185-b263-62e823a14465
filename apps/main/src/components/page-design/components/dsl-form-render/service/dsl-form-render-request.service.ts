import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { SystemConfigService } from 'common/service/system-config.service';
import { AdUserService } from 'pages/login/service/user.service';

@Injectable()
export class DslFormRenderRequestService {
  apiUrl = '';
  appToken = '';
  config: any;
  get authToken(): string {
    return (this.adUserService.userInfo as any).iamToken ?? '';
  }

  constructor(
    private httpClient: HttpClient,
    private systemConfig: SystemConfigService,
    private adUserService: AdUserService,
  ) {
    this.systemConfig.getConfig().subscribe((_config: any) => {
      this.config = _config;
    });
    this.systemConfig.get('adesignerUrl').subscribe((url) => {
      this.apiUrl = url;
    });
    this.systemConfig.get('appToken').subscribe((appToken) => {
      this.appToken = appToken;
    });
  }

  loadActionField(param: any): Observable<any> {
    const httpHeader: HttpHeaders = new HttpHeaders({
      'digi-middleware-auth-app':
        this.config?.platformCategory === 'TENANT' ? this.config?.tenantAppToken : this.appToken,
      'digi-middleware-auth-user': this.authToken,
    });
    const url = `${this.apiUrl}/athena-designer/action/queryEspActionFields?actionId=${param}`;
    return this.httpClient.get(url, { headers: httpHeader });
  }
}
