import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { AuthService } from 'common/service/auth.service';
import { LocaleService } from 'common/service/locale.service';
import { AdUserService } from 'pages/login/service/user.service';
import { DigiMiddlewareAuthApp } from 'common/config/app-auth-token';
import { AuthResources } from 'common/types/auth.types';
import microApp from '@micro-zoe/micro-app';
import { AD_AUTH_TOKEN } from 'pages/login/service/auth.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { ActivatedRoute, Router } from '@angular/router';
import { NzMessageService } from 'ng-zorro-antd/message';
import { environment } from 'environments/environment';
import { StandaloneMicroappName } from 'common/types/standalone.type';
import { GlobalService } from 'common/service/global.service';

@Component({
  selector: 'app-standalone-tbb',
  templateUrl: './standalone-tbb.component.html',
  styleUrls: ['./standalone-tbb.component.less'],
})
export class StandaloneTbbComponent implements OnInit, OnDestroy {
  appName = StandaloneMicroappName.TBB;
  url = '';

  data = {
    currentLanguage: this.languageService?.currentLanguage || 'zh_CN',
    digiMiddlewareAuthApp: DigiMiddlewareAuthApp,
    userInfo: this.userService.getUserInfo(),
    sessionStorage: sessionStorage,
    auth: {
      resourcePrefix: AuthResources.STANDALONE_SOLUTION_TBB, // 权限资源类型
      permission: this.newAuthService?.authOriginData || [], // 具体权限
      roles: this.newAuthService?.roleData, // 角色数据
    },
    params: {
      sourceApp: 'developWeb',
    },

    // 获取tbb内部路由实例
    call: 'getRouter',
  };

  visible: boolean = false; // 是否显示microApp
  loading: boolean = true;
  static subRouter: any; // tbb路由

  constructor(
    private userService: AdUserService,
    private newAuthService: AuthService,
    private languageService: LocaleService,
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
    protected configService: SystemConfigService,
    private route: ActivatedRoute,
    private message: NzMessageService,
    private router: Router,
    private globalService: GlobalService,
  ) {
    this.configService.getConfig().subscribe((config) => {
      this.url = config.tbbRenderUrl;
    });

    // 是否有app信息
    this.route.data.subscribe((res) => {
      if (!res.selectedApp?.code) {
        this.message.error('缺少参数：appCode');
        return;
      }
      if (res.selectedApp?.auth === false) {
        // 无授权情况
        this.router.navigateByUrl('/unauthorised', { replaceUrl: true });
        return;
      }

      // 监听子解决方案路由变化
      this.listenAppRouteChange(res.selectedApp);

      this.visible = true;
      microApp.setData(this.appName, {
        authToken: this.authToken,
        appCode: res.selectedApp.code,
      });
    });
  }
  ngOnDestroy(): void {}

  ngOnInit() {}

  handleCreate(): void {}

  handleBeforeMount(): void {}

  handleMount(): void {
    this.loading = false;
    this.setMicroAppMap({ keepAliveShow: true });
  }

  handleUnmount(): void {}

  handleError(): void {}

  handleDataChange(e: CustomEvent): void {
    const { call, callbackName, result } = e.detail.data;

    if (call === 'tokenTimeout') {
      // 处理token过期
      this.router.navigateByUrl('/login', { replaceUrl: true });
      return;
    }

    if (callbackName === 'getRouter') {
      this.setMicroAppMap({ router: result });
    }
  }

  handleAfterhidden() {
    this.setMicroAppMap({ keepAliveShow: false });
  }

  /**
   * keep-alive显示后
   * @param e
   */
  handleAftershow(e) {
    // 设置活激活状态，仅对keep-alive的子解决方案
    const info = this.globalService.microAppStateMap.get(this.appName);
    this.setMicroAppMap({ keepAliveShow: true });

    const currentRoute = location.hash.split('?')[0];
    const route = currentRoute.replace('#', '');

    info.router?.push({ path: route });
  }

  handleBeforeshow(e) {}

  /**
   * 监听子解决方案路由变化
   * 给子解决方案路由后自动添加appCode
   */
  listenAppRouteChange(selectedApp) {
    const name = this.appName;
    microApp.router.beforeEach({
      [name](to, from) {
        const hashUrls = to.hash.split('?');
        const searchObj = new URLSearchParams(hashUrls[1] || '');
        if (!searchObj.get('appCode') && to.hash.startsWith('#/') && to.hash !== '#/') {
          searchObj.append('appCode', selectedApp?.code);
          microApp.router.push({
            name,
            path: `${hashUrls[0]}?${searchObj.toString()}`,
            replace: true,
          });
        }
      },
    });
  }

  setMicroAppMap(values) {
    const info = this.globalService.microAppStateMap.get(this.appName) || {};
    this.globalService.microAppStateMap.set(this.appName, { ...info, ...values });
  }
}
