/**
 * 技术文档：https://z0lxpczot6u.feishu.cn/wiki/GZohwlrKHiKqfoksACBctghGnFb?fromScene=spaceOverview
 * 例如租户：
 * { "effect": "allow", "action": ["tenant:*"] }
 * { "effect": "deny", "action": ["tenant:mgr"] }
 * 表示允许所有租户访问，但禁止租户mgr权限(禁止管理用户的权限)
 * 前端处理后：
  authData = {
      tenant:{
          allow: true,
          deny: []
      },
      application:{
          allow: true,
          deny: []
      }
  }
 */
import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { SystemConfigService } from 'common/service/system-config.service';
import {
  AuthOperate,
  AuthOperateType,
  AuthPanelContextData,
  AuthPanelData,
  AuthQueryParams,
  AuthResources,
} from 'common/types/auth.types';
import { AdUserService } from 'pages/login/service/user.service';
import { AuthPanelTypeEnum } from '../config/auth.config';
import { isEmpty } from 'lodash';

import { Subject } from 'rxjs';
import { Router } from '@angular/router';

@Injectable({ providedIn: 'root' })
export class AuthService {
  updateCooperateEnterDataSubject$ = new Subject();
  openAuthPanel$ = new Subject();
  authPanelData: AuthPanelData = {
    visual: false,
    type: AuthPanelTypeEnum.SuperAdmin,
    data: { enterType: AuthResources.TENANT },
  };
  //权限面板数据
  authOriginData = null;
  // 角色数据
  roleData = null;

  // 第一级prefix
  resourcePrefix = '';

  // 第二级prefix，目前是一级+operatePrefix，后续可能有多级
  // actionPrefix = '';

  // 按钮级prexfix
  operatePrefix = '';

  // 权限数据
  authData: any = {};

  adesignerUrl: string;

  private operats: AuthOperateType[] = [AuthOperate.MGR, AuthOperate.CREATE, AuthOperate.UPDATE, AuthOperate.DELETE];

  constructor(
    private http: HttpClient,
    private userInfo: AdUserService,
    private configService: SystemConfigService,
    private route: Router,
  ) {
    this.configService.getConfig().subscribe((config) => {
      const { adesignerUrl } = config;
      this.adesignerUrl = adesignerUrl;
    });
  }

  /**
   * 打开权限面板
   * @param type
   */
  openAuthPanel(type: AuthPanelTypeEnum, data: AuthPanelContextData) {
    if (data) {
      this.authPanelData.data = data;
    }
    this.authPanelData.type = type;
    this.authPanelData.visual = true;
    setTimeout(() => {
      this.openAuthPanel$.next();
    });
  }

  /**
   * 关闭权限面板
   */
  closeAuthPanel() {
    this.authPanelData.visual = false;
  }

  /**
   * 获取权限
   * @param operatePrefix 按钮级前缀
   * @returns
   */
  getAuth(operatePrefix: AuthOperateType, extra?: { templateSignal: boolean; tenantPaas: boolean }) {
    // 一般场景： 租户级没有操作权限
    // 租户级特殊场景，即使是租户级，但是也有操作权限
    if (extra?.tenantPaas) return true;
    if (!!this.userInfo.getUser('isTenantActive')) {
      return false;
    }

    // 特殊处理-开发成果-模板管理
    // templateSignal表示是否是其他Tab
    // if (extra?.templateSignal) {
    const templateResult = this.dealTemplateAuth(extra?.templateSignal);
    if (templateResult !== 'continue') {
      return templateResult;
    }
    // }

    /**
     * TODO: 目前是一级+operatePrefix，后续可能有多级
     */
    let fullPrefix = this.resourcePrefix;
    if (!this.operats.includes(operatePrefix)) return false;

    const currentAuth = this.authData[this.resourcePrefix];

    // 处理mgr权限，由于目前模块内除了 mgr 外，没有其他细颗粒的控制，所以 deny 中只有可能是 mgr，所以这里先写死
    if (operatePrefix === AuthOperate.MGR) {
      fullPrefix = `${this.resourcePrefix}:${operatePrefix}`;

      if (currentAuth?.allow && !currentAuth?.deny?.includes(fullPrefix)) {
        return true;
      }
      return false;
    }

    // 其他操作权限(由于本期没有操作权限)
    if (currentAuth?.allow) {
      return true;
    }

    return false;
  }

  /**
   * 扭转权限数据
   * @param authOriginData 原始权限数据
   * @param actionPrefix 访问前缀
   */
  transformAuthData(authOriginData: any = [], actionPrefix: string): any {
    if (!authOriginData?.length) return;

    const allows = authOriginData.filter((item) => item.effect === 'allow');
    const denies = authOriginData.filter((item) => item.effect === 'deny');
    const allowActions: string[] = allows.reduce((pre, curr) => pre.concat(curr.action), []).map(this.getFullPrefix);
    const denyActions: string[] = denies.reduce((pre, curr) => pre.concat(curr.action), []).map(this.getFullPrefix);

    this.authData[actionPrefix] = { allow: !!allowActions.length, deny: denyActions };

    (window as any).authData = this.authData;
  }

  /**
   * 获取action最后一个:之前的部分
   * @param action
   */
  getFullPrefix(action: string): string {
    const index = action.lastIndexOf(':');
    if (index === -1) {
      return '';
    }
    return action.slice(0, index);
  }

  /**
   * 请求权限数据
   * @param resourceType 资源类型
   */
  async fetchAuthData(params: AuthQueryParams): Promise<any> {
    const url = `${this.adesignerUrl}/athena-designer/auth/queryAuthPolicy`;
    return await this.http.post(url, params).toPromise();
    // return new Promise((resolve) => {
    //   resolve({ code: 0, data: [] });
    // });
  }

  fetchAuthDataByRole(params): Promise<any> {
    const url = `${this.adesignerUrl}/athena-designer/auth/queryResourceRoleUser`;
    return this.http.get(url, { params }).toPromise();
  }

  /**
   * 获取角色类型
   *
   * @param data
   * @returns
   */
  getRoleType(data) {
    const { roles = [], resourcePrefix } = data;
    const roleItem = roles?.find((item) => this.userInfo.getUserInfo()?.userId === item.userId) || {};
    if (isEmpty(roleItem)) return AuthPanelTypeEnum.CooperateCommon;

    return roleItem?.role === `${resourcePrefix}:${AuthPanelTypeEnum.CooperateActor}`
      ? AuthPanelTypeEnum.CooperateActor
      : AuthPanelTypeEnum.CooperateAdmin;
  }

  handleAuthToBase(data) {
    const { resourcePrefix, roles } = data;
    this.openAuthPanel(this.getRoleType(data), {
      enterType: resourcePrefix,
      roles: roles,
    });
  }

  /**
   * 特殊处理-开发成果-模板管理
   * 模板管理有特殊的权限控制：
   * 1. 只有管理员可以访问其他
   * 2. 一般用户及协作者无法访问模板管理中的其他
   * @param signal 是否是其他Tab
   */
  dealTemplateAuth(signal) {
    const templateRoute = [
      'dtd-template-manage',
      'flow-template-manage',
      'service-template-manage',
      'rule-template-manage',
    ];
    const routeUrl = this.route.url.split('/').pop();
    const role = this.getRoleType({ roles: this.roleData, resourcePrefix: AuthResources.RESULT });

    if (!templateRoute.includes(routeUrl)) {
      return 'continue';
    }

    if (templateRoute.includes(routeUrl)) {
      if (!signal || role === AuthPanelTypeEnum.CooperateAdmin) {
        return true;
      }
    }
    return false;
  }
}
