/**
 * @Description 描述
 * <AUTHOR>
 * @Date 2023-04-18 09:58:11
 * @LastEditors 庄泽宇
 * @LastEditTime 2023-07-28 13:43:44
 * -------------------------------------- @AddModifications --------------------------------------
 * @ModifyDescription 删除Home首页引导（现仅有EDU体验租户时开启的引导、出现条件和关闭条件与此引导服务不一致）
 * @ModifyReason
 * （1）2023年设计的该引导针对的新用户（路由中带上参数‘experience’是‘true’）登陆时，满足新用户条件均会进入该引导；
 * （2）2023年设计的引导，均能使用跳过步骤，用户点击跳过会将状态机中的所有引导步骤跳过；
 * 所以，如果不符合上述引导设计的，需要绕过此引导体系，重写逻辑。
 * @ModifyEditor 贾莹
 * @ModifyEditTime 2024-09-20 10:00:00
 */
import { Injectable, OnDestroy } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ShepherdService } from 'angular-shepherd';
import { defaultStepOptions, stepDict, stateMachineList, baseInfo } from './intro.config';
import { StateMachineItem, StateMachineItemState } from './intro.type';
import _ from 'lodash';
import { TranslateService } from '@ngx-translate/core';
import { AppService } from 'pages/apps/app.service';
import { SystemConfigService } from '../system-config.service';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable({ providedIn: 'root' })
export class IntroService implements OnDestroy {
  stateMachineList: StateMachineItem[];
  isActive = false;
  messageCallback: (this: Window, ev: MessageEvent<any>) => any;
  adesignerUrl: string;
  isCheckIsSkipIntro = false;
  isSkipIntro = false;
  projectHandleType: string;

  constructor(
    private route: ActivatedRoute,
    private shepherdService: ShepherdService,
    private translateService: TranslateService,
    private router: Router,
    private appService: AppService,
    protected http: HttpClient,
    protected configService: SystemConfigService,
  ) {
    this.initStateMachine();

    this.initShepherdService();

    this.router.events.subscribe(() => {
      this.cancel();
    });

    this.route.queryParams.subscribe((queryParams) => {
      // 路由参数发生变化时
      this.updateActive(queryParams.experience === 'true');
    });

    this.messageCallback = (e: MessageEvent) => {
      this.handleIntroMessage.bind(this)(e);
    };

    window.addEventListener('message', this.messageCallback);

    baseInfo.appService = this.appService;
    this.configService.get('developerPortalUrl').subscribe((url) => {
      const userInfo = JSON.parse(sessionStorage.getItem('AdUserInfo'));
      const { iamToken = '' } = userInfo;
      baseInfo.studyUrl = `${url}/sso-login?userToken=${iamToken}&routerLink=${encodeURIComponent(
        'knowledge-base/content?fileId=DEVE001',
      )}`;
    });

    this.configService.get('adesignerUrl').subscribe((url) => {
      this.adesignerUrl = url;
      // this.adesignerUrl = 'http://***************:8000';
    });

    // this.checkIsSkipIntro();
  }

  ngOnDestroy(): void {
    window.removeEventListener('message', this.messageCallback);
  }

  initStateMachine() {
    const stroge = sessionStorage.getItem('stateMachineList');
    if (!stroge) {
      this.stateMachineList = stateMachineList;
      this.storeStateMachineList();
    } else {
      this.stateMachineList = JSON.parse(stroge);
    }
  }

  initShepherdService() {
    this.shepherdService.defaultStepOptions = defaultStepOptions;
    this.shepherdService.confirmCancel = false;
    this.shepherdService.modal = true;
  }

  handleIntroMessage(e: MessageEvent) {
    if (e.data.key === 'intro' && e.data.step) {
      this.handleStateMachineItem(e.data.step);
    }
  }

  storeStateMachineList() {
    sessionStorage.setItem('stateMachineList', JSON.stringify(this.stateMachineList));
  }

  // 关于激活，只要激活一次，在页面关闭之前，引导组件将永远处于激活状态
  updateActive(isActive: boolean) {
    if (this.isSkipIntro) {
      sessionStorage.setItem('experience', 'false');
      this.isActive = false;
      return;
    }

    if (sessionStorage.getItem('experience') === 'true') {
      this.isActive = true;
      return;
    }

    if (isActive) {
      sessionStorage.setItem('experience', 'true');
      this.isActive = true;
    }
  }

  handleStateMachineItem(step: string) {
    const stateMachineItem = this.getStateMachineItem(step);
    if (stateMachineItem.state === StateMachineItemState.init) stateMachineItem.state = StateMachineItemState.end;
    this.storeStateMachineList();
    window.opener?.postMessage({ key: 'intro', step: step }, '*');
  }

  getStateMachineItemKey(step: string): string | null {
    const stateMachineItem = this.getStateMachineItem(step);
    if (!stateMachineItem || stateMachineItem.state === StateMachineItemState.end) return null;
    return stateMachineItem.stepKey;
  }

  getStateMachineItem(name: string) {
    return this.stateMachineList.find((item) => {
      return name === item.name;
    });
  }

  getIsSkipIntro(): Observable<any> {
    const url = this.adesignerUrl + '/athena-designer/guide/isSkip';
    return this.http.get(url);
  }

  setIsSkipIntro(param: any): Observable<any> {
    const url = this.adesignerUrl + '/athena-designer/guide/skipSet';
    return this.http.post(url, param);
  }

  async checkIsSkipIntro() {
    const res = await this.getIsSkipIntro().toPromise();

    if (res.code === 0) {
      this.isSkipIntro = res.data;
      this.isCheckIsSkipIntro = true;
      this.updateActive(this.isActive);
    }
  }

  handleIsSkipIntro(isSkip) {
    this.setIsSkipIntro({
      isSkip: isSkip,
    }).subscribe((res) => {
      if (res.code === 0) {
        this.checkIsSkipIntro();
      }
    });
  }

  getSetpList(stepKey: string) {
    if (
      ![
        'generateSchemeStep',
        'generateSchemeFirstStep',
        'addPlanStep',
        'phasedExportBtnStep',
        'phasedExportStep',
        'aiFlowStep',
      ].includes(stepKey)
    ) {
      return stepDict[stepKey]?.map((item) => {
        if (item.text) {
          item.text = this.translateService.instant(`dj-${item.text}`);
        }
        if (item.buttons?.length > 0) {
          item.buttons = item.buttons.map((bottonItem) => {
            if (bottonItem.text) {
              bottonItem.text = this.translateService.instant(`dj-${bottonItem.text}`);
            }
            return bottonItem;
          });
        }
        return item;
      });
    } else {
      return stepDict[stepKey];
    }
  }

  cancel() {
    if (this.shepherdService.isActive) this.shepherdService.cancel();
  }

  async play(step: string, delay?: number) {
    const stepKey = this.getStateMachineItemKey(step);
    if (!stepKey) return;

    if (!this.isCheckIsSkipIntro) await this.checkIsSkipIntro();

    _.delay(
      () => {
        if (!this.isActive) return;

        this.cancel();
        this.shepherdService.addSteps(this.getSetpList(stepKey));

        this.shepherdService.start();
        this.shepherdService.tourObject.on('complete', () => {
          this.handleStateMachineItem(step);
        });
        this.shepherdService.tourObject.on('cancel', () => {
          if (baseInfo.currentCancelisSkipIntro) {
            baseInfo.currentCancelisSkipIntro = false;
            this.handleIsSkipIntro(true);
          }
        });
      },
      delay ? delay : 0,
    );
  }

  async getAiEvent(step) {
    const res = await this.appService.aiEvent({ q: { code: step } }).toPromise();

    if (res.code === 0) {
      if (res.dataList?.length > 0) {
        this.isActive = false;
        if (step === 'aiFlowStep') {
          (document.querySelector('.ai-flow-intro') as HTMLElement).style.display = 'none';
        }
      } else {
        if (step === 'aiFlowStep') {
          (document.querySelector('.ai-flow-intro') as HTMLElement).style.display = 'block';
        }
        this.isActive = true;
      }
    }
  }

  async playWithAI(step: string, callback?: any) {
    const stepKey = this.getStateMachineItemKey(step);
    if (!stepKey) return;

    if (!this.isCheckIsSkipIntro && step !== 'generateSchemeStep') await this.getAiEvent(step);

    _.delay(() => {
      if (!this.isActive && step !== 'generateSchemeStep') return;

      this.cancel();
      this.shepherdService.addSteps(this.getSetpList(stepKey));

      this.shepherdService.start();
      this.shepherdService.tourObject.on('complete', () => {
        this.handleStateMachineItem(step);
      });
      this.shepherdService.tourObject.on('cancel', () => {
        if (baseInfo.currentCancelisSkipIntro) {
          baseInfo.currentCancelisSkipIntro = false;
          if (step !== 'generateSchemeStep') {
            this.appService.aiEventSave({ code: step }).subscribe();
          }
          callback && callback();
        }
      });
    }, 0);
  }
}
