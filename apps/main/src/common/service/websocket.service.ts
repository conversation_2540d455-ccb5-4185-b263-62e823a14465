import { Injectable } from '@angular/core';
import { WebSocketConnection } from 'common/utils/websocket';
import { AdUserService } from 'pages/login/service/user.service';

@Injectable()
export class WebsocketService {
  private websocketInstance: WebSocketConnection;

  constructor(private userService: AdUserService) {}

  /**
   * 工厂：创建websocket连接
   * @param url
   * @param maxReconnectCount
   * @returns
   */
  public createWebsocket(url: string, maxReconnectCount?: number): WebSocketConnection {
    this.websocketInstance = new WebSocketConnection(url, this.userService.getUser('iamToken'), maxReconnectCount);
    return this.websocketInstance;
  }

  public hasWebsocketInstance(): boolean {
    return !!this.websocketInstance;
  }
}
