import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { map, skipWhile } from 'rxjs/operators';
import { ISystemConfig, IAppConfig } from './types';
import { DigiMiddlewareAuthApp } from 'common/config/app-auth-token';

// 维持之前的一些基础设定，尽量减少调用差异
export const DW_APP_AUTH_TOKEN = 'dwSystemConfig.dwAppAuthToken'; // IAM的[各解决方案系統的AppToken].
export const DW_APP_ID = 'dwSystemConfig.dwAppId'; // Application ID(對應到互聯解决方案管理中心)
export const APP_DEFAULT = 'dwSystemConfig.defaultApp'; // 首頁路徑
export const Logo_Path = 'dwSystemConfig.dwLogoPath'; // Logo圖檔路徑
export const APP_DATE_FORMAT = 'dwSystemConfig.dwDateFormat'; // 日期格式
export const APP_TIME_FORMAT = 'dwSystemConfig.dwTimeFormat'; // 時間格式
export const DW_USING_TAB = 'dwSystemConfig.dwUsingTab'; // 是否啟用多頁佈局
export const DW_TAB_MULTI_OPEN = 'dwSystemConfig.dwTabMultiOpen'; // 多頁佈局預設是否可重覆開啟作業
export const LONIG_DEFAULT = 'dwSystemConfig.defaultLogin'; // 登入頁路徑
export const DW_LOAD_MASK_HTTP = 'dwSystemConfig.dwLoadMaskHttp'; // HTTP加載遮罩是否啟用
export const DW_LOAD_MASK_DELAY = 'dwSystemConfig.dwLoadMaskDelay'; // 延遲顯示加載效果的時間毫秒
export const DW_DMC_USERINFO = 'dwSystemConfig.dwDmcUserInfo'; // 文檔中心的登入帳密
// dpt系统变量配置
export const SYSTEM_CONFIG: IAppConfig = {
  dwAppId: 'Athena',
  defaultApp: '/',
  dwLogoPath: './assets/img/dwLogo.svg',
  dwDateFormat: 'yyyy/MM/dd',
  dwTimeFormat: 'HH:mm:ss',
  dwUsingTab: false,
  dwTabMultiOpen: false,
  defaultLogin: '/login',
  dwAppAuthToken: DigiMiddlewareAuthApp,
  dwLoadMaskHttp: true,
  dwLoadMaskDelay: 0,
  dwDmcUserInfo: {
    username: 'Athena',
    password: 'Athena',
  },
};
@Injectable({
  providedIn: 'root',
})
export class SystemConfigService {
  private _systemConfig: ISystemConfig;
  get systemConfig() {
    return this._systemConfig;
  }
  private _systemConfigQuery$ = new BehaviorSubject<Partial<ISystemConfig>>({});

  setSystemConfig(config: ISystemConfig) {
    this._systemConfig = config;
    this._systemConfigQuery$.next(config);
  }

  getConfig(): Observable<Partial<ISystemConfig>> {
    return this._systemConfigQuery$.asObservable();
  }

  get(key: string): Observable<any> {
    return this._systemConfigQuery$.pipe(
      map((result) => {
        return result ? (result[key] ? result[key] : '') : result;
      }),
      skipWhile((result) => {
        return result === null;
      }),
    );
  }
}
