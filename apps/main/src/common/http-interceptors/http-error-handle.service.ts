import { Injectable, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@angular/core';
import { HttpErrorResponse } from '@angular/common/http';
import { NzMessageService } from 'ng-zorro-antd/message';
import { Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { AdUserService } from 'pages/login/service/user.service';
import { GlobalService } from 'common/service/global.service';
@Injectable()
export class AppErrorHandleService implements ErrorHandler {
  tokenInvalidation = false;
  constructor(
    private messageService: NzMessageService,
    private router: Router,
    private translate: TranslateService,
    private adUserService: AdUserService,
    private globalService: GlobalService,
  ) {}
  handleError(error: any): void {
    // 如果是http的错误
    if (error instanceof HttpErrorResponse) {
      if (error.error && (!!error.error.errorMessage || !!error.error.msg)) {
        if (error.error.errorCode === '1001105') {
          this.messageService.error(this.translate.instant('dj-租户不正确'));
        } else if (error.error.code === 1010001) {
          // token失效处理
          if (!this.tokenInvalidation) {
            this.tokenInvalidation = true;
            this.messageService.error(this.translate.instant('dj-token失效'));
            this.adUserService.isLoggedIn$.next(false);
            setTimeout(() => {
              localStorage.clear();
              sessionStorage.clear();
              this.router.navigateByUrl('/login').then(() => {
                location.reload();
              });
            }, 500);
          }
        } else if (error.status === 401 || error.error.errorCode === 401 || error.error.code === 401) {
          localStorage.clear();
          sessionStorage.clear();
          this.adUserService.isLoggedIn$.next(false);
          this.router.navigateByUrl('/login').then(() => {
            location.reload();
          });
        } else if (error.error.errorMessage) {
          if ([2000100001, 2000100002, 2000100003].includes(error.error?.code)) {
            // 全局通过拦截接口异常码，统一处理未授权、解决方案过期等异常
            this.globalService.triggerExceptionCode$.next({ code: error.error.code, errorInfo: error.error });
            return;
          }
          this.messageService.error(error.error.errorMessage);
        } else if (error.error.msg) {
          this.messageService.error(error.error.msg);
        }
      } else {
        this.messageService.error(error.message);
      }
    }
    console.error(error);
  }
}
