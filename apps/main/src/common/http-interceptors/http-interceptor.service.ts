import { HttpInterceptor, HttpRequest, HttpHandler, HttpResponse, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { map, catchError } from 'rxjs/operators';
import { Inject, Injectable } from '@angular/core';
import { UUID } from 'angular2-uuid';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AppErrorHandleService } from './http-error-handle.service';
import { isObject } from '../utils/core.utils';
import { CookieUtil } from '../utils/cookie-util';
import { AD_AUTH_TOKEN } from '../../pages/login/service/auth.service';
import { AdUserService } from '../../pages/login/service/user.service';
import { LocaleService } from 'common/service/locale.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { Router } from '@angular/router';
import { IndividualService } from 'pages/individual/individual.service';
import { isNone } from 'common/utils/core.utils';

@Injectable()
export class HttpInterceptorService implements HttpInterceptor {
  whiteList: string[] = ['iam', 'emc', 'eoc'];
  adesignerUrl: string;
  publishUrl: string;
  iamUrl: string;
  uibotUrl: string;
  appToken: string;
  tenantAppToken: string;
  platformCategory: any;

  constructor(
    @Inject(AD_AUTH_TOKEN) protected authToken: any,
    private messageService: NzMessageService,
    private languageService: LocaleService,
    private userService: AdUserService,
    private appErrorHandleService: AppErrorHandleService,
    protected configService: SystemConfigService,
    private router: Router,
    private individualService: IndividualService,
  ) {
    this.configService.getConfig().subscribe((config) => {
      const { adesignerUrl, publishUrl, appToken, tenantAppToken, platformCategory, iamUrl, uibotUrl } = config;
      this.adesignerUrl = adesignerUrl;
      this.publishUrl = publishUrl;
      this.appToken = appToken;
      this.tenantAppToken = tenantAppToken;
      this.platformCategory = platformCategory;
      this.iamUrl = iamUrl;
      this.uibotUrl = uibotUrl;
    });
  }

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<any> {
    // 每个请求添加唯一标志uuid
    req = this.setUUid(req);

    // 设置语言别
    req = this.setLocale(req);

    // 设置token
    req = this.setToken(req);

    // 设置平台别
    req = this.setClientAgent(req);

    // 设置routerKey
    req = this.setRouterKey(req);

    // 设置appToken
    req = this.setAppToken(req);

    // 设置分支
    req = this.setBranch(req);

    // 设置租户级
    req = this.setTenant(req);

    // 设置个案应用
    if (this.platformCategory !== 'TENANT') {
      req = this.setIndividualHeader(req);
    }

    // 设置租户级下大ai相关特殊请求头
    if (this.platformCategory === 'TENANT') {
      req = this.setAiModel(req);
    }

    // 统一处理date
    // req = this.setDate(req);

    return next.handle(req).pipe(
      map((res: HttpResponse<any>) => {
        // 统一处理response
        return this.interceptResponse(res);
      }),
      catchError((error: any) => {
        // console.error(error);
        // AI生成字段特殊处理
        if (!error.url.includes('ai/gptGenerateFieldName')) {
          this.appErrorHandleService.handleError(error);
        }
        // throw error;
        return throwError(error);
      }),
    );
  }

  // 处理uuid
  private setUUid(req: HttpRequest<any>): HttpRequest<any> {
    req = req.clone({
      setHeaders: {
        'X-Requested-With': UUID.UUID(),
      },
    });
    return req;
  }

  // 处理语言别
  private setLocale(req: HttpRequest<any>): HttpRequest<any> {
    if (this.languageService && this.languageService.currentLocale) {
      req = req.clone({
        setHeaders: {
          locale: this.languageService.currentLocale,
        },
      });
    }
    return req;
  }

  // 处理token
  private setToken(req: HttpRequest<any>): HttpRequest<any> {
    // 需要传iamToken的判断
    if (this.authToken.iamToken && !req.headers.get('token')) {
      if (this.checkUrl('token', req.url)) {
        req = req.clone({
          setHeaders: {
            token: this.authToken.iamToken,
          },
        });
      }
    }
    // 兼容api平台:用户token
    if (this.authToken.iamToken && !req.headers.get('digi-middleware-auth-user')) {
      req = req.clone({
        setHeaders: {
          'digi-middleware-auth-user': this.authToken.iamToken,
        },
      });
    }
    // 开发平台token
    if (this.authToken.token && !req.headers.get('Authorization')) {
      if (!req.url.includes('/iam/')) {
        req = req.clone({
          setHeaders: {
            Authorization: this.authToken.token,
          },
        });
      }
    }
    return req;
  }

  // 设置平台别
  private setClientAgent(req: HttpRequest<any>): HttpRequest<any> {
    req = req.clone({
      setHeaders: {
        'client-agent': 'webplatform',
      },
    });
    return req;
  }

  // 设置routerKey
  private setRouterKey(req: HttpRequest<any>): HttpRequest<any> {
    if (this.checkUrl('routerKey', req.url)) {
      const tenantId = this.userService.getUser('tenantId');
      if (!!tenantId) {
        req = req.clone({
          setHeaders: {
            routerKey: tenantId,
          },
        });
        if (CookieUtil.get('routerKey') !== tenantId) {
          CookieUtil.set('routerKey', tenantId);
        }
      }
    }
    return req;
  }

  // 设置appToken
  // private setAppToken(req: HttpRequest<any>): HttpRequest<any> {
  //   const k = { TENANT: 'tenantAppToken', SYSTEM: 'appToken' };

  //   if (this.checkUrl('appToken', req.url)) {
  //     if (!req.headers.get('digi-middleware-auth-app')) {
  //       req = req.clone({
  //         setHeaders: {
  //           'digi-middleware-auth-app': this.platformCategory === 'TENANT' ? this.tenantAppToken : this.appToken,
  //         },
  //       });
  //     }
  //   }
  //   return req;
  // }

  private setAppToken(req: HttpRequest<any>): HttpRequest<any> {
    const k = { TENANT: 'tenantAppToken', SYSTEM: 'appToken' };
    const appToken = { TENANT: this.tenantAppToken, SYSTEM: this.appToken };

    if (this.checkUrl(k[this.platformCategory], req.url)) {
      if (!req.headers.get('digi-middleware-auth-app')) {
        req = req.clone({
          setHeaders: {
            'digi-middleware-auth-app': appToken[this.platformCategory],
          },
        });
      }
    }
    return req;
  }

  // 设置分支
  private setBranch(req: HttpRequest<any>): HttpRequest<any> {
    const branch = this.userService.getUser('branch');
    if (!!branch && this.checkUrl('branch', req.url)) {
      req = req.clone({
        setHeaders: {
          branch: branch,
        },
      });
    }
    return req;
  }

  // 设置租户级(租户级开发平台的branch固定设置为master)
  private setTenant(req: HttpRequest<any>): HttpRequest<any> {
    const isTenantActive = this.userService.getUser('isTenantActive');
    if (!!isTenantActive && this.checkUrl('branch', req.url)) {
      req = req.clone({
        setHeaders: {
          branch: 'master',
        },
      });
    }
    return req;
  }

  /**
   * 设置个案应用请求头
   * @param req
   * @returns
   */
  private setIndividualHeader(req: HttpRequest<any>): HttpRequest<any> {
    // 前端控制只有adp和aadc的域名加请求头
    if (![this.adesignerUrl, this.publishUrl].some((urlKey) => req.url.includes(urlKey))) {
      return req;
    }
    // 接口白名单
    if (
      [`${this.iamUrl}/api/iam/v2/permission/user`, `${this.uibotUrl}/api/ai/v1/bot/designer/view/category`].some(
        (url) => url === req.url,
      )
    ) {
      return req;
    }
    const { individualCaseApp, individualCaseApps, individualCaseDeployer, individualCaseAppCode } =
      this.individualService;
    if (individualCaseApp || individualCaseApps || individualCaseDeployer) {
      req = req.clone({
        setHeaders: {
          individualCase: 'true',
        },
      });
      if (!isNone(individualCaseAppCode)) {
        req = req.clone({
          setHeaders: {
            individualCaseAppCode,
          },
        });
      }
    }
    return req;
  }

  private setAiModel(req: HttpRequest<any>): HttpRequest<any> {
    const isTenantActive = this.userService.getUser('isTenantActive');
    // noLevel里的接口不需要level
    const noLevel = ['process/findProcessCountByTriggerType', 'process/tenant/findProcessPagination'];

    if (!!isTenantActive && this.checkUrl('branch', req.url)) {
      const containsNoLevel = noLevel.some((path) => req.url.includes(path));

      if (containsNoLevel) {
        return req;
      }

      req = req.clone({
        setHeaders: {
          level: 'tenant',
        },
      });
    }
    return req;
  }

  // 匹配url
  private checkUrl(flag: string, url: string): boolean {
    if (!flag) return false;
    const reflectUrl = {
      token: ['/iam/', '/athenadeployer', '/apimgmt', '/bot/'],
      routerKey: [
        'uibot',
        'atmc',
        'atdm',
        'aim',
        'aam',
        'smartdata',
        'flowengine',
        'im',
        'digiwinabi',
        'thememap',
        'themedata',
        'apimgmt',
        'deployer',
        this.adesignerUrl,
      ],
      branch: [this.adesignerUrl],
      appToken: ['iam', 'boss', 'cac', 'dmc', 'msc', 'emc', 'lmc', 'eoc', 'pmc', 'omc', 'gmc'],
      tenantAppToken: ['iam', 'boss', 'cac', 'dmc', 'msc', 'emc', 'lmc', 'eoc', 'pmc', 'omc', 'gmc'],
    };
    return reflectUrl[flag].some((s) => url.includes(s));
  }

  // 处理、包装response
  private interceptResponse(res: HttpResponse<any>): HttpResponse<any> {
    if (isObject(res) && res instanceof HttpResponse && isObject(res.body)) {
      const { url, body, headers } = res as any;
      // 符合规范的接口
      if (this.whiteList.every((item) => !url.includes(item)) && body.status) {
        if (body.status === 200) {
          res = res.clone({
            body: {
              code: 0,
              data: body.response,
              url,
            },
          });
        } else {
          const { errorMessage, statusDescription, errorCode, status } = body;
          const message = errorMessage || statusDescription;
          this.messageService.error(message);
          throw new HttpErrorResponse({
            error: {
              errorMessage: message,
              errorCode,
              code: status,
            },
            headers,
            status,
            statusText: status,
            url,
          });
        }
      } else {
        // 未授权
        if (body.code && body.code === 21007) {
          this.router.navigateByUrl('/unauthorised', { replaceUrl: true });
        }
        // 不符合规范的接口
        else if (body.code && body.code !== 200 && this.handleFilterApi(res)) {
          const { message, msg, code } = body;
          throw new HttpErrorResponse({
            error: {
              errorMessage: message || msg,
              errorCode: code,
              code,
            },
            headers,
            status: code,
            statusText: code,
            url,
          });
        }
      }
    }
    // this.convertAllStrDateFromData(res.body);
    return res;
  }

  // 过滤ai、gpt等不需要处理的api
  private handleFilterApi(res: HttpResponse<any>): boolean {
    const { url, body } = res as any;
    return !(
      ((url.includes('ai/gptResult') || url.includes('ai/chatResult') || url.includes('chat/todo')) &&
        [0, 1, 2, 3].includes(body.code)) ||
      url.includes('paradigm/deleteParadigm')
    );
  }

  // 处理date, 循环修改返回数据中的时间格式从-改为/
  // 方法已删除, 后续使用请联系思伟或参考athena-web
  // private setDate(req): void {
  //   if (req.body['data']) {
  //     this.dateFormateService.formatDate(req.body['data']);
  //   } else {
  //     this.dateFormateService.formatDate(req.body);
  //   }
  //   this.convertAllStrDateFromData(req.body, true);
  // }
}
