import { enableProdMode } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';

import { AppModule } from './app/app.module';

import { registerLocaleData } from '@angular/common';
import zh from '@angular/common/locales/zh';
import { environment } from './environments/environment';
import microApp from '@micro-zoe/micro-app';
import { StandaloneMicroappName } from 'common/types/standalone.type';
import './service-worker-registry';
import { CookieUtil } from 'common/utils/cookie-util';
// import { loadTheme } from './pages/tools/load-ag-theme';

// 将所有正在运行的子解决方案路由信息同步到浏览器地址上，包含预渲染解决方案
microApp.router.attachAllToURL({ includePreRender: true });

if (environment.production) {
  enableProdMode();
  const originLog = console.log;
  console.log = (...rest) => {
    if (window.location?.href?.indexOf('#debug') > -1) {
      originLog?.apply(null, rest);
    }
  };
  console.warn = () => {};
  // console.error = () => {};
}

// 预加载资源
// microApp.preFetch([{ name:StandaloneMicroappName.TBB, url: 'https://tbb.apps.digiwincloud.com.cn:2456' }]);

// console.log(zh);
microApp.start({
  'disable-memory-router': true, // 关闭虚拟路由系统
  'disable-patch-request': true, // 关闭子应用请求的自动补全功能
  plugins: {
    modules: {
      [StandaloneMicroappName.TBB]: [
        {
          loader(code, url) {
            if (url.includes('gc.spread.sheets.all.min.js')) {
              code = code.replace('var GC=', 'window.GC=');
            }
            return code;
          },
        },
      ],
      /**
       * WARN: 增加如下处理，解决在主应用打开monaco-editor之后，再打开lowcode的界面设计器报错问题
       * 原因：
       *  在主应用打开monaco-editor之后，monaco-editor的loader.js会在window全局注册如下四个属性，这四个属性管理monaco-editor的资源加载（AMD）
       *  造成后面打开界面设计器报错的属性是: define
       *  前置操作完成之后，打开界面设计器，这时候microApp会劫持加载js，出现问题的是react-dom.min.production.js,它里面判断了window上的define是否存在，如果存在，它会认为当前在amd环境，所以。。。
       *  而monaco-editor这个define和传统的amd加载的define并不一致，后续执行就会报错
       *  这边有一个很恶心的点，就是，microApp加载子应用不是有沙箱吗？为什么主应用的define会被子应用用到，
       *  具体原因参见microApp文档：https://jd-opensource.github.io/micro-app/0.x/docs.html#/zh-cn/plugins?id=%e4%bd%bf%e7%94%a8%e6%96%b9%e5%bc%8f
       *  microApp.start({ plugins: global: [{ scopeProperties }] }), 这个scopeProperties属性的说明，核心的话就是：
       *  "默认情况下子应用无法找到的全局变量会兜底到基座应用中，scopeProperties可以禁止这种情况"
       * 处理：
       *  如下处理，就是利用这个属性，彻底屏蔽loader.js在window上注入的这四个属性
       */
      [StandaloneMicroappName.ATHENA_DESIGNER_EDITOR]: [
        {
          scopeProperties: ['define', 'AmdLoader', 'require', 'monaco'],
        },
      ],
      [StandaloneMicroappName.BUSINESS_SHARE]: [
        {
          scopeProperties: ['define', 'AmdLoader', 'require', 'monaco'],
        },
      ],
    },
  },
  fetch(url, options, appName) {
    // 机制设计微前端子应用
    if (appName === 'athena-mechanism-core' && !url.includes('at.alicdn.com')) {
      const config = {
        ...options,
        headers: Object.assign(options.headers || {}, { routerKey: CookieUtil.get('routerKey') }),
      } as any;
      return window.fetch(url, config).then((res) => {
        return res.text();
      });
    }

    return window.fetch(url, options).then((res) => {
      return res.text();
    });
  },
});

// 将microApp 挂载 到 window上
// 在这里，如果多层嵌套的 microApp 环境，子层级的 microApp 其实并没法 实际控制 孙应用，所以需要 取到 主应用的 microApp 对象
// 这里带上ath 前缀，避免 冲突
// TODO 更新：micro app 已经提供了解法，所以该逻辑 就不需要了，保留一个迭代验证后删除
window.athMicroApp = microApp;

registerLocaleData(zh);
platformBrowserDynamic()
  .bootstrapModule(AppModule)
  .then()
  .catch((err) => console.error(err));
