{"apiUrl": "@WEB_SERVICE_URL@", "iamUrl": "@IAM_SERVICE_URL@", "emcUrl": "@EMC_SERVICE_URL@", "mscUrl": "@MSC_SERVICE_URL@", "eocUrl": "@EOC_SERVICE_URL@", "dmcUrl": "@DMC_SERVICE_URL@", "themeMapUrl": "@THEMEMAP_SERVICE_URL@", "smartDataUrl": "@SMARTDATA_SERVICE_URL@", "uibotUrl": "@UIBOT_URL@", "agileInteractionUrl": "@AGILE_INTERACTION_URL@", "atmcUrl": "@ATMC_URL@", "multiTenant": "@MULTI_TENANT@", "athenaUrl": "@ATHENA_URL@", "consoleUrl": "@CONSOLE_URL@", "muiUrl": "@MUI_URL@", "bpmUrl": "@BPM_URL@", "digiwincloudUrl": "@DIGIWINCLOUD_URL@", "aimUrl": "@AIM_URL@", "aamUrl": "@AAM_URL@", "adesignerUrl": "@ADESIGNER_URL@", "uiDesignerUrl": "@UIDESIGNER_URL@", "publishUrl": "@PUBLISH_URL@", "clientId": "@CLIENT_ID@", "appToken": "@APP_TOKEN@", "tenantAppToken": "@TENANT_APP_TOKEN@", "abiReportUrl": "@ABIREPORT_URL@", "lcdpDesignerUrl": "@LCDPDESIGNER_URL@", "apiMgmtUrl": "@APIMGMT_URL@", "athenaDeployUrl": "@ATHENADEPLOY_URL@", "tbbReportUrl": "@TBBREPORT_URL@", "tbbLoginUrl": "@TBBLOGIN_URL@", "kcfUrl": "@KCF_URL@", "gmcUrl": "@GMC_SERVICE_URL@", "bossIamUrl": "@BOSSIAM_SERVICE_URL@", "athenaConsole": "@ATHENA_CONSOLE@", "designerUrl": "@DESIGNER_URL@", "mobileUrl": "@MOBILE_URL@", "tddUrl": "@TDD_URL@", "imUrl": "@IM_URL@", "tbbUrl": "@TBB_URL@", "biUrl": "@BI_URL@", "alearningUrl": "@ALEARNING_URL@", "taskEngineUrl": "@TASK_ENGINE_URL@", "nlpImUrl": "@NLP_IM_URL@", "nlpBotUrl": "@NLP_BOT_URL@", "itUrl": "@IT_URL@", "ocrApiUrl": "@OCR_API_URL@", "mdcUrl": "@MDC_URL@", "lmcUrl": "@LMC_URL@", "ddsmDesignerUrl": "@DDSM_DESIGNER_URL@", "espUrl": "@ESP_URL@", "ganttChartServiceUrl": "@GANTT_CHART_SERVICE_URL@", "apaTKUrl": "@APA_TK_URL@", "semcUrl": "@SEMC_URL@", "semcWebUrl": "@SEMC_WEB_URL@", "knowledgeMapsUrl": "@KNOWLEDGE_MAPS_URL@", "developerPortalUrl": "@DEVELOPER_PORTAL_URL@", "atdmUrl": "@ATDM_URL@", "athenaDesignerCoreUrl": "@ATHENA_DESIGNER_CORE_URL@", "dccUrl": "@DCC_URL@", "developerUrl": "@DEVELOPER_URL@", "asaDesignerUrl": "@ASA_DESIGNER_URL@", "hooksDemoUrl": "@HOOKS_DEMO_URL@", "enableMqtt": "@ENABLE_MQTT@", "mqttUrl": "@MQTT_URL@", "mqttPort": "@MQTT_PORT@", "mqttPath": "@MQTT_PATH@", "mqttUserName": "@MQTT_USER_NAME@", "mqttPwd": "@MQTT_PWD@", "mqttTopicPrefix": "@MQTT_TOPIC_PREFIX@", "appCodeEnv": "@APPCODE_ENV@", "envAlias": "@ENV_ALIAS@", "experienceServiceCode": "@EXPERIENCE_SERVICE_CODE@", "cloud": "@CLOUD@", "platformCategory": "@PLATFORM_CATEGORY@", "tenantDesignerUrl": "@TENANT_DESIGNER_URL@", "athenaDesignerUrl": "@ATHENA_DESIGNER_URL@", "athenaMechanismCoreUrl": "@ATHENA_MECHANISM_CORE_URL@", "dataCenterUrl": "@DATA_CENTER_URL@", "devOpsUrl": "@DEV_OPS_URL@", "isLandholder": "@IS_LANDHOLDER@", "tbbRenderUrl": "@TBB_RENDER_URL@", "hiddenMenuByEnv": "@HIDDEN_MENU_BY_ENV@", "allowLowcodeTenants": "@ALL_LOWCODE_TENANTS@", "nanaUrl": "@NANA_URL@", "aiEnv": "@AI_ENV@", "scherecom": "@SCHERECOM@", "enableOpenVscode": "@ENABLE_OPEN_VSCODE@", "showHighCodeByAppTypes": "@SHOW_HIGH_CODE_BY_APP_TYPES@"}