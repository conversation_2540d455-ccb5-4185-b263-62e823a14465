{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"athena-designer": {"root": "src", "sourceRoot": "src", "projectType": "application", "architect": {"build": {"builder": "@angular-builders/custom-webpack:browser", "options": {"allowedCommonJsDependencies": ["crypto-js", "lodash", "ag-grid-enterprise", "echarts"], "customWebpackConfig": {"path": "./webpack.config.ts"}, "outputPath": "dist", "index": "src/index.html", "main": "src/main.ts", "tsConfig": "src/tsconfig.app.json", "polyfills": "src/polyfills.ts", "assets": ["src/favicon.ico", "src/service-worker.js", {"glob": "**/*", "input": "src/assets", "output": "assets", "ignore": ["i18n-origin/**/*"]}, {"glob": "**/*", "input": "./node_modules/@ant-design/icons-angular/src/inline-svg/", "output": "assets"}, {"glob": "**/*", "input": "./node_modules/monaco-editor/min/vs", "output": "/assets/monaco-editor/min/vs"}], "styles": ["node_modules/ng-zorro-antd-mobile/src/ng-zorro-antd-mobile.min.css", "node_modules/shepherd.js/dist/css/shepherd.css", "projects/form-editor-components/src/lib/components/styles/components.less", "projects/mobile-ui/src/lib/styles/mobile-ui-components.less", "projects/mobile-ui/src/lib/styles/theme.less", "node_modules/vditor/src/assets/less/index.less", "src/assets/css/ag-grid-athena.css", "src/assets/css/theme/theme-darkblue.less", "src/components/ad-ui-components/assets/index.less", "src/assets/css/index.less", "node_modules/quill/dist/quill.core.css", "node_modules/quill/dist/quill.snow.css"], "scripts": ["node_modules/echarts/dist/echarts.min.js", "node_modules/jsencrypt/bin/jsencrypt.js", "src/assets/iconfont/iconfont.js"]}, "configurations": {"production": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": true, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.ts"}], "serviceWorker": true}, "production-mobile": {"optimization": true, "outputHashing": "all", "sourceMap": false, "extractCss": true, "namedChunks": false, "aot": true, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": true, "fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.prod.mobile.ts"}], "serviceWorker": true}, "service-worker-dev": {"optimization": false, "outputHashing": "all", "sourceMap": true, "extractCss": true, "namedChunks": false, "aot": false, "extractLicenses": true, "vendorChunk": false, "buildOptimizer": false, "serviceWorker": true}, "development": {"buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}, "mobile": {"fileReplacements": [{"replace": "src/environments/environment.ts", "with": "src/environments/environment.mobile.ts"}], "buildOptimizer": false, "optimization": false, "vendorChunk": true, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "development"}, "serve": {"builder": "@angular-builders/custom-webpack:dev-server", "options": {"browserTarget": "athena-designer:build:development"}, "configurations": {"production": {"browserTarget": "athena-designer:build:production"}, "development": {"browserTarget": "athena-designer:build:development"}, "mobile": {"browserTarget": "athena-designer:build:mobile"}}}, "extract-i18n": {"builder": "@angular-devkit/build-angular:extract-i18n", "options": {"browserTarget": "athena-designer:build"}}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["src/**/*.ts", "src/**/*.html"]}}}}, "mobile-ui": {"projectType": "library", "root": "projects/mobile-ui", "sourceRoot": "projects/mobile-ui/src", "prefix": "lib", "architect": {"build": {"builder": "@angular-devkit/build-angular:ng-packagr", "options": {"project": "projects/mobile-ui/ng-package.json"}, "configurations": {"production": {"tsConfig": "projects/mobile-ui/tsconfig.lib.prod.json"}, "development": {"tsConfig": "projects/mobile-ui/tsconfig.lib.json"}}, "defaultConfiguration": "production"}, "lint": {"builder": "@angular-eslint/builder:lint", "options": {"lintFilePatterns": ["projects/mobile-ui/**/*.ts", "projects/mobile-ui/**/*.html"]}}}}}, "defaultProject": "at<PERSON>a-designer", "schematics": {"@schematics/angular:component": {"prefix": "app", "style": "less", "skipTests": true}, "@schematics/angular:directive": {"prefix": "app", "skipTests": true}, "@schematics/angular:pipe": {"skipTests": true}, "@schematics/angular:service": {"skipTests": true}}, "cli": {"warnings": {}, "analytics": false, "defaultCollection": "@angular-eslint/schematics"}}